{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/AI-Code/fire_UI/frontnextjs/src/components/Navbar.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState } from 'react';\r\nimport Link from 'next/link';\r\n\r\nconst Navbar: React.FC = () => {\r\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\r\n\r\n  const toggleMenu = () => {\r\n    setIsMenuOpen(!isMenuOpen);\r\n  };\r\n\r\n  const closeMenu = () => {\r\n    setIsMenuOpen(false);\r\n  };\r\n\r\n  return (\r\n    <nav className=\"navbar navbar-expand-lg navbar-dark bg-dark\">\r\n      <div className=\"container-fluid\">\r\n        <Link className=\"navbar-brand\" href=\"/\" onClick={closeMenu}>\r\n          FIRE Dashboard\r\n        </Link>\r\n        <button\r\n          className=\"navbar-toggler d-lg-none\"\r\n          type=\"button\"\r\n          onClick={toggleMenu}\r\n          aria-controls=\"navbarNavContent\"\r\n          aria-expanded={isMenuOpen}\r\n          aria-label=\"Toggle navigation\"\r\n        >\r\n          <span className=\"navbar-toggler-icon\"></span>\r\n        </button>\r\n        <div className=\"navbar-nav-wrapper d-lg-flex\">\r\n          <ul className=\"navbar-nav mb-2 mb-lg-0 d-lg-flex\">\r\n            <li className=\"nav-item\">\r\n              <Link className=\"nav-link\" href=\"/\" onClick={closeMenu}>\r\n                Dashboard\r\n              </Link>\r\n            </li>\r\n            <li className=\"nav-item\">\r\n              <Link className=\"nav-link\" href=\"/assets\" onClick={closeMenu}>\r\n                Patrimoine\r\n              </Link>\r\n            </li>\r\n            <li className=\"nav-item\">\r\n              <Link className=\"nav-link\" href=\"/liabilities\" onClick={closeMenu}>\r\n                Emprunts\r\n              </Link>\r\n            </li>\r\n            <li className=\"nav-item\">\r\n              <Link className=\"nav-link\" href=\"/scpi\" onClick={closeMenu}>\r\n                SCPI\r\n              </Link>\r\n            </li>\r\n            <li className=\"nav-item\">\r\n              <Link className=\"nav-link\" href=\"/evolution\" onClick={closeMenu}>\r\n                Évolution\r\n              </Link>\r\n            </li>\r\n            <li className=\"nav-item\">\r\n              <Link className=\"nav-link\" href=\"/budget\" onClick={closeMenu}>\r\n                Budget FIRE\r\n              </Link>\r\n            </li>\r\n            <li className=\"nav-item\">\r\n              <Link className=\"nav-link\" href=\"/scenarios\" onClick={closeMenu}>\r\n                Scénarios\r\n              </Link>\r\n            </li>\r\n            <li className=\"nav-item\">\r\n              <Link className=\"nav-link\" href=\"/fire\" onClick={closeMenu}>\r\n                Objectif FIRE\r\n              </Link>\r\n            </li>\r\n            <li className=\"nav-item\">\r\n              <Link className=\"nav-link\" href=\"/test\" onClick={closeMenu}>\r\n                Test API\r\n              </Link>\r\n            </li>\r\n          </ul>\r\n        </div>\r\n        {/* Mobile menu */}\r\n        {isMenuOpen && (\r\n          <div className=\"navbar-collapse show d-lg-none\">\r\n            <ul className=\"navbar-nav\">\r\n              <li className=\"nav-item\">\r\n                <Link className=\"nav-link\" href=\"/\" onClick={closeMenu}>\r\n                  Dashboard\r\n                </Link>\r\n              </li>\r\n              <li className=\"nav-item\">\r\n                <Link className=\"nav-link\" href=\"/assets\" onClick={closeMenu}>\r\n                  Patrimoine\r\n                </Link>\r\n              </li>\r\n              <li className=\"nav-item\">\r\n                <Link className=\"nav-link\" href=\"/liabilities\" onClick={closeMenu}>\r\n                  Emprunts\r\n                </Link>\r\n              </li>\r\n              <li className=\"nav-item\">\r\n                <Link className=\"nav-link\" href=\"/scpi\" onClick={closeMenu}>\r\n                  SCPI\r\n                </Link>\r\n              </li>\r\n              <li className=\"nav-item\">\r\n                <Link className=\"nav-link\" href=\"/evolution\" onClick={closeMenu}>\r\n                  Évolution\r\n                </Link>\r\n              </li>\r\n              <li className=\"nav-item\">\r\n                <Link className=\"nav-link\" href=\"/budget\" onClick={closeMenu}>\r\n                  Budget FIRE\r\n                </Link>\r\n              </li>\r\n              <li className=\"nav-item\">\r\n                <Link className=\"nav-link\" href=\"/scenarios\" onClick={closeMenu}>\r\n                  Scénarios\r\n                </Link>\r\n              </li>\r\n              <li className=\"nav-item\">\r\n                <Link className=\"nav-link\" href=\"/fire\" onClick={closeMenu}>\r\n                  Objectif FIRE\r\n                </Link>\r\n              </li>\r\n              <li className=\"nav-item\">\r\n                <Link className=\"nav-link\" href=\"/test\" onClick={closeMenu}>\r\n                  Test API\r\n                </Link>\r\n              </li>\r\n            </ul>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </nav>\r\n  );\r\n};\r\n\r\nexport default Navbar;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKA,MAAM,SAAmB;IACvB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,aAAa;QACjB,cAAc,CAAC;IACjB;IAEA,MAAM,YAAY;QAChB,cAAc;IAChB;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,4JAAA,CAAA,UAAI;oBAAC,WAAU;oBAAe,MAAK;oBAAI,SAAS;8BAAW;;;;;;8BAG5D,8OAAC;oBACC,WAAU;oBACV,MAAK;oBACL,SAAS;oBACT,iBAAc;oBACd,iBAAe;oBACf,cAAW;8BAEX,cAAA,8OAAC;wBAAK,WAAU;;;;;;;;;;;8BAElB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAG,WAAU;;0CACZ,8OAAC;gCAAG,WAAU;0CACZ,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,WAAU;oCAAW,MAAK;oCAAI,SAAS;8CAAW;;;;;;;;;;;0CAI1D,8OAAC;gCAAG,WAAU;0CACZ,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,WAAU;oCAAW,MAAK;oCAAU,SAAS;8CAAW;;;;;;;;;;;0CAIhE,8OAAC;gCAAG,WAAU;0CACZ,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,WAAU;oCAAW,MAAK;oCAAe,SAAS;8CAAW;;;;;;;;;;;0CAIrE,8OAAC;gCAAG,WAAU;0CACZ,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,WAAU;oCAAW,MAAK;oCAAQ,SAAS;8CAAW;;;;;;;;;;;0CAI9D,8OAAC;gCAAG,WAAU;0CACZ,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,WAAU;oCAAW,MAAK;oCAAa,SAAS;8CAAW;;;;;;;;;;;0CAInE,8OAAC;gCAAG,WAAU;0CACZ,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,WAAU;oCAAW,MAAK;oCAAU,SAAS;8CAAW;;;;;;;;;;;0CAIhE,8OAAC;gCAAG,WAAU;0CACZ,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,WAAU;oCAAW,MAAK;oCAAa,SAAS;8CAAW;;;;;;;;;;;0CAInE,8OAAC;gCAAG,WAAU;0CACZ,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,WAAU;oCAAW,MAAK;oCAAQ,SAAS;8CAAW;;;;;;;;;;;0CAI9D,8OAAC;gCAAG,WAAU;0CACZ,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,WAAU;oCAAW,MAAK;oCAAQ,SAAS;8CAAW;;;;;;;;;;;;;;;;;;;;;;gBAOjE,4BACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAG,WAAU;;0CACZ,8OAAC;gCAAG,WAAU;0CACZ,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,WAAU;oCAAW,MAAK;oCAAI,SAAS;8CAAW;;;;;;;;;;;0CAI1D,8OAAC;gCAAG,WAAU;0CACZ,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,WAAU;oCAAW,MAAK;oCAAU,SAAS;8CAAW;;;;;;;;;;;0CAIhE,8OAAC;gCAAG,WAAU;0CACZ,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,WAAU;oCAAW,MAAK;oCAAe,SAAS;8CAAW;;;;;;;;;;;0CAIrE,8OAAC;gCAAG,WAAU;0CACZ,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,WAAU;oCAAW,MAAK;oCAAQ,SAAS;8CAAW;;;;;;;;;;;0CAI9D,8OAAC;gCAAG,WAAU;0CACZ,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,WAAU;oCAAW,MAAK;oCAAa,SAAS;8CAAW;;;;;;;;;;;0CAInE,8OAAC;gCAAG,WAAU;0CACZ,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,WAAU;oCAAW,MAAK;oCAAU,SAAS;8CAAW;;;;;;;;;;;0CAIhE,8OAAC;gCAAG,WAAU;0CACZ,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,WAAU;oCAAW,MAAK;oCAAa,SAAS;8CAAW;;;;;;;;;;;0CAInE,8OAAC;gCAAG,WAAU;0CACZ,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,WAAU;oCAAW,MAAK;oCAAQ,SAAS;8CAAW;;;;;;;;;;;0CAI9D,8OAAC;gCAAG,WAAU;0CACZ,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,WAAU;oCAAW,MAAK;oCAAQ,SAAS;8CAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU5E;uCAEe", "debugId": null}}, {"offset": {"line": 448, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/AI-Code/fire_UI/frontnextjs/src/components/BootstrapClient.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\n\nexport default function BootstrapClient() {\n  useEffect(() => {\n    // Dynamically import Bootstrap JavaScript\n    import('bootstrap/dist/js/bootstrap.bundle.min.js');\n  }, []);\n\n  return null;\n}\n"], "names": [], "mappings": ";;;AAEA;AAFA;;AAIe,SAAS;IACtB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,0CAA0C;;IAE5C,GAAG,EAAE;IAEL,OAAO;AACT", "debugId": null}}]}