(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{2365:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let a=r(3464).A.create({baseURL:"http://localhost:8000/api",headers:{"Content-Type":"application/json"},timeout:1e4});a.interceptors.response.use(e=>e,e=>{var t,r;return console.error("API Error:",null==(t=e.response)?void 0:t.status,null==(r=e.config)?void 0:r.url,e.message),Promise.reject(e)});let s=a},3622:(e,t,r)=>{"use strict";r.d(t,{A:()=>u});var a=r(9300),s=r.n(a),n=r(2115),l=r(7390),i=r(9385),c=r(5155);function d(e,t){let{min:r,now:a,max:n,label:l,visuallyHidden:i,striped:d,animated:o,className:u,style:h,variant:m,bsPrefix:x,...g}=e;return(0,c.jsx)("div",{ref:t,...g,role:"progressbar",className:s()(u,"".concat(x,"-bar"),{["bg-".concat(m)]:m,["".concat(x,"-bar-animated")]:o,["".concat(x,"-bar-striped")]:o||d}),style:{width:"".concat(Math.round((a-r)/(n-r)*1e5)/1e3,"%"),...h},"aria-valuenow":a,"aria-valuemin":r,"aria-valuemax":n,children:i?(0,c.jsx)("span",{className:"visually-hidden",children:l}):l})}let o=n.forwardRef((e,t)=>{let{isChild:r=!1,...a}=e,o={min:0,max:100,animated:!1,visuallyHidden:!1,striped:!1,...a};if(o.bsPrefix=(0,l.oU)(o.bsPrefix,"progress"),r)return d(o,t);let{min:u,now:h,max:m,label:x,visuallyHidden:g,striped:b,animated:y,bsPrefix:f,variant:j,className:p,children:v,...N}=o;return(0,c.jsx)("div",{ref:t,...N,className:s()(p,f),children:v?(0,i.Tj)(v,e=>(0,n.cloneElement)(e,{isChild:!0})):d({min:u,now:h,max:m,label:x,visuallyHidden:g,striped:b,animated:y,bsPrefix:f,variant:j},t)})});o.displayName="ProgressBar";let u=o},3792:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d});var a=r(5155),s=r(2115),n=r(2365),l=r(4065),i=r(2502),c=r(3622);i.t1.register(i.Bs,i.m_,i.s$);let d=()=>{let[e,t]=(0,s.useState)(null),[r,i]=(0,s.useState)([]),[d,o]=(0,s.useState)({}),[u,h]=(0,s.useState)(!0),[m,x]=(0,s.useState)(null),[g,b]=(0,s.useState)(0),[y,f]=(0,s.useState)(!1),j=[{category:"Liquidit\xe9",categoryKey:"Liquidit\xe9",defaultTargetPercent:2.5},{category:"Bourse",categoryKey:"Bourse",defaultTargetPercent:35.5},{category:"Immobilier",categoryKey:"Immobilier",defaultTargetPercent:35},{category:"Fonds s\xe9curis\xe9s",categoryKey:"Fonds s\xe9curis\xe9s",defaultTargetPercent:20},{category:"Pr\xeats participatifs",categoryKey:"Pr\xeats participatifs",defaultTargetPercent:2},{category:"Crypto-Actifs",categoryKey:"Crypto-Actifs",defaultTargetPercent:5}],p=(e,t)=>e?j.map(r=>{let a=e.allocation[r.categoryKey]||0,s=e.total_assets>0?a/e.total_assets*100:0,n=void 0!==t[r.categoryKey]?t[r.categoryKey]:r.defaultTargetPercent,l=910150*n/100,i=Math.max(0,l-a),c=l>0?Math.min(100,a/l*100):0;return{category:r.category,categoryKey:r.categoryKey,currentValue:a,currentPercent:s,targetPercent:n,targetValue:l,amountToInvest:i,progressPercent:c}}):[],v=()=>{h(!0),x(null),Promise.all([n.A.get("/dashboard"),n.A.get("/fire-allocation-targets/")]).then(e=>{let[r,a]=e,s=r.data;t(s);let n=a.data.reduce((e,t)=>(e[t.category_key]=t.target_percentage,e),{});o(n),i(p(s,n)),h(!1),b(0)}).catch(e=>{console.error("Error fetching data for DashboardPage:",e),g<2?(b(e=>e+1),setTimeout(v,1e3)):(x("Erreur lors de la r\xe9cup\xe9ration des donn\xe9es du dashboard."),h(!1))})};(0,s.useEffect)(()=>{v()},[]),(0,s.useEffect)(()=>{if(e){var t,a;let s=p(e,d),n=r.length>0?r:s,l=s.map(e=>{let t=n.find(t=>t.categoryKey===e.categoryKey);if(t&&t.targetPercent!==e.targetPercent){let r=t.targetPercent,a=910150*r/100,s=Math.max(0,a-e.currentValue),n=a>0?Math.min(100,e.currentValue/a*100):0;return{...e,targetPercent:r,targetValue:a,amountToInvest:s,progressPercent:n}}return e});(0===r.length||P(e,d,null==(t=N.current)?void 0:t.data,null==(a=N.current)?void 0:a.initialTargetPercentages))&&i(l)}N.current={data:e,initialTargetPercentages:d}},[e,d]);let N=s.useRef(),P=(e,t,r,a)=>JSON.stringify(e)!==JSON.stringify(r)||JSON.stringify(t)!==JSON.stringify(a),C=(e,t)=>{let r=parseFloat(t);isNaN(r)&&""!==t&&"."!==t||i(a=>a.map(a=>{if(a.categoryKey===e){let e=isNaN(r)?""===t?0:a.targetPercent:r,s=910150*e/100,n=Math.max(0,s-a.currentValue),l=s>0?Math.min(100,a.currentValue/s*100):0;return{...a,targetPercent:e,targetValue:s,amountToInvest:n,progressPercent:l}}return a}))},A=async()=>{f(!0),x(null);let e=r.reduce((e,t)=>e+(t.targetPercent||0),0);if(Math.abs(e-100)>.1&&!window.confirm("Le total des pourcentages cibles est ".concat(e.toFixed(1),"%, ce qui n'est pas \xe9gal \xe0 100%. Voulez-vous continuer quand m\xeame ?")))return void f(!1);let t=r.map(e=>({category_key:e.categoryKey,target_percentage:e.targetPercent||0}));try{await n.A.post("/fire-allocation-targets/batch_update/",t);let e=t.reduce((e,t)=>(e[t.category_key]=t.target_percentage,e),{});o(e)}catch(e){console.error("Erreur sauvegarde allocations:",e),x("Erreur sauvegarde. R\xe9essayez.")}finally{f(!1)}},w=r.reduce((e,t)=>e+(t.targetPercent||0),0),S=r.some(e=>{var t;let r=d[e.categoryKey];return Math.abs((e.targetPercent||0)-(void 0===r?(null==(t=j.find(t=>t.categoryKey===e.categoryKey))?void 0:t.defaultTargetPercent)||0:r))>.001});if(u&&0===g)return(0,a.jsx)("p",{children:"Chargement initial du dashboard..."});if(m&&g>=2)return(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-danger",children:m}),(0,a.jsx)("button",{className:"btn btn-primary",onClick:()=>{b(0),v()},children:"R\xe9essayer"})]});if(!e&&!u)return(0,a.jsx)("p",{children:"Aucune donn\xe9e de dashboard disponible ou erreur de chargement."});if(u)return(0,a.jsxs)("p",{children:["Chargement du dashboard, tentative ",g+1,"..."]});if(!e)return(0,a.jsx)("p",{children:"Donn\xe9es du dashboard non disponibles."});let _=e=>new Intl.NumberFormat("fr-FR",{style:"currency",currency:"EUR",maximumFractionDigits:0}).format(e),k={Liquidité:{bg:"#17a2b8",border:"#138496",hover:"#20c997"},Bourse:{bg:"#007bff",border:"#0056b3",hover:"#0069d9"},"Crypto-Actifs":{bg:"#fd7e14",border:"#e55a00",hover:"#ff8c42"},"Fonds s\xe9curis\xe9s":{bg:"#28a745",border:"#1e7e34",hover:"#34ce57"},Immobilier:{bg:"#6f42c1",border:"#59359a",hover:"#7952b3"},"Pr\xeats participatifs":{bg:"#dc3545",border:"#bd2130",hover:"#e4606d"}},F=Object.keys(e.allocation),K=Object.values(e.allocation),T=K.reduce((e,t)=>e+t,0),E={labels:F,datasets:[{label:"R\xe9partition du portefeuille",data:K,backgroundColor:F.map(e=>{var t;return(null==(t=k[e])?void 0:t.bg)||"#6c757d"}),borderColor:F.map(e=>{var t;return(null==(t=k[e])?void 0:t.border)||"#495057"}),hoverBackgroundColor:F.map(e=>{var t;return(null==(t=k[e])?void 0:t.hover)||"#868e96"}),borderWidth:3,hoverBorderWidth:4,borderRadius:8,spacing:4}]};return(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"mb-4",children:"Dashboard"}),(0,a.jsxs)("div",{className:"row",children:[(0,a.jsx)("div",{className:"col-md-4",children:(0,a.jsxs)("div",{className:"card text-white bg-primary mb-3",children:[(0,a.jsx)("div",{className:"card-header",children:"Patrimoine Net Total"}),(0,a.jsx)("div",{className:"card-body",children:(0,a.jsx)("h5",{className:"card-title",children:_(e.net_patrimoine)})})]})}),(0,a.jsx)("div",{className:"col-md-4",children:(0,a.jsxs)("div",{className:"card text-white bg-success mb-3",children:[(0,a.jsx)("div",{className:"card-header",children:"Total Actifs"}),(0,a.jsx)("div",{className:"card-body",children:(0,a.jsx)("h5",{className:"card-title",children:_(e.total_assets)})})]})}),(0,a.jsx)("div",{className:"col-md-4",children:(0,a.jsxs)("div",{className:"card text-white bg-danger mb-3",children:[(0,a.jsx)("div",{className:"card-header",children:"Total Passifs"}),(0,a.jsx)("div",{className:"card-body",children:(0,a.jsx)("h5",{className:"card-title",children:_(e.total_liabilities)})})]})})]}),(0,a.jsx)("div",{className:"row mt-4",children:(0,a.jsx)("div",{className:"col-md-8 offset-md-2",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("h2",{className:"mb-4",children:"R\xe9partition des Actifs"}),(0,a.jsx)("div",{style:{maxWidth:"500px",margin:"0 auto"},children:(0,a.jsx)(l.nu,{data:E,options:{responsive:!0,maintainAspectRatio:!0,plugins:{legend:{position:"bottom",labels:{padding:20,usePointStyle:!0,pointStyle:"circle",font:{size:14,weight:"bold"},generateLabels:e=>{let t=e.data;return t.labels&&t.datasets.length?t.labels.map((e,r)=>{let a=t.datasets[0].data[r],s=T>0?(a/T*100).toFixed(1):"0",n=Array.isArray(t.datasets[0].backgroundColor)?t.datasets[0].backgroundColor[r]:t.datasets[0].backgroundColor,l=Array.isArray(t.datasets[0].borderColor)?t.datasets[0].borderColor[r]:t.datasets[0].borderColor;return{text:"".concat(e," (").concat(s,"%)"),fillStyle:n,strokeStyle:l,lineWidth:2,hidden:!1,index:r}}):[]}}},tooltip:{enabled:!0,backgroundColor:"rgba(0, 0, 0, 0.9)",titleColor:"#fff",bodyColor:"#fff",borderColor:"#fff",borderWidth:1,cornerRadius:8,displayColors:!0,callbacks:{title:e=>e[0].label||"",label:e=>{let t=e.parsed,r=T>0?(t/T*100).toFixed(1):"0";return["Valeur: ".concat(_(t)),"Pourcentage: ".concat(r,"%")]}}}},animation:{animateRotate:!0,animateScale:!0,duration:1500,easing:"easeInOutQuart"},interaction:{intersect:!1,mode:"index"},onHover:(e,t)=>{var r;(null==(r=e.native)?void 0:r.target)&&(e.native.target.style.cursor=t.length>0?"pointer":"default")},onClick:(e,t)=>{if(t.length>0){let e=t[0].index,r=F[e],a={Liquidité:"assets",Bourse:"assets","Crypto-Actifs":"assets","Fonds s\xe9curis\xe9s":"assets",Immobilier:"scpi","Pr\xeats participatifs":"assets"}[r];if(a){let t=K[e],s=T>0?(t/T*100).toFixed(1):"0";alert("".concat(r,"\nValeur: ").concat(_(t),"\nPourcentage: ").concat(s,"%\n\nCliquez sur l'onglet \"").concat("scpi"===a?"SCPI":"Patrimoine",'" pour g\xe9rer cette cat\xe9gorie.'))}}}}})})]})})}),(0,a.jsx)("div",{className:"row mt-5",children:(0,a.jsx)("div",{className:"col-12",children:(0,a.jsxs)("div",{className:"card",children:[(0,a.jsxs)("div",{className:"card-header",children:[(0,a.jsx)("h3",{className:"mb-0",children:"\uD83C\uDFAF Allocation Cible FIRE"}),(0,a.jsxs)("small",{className:"text-muted",children:["Progression vers l'objectif de ",_(910150)," selon la strat\xe9gie document\xe9e"]})]}),(0,a.jsxs)("div",{className:"card-body",children:[m&&0===r.length&&(0,a.jsx)("div",{className:"alert alert-danger",children:m}),m&&r.length>0&&(0,a.jsxs)("div",{className:"alert alert-danger",children:["Erreur lors de la sauvegarde : ",m]}),(0,a.jsx)("div",{className:"table-responsive",children:(0,a.jsxs)("table",{className:"table table-striped table-hover",children:[(0,a.jsx)("thead",{className:"table-dark",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{children:"Cat\xe9gorie d'Actif"}),(0,a.jsx)("th",{className:"text-end",children:"Valeur Actuelle"}),(0,a.jsx)("th",{className:"text-end",children:"% Actuel"}),(0,a.jsx)("th",{className:"text-end",style:{minWidth:"100px"},children:"% Cible"}),(0,a.jsx)("th",{className:"text-end",children:"Valeur Cible"}),(0,a.jsx)("th",{className:"text-end",children:"Montant \xe0 Investir"}),(0,a.jsx)("th",{className:"text-center",children:"Progression"})]})}),(0,a.jsx)("tbody",{children:r.map(e=>(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{children:(0,a.jsx)("strong",{children:e.category})}),(0,a.jsx)("td",{className:"text-end",children:_(e.currentValue)}),(0,a.jsxs)("td",{className:"text-end",children:[e.currentPercent.toFixed(1),"%"]}),(0,a.jsxs)("td",{className:"text-end",children:[(0,a.jsx)("input",{type:"number",value:void 0===e.targetPercent?"":e.targetPercent.toString(),onChange:t=>C(e.categoryKey,t.target.value),className:"form-control form-control-sm text-end",style:{width:"70px",display:"inline-block"},step:"0.1",min:"0",max:"100"})," %"]}),(0,a.jsx)("td",{className:"text-end",children:_(e.targetValue)}),(0,a.jsx)("td",{className:"text-end",children:e.amountToInvest>0?(0,a.jsx)("span",{className:"text-warning",children:_(e.amountToInvest)}):(0,a.jsx)("span",{className:"text-success",children:"Objectif atteint"})}),(0,a.jsx)("td",{className:"text-center",style:{width:"150px"},children:(0,a.jsx)(c.A,{now:e.progressPercent,variant:e.progressPercent>=100?"success":e.progressPercent>=75?"info":e.progressPercent>=50?"warning":"danger",style:{height:"20px"},label:"".concat(e.progressPercent.toFixed(0),"%")})})]},e.categoryKey))}),(0,a.jsx)("tfoot",{children:(0,a.jsxs)("tr",{className:"table-info",children:[(0,a.jsx)("th",{children:"TOTAL"}),(0,a.jsx)("th",{className:"text-end",children:_(e.total_assets)}),(0,a.jsx)("th",{className:"text-end",children:"100%"}),(0,a.jsxs)("th",{className:"text-end",children:[(0,a.jsxs)("strong",{children:[w.toFixed(1),"%"]}),Math.abs(w-100)>.1&&(0,a.jsx)("span",{className:"text-danger ms-1",children:"(≠100%)"})]}),(0,a.jsx)("th",{className:"text-end",children:_(910150)}),(0,a.jsx)("th",{className:"text-end",children:(0,a.jsx)("strong",{className:"text-primary",children:_(Math.max(0,910150-e.total_assets))})}),(0,a.jsx)("th",{className:"text-center",children:(0,a.jsxs)("strong",{children:[(e.total_assets/910150*100).toFixed(1),"%"]})})]})})]})}),S&&(0,a.jsx)("div",{className:"alert alert-warning mt-3",children:"Vous avez des modifications non enregistr\xe9es."}),(0,a.jsxs)("div",{className:"mt-3 d-flex justify-content-end",children:[(0,a.jsx)("button",{className:"btn btn-secondary me-2",onClick:()=>{e&&i(p(e,d))},disabled:!S||y,children:"R\xe9initialiser"}),(0,a.jsx)("button",{className:"btn btn-primary",onClick:A,disabled:y||!S,children:y?"Sauvegarde...":"Sauvegarder les % Cibles"})]}),(0,a.jsxs)("div",{className:"row mt-4",children:[(0,a.jsx)("div",{className:"col-md-4",children:(0,a.jsx)("div",{className:"card text-white bg-info",children:(0,a.jsxs)("div",{className:"card-body text-center",children:[(0,a.jsx)("h5",{children:"Progression Globale"}),(0,a.jsxs)("h4",{children:[(e.total_assets/910150*100).toFixed(1),"%"]}),(0,a.jsx)("small",{children:"vers l'objectif FIRE"})]})})}),(0,a.jsx)("div",{className:"col-md-4",children:(0,a.jsx)("div",{className:"card text-white bg-warning",children:(0,a.jsxs)("div",{className:"card-body text-center",children:[(0,a.jsx)("h5",{children:"Capital Restant"}),(0,a.jsx)("h4",{children:_(Math.max(0,910150-e.total_assets))}),(0,a.jsx)("small",{children:"\xe0 investir"})]})})}),(0,a.jsx)("div",{className:"col-md-4",children:(0,a.jsx)("div",{className:"card text-white bg-success",children:(0,a.jsxs)("div",{className:"card-body text-center",children:[(0,a.jsx)("h5",{children:"Estimation"}),(0,a.jsxs)("h4",{children:[Math.max(0,Math.ceil((910150-e.total_assets)/40980))," ans"]}),(0,a.jsx)("small",{children:"\xe0 3 415€/mois"})]})})})]})]})]})})})]})}},6378:(e,t,r)=>{Promise.resolve().then(r.bind(r,3792))},7390:(e,t,r)=>{"use strict";r.d(t,{Jm:()=>d,Wz:()=>o,gy:()=>c,oU:()=>i});var a=r(2115);r(5155);let s=a.createContext({prefixes:{},breakpoints:["xxl","xl","lg","md","sm","xs"],minBreakpoint:"xs"}),{Consumer:n,Provider:l}=s;function i(e,t){let{prefixes:r}=(0,a.useContext)(s);return e||r[t]||t}function c(){let{breakpoints:e}=(0,a.useContext)(s);return e}function d(){let{minBreakpoint:e}=(0,a.useContext)(s);return e}function o(){let{dir:e}=(0,a.useContext)(s);return"rtl"===e}},9300:(e,t)=>{var r;!function(){"use strict";var a={}.hasOwnProperty;function s(){for(var e="",t=0;t<arguments.length;t++){var r=arguments[t];r&&(e=n(e,function(e){if("string"==typeof e||"number"==typeof e)return e;if("object"!=typeof e)return"";if(Array.isArray(e))return s.apply(null,e);if(e.toString!==Object.prototype.toString&&!e.toString.toString().includes("[native code]"))return e.toString();var t="";for(var r in e)a.call(e,r)&&e[r]&&(t=n(t,r));return t}(r)))}return e}function n(e,t){return t?e?e+" "+t:e+t:e}e.exports?(s.default=s,e.exports=s):void 0===(r=(function(){return s}).apply(t,[]))||(e.exports=r)}()},9385:(e,t,r)=>{"use strict";r.d(t,{Tj:()=>s,mf:()=>n});var a=r(2115);function s(e,t){let r=0;return a.Children.map(e,e=>a.isValidElement(e)?t(e,r++):e)}function n(e,t){return a.Children.toArray(e).some(e=>a.isValidElement(e)&&e.type===t)}}},e=>{var t=t=>e(e.s=t);e.O(0,[647,464,579,441,684,358],()=>t(6378)),_N_E=e.O()}]);