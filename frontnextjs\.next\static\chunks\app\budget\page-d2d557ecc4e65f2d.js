(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[942],{1616:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>u});var t=r(5155),a=r(2115),n=r(2365),l=r(8827),i=r(902),d=r(7111),c=r(9986);let o=e=>{let{category:s,onSave:r,onCancel:d}=e,[c,o]=(0,a.useState)({nom:(null==s?void 0:s.nom)||"",budget_annuel:(null==s?void 0:s.budget_annuel)||0,description:(null==s?void 0:s.description)||"",ordre_affichage:(null==s?void 0:s.ordre_affichage)||0});(0,a.useEffect)(()=>{o({nom:(null==s?void 0:s.nom)||"",budget_annuel:(null==s?void 0:s.budget_annuel)||0,description:(null==s?void 0:s.description)||"",ordre_affichage:(null==s?void 0:s.ordre_affichage)||0})},[s]);let u=async e=>{e.preventDefault();let t=(null==s?void 0:s.id)?"put":"post",a=(null==s?void 0:s.id)?"/budget/categories/".concat(s.id):"/budget/categories";try{await n.A[t](a,c),r()}catch(s){let e=s instanceof Error?s.message:"Unknown error";console.error("Erreur lors de la sauvegarde de la cat\xe9gorie",e),alert("Erreur: ".concat(e))}};return(0,t.jsxs)(l.A,{onSubmit:u,children:[(0,t.jsxs)(l.A.Group,{className:"mb-3",children:[(0,t.jsx)(l.A.Label,{children:"Nom de la cat\xe9gorie"}),(0,t.jsx)(l.A.Control,{type:"text",value:c.nom,onChange:e=>o({...c,nom:e.target.value}),required:!0,placeholder:"Ex: Alimentation, Assurance Maison..."})]}),(0,t.jsxs)(l.A.Group,{className:"mb-3",children:[(0,t.jsx)(l.A.Label,{children:"Budget annuel (€)"}),(0,t.jsx)(l.A.Control,{type:"number",step:"0.01",value:c.budget_annuel,onChange:e=>o({...c,budget_annuel:parseFloat(e.target.value)||0}),required:!0})]}),(0,t.jsxs)(l.A.Group,{className:"mb-3",children:[(0,t.jsx)(l.A.Label,{children:"Description"}),(0,t.jsx)(l.A.Control,{type:"text",value:c.description,onChange:e=>o({...c,description:e.target.value}),placeholder:"Description de la cat\xe9gorie..."})]}),(0,t.jsxs)(l.A.Group,{className:"mb-3",children:[(0,t.jsx)(l.A.Label,{children:"Ordre d'affichage"}),(0,t.jsx)(l.A.Control,{type:"number",value:c.ordre_affichage,onChange:e=>o({...c,ordre_affichage:parseInt(e.target.value)||0}),placeholder:"Ordre d'affichage (1, 2, 3...)"}),(0,t.jsx)(l.A.Text,{className:"text-muted",children:"Plus le nombre est petit, plus la cat\xe9gorie appara\xeetra en haut de la liste"})]}),(0,t.jsxs)("div",{className:"d-flex justify-content-end gap-2",children:[(0,t.jsx)(i.A,{variant:"secondary",onClick:d,children:"Annuler"}),(0,t.jsx)(i.A,{variant:"primary",type:"submit",children:"Sauvegarder"})]})]})},u=()=>{let[e,s]=(0,a.useState)([]),[r,l]=(0,a.useState)(null),[u,h]=(0,a.useState)(!0),[x,j]=(0,a.useState)(null),[m,g]=(0,a.useState)(!1),[b,p]=(0,a.useState)(null),[v,N]=(0,a.useState)(0),[f,A]=(0,a.useState)(!1),_=async()=>{h(!0),j(null);try{let[e,r]=await Promise.all([n.A.get("/budget/categories"),n.A.get("/budget/summary")]);s(e.data.sort((e,s)=>e.ordre_affichage-s.ordre_affichage)),l(r.data),h(!1),N(0)}catch(e){console.error("Budget API error:",e),v<2?(N(e=>e+1),setTimeout(()=>_(),1e3)):(j("Erreur lors de la r\xe9cup\xe9ration des donn\xe9es budg\xe9taires."),h(!1))}};(0,a.useEffect)(()=>{_()},[]);let y=async(e,s)=>{if(window.confirm('\xcates-vous s\xfbr de vouloir supprimer la cat\xe9gorie "'.concat(s,'" ? Cette action est irr\xe9versible.')))try{await n.A.delete("/budget/categories/".concat(e)),_()}catch(s){let e=s instanceof Error?s.message:"Unknown error";console.error("Erreur lors de la suppression de la cat\xe9gorie",e),alert("Erreur lors de la suppression: ".concat(e))}},C=e=>new Intl.NumberFormat("fr-FR",{style:"currency",currency:"EUR"}).format(e);return u&&!m&&0===v?(0,t.jsx)("p",{children:"Chargement du budget..."}):x&&v>=2?(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-danger",children:x}),(0,t.jsx)("button",{className:"btn btn-primary",onClick:()=>{N(0),_()},children:"R\xe9essayer"})]}):u||r||x?u&&!m?(0,t.jsxs)("p",{children:["Chargement du budget, tentative ",v+1,"..."]}):(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"d-flex justify-content-between align-items-center mb-4",children:[(0,t.jsx)("h1",{children:"Calculateur Budget FIRE"}),(0,t.jsxs)("div",{className:"d-flex gap-2",children:[(0,t.jsxs)(i.A,{variant:"outline-secondary",onClick:()=>A(!f),children:[f?"Masquer":"G\xe9rer"," Cat\xe9gories"]}),(0,t.jsx)(i.A,{variant:"success",onClick:()=>{p({}),g(!0)},children:"Nouvelle Cat\xe9gorie"})]})]}),r&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("div",{className:"row mb-4",children:[(0,t.jsx)("div",{className:"col-md-4",children:(0,t.jsx)("div",{className:"card text-white bg-primary",children:(0,t.jsxs)("div",{className:"card-body text-center",children:[(0,t.jsx)("h5",{children:"Budget Annuel FIRE"}),(0,t.jsx)("h4",{children:C(r.total_budget_annuel)}),(0,t.jsx)("small",{children:"D\xe9penses nettes cibles"})]})})}),(0,t.jsx)("div",{className:"col-md-4",children:(0,t.jsx)("div",{className:"card text-white bg-warning",children:(0,t.jsxs)("div",{className:"card-body text-center",children:[(0,t.jsx)("h5",{children:"Retrait Brut N\xe9cessaire"}),(0,t.jsx)("h4",{children:C(r.retrait_brut_necessaire)}),(0,t.jsx)("small",{children:"+30% imp\xf4ts/PS"})]})})}),(0,t.jsx)("div",{className:"col-md-4",children:(0,t.jsx)("div",{className:"card text-white bg-success",children:(0,t.jsxs)("div",{className:"card-body text-center",children:[(0,t.jsx)("h5",{children:"Capital FIRE Requis"}),(0,t.jsx)("h4",{children:C(910150)}),(0,t.jsx)("small",{children:"Objectif document\xe9"})]})})})]}),(0,t.jsxs)(d.A,{variant:"info",className:"mb-4",children:[(0,t.jsx)(d.A.Heading,{children:"Objectif FIRE 2038"}),(0,t.jsxs)("p",{className:"mb-0",children:["Avec ce budget de ",(0,t.jsx)("strong",{children:C(r.total_budget_annuel)})," par an, votre objectif FIRE document\xe9 est de ",(0,t.jsx)("strong",{children:C(910150)})," (retrait brut de ",(0,t.jsx)("strong",{children:C(r.retrait_brut_necessaire)}),"/an)."]})]})]}),f&&(0,t.jsxs)("div",{className:"mb-4",children:[(0,t.jsx)("h3",{children:"Gestion des Cat\xe9gories Budg\xe9taires"}),(0,t.jsx)("div",{className:"table-responsive",children:(0,t.jsxs)("table",{className:"table table-striped table-hover",children:[(0,t.jsx)("thead",{className:"table-secondary",children:(0,t.jsxs)("tr",{children:[(0,t.jsx)("th",{children:"Ordre"}),(0,t.jsx)("th",{children:"Nom"}),(0,t.jsx)("th",{className:"text-end",children:"Budget Annuel"}),(0,t.jsx)("th",{children:"Description"}),(0,t.jsx)("th",{className:"text-center",children:"Actions"})]})}),(0,t.jsx)("tbody",{children:e.map(e=>(0,t.jsxs)("tr",{children:[(0,t.jsx)("td",{children:e.ordre_affichage}),(0,t.jsx)("td",{children:(0,t.jsx)("strong",{children:e.nom})}),(0,t.jsx)("td",{className:"text-end",children:C(e.budget_annuel)}),(0,t.jsx)("td",{children:e.description}),(0,t.jsxs)("td",{className:"text-center",children:[(0,t.jsx)(i.A,{variant:"outline-primary",size:"sm",className:"me-2",onClick:()=>{p(e),g(!0)},children:"Modifier"}),(0,t.jsx)(i.A,{variant:"outline-danger",size:"sm",onClick:()=>y(e.id,e.nom),children:"Supprimer"})]})]},e.id))}),(0,t.jsx)("tfoot",{children:(0,t.jsxs)("tr",{className:"table-info",children:[(0,t.jsx)("th",{colSpan:2,children:"TOTAL"}),(0,t.jsx)("th",{className:"text-end",children:C(e.reduce((e,s)=>e+s.budget_annuel,0))}),(0,t.jsx)("th",{colSpan:2})]})})]})})]}),(0,t.jsx)("div",{className:"table-responsive",children:(0,t.jsxs)("table",{className:"table table-striped table-hover",children:[(0,t.jsx)("thead",{className:"table-dark",children:(0,t.jsxs)("tr",{children:[(0,t.jsx)("th",{children:"Cat\xe9gorie"}),(0,t.jsx)("th",{className:"text-end",children:"Budget Annuel"}),(0,t.jsx)("th",{className:"text-end",children:"Budget Mensuel"})]})}),(0,t.jsx)("tbody",{children:e.map(e=>(0,t.jsxs)("tr",{children:[(0,t.jsxs)("td",{children:[(0,t.jsx)("strong",{children:e.nom}),e.description&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("br",{}),(0,t.jsx)("small",{className:"text-muted",children:e.description})]})]}),(0,t.jsx)("td",{className:"text-end",children:C(e.budget_annuel)}),(0,t.jsx)("td",{className:"text-end",children:C(e.budget_annuel/12)})]},e.id))}),(0,t.jsx)("tfoot",{children:(0,t.jsxs)("tr",{className:"table-info",children:[(0,t.jsx)("th",{children:"TOTAL"}),(0,t.jsx)("th",{className:"text-end",children:C(e.reduce((e,s)=>e+s.budget_annuel,0))}),(0,t.jsx)("th",{className:"text-end",children:C(e.reduce((e,s)=>e+s.budget_annuel,0)/12)})]})})]})}),(0,t.jsxs)(c.A,{show:m,onHide:()=>g(!1),size:"lg",children:[(0,t.jsx)(c.A.Header,{closeButton:!0,children:(0,t.jsx)(c.A.Title,{children:(null==b?void 0:b.id)?'Modifier "'.concat(b.nom,'"'):"Nouvelle cat\xe9gorie budg\xe9taire"})}),(0,t.jsx)(c.A.Body,{children:(0,t.jsx)(o,{category:b,onSave:()=>{g(!1),p(null),_()},onCancel:()=>g(!1)})})]})]}):(0,t.jsx)("p",{children:"Aucune donn\xe9e budg\xe9taire trouv\xe9e."})}},2365:(e,s,r)=>{"use strict";r.d(s,{A:()=>a});let t=r(3464).A.create({baseURL:"http://localhost:8000/api",headers:{"Content-Type":"application/json"},timeout:1e4});t.interceptors.response.use(e=>e,e=>{var s,r;return console.error("API Error:",null==(s=e.response)?void 0:s.status,null==(r=e.config)?void 0:r.url,e.message),Promise.reject(e)});let a=t},6281:(e,s,r)=>{Promise.resolve().then(r.bind(r,1616))}},e=>{var s=s=>e(e.s=s);e.O(0,[464,160,405,111,441,684,358],()=>s(6281)),_N_E=e.O()}]);