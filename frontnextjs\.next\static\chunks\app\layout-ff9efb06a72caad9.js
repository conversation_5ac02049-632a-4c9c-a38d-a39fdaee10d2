(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{347:()=>{},2664:(e,n,t)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"isLocalURL",{enumerable:!0,get:function(){return i}});let r=t(9991),a=t(7102);function i(e){if(!(0,r.isAbsoluteUrl)(e))return!0;try{let n=(0,r.getLocationOrigin)(),t=new URL(e,n);return t.origin===n&&(0,a.hasBasePath)(t.pathname)}catch(e){return!1}}},2757:(e,n,t)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),!function(e,n){for(var t in n)Object.defineProperty(e,t,{enumerable:!0,get:n[t]})}(n,{formatUrl:function(){return i},formatWithValidation:function(){return s},urlObjectKeys:function(){return l}});let r=t(6966)._(t(8859)),a=/https?|ftp|gopher|file/;function i(e){let{auth:n,hostname:t}=e,i=e.protocol||"",l=e.pathname||"",s=e.hash||"",o=e.query||"",c=!1;n=n?encodeURIComponent(n).replace(/%3A/i,":")+"@":"",e.host?c=n+e.host:t&&(c=n+(~t.indexOf(":")?"["+t+"]":t),e.port&&(c+=":"+e.port)),o&&"object"==typeof o&&(o=String(r.urlQueryToSearchParams(o)));let u=e.search||o&&"?"+o||"";return i&&!i.endsWith(":")&&(i+=":"),e.slashes||(!i||a.test(i))&&!1!==c?(c="//"+(c||""),l&&"/"!==l[0]&&(l="/"+l)):c||(c=""),s&&"#"!==s[0]&&(s="#"+s),u&&"?"!==u[0]&&(u="?"+u),""+i+c+(l=l.replace(/[?#]/g,encodeURIComponent))+(u=u.replace("#","%23"))+s}let l=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function s(e){return i(e)}},3180:(e,n)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"errorOnce",{enumerable:!0,get:function(){return t}});let t=e=>{}},5494:(e,n,t)=>{"use strict";t.d(n,{default:()=>s});var r=t(5155),a=t(2115),i=t(6874),l=t.n(i);let s=()=>{let[e,n]=(0,a.useState)(!1),t=()=>{n(!1)};return(0,r.jsx)("nav",{className:"navbar navbar-expand-lg navbar-dark bg-dark",children:(0,r.jsxs)("div",{className:"container-fluid d-flex",children:[(0,r.jsx)(l(),{className:"navbar-brand me-3",href:"/",onClick:t,children:"FIRE Dashboard"}),(0,r.jsxs)("ul",{className:"navbar-nav d-none d-lg-flex flex-row",children:[(0,r.jsx)("li",{className:"nav-item",children:(0,r.jsx)(l(),{className:"nav-link",href:"/",onClick:t,children:"Dashboard"})}),(0,r.jsx)("li",{className:"nav-item",children:(0,r.jsx)(l(),{className:"nav-link",href:"/assets",onClick:t,children:"Patrimoine"})}),(0,r.jsx)("li",{className:"nav-item",children:(0,r.jsx)(l(),{className:"nav-link",href:"/liabilities",onClick:t,children:"Emprunts"})}),(0,r.jsx)("li",{className:"nav-item",children:(0,r.jsx)(l(),{className:"nav-link",href:"/scpi",onClick:t,children:"SCPI"})}),(0,r.jsx)("li",{className:"nav-item",children:(0,r.jsx)(l(),{className:"nav-link",href:"/evolution",onClick:t,children:"\xc9volution"})}),(0,r.jsx)("li",{className:"nav-item",children:(0,r.jsx)(l(),{className:"nav-link",href:"/budget",onClick:t,children:"Budget FIRE"})}),(0,r.jsx)("li",{className:"nav-item",children:(0,r.jsx)(l(),{className:"nav-link",href:"/scenarios",onClick:t,children:"Sc\xe9narios"})}),(0,r.jsx)("li",{className:"nav-item",children:(0,r.jsx)(l(),{className:"nav-link",href:"/fire",onClick:t,children:"Objectif FIRE"})}),(0,r.jsx)("li",{className:"nav-item",children:(0,r.jsx)(l(),{className:"nav-link",href:"/test",onClick:t,children:"Test API"})})]}),(0,r.jsx)("button",{className:"navbar-toggler d-lg-none",type:"button",onClick:()=>{n(!e)},"aria-controls":"navbarNavContent","aria-expanded":e,"aria-label":"Toggle navigation",children:(0,r.jsx)("span",{className:"navbar-toggler-icon"})}),e&&(0,r.jsx)("div",{className:"navbar-collapse show d-lg-none position-absolute top-100 start-0 w-100 bg-dark",children:(0,r.jsxs)("ul",{className:"navbar-nav",children:[(0,r.jsx)("li",{className:"nav-item",children:(0,r.jsx)(l(),{className:"nav-link",href:"/",onClick:t,children:"Dashboard"})}),(0,r.jsx)("li",{className:"nav-item",children:(0,r.jsx)(l(),{className:"nav-link",href:"/assets",onClick:t,children:"Patrimoine"})}),(0,r.jsx)("li",{className:"nav-item",children:(0,r.jsx)(l(),{className:"nav-link",href:"/liabilities",onClick:t,children:"Emprunts"})}),(0,r.jsx)("li",{className:"nav-item",children:(0,r.jsx)(l(),{className:"nav-link",href:"/scpi",onClick:t,children:"SCPI"})}),(0,r.jsx)("li",{className:"nav-item",children:(0,r.jsx)(l(),{className:"nav-link",href:"/evolution",onClick:t,children:"\xc9volution"})}),(0,r.jsx)("li",{className:"nav-item",children:(0,r.jsx)(l(),{className:"nav-link",href:"/budget",onClick:t,children:"Budget FIRE"})}),(0,r.jsx)("li",{className:"nav-item",children:(0,r.jsx)(l(),{className:"nav-link",href:"/scenarios",onClick:t,children:"Sc\xe9narios"})}),(0,r.jsx)("li",{className:"nav-item",children:(0,r.jsx)(l(),{className:"nav-link",href:"/fire",onClick:t,children:"Objectif FIRE"})}),(0,r.jsx)("li",{className:"nav-item",children:(0,r.jsx)(l(),{className:"nav-link",href:"/test",onClick:t,children:"Test API"})})]})})]})})}},6496:(e,n,t)=>{Promise.resolve().then(t.t.bind(t,347,23)),Promise.resolve().then(t.bind(t,6843)),Promise.resolve().then(t.bind(t,5494))},6654:(e,n,t)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"useMergedRef",{enumerable:!0,get:function(){return a}});let r=t(2115);function a(e,n){let t=(0,r.useRef)(null),a=(0,r.useRef)(null);return(0,r.useCallback)(r=>{if(null===r){let e=t.current;e&&(t.current=null,e());let n=a.current;n&&(a.current=null,n())}else e&&(t.current=i(e,r)),n&&(a.current=i(n,r))},[e,n])}function i(e,n){if("function"!=typeof e)return e.current=n,()=>{e.current=null};{let t=e(n);return"function"==typeof t?t:()=>e(null)}}("function"==typeof n.default||"object"==typeof n.default&&null!==n.default)&&void 0===n.default.__esModule&&(Object.defineProperty(n.default,"__esModule",{value:!0}),Object.assign(n.default,n),e.exports=n.default)},6843:(e,n,t)=>{"use strict";t.d(n,{default:()=>a});var r=t(2115);function a(){return(0,r.useEffect)(()=>{t.e(737).then(t.t.bind(t,5737,23))},[]),null}},6874:(e,n,t)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),!function(e,n){for(var t in n)Object.defineProperty(e,t,{enumerable:!0,get:n[t]})}(n,{default:function(){return v},useLinkStatus:function(){return b}});let r=t(6966),a=t(5155),i=r._(t(2115)),l=t(2757),s=t(5227),o=t(9818),c=t(6654),u=t(9991),f=t(5929);t(3230);let d=t(4930),h=t(2664),p=t(6634);function m(e){return"string"==typeof e?e:(0,l.formatUrl)(e)}function v(e){let n,t,r,[l,v]=(0,i.useOptimistic)(d.IDLE_LINK_STATUS),b=(0,i.useRef)(null),{href:j,as:N,children:x,prefetch:y=null,passHref:k,replace:P,shallow:C,scroll:E,onClick:_,onMouseEnter:O,onTouchStart:S,legacyBehavior:T=!1,onNavigate:I,ref:A,unstable_dynamicOnHover:R,...L}=e;n=x,T&&("string"==typeof n||"number"==typeof n)&&(n=(0,a.jsx)("a",{children:n}));let M=i.default.useContext(s.AppRouterContext),w=!1!==y,U=null===y?o.PrefetchKind.AUTO:o.PrefetchKind.FULL,{href:F,as:D}=i.default.useMemo(()=>{let e=m(j);return{href:e,as:N?m(N):e}},[j,N]);T&&(t=i.default.Children.only(n));let K=T?t&&"object"==typeof t&&t.ref:A,B=i.default.useCallback(e=>(null!==M&&(b.current=(0,d.mountLinkInstance)(e,F,M,U,w,v)),()=>{b.current&&((0,d.unmountLinkForCurrentNavigation)(b.current),b.current=null),(0,d.unmountPrefetchableInstance)(e)}),[w,F,M,U,v]),z={ref:(0,c.useMergedRef)(B,K),onClick(e){T||"function"!=typeof _||_(e),T&&t.props&&"function"==typeof t.props.onClick&&t.props.onClick(e),M&&(e.defaultPrevented||function(e,n,t,r,a,l,s){let{nodeName:o}=e.currentTarget;if(!("A"===o.toUpperCase()&&function(e){let n=e.currentTarget.getAttribute("target");return n&&"_self"!==n||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e)||e.currentTarget.hasAttribute("download"))){if(!(0,h.isLocalURL)(n)){a&&(e.preventDefault(),location.replace(n));return}e.preventDefault(),i.default.startTransition(()=>{if(s){let e=!1;if(s({preventDefault:()=>{e=!0}}),e)return}(0,p.dispatchNavigateAction)(t||n,a?"replace":"push",null==l||l,r.current)})}}(e,F,D,b,P,E,I))},onMouseEnter(e){T||"function"!=typeof O||O(e),T&&t.props&&"function"==typeof t.props.onMouseEnter&&t.props.onMouseEnter(e),M&&w&&(0,d.onNavigationIntent)(e.currentTarget,!0===R)},onTouchStart:function(e){T||"function"!=typeof S||S(e),T&&t.props&&"function"==typeof t.props.onTouchStart&&t.props.onTouchStart(e),M&&w&&(0,d.onNavigationIntent)(e.currentTarget,!0===R)}};return(0,u.isAbsoluteUrl)(D)?z.href=D:T&&!k&&("a"!==t.type||"href"in t.props)||(z.href=(0,f.addBasePath)(D)),r=T?i.default.cloneElement(t,z):(0,a.jsx)("a",{...L,...z,children:n}),(0,a.jsx)(g.Provider,{value:l,children:r})}t(3180);let g=(0,i.createContext)(d.IDLE_LINK_STATUS),b=()=>(0,i.useContext)(g);("function"==typeof n.default||"object"==typeof n.default&&null!==n.default)&&void 0===n.default.__esModule&&(Object.defineProperty(n.default,"__esModule",{value:!0}),Object.assign(n.default,n),e.exports=n.default)},8859:(e,n)=>{"use strict";function t(e){let n={};for(let[t,r]of e.entries()){let e=n[t];void 0===e?n[t]=r:Array.isArray(e)?e.push(r):n[t]=[e,r]}return n}function r(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function a(e){let n=new URLSearchParams;for(let[t,a]of Object.entries(e))if(Array.isArray(a))for(let e of a)n.append(t,r(e));else n.set(t,r(a));return n}function i(e){for(var n=arguments.length,t=Array(n>1?n-1:0),r=1;r<n;r++)t[r-1]=arguments[r];for(let n of t){for(let t of n.keys())e.delete(t);for(let[t,r]of n.entries())e.append(t,r)}return e}Object.defineProperty(n,"__esModule",{value:!0}),!function(e,n){for(var t in n)Object.defineProperty(e,t,{enumerable:!0,get:n[t]})}(n,{assign:function(){return i},searchParamsToUrlQuery:function(){return t},urlQueryToSearchParams:function(){return a}})},9991:(e,n)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),!function(e,n){for(var t in n)Object.defineProperty(e,t,{enumerable:!0,get:n[t]})}(n,{DecodeError:function(){return p},MiddlewareNotFoundError:function(){return b},MissingStaticPage:function(){return g},NormalizeError:function(){return m},PageNotFoundError:function(){return v},SP:function(){return d},ST:function(){return h},WEB_VITALS:function(){return t},execOnce:function(){return r},getDisplayName:function(){return o},getLocationOrigin:function(){return l},getURL:function(){return s},isAbsoluteUrl:function(){return i},isResSent:function(){return c},loadGetInitialProps:function(){return f},normalizeRepeatedSlashes:function(){return u},stringifyError:function(){return j}});let t=["CLS","FCP","FID","INP","LCP","TTFB"];function r(e){let n,t=!1;return function(){for(var r=arguments.length,a=Array(r),i=0;i<r;i++)a[i]=arguments[i];return t||(t=!0,n=e(...a)),n}}let a=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,i=e=>a.test(e);function l(){let{protocol:e,hostname:n,port:t}=window.location;return e+"//"+n+(t?":"+t:"")}function s(){let{href:e}=window.location,n=l();return e.substring(n.length)}function o(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function c(e){return e.finished||e.headersSent}function u(e){let n=e.split("?");return n[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(n[1]?"?"+n.slice(1).join("?"):"")}async function f(e,n){let t=n.res||n.ctx&&n.ctx.res;if(!e.getInitialProps)return n.ctx&&n.Component?{pageProps:await f(n.Component,n.ctx)}:{};let r=await e.getInitialProps(n);if(t&&c(t))return r;if(!r)throw Object.defineProperty(Error('"'+o(e)+'.getInitialProps()" should resolve to an object. But found "'+r+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return r}let d="undefined"!=typeof performance,h=d&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class p extends Error{}class m extends Error{}class v extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class g extends Error{constructor(e,n){super(),this.message="Failed to load static file for page: "+e+" "+n}}class b extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function j(e){return JSON.stringify({message:e.message,stack:e.stack})}}},e=>{var n=n=>e(e.s=n);e.O(0,[740,690,441,684,358],()=>n(6496)),_N_E=e.O()}]);