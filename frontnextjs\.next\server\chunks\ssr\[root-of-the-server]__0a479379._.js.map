{"version": 3, "sources": [], "sections": [{"offset": {"line": 111, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/AI-Code/fire_UI/frontnextjs/src/components/ApiClient.ts"], "sourcesContent": ["import axios from 'axios';\r\n\r\nconst apiClient = axios.create({\r\n  baseURL: 'http://localhost:8000/api',\r\n  headers: {\r\n    'Content-Type': 'application/json',\r\n  },\r\n  timeout: 10000, // 10 second timeout\r\n});\r\n\r\n// Add response interceptor for error handling\r\napiClient.interceptors.response.use(\r\n  (response) => response,\r\n  (error) => {\r\n    console.error('API Error:', error.response?.status, error.config?.url, error.message);\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\nexport default apiClient;\r\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,YAAY,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAC7B,SAAS;IACT,SAAS;QACP,gBAAgB;IAClB;IACA,SAAS;AACX;AAEA,8CAA8C;AAC9C,UAAU,YAAY,CAAC,QAAQ,CAAC,GAAG,CACjC,CAAC,WAAa,UACd,CAAC;IACC,QAAQ,KAAK,CAAC,cAAc,MAAM,QAAQ,EAAE,QAAQ,MAAM,MAAM,EAAE,KAAK,MAAM,OAAO;IACpF,OAAO,QAAQ,MAAM,CAAC;AACxB;uCAGa", "debugId": null}}, {"offset": {"line": 135, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/AI-Code/fire_UI/frontnextjs/src/components/EvolutionChart.tsx"], "sourcesContent": ["'use client'; // Nécessaire car utilise react-chartjs-2 et potentiellement des hooks internes\r\n\r\nimport React from 'react';\r\nimport { Line } from 'react-chartjs-2';\r\nimport {\r\n  Chart as ChartJS,\r\n  CategoryScale,\r\n  LinearScale,\r\n  PointElement,\r\n  LineElement,\r\n  Title,\r\n  Tooltip,\r\n  Legend,\r\n  Filler, // Import Filler for filling area under the line\r\n} from 'chart.js';\r\nimport annotationPlugin, { AnnotationOptions, LineAnnotationOptions } from 'chartjs-plugin-annotation'; // Import annotation plugin and types\r\n\r\nChartJS.register(\r\n  CategoryScale,\r\n  LinearScale,\r\n  PointElement,\r\n  LineElement,\r\n  Title,\r\n  Tooltip,\r\n  Legend,\r\n  Filler, // Register Filler\r\n  annotationPlugin // Register annotation plugin\r\n);\r\n\r\ninterface EvolutionData {\r\n  annee: number;\r\n  total_patrimoine: number;\r\n}\r\n\r\ninterface EvolutionChartProps {\r\n  evolutionData: EvolutionData[];\r\n  fireTargetAmount: number | null;\r\n}\r\n\r\nconst EvolutionChart: React.FC<EvolutionChartProps> = ({ evolutionData, fireTargetAmount }) => {\r\n  const data = {\r\n    labels: evolutionData.map(d => d.annee.toString()),\r\n    datasets: [\r\n      {\r\n        label: 'Patrimoine Total (€)',\r\n        data: evolutionData.map(d => d.total_patrimoine),\r\n        borderColor: 'rgb(75, 192, 192)',\r\n        backgroundColor: 'rgba(75, 192, 192, 0.2)', // Fill color\r\n        tension: 0.1,\r\n        fill: true, // Enable fill\r\n      },\r\n    ],\r\n  };\r\n\r\n  const annotationsConfig: Record<string, AnnotationOptions> = {};\r\n\r\n  if (fireTargetAmount !== null) {\r\n    const lineAnnotation: LineAnnotationOptions & { type: 'line' } = { // Assurer que 'type' est inclus\r\n      type: 'line', // Explicitement ajouter 'type'\r\n      yMin: fireTargetAmount,\r\n      yMax: fireTargetAmount,\r\n      borderColor: 'rgb(255, 99, 132)',\r\n      borderWidth: 2,\r\n      borderDash: [6, 6],\r\n      label: {\r\n        content: `Objectif FIRE: ${fireTargetAmount.toLocaleString('fr-FR', { style: 'currency', currency: 'EUR' })}`,\r\n        display: true, // Changed from enabled to display for chartjs-plugin-annotation v2+\r\n        position: 'end',\r\n        backgroundColor: 'rgba(255, 99, 132, 0.8)',\r\n        font: {\r\n          weight: 'bold',\r\n        },\r\n        color: 'white',\r\n        padding: {\r\n          x: 6,\r\n          y: 3,\r\n        },\r\n        yAdjust: -10,\r\n      }\r\n    };\r\n    annotationsConfig.line1 = lineAnnotation;\r\n  }\r\n\r\n  const options = {\r\n    responsive: true,\r\n    maintainAspectRatio: false,\r\n    scales: {\r\n      y: {\r\n        beginAtZero: true,\r\n        ticks: {\r\n          callback: function(value: string | number) {\r\n            if (typeof value === 'number') {\r\n              return value.toLocaleString('fr-FR', { style: 'currency', currency: 'EUR' });\r\n            }\r\n            return value;\r\n          }\r\n        }\r\n      }\r\n    },\r\n    plugins: {\r\n      legend: {\r\n        position: 'top' as const,\r\n      },\r\n      title: {\r\n        display: true,\r\n        text: 'Évolution du Patrimoine Total et Objectif FIRE',\r\n        font: {\r\n          size: 18,\r\n        }\r\n      },\r\n      tooltip: {\r\n        callbacks: {\r\n          label: function(context: any) {\r\n            let label = context.dataset.label || '';\r\n            if (label) {\r\n              label += ': ';\r\n            }\r\n            if (context.parsed.y !== null) {\r\n              label += new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR' }).format(context.parsed.y);\r\n            }\r\n            return label;\r\n          }\r\n        }\r\n      },\r\n      annotation: {\r\n        annotations: annotationsConfig\r\n      }\r\n    },\r\n    interaction: {\r\n      mode: 'index' as const,\r\n      intersect: false,\r\n    },\r\n  };\r\n\r\n  return (\r\n    <div style={{ height: '400px', marginTop: '20px', marginBottom: '20px' }}>\r\n      <Line data={data} options={options as any} /> {/* Use 'as any' for options if type issues persist with plugins */}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default EvolutionChart;\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAWA,kVAAwG,qCAAqC;AAf7I,cAAc,+EAA+E;;;;;AAiB7F,4JAAA,CAAA,QAAO,CAAC,QAAQ,CACd,4JAAA,CAAA,gBAAa,EACb,4JAAA,CAAA,cAAW,EACX,4JAAA,CAAA,eAAY,EACZ,4JAAA,CAAA,cAAW,EACX,4JAAA,CAAA,QAAK,EACL,4JAAA,CAAA,UAAO,EACP,4JAAA,CAAA,SAAM,EACN,4JAAA,CAAA,SAAM,EACN,kMAAiB,6BAA6B;AAA9C,CAAA,UAAgB;AAalB,MAAM,iBAAgD,CAAC,EAAE,aAAa,EAAE,gBAAgB,EAAE;IACxF,MAAM,OAAO;QACX,QAAQ,cAAc,GAAG,CAAC,CAAA,IAAK,EAAE,KAAK,CAAC,QAAQ;QAC/C,UAAU;YACR;gBACE,OAAO;gBACP,MAAM,cAAc,GAAG,CAAC,CAAA,IAAK,EAAE,gBAAgB;gBAC/C,aAAa;gBACb,iBAAiB;gBACjB,SAAS;gBACT,MAAM;YACR;SACD;IACH;IAEA,MAAM,oBAAuD,CAAC;IAE9D,IAAI,qBAAqB,MAAM;QAC7B,MAAM,iBAA2D;YAC/D,MAAM;YACN,MAAM;YACN,MAAM;YACN,aAAa;YACb,aAAa;YACb,YAAY;gBAAC;gBAAG;aAAE;YAClB,OAAO;gBACL,SAAS,CAAC,eAAe,EAAE,iBAAiB,cAAc,CAAC,SAAS;oBAAE,OAAO;oBAAY,UAAU;gBAAM,IAAI;gBAC7G,SAAS;gBACT,UAAU;gBACV,iBAAiB;gBACjB,MAAM;oBACJ,QAAQ;gBACV;gBACA,OAAO;gBACP,SAAS;oBACP,GAAG;oBACH,GAAG;gBACL;gBACA,SAAS,CAAC;YACZ;QACF;QACA,kBAAkB,KAAK,GAAG;IAC5B;IAEA,MAAM,UAAU;QACd,YAAY;QACZ,qBAAqB;QACrB,QAAQ;YACN,GAAG;gBACD,aAAa;gBACb,OAAO;oBACL,UAAU,SAAS,KAAsB;wBACvC,IAAI,OAAO,UAAU,UAAU;4BAC7B,OAAO,MAAM,cAAc,CAAC,SAAS;gCAAE,OAAO;gCAAY,UAAU;4BAAM;wBAC5E;wBACA,OAAO;oBACT;gBACF;YACF;QACF;QACA,SAAS;YACP,QAAQ;gBACN,UAAU;YACZ;YACA,OAAO;gBACL,SAAS;gBACT,MAAM;gBACN,MAAM;oBACJ,MAAM;gBACR;YACF;YACA,SAAS;gBACP,WAAW;oBACT,OAAO,SAAS,OAAY;wBAC1B,IAAI,QAAQ,QAAQ,OAAO,CAAC,KAAK,IAAI;wBACrC,IAAI,OAAO;4BACT,SAAS;wBACX;wBACA,IAAI,QAAQ,MAAM,CAAC,CAAC,KAAK,MAAM;4BAC7B,SAAS,IAAI,KAAK,YAAY,CAAC,SAAS;gCAAE,OAAO;gCAAY,UAAU;4BAAM,GAAG,MAAM,CAAC,QAAQ,MAAM,CAAC,CAAC;wBACzG;wBACA,OAAO;oBACT;gBACF;YACF;YACA,YAAY;gBACV,aAAa;YACf;QACF;QACA,aAAa;YACX,MAAM;YACN,WAAW;QACb;IACF;IAEA,qBACE,8OAAC;QAAI,OAAO;YAAE,QAAQ;YAAS,WAAW;YAAQ,cAAc;QAAO;;0BACrE,8OAAC,sJAAA,CAAA,OAAI;gBAAC,MAAM;gBAAM,SAAS;;;;;;YAAkB;;;;;;;AAGnD;uCAEe", "debugId": null}}, {"offset": {"line": 282, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/AI-Code/fire_UI/frontnextjs/src/app/evolution/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useEffect, useState, FormEvent } from 'react';\r\nimport apiClient from '../../components/ApiClient'; // Ajusté\r\nimport { Modal, Button, Form, Alert } from 'react-bootstrap';\r\nimport EvolutionChart from '../../components/EvolutionChart'; // Ajusté\r\n\r\ninterface EvolutionData {\r\n  id: number;\r\n  annee: number;\r\n  investissement: number;\r\n  prix_part_scpi: number;\r\n  remboursement_credit: number;\r\n  valeur_reelle_scpi: number;\r\n  total_patrimoine: number;\r\n  evolution_pourcentage: number | null;\r\n  evolution_euros: number | null;\r\n  croissance_moyenne: number | null;\r\n  tcam: number | null;\r\n}\r\n\r\nconst EvolutionForm: React.FC<{\r\n  evolution: Partial<EvolutionData> | null,\r\n  onSave: () => void,\r\n  onCancel: () => void\r\n}> = ({ evolution, onSave, onCancel }) => {\r\n  const [formData, setFormData] = useState({\r\n    annee: evolution?.annee || new Date().getFullYear(),\r\n    investissement: evolution?.investissement || 0,\r\n    prix_part_scpi: evolution?.prix_part_scpi || 0,\r\n    remboursement_credit: evolution?.remboursement_credit || 0,\r\n    valeur_reelle_scpi: evolution?.valeur_reelle_scpi || 0,\r\n    total_patrimoine: evolution?.total_patrimoine || 0,\r\n  });\r\n\r\n  useEffect(() => {\r\n    setFormData({\r\n        annee: evolution?.annee || new Date().getFullYear(),\r\n        investissement: evolution?.investissement || 0,\r\n        prix_part_scpi: evolution?.prix_part_scpi || 0,\r\n        remboursement_credit: evolution?.remboursement_credit || 0,\r\n        valeur_reelle_scpi: evolution?.valeur_reelle_scpi || 0,\r\n        total_patrimoine: evolution?.total_patrimoine || 0,\r\n    });\r\n  }, [evolution]);\r\n\r\n  const handleSubmit = async (e: FormEvent) => {\r\n    e.preventDefault();\r\n    const method = evolution?.id ? 'put' : 'post';\r\n    const url = evolution?.id ? `/evolution/${evolution.annee}` : '/evolution';\r\n    try {\r\n      await apiClient[method](url, formData);\r\n      onSave();\r\n    } catch (error: unknown) {\r\n      const errorMessage = error instanceof Error ? error.message : 'Unknown error';\r\n      console.error(\"Erreur lors de la sauvegarde des données d&apos;évolution\", errorMessage);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Form onSubmit={handleSubmit}>\r\n      <Form.Group className=\"mb-3\">\r\n        <Form.Label>Année</Form.Label>\r\n        <Form.Control type=\"number\" min=\"2000\" max={new Date().getFullYear() + 5} value={formData.annee} onChange={(e) => setFormData({ ...formData, annee: parseInt(e.target.value) || 0 })} required disabled={!!evolution?.id} />\r\n      </Form.Group>\r\n      <Form.Group className=\"mb-3\"><Form.Label>Investissement total (€)</Form.Label><Form.Control type=\"number\" step=\"0.01\" value={formData.investissement} onChange={(e) => setFormData({ ...formData, investissement: parseFloat(e.target.value) || 0 })} required /></Form.Group>\r\n      <Form.Group className=\"mb-3\"><Form.Label>Prix moyen des parts SCPI (€)</Form.Label><Form.Control type=\"number\" step=\"0.01\" value={formData.prix_part_scpi} onChange={(e) => setFormData({ ...formData, prix_part_scpi: parseFloat(e.target.value) || 0 })} required /></Form.Group>\r\n      <Form.Group className=\"mb-3\"><Form.Label>Remboursement crédit (€)</Form.Label><Form.Control type=\"number\" step=\"0.01\" value={formData.remboursement_credit} onChange={(e) => setFormData({ ...formData, remboursement_credit: parseFloat(e.target.value) || 0 })} required /></Form.Group>\r\n      <Form.Group className=\"mb-3\"><Form.Label>Valeur réelle SCPI (€)</Form.Label><Form.Control type=\"number\" step=\"0.01\" value={formData.valeur_reelle_scpi} onChange={(e) => setFormData({ ...formData, valeur_reelle_scpi: parseFloat(e.target.value) || 0 })} required /></Form.Group>\r\n      <Form.Group className=\"mb-3\"><Form.Label>Total patrimoine (€)</Form.Label><Form.Control type=\"number\" step=\"0.01\" value={formData.total_patrimoine} onChange={(e) => setFormData({ ...formData, total_patrimoine: parseFloat(e.target.value) || 0 })} required /></Form.Group>\r\n      <Alert variant=\"info\"><small>Les métriques d&apos;évolution (%, €, TCAM) seront calculées automatiquement après la sauvegarde.</small></Alert>\r\n      <div className=\"d-flex justify-content-end gap-2\"><Button variant=\"secondary\" onClick={onCancel}>Annuler</Button><Button variant=\"primary\" type=\"submit\">Sauvegarder</Button></div>\r\n    </Form>\r\n  );\r\n};\r\n\r\nconst EvolutionAnnuellePage: React.FC = () => {\r\n  const [evolutions, setEvolutions] = useState<EvolutionData[]>([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const [showModal, setShowModal] = useState(false);\r\n  const [selectedEvolution, setSelectedEvolution] = useState<Partial<EvolutionData> | null>(null);\r\n  const [retryCount, setRetryCount] = useState(0);\r\n  const [fireTargetAmount, setFireTargetAmount] = useState<number | null>(null);\r\n\r\n  const fetchFireTarget = async () => {\r\n    try {\r\n      const response = await apiClient.get('/fire-settings');\r\n      if (response.data && response.data.fire_target_amount !== undefined) {\r\n        setFireTargetAmount(response.data.fire_target_amount);\r\n      } else {\r\n        const defaultTargetResponse = await apiClient.get('/fire-target'); // Vérifier si ce endpoint existe ou est pertinent\r\n        setFireTargetAmount(defaultTargetResponse.data.fire_target_amount);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Erreur lors de la récupération de l'objectif FIRE\", error);\r\n      setFireTargetAmount(910150); // Fallback à une valeur par défaut si l'API échoue\r\n    }\r\n  };\r\n\r\n  const fetchEvolutions = () => {\r\n    setLoading(true);\r\n    setError(null);\r\n    apiClient.get('/evolution')\r\n      .then(response => {\r\n        setEvolutions(response.data);\r\n        setLoading(false);\r\n        setRetryCount(0);\r\n      })\r\n      .catch(error => {\r\n        console.error('Evolution API error:', error);\r\n        if (retryCount < 2) {\r\n          setRetryCount(prev => prev + 1);\r\n          setTimeout(() => fetchEvolutions(), 1000);\r\n        } else {\r\n          setError('Erreur lors de la récupération des données d\\'évolution.');\r\n          setLoading(false);\r\n        }\r\n      });\r\n  };\r\n\r\n  useEffect(() => {\r\n    fetchEvolutions();\r\n    fetchFireTarget();\r\n  // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, []);\r\n\r\n  const handleSave = () => {\r\n    setShowModal(false);\r\n    setSelectedEvolution(null);\r\n    fetchEvolutions();\r\n  };\r\n\r\n  const handleDelete = async (annee: number) => {\r\n    if (window.confirm(`Êtes-vous sûr de vouloir supprimer les données de ${annee} ?`)) {\r\n      try {\r\n        await apiClient.delete(`/evolution/${annee}`);\r\n        fetchEvolutions();\r\n      } catch (error) {\r\n        console.error(\"Erreur lors de la suppression des données d'évolution\", error);\r\n      }\r\n    }\r\n  };\r\n\r\n  const formatCurrency = (value: number | null) => {\r\n    if (value === null || value === undefined) return 'N/A';\r\n    return new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR' }).format(value);\r\n  };\r\n\r\n  const formatPercentage = (value: number | null) => {\r\n    if (value === null || value === undefined) return 'N/A';\r\n    return new Intl.NumberFormat('fr-FR', { style: 'percent', minimumFractionDigits: 2, maximumFractionDigits: 2 }).format(value / 100);\r\n  };\r\n\r\n  if (loading && !showModal && retryCount === 0) return <p>Chargement des données d&apos;évolution...</p>;\r\n  if (error && retryCount >=2) return (\r\n    <div>\r\n      <p className=\"text-danger\">{error}</p>\r\n      <button className=\"btn btn-primary\" onClick={() => {setRetryCount(0); fetchEvolutions();}}>Réessayer</button>\r\n    </div>\r\n  );\r\n  if (!loading && !evolutions.length && !error) return <p>Aucune donnée d&apos;évolution trouvée.</p>;\r\n  if (loading && !showModal) return <p>Chargement des données d&apos;évolution, tentative {retryCount + 1}...</p>;\r\n\r\n  return (\r\n    <div>\r\n      <div className=\"d-flex justify-content-between align-items-center mb-4\">\r\n        <h1>Évolution Annuelle du Patrimoine</h1>\r\n        <Button variant=\"primary\" onClick={() => { setSelectedEvolution({}); setShowModal(true); }}>Ajouter une année</Button>\r\n      </div>\r\n\r\n      {evolutions.length > 0 && (\r\n        <div className=\"mb-4\">\r\n          <div className=\"row\">\r\n            <div className=\"col-md-3\"><div className=\"card text-white bg-primary\"><div className=\"card-body text-center\"><h5>Période</h5><h4>{evolutions[0]?.annee} - {evolutions[evolutions.length - 1]?.annee}</h4></div></div></div>\r\n            <div className=\"col-md-3\"><div className=\"card text-white bg-success\"><div className=\"card-body text-center\"><h5>Croissance Totale</h5><h4>{formatCurrency(evolutions[evolutions.length - 1]?.total_patrimoine - evolutions[0]?.total_patrimoine)}</h4></div></div></div>\r\n            <div className=\"col-md-3\"><div className=\"card text-white bg-info\"><div className=\"card-body text-center\"><h5>TCAM Moyen</h5><h4>{formatPercentage(evolutions[evolutions.length - 1]?.tcam)}</h4></div></div></div>\r\n            <div className=\"col-md-3\"><div className=\"card text-white bg-warning\"><div className=\"card-body text-center\"><h5>Patrimoine Actuel</h5><h4>{formatCurrency(evolutions[evolutions.length - 1]?.total_patrimoine)}</h4></div></div></div>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      { evolutions.length > 0 &&\r\n        <div className=\"table-responsive\">\r\n          <table className=\"table table-striped table-hover\">\r\n            <thead className=\"table-dark\"><tr><th>Année</th><th className=\"text-end\">Total Patrimoine</th><th className=\"text-end\">Evolution en %</th><th className=\"text-end\">Evolution en €</th><th className=\"text-end\">Croissance Annuel Moyen (€)</th><th className=\"text-end\">TCAM (%)</th><th className=\"text-center\">Actions</th></tr></thead>\r\n            <tbody>\r\n              {evolutions.map(evolution => (\r\n                <tr key={evolution.id}>\r\n                  <td><strong>{evolution.annee}</strong></td>\r\n                  <td className=\"text-end\"><strong>{formatCurrency(evolution.total_patrimoine)}</strong></td>\r\n                  <td className={`text-end ${(evolution.evolution_pourcentage || 0) >= 0 ? 'text-success' : 'text-danger'}`}>{formatPercentage(evolution.evolution_pourcentage)}</td>\r\n                  <td className={`text-end ${(evolution.evolution_euros || 0) >= 0 ? 'text-success' : 'text-danger'}`}>{formatCurrency(evolution.evolution_euros)}</td>\r\n                  <td className=\"text-end\">{formatCurrency(evolution.croissance_moyenne)}</td>\r\n                  <td className=\"text-end text-info\">{formatPercentage(evolution.tcam)}</td>\r\n                  <td className=\"text-center\">\r\n                    <Button variant=\"outline-primary\" size=\"sm\" className=\"me-2\" onClick={() => { setSelectedEvolution(evolution); setShowModal(true); }}>Modifier</Button>\r\n                    <Button variant=\"outline-danger\" size=\"sm\" onClick={() => handleDelete(evolution.annee)}>Supprimer</Button>\r\n                  </td>\r\n                </tr>\r\n              ))}\r\n            </tbody>\r\n          </table>\r\n        </div>\r\n      }\r\n\r\n      <Modal show={showModal} onHide={() => setShowModal(false)} size=\"lg\"><Modal.Header closeButton><Modal.Title>{selectedEvolution?.id ? `Modifier ${selectedEvolution.annee}` : 'Ajouter une année'}</Modal.Title></Modal.Header><Modal.Body><EvolutionForm evolution={selectedEvolution} onSave={handleSave} onCancel={() => setShowModal(false)} /></Modal.Body></Modal>\r\n\r\n      {evolutions.length > 0 && fireTargetAmount !== null && (\r\n        <EvolutionChart evolutionData={evolutions} fireTargetAmount={fireTargetAmount} />\r\n      )}\r\n\r\n      <div className=\"mt-5 p-4 bg-light rounded\"><h4>Comprendre les métriques d&apos;évolution</h4><hr /><dl className=\"row\"><dt className=\"col-sm-4\">TCAM (Taux de Croissance Annuel Moyen)</dt><dd className=\"col-sm-8\"><p>Le TCAM représente le taux de croissance annuel composé moyen de votre patrimoine sur une période donnée (depuis 2015 dans ce tableau). Il lisse les fluctuations annuelles pour donner une idée de la performance moyenne à long terme.</p><p><em>Formule :</em> <code>((Valeur Finale / Valeur Initiale)^(1 / Nombre d&apos;Années)) - 1</code></p><p>Un TCAM élevé indique une croissance robuste et constante de votre patrimoine au fil du temps.</p></dd><dt className=\"col-sm-4 mt-3\">Evolution en %</dt><dd className=\"col-sm-8 mt-3\"><p>Cette valeur montre la variation en pourcentage du patrimoine total par rapport à l&apos;année précédente.</p><p><em>Formule :</em> <code>((Total Patrimoine Année N - Total Patrimoine Année N-1) / Total Patrimoine Année N-1) * 100</code></p><p>Elle permet d&apos;identifier rapidement les années de forte croissance ou de stagnation.</p></dd><dt className=\"col-sm-4 mt-3\">Evolution en €</dt><dd className=\"col-sm-8 mt-3\"><p>Indique l&apos;augmentation (ou la diminution) absolue du patrimoine total en euros par rapport à l&apos;année précédente.</p><p><em>Formule :</em> <code>Total Patrimoine Année N - Total Patrimoine Année N-1</code></p><p>Cette métrique donne une mesure concrète de la richesse créée (ou perdue) chaque année.</p></dd><dt className=\"col-sm-4 mt-3\">Croissance Annuel Moyen (€)</dt><dd className=\"col-sm-8 mt-3\"><p>Représente la moyenne simple de l&apos;augmentation annuelle de votre patrimoine en euros depuis l&apos;année de base (2015).</p><p><em>Formule :</em> <code>(Total Patrimoine Année N - Total Patrimoine 2015) / (Année N - 2015)</code></p><p>Elle donne une indication de la contribution monétaire moyenne annuelle à la croissance de votre patrimoine depuis le début de la période de suivi.</p></dd></dl></div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default EvolutionAnnuellePage;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA,8NAAoD,SAAS;AAC7D;AAAA;AAAA;AAAA;AACA,0OAA8D,SAAS;AALvE;;;;;;AAqBA,MAAM,gBAID,CAAC,EAAE,SAAS,EAAE,MAAM,EAAE,QAAQ,EAAE;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,OAAO,WAAW,SAAS,IAAI,OAAO,WAAW;QACjD,gBAAgB,WAAW,kBAAkB;QAC7C,gBAAgB,WAAW,kBAAkB;QAC7C,sBAAsB,WAAW,wBAAwB;QACzD,oBAAoB,WAAW,sBAAsB;QACrD,kBAAkB,WAAW,oBAAoB;IACnD;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,YAAY;YACR,OAAO,WAAW,SAAS,IAAI,OAAO,WAAW;YACjD,gBAAgB,WAAW,kBAAkB;YAC7C,gBAAgB,WAAW,kBAAkB;YAC7C,sBAAsB,WAAW,wBAAwB;YACzD,oBAAoB,WAAW,sBAAsB;YACrD,kBAAkB,WAAW,oBAAoB;QACrD;IACF,GAAG;QAAC;KAAU;IAEd,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,MAAM,SAAS,WAAW,KAAK,QAAQ;QACvC,MAAM,MAAM,WAAW,KAAK,CAAC,WAAW,EAAE,UAAU,KAAK,EAAE,GAAG;QAC9D,IAAI;YACF,MAAM,8HAAA,CAAA,UAAS,CAAC,OAAO,CAAC,KAAK;YAC7B;QACF,EAAE,OAAO,OAAgB;YACvB,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC9D,QAAQ,KAAK,CAAC,6DAA6D;QAC7E;IACF;IAEA,qBACE,8OAAC,oLAAA,CAAA,OAAI;QAAC,UAAU;;0BACd,8OAAC,oLAAA,CAAA,OAAI,CAAC,KAAK;gBAAC,WAAU;;kCACpB,8OAAC,oLAAA,CAAA,OAAI,CAAC,KAAK;kCAAC;;;;;;kCACZ,8OAAC,oLAAA,CAAA,OAAI,CAAC,OAAO;wBAAC,MAAK;wBAAS,KAAI;wBAAO,KAAK,IAAI,OAAO,WAAW,KAAK;wBAAG,OAAO,SAAS,KAAK;wBAAE,UAAU,CAAC,IAAM,YAAY;gCAAE,GAAG,QAAQ;gCAAE,OAAO,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;4BAAE;wBAAI,QAAQ;wBAAC,UAAU,CAAC,CAAC,WAAW;;;;;;;;;;;;0BAExN,8OAAC,oLAAA,CAAA,OAAI,CAAC,KAAK;gBAAC,WAAU;;kCAAO,8OAAC,oLAAA,CAAA,OAAI,CAAC,KAAK;kCAAC;;;;;;kCAAqC,8OAAC,oLAAA,CAAA,OAAI,CAAC,OAAO;wBAAC,MAAK;wBAAS,MAAK;wBAAO,OAAO,SAAS,cAAc;wBAAE,UAAU,CAAC,IAAM,YAAY;gCAAE,GAAG,QAAQ;gCAAE,gBAAgB,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;4BAAE;wBAAI,QAAQ;;;;;;;;;;;;0BAC9P,8OAAC,oLAAA,CAAA,OAAI,CAAC,KAAK;gBAAC,WAAU;;kCAAO,8OAAC,oLAAA,CAAA,OAAI,CAAC,KAAK;kCAAC;;;;;;kCAA0C,8OAAC,oLAAA,CAAA,OAAI,CAAC,OAAO;wBAAC,MAAK;wBAAS,MAAK;wBAAO,OAAO,SAAS,cAAc;wBAAE,UAAU,CAAC,IAAM,YAAY;gCAAE,GAAG,QAAQ;gCAAE,gBAAgB,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;4BAAE;wBAAI,QAAQ;;;;;;;;;;;;0BACnQ,8OAAC,oLAAA,CAAA,OAAI,CAAC,KAAK;gBAAC,WAAU;;kCAAO,8OAAC,oLAAA,CAAA,OAAI,CAAC,KAAK;kCAAC;;;;;;kCAAqC,8OAAC,oLAAA,CAAA,OAAI,CAAC,OAAO;wBAAC,MAAK;wBAAS,MAAK;wBAAO,OAAO,SAAS,oBAAoB;wBAAE,UAAU,CAAC,IAAM,YAAY;gCAAE,GAAG,QAAQ;gCAAE,sBAAsB,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;4BAAE;wBAAI,QAAQ;;;;;;;;;;;;0BAC1Q,8OAAC,oLAAA,CAAA,OAAI,CAAC,KAAK;gBAAC,WAAU;;kCAAO,8OAAC,oLAAA,CAAA,OAAI,CAAC,KAAK;kCAAC;;;;;;kCAAmC,8OAAC,oLAAA,CAAA,OAAI,CAAC,OAAO;wBAAC,MAAK;wBAAS,MAAK;wBAAO,OAAO,SAAS,kBAAkB;wBAAE,UAAU,CAAC,IAAM,YAAY;gCAAE,GAAG,QAAQ;gCAAE,oBAAoB,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;4BAAE;wBAAI,QAAQ;;;;;;;;;;;;0BACpQ,8OAAC,oLAAA,CAAA,OAAI,CAAC,KAAK;gBAAC,WAAU;;kCAAO,8OAAC,oLAAA,CAAA,OAAI,CAAC,KAAK;kCAAC;;;;;;kCAAiC,8OAAC,oLAAA,CAAA,OAAI,CAAC,OAAO;wBAAC,MAAK;wBAAS,MAAK;wBAAO,OAAO,SAAS,gBAAgB;wBAAE,UAAU,CAAC,IAAM,YAAY;gCAAE,GAAG,QAAQ;gCAAE,kBAAkB,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;4BAAE;wBAAI,QAAQ;;;;;;;;;;;;0BAC9P,8OAAC,sLAAA,CAAA,QAAK;gBAAC,SAAQ;0BAAO,cAAA,8OAAC;8BAAM;;;;;;;;;;;0BAC7B,8OAAC;gBAAI,WAAU;;kCAAmC,8OAAC,wLAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAY,SAAS;kCAAU;;;;;;kCAAgB,8OAAC,wLAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAU,MAAK;kCAAS;;;;;;;;;;;;;;;;;;AAG/J;AAEA,MAAM,wBAAkC;IACtC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB,EAAE;IAChE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiC;IAC1F,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAExE,MAAM,kBAAkB;QACtB,IAAI;YACF,MAAM,WAAW,MAAM,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAC;YACrC,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,kBAAkB,KAAK,WAAW;gBACnE,oBAAoB,SAAS,IAAI,CAAC,kBAAkB;YACtD,OAAO;gBACL,MAAM,wBAAwB,MAAM,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAC,iBAAiB,kDAAkD;gBACrH,oBAAoB,sBAAsB,IAAI,CAAC,kBAAkB;YACnE;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qDAAqD;YACnE,oBAAoB,SAAS,mDAAmD;QAClF;IACF;IAEA,MAAM,kBAAkB;QACtB,WAAW;QACX,SAAS;QACT,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAC,cACX,IAAI,CAAC,CAAA;YACJ,cAAc,SAAS,IAAI;YAC3B,WAAW;YACX,cAAc;QAChB,GACC,KAAK,CAAC,CAAA;YACL,QAAQ,KAAK,CAAC,wBAAwB;YACtC,IAAI,aAAa,GAAG;gBAClB,cAAc,CAAA,OAAQ,OAAO;gBAC7B,WAAW,IAAM,mBAAmB;YACtC,OAAO;gBACL,SAAS;gBACT,WAAW;YACb;QACF;IACJ;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;QACA;IACF,uDAAuD;IACvD,GAAG,EAAE;IAEL,MAAM,aAAa;QACjB,aAAa;QACb,qBAAqB;QACrB;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,IAAI,OAAO,OAAO,CAAC,CAAC,kDAAkD,EAAE,MAAM,EAAE,CAAC,GAAG;YAClF,IAAI;gBACF,MAAM,8HAAA,CAAA,UAAS,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,OAAO;gBAC5C;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,yDAAyD;YACzE;QACF;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,UAAU,QAAQ,UAAU,WAAW,OAAO;QAClD,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YAAE,OAAO;YAAY,UAAU;QAAM,GAAG,MAAM,CAAC;IACvF;IAEA,MAAM,mBAAmB,CAAC;QACxB,IAAI,UAAU,QAAQ,UAAU,WAAW,OAAO;QAClD,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YAAE,OAAO;YAAW,uBAAuB;YAAG,uBAAuB;QAAE,GAAG,MAAM,CAAC,QAAQ;IACjI;IAEA,IAAI,WAAW,CAAC,aAAa,eAAe,GAAG,qBAAO,8OAAC;kBAAE;;;;;;IACzD,IAAI,SAAS,cAAa,GAAG,qBAC3B,8OAAC;;0BACC,8OAAC;gBAAE,WAAU;0BAAe;;;;;;0BAC5B,8OAAC;gBAAO,WAAU;gBAAkB,SAAS;oBAAO,cAAc;oBAAI;gBAAkB;0BAAG;;;;;;;;;;;;IAG/F,IAAI,CAAC,WAAW,CAAC,WAAW,MAAM,IAAI,CAAC,OAAO,qBAAO,8OAAC;kBAAE;;;;;;IACxD,IAAI,WAAW,CAAC,WAAW,qBAAO,8OAAC;;YAAE;YAAoD,aAAa;YAAE;;;;;;;IAExG,qBACE,8OAAC;;0BACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;kCAAG;;;;;;kCACJ,8OAAC,wLAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAU,SAAS;4BAAQ,qBAAqB,CAAC;4BAAI,aAAa;wBAAO;kCAAG;;;;;;;;;;;;YAG7F,WAAW,MAAM,GAAG,mBACnB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCAAW,cAAA,8OAAC;gCAAI,WAAU;0CAA6B,cAAA,8OAAC;oCAAI,WAAU;;sDAAwB,8OAAC;sDAAG;;;;;;sDAAY,8OAAC;;gDAAI,UAAU,CAAC,EAAE,EAAE;gDAAM;gDAAI,UAAU,CAAC,WAAW,MAAM,GAAG,EAAE,EAAE;;;;;;;;;;;;;;;;;;;;;;;sCAC9L,8OAAC;4BAAI,WAAU;sCAAW,cAAA,8OAAC;gCAAI,WAAU;0CAA6B,cAAA,8OAAC;oCAAI,WAAU;;sDAAwB,8OAAC;sDAAG;;;;;;sDAAsB,8OAAC;sDAAI,eAAe,UAAU,CAAC,WAAW,MAAM,GAAG,EAAE,EAAE,mBAAmB,UAAU,CAAC,EAAE,EAAE;;;;;;;;;;;;;;;;;;;;;;sCAChO,8OAAC;4BAAI,WAAU;sCAAW,cAAA,8OAAC;gCAAI,WAAU;0CAA0B,cAAA,8OAAC;oCAAI,WAAU;;sDAAwB,8OAAC;sDAAG;;;;;;sDAAe,8OAAC;sDAAI,iBAAiB,UAAU,CAAC,WAAW,MAAM,GAAG,EAAE,EAAE;;;;;;;;;;;;;;;;;;;;;;sCACtL,8OAAC;4BAAI,WAAU;sCAAW,cAAA,8OAAC;gCAAI,WAAU;0CAA6B,cAAA,8OAAC;oCAAI,WAAU;;sDAAwB,8OAAC;sDAAG;;;;;;sDAAsB,8OAAC;sDAAI,eAAe,UAAU,CAAC,WAAW,MAAM,GAAG,EAAE,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAKlM,WAAW,MAAM,GAAG,mBACpB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAM,WAAU;;sCACf,8OAAC;4BAAM,WAAU;sCAAa,cAAA,8OAAC;;kDAAG,8OAAC;kDAAG;;;;;;kDAAU,8OAAC;wCAAG,WAAU;kDAAW;;;;;;kDAAqB,8OAAC;wCAAG,WAAU;kDAAW;;;;;;kDAAmB,8OAAC;wCAAG,WAAU;kDAAW;;;;;;kDAAmB,8OAAC;wCAAG,WAAU;kDAAW;;;;;;kDAAgC,8OAAC;wCAAG,WAAU;kDAAW;;;;;;kDAAa,8OAAC;wCAAG,WAAU;kDAAc;;;;;;;;;;;;;;;;;sCACjT,8OAAC;sCACE,WAAW,GAAG,CAAC,CAAA,0BACd,8OAAC;;sDACC,8OAAC;sDAAG,cAAA,8OAAC;0DAAQ,UAAU,KAAK;;;;;;;;;;;sDAC5B,8OAAC;4CAAG,WAAU;sDAAW,cAAA,8OAAC;0DAAQ,eAAe,UAAU,gBAAgB;;;;;;;;;;;sDAC3E,8OAAC;4CAAG,WAAW,CAAC,SAAS,EAAE,CAAC,UAAU,qBAAqB,IAAI,CAAC,KAAK,IAAI,iBAAiB,eAAe;sDAAG,iBAAiB,UAAU,qBAAqB;;;;;;sDAC5J,8OAAC;4CAAG,WAAW,CAAC,SAAS,EAAE,CAAC,UAAU,eAAe,IAAI,CAAC,KAAK,IAAI,iBAAiB,eAAe;sDAAG,eAAe,UAAU,eAAe;;;;;;sDAC9I,8OAAC;4CAAG,WAAU;sDAAY,eAAe,UAAU,kBAAkB;;;;;;sDACrE,8OAAC;4CAAG,WAAU;sDAAsB,iBAAiB,UAAU,IAAI;;;;;;sDACnE,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC,wLAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAkB,MAAK;oDAAK,WAAU;oDAAO,SAAS;wDAAQ,qBAAqB;wDAAY,aAAa;oDAAO;8DAAG;;;;;;8DACtI,8OAAC,wLAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAiB,MAAK;oDAAK,SAAS,IAAM,aAAa,UAAU,KAAK;8DAAG;;;;;;;;;;;;;mCATpF,UAAU,EAAE;;;;;;;;;;;;;;;;;;;;;0BAkB/B,8OAAC,sLAAA,CAAA,QAAK;gBAAC,MAAM;gBAAW,QAAQ,IAAM,aAAa;gBAAQ,MAAK;;kCAAK,8OAAC,sLAAA,CAAA,QAAK,CAAC,MAAM;wBAAC,WAAW;kCAAC,cAAA,8OAAC,sLAAA,CAAA,QAAK,CAAC,KAAK;sCAAE,mBAAmB,KAAK,CAAC,SAAS,EAAE,kBAAkB,KAAK,EAAE,GAAG;;;;;;;;;;;kCAAiD,8OAAC,sLAAA,CAAA,QAAK,CAAC,IAAI;kCAAC,cAAA,8OAAC;4BAAc,WAAW;4BAAmB,QAAQ;4BAAY,UAAU,IAAM,aAAa;;;;;;;;;;;;;;;;;YAEvU,WAAW,MAAM,GAAG,KAAK,qBAAqB,sBAC7C,8OAAC,oIAAA,CAAA,UAAc;gBAAC,eAAe;gBAAY,kBAAkB;;;;;;0BAG/D,8OAAC;gBAAI,WAAU;;kCAA4B,8OAAC;kCAAG;;;;;;kCAA8C,8OAAC;;;;;kCAAK,8OAAC;wBAAG,WAAU;;0CAAM,8OAAC;gCAAG,WAAU;0CAAW;;;;;;0CAA2C,8OAAC;gCAAG,WAAU;;kDAAW,8OAAC;kDAAE;;;;;;kDAA4O,8OAAC;;0DAAE,8OAAC;0DAAG;;;;;;4CAAc;0DAAC,8OAAC;0DAAK;;;;;;;;;;;;kDAA6E,8OAAC;kDAAE;;;;;;;;;;;;0CAAuG,8OAAC;gCAAG,WAAU;0CAAgB;;;;;;0CAAmB,8OAAC;gCAAG,WAAU;;kDAAgB,8OAAC;kDAAE;;;;;;kDAA8G,8OAAC;;0DAAE,8OAAC;0DAAG;;;;;;4CAAc;0DAAC,8OAAC;0DAAK;;;;;;;;;;;;kDAAuG,8OAAC;kDAAE;;;;;;;;;;;;0CAAkG,8OAAC;gCAAG,WAAU;0CAAgB;;;;;;0CAAmB,8OAAC;gCAAG,WAAU;;kDAAgB,8OAAC;kDAAE;;;;;;kDAA8H,8OAAC;;0DAAE,8OAAC;0DAAG;;;;;;4CAAc;0DAAC,8OAAC;0DAAK;;;;;;;;;;;;kDAAgE,8OAAC;kDAAE;;;;;;;;;;;;0CAAgG,8OAAC;gCAAG,WAAU;0CAAgB;;;;;;0CAAgC,8OAAC;gCAAG,WAAU;;kDAAgB,8OAAC;kDAAE;;;;;;kDAAiI,8OAAC;;0DAAE,8OAAC;0DAAG;;;;;;4CAAc;0DAAC,8OAAC;0DAAK;;;;;;;;;;;;kDAAgF,8OAAC;kDAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGlyD;uCAEe", "debugId": null}}]}