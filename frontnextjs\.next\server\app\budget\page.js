(()=>{var e={};e.id=942,e.ids=[942],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11227:(e,s,t)=>{"use strict";t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\AI-Code\\\\fire_UI\\\\frontnextjs\\\\src\\\\components\\\\BootstrapClient.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\AI-Code\\fire_UI\\frontnextjs\\src\\components\\BootstrapClient.tsx","default")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},24255:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,86346,23)),Promise.resolve().then(t.t.bind(t,27924,23)),Promise.resolve().then(t.t.bind(t,35656,23)),Promise.resolve().then(t.t.bind(t,40099,23)),Promise.resolve().then(t.t.bind(t,38243,23)),Promise.resolve().then(t.t.bind(t,28827,23)),Promise.resolve().then(t.t.bind(t,62763,23)),Promise.resolve().then(t.t.bind(t,97173,23))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29190:(e,s,t)=>{"use strict";t.d(s,{default:()=>l});var r=t(60687),n=t(43210),a=t(85814),i=t.n(a);let l=()=>{let[e,s]=(0,n.useState)(!1),t=()=>{s(!1)};return(0,r.jsx)("nav",{className:"navbar navbar-expand-lg navbar-dark bg-dark",children:(0,r.jsxs)("div",{className:"container-fluid d-flex",children:[(0,r.jsx)(i(),{className:"navbar-brand me-3",href:"/",onClick:t,children:"FIRE Dashboard"}),(0,r.jsxs)("ul",{className:"navbar-nav d-none d-lg-flex flex-row",children:[(0,r.jsx)("li",{className:"nav-item",children:(0,r.jsx)(i(),{className:"nav-link",href:"/",onClick:t,children:"Dashboard"})}),(0,r.jsx)("li",{className:"nav-item",children:(0,r.jsx)(i(),{className:"nav-link",href:"/assets",onClick:t,children:"Patrimoine"})}),(0,r.jsx)("li",{className:"nav-item",children:(0,r.jsx)(i(),{className:"nav-link",href:"/liabilities",onClick:t,children:"Emprunts"})}),(0,r.jsx)("li",{className:"nav-item",children:(0,r.jsx)(i(),{className:"nav-link",href:"/scpi",onClick:t,children:"SCPI"})}),(0,r.jsx)("li",{className:"nav-item",children:(0,r.jsx)(i(),{className:"nav-link",href:"/evolution",onClick:t,children:"\xc9volution"})}),(0,r.jsx)("li",{className:"nav-item",children:(0,r.jsx)(i(),{className:"nav-link",href:"/budget",onClick:t,children:"Budget FIRE"})}),(0,r.jsx)("li",{className:"nav-item",children:(0,r.jsx)(i(),{className:"nav-link",href:"/scenarios",onClick:t,children:"Sc\xe9narios"})}),(0,r.jsx)("li",{className:"nav-item",children:(0,r.jsx)(i(),{className:"nav-link",href:"/fire",onClick:t,children:"Objectif FIRE"})}),(0,r.jsx)("li",{className:"nav-item",children:(0,r.jsx)(i(),{className:"nav-link",href:"/test",onClick:t,children:"Test API"})})]}),(0,r.jsx)("button",{className:"navbar-toggler d-lg-none",type:"button",onClick:()=>{s(!e)},"aria-controls":"navbarNavContent","aria-expanded":e,"aria-label":"Toggle navigation",children:(0,r.jsx)("span",{className:"navbar-toggler-icon"})}),e&&(0,r.jsx)("div",{className:"navbar-collapse show d-lg-none position-absolute top-100 start-0 w-100 bg-dark",children:(0,r.jsxs)("ul",{className:"navbar-nav",children:[(0,r.jsx)("li",{className:"nav-item",children:(0,r.jsx)(i(),{className:"nav-link",href:"/",onClick:t,children:"Dashboard"})}),(0,r.jsx)("li",{className:"nav-item",children:(0,r.jsx)(i(),{className:"nav-link",href:"/assets",onClick:t,children:"Patrimoine"})}),(0,r.jsx)("li",{className:"nav-item",children:(0,r.jsx)(i(),{className:"nav-link",href:"/liabilities",onClick:t,children:"Emprunts"})}),(0,r.jsx)("li",{className:"nav-item",children:(0,r.jsx)(i(),{className:"nav-link",href:"/scpi",onClick:t,children:"SCPI"})}),(0,r.jsx)("li",{className:"nav-item",children:(0,r.jsx)(i(),{className:"nav-link",href:"/evolution",onClick:t,children:"\xc9volution"})}),(0,r.jsx)("li",{className:"nav-item",children:(0,r.jsx)(i(),{className:"nav-link",href:"/budget",onClick:t,children:"Budget FIRE"})}),(0,r.jsx)("li",{className:"nav-item",children:(0,r.jsx)(i(),{className:"nav-link",href:"/scenarios",onClick:t,children:"Sc\xe9narios"})}),(0,r.jsx)("li",{className:"nav-item",children:(0,r.jsx)(i(),{className:"nav-link",href:"/fire",onClick:t,children:"Objectif FIRE"})}),(0,r.jsx)("li",{className:"nav-item",children:(0,r.jsx)(i(),{className:"nav-link",href:"/test",onClick:t,children:"Test API"})})]})})]})})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30004:(e,s,t)=>{"use strict";t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\AI-Code\\\\fire_UI\\\\frontnextjs\\\\src\\\\components\\\\Navbar.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\AI-Code\\fire_UI\\frontnextjs\\src\\components\\Navbar.tsx","default")},33873:e=>{"use strict";e.exports=require("path")},42226:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\AI-Code\\\\fire_UI\\\\frontnextjs\\\\src\\\\app\\\\budget\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\AI-Code\\fire_UI\\frontnextjs\\src\\app\\budget\\page.tsx","default")},51832:(e,s,t)=>{Promise.resolve().then(t.bind(t,11227)),Promise.resolve().then(t.bind(t,30004))},52504:(e,s,t)=>{Promise.resolve().then(t.bind(t,85461)),Promise.resolve().then(t.bind(t,29190))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},59959:(e,s,t)=>{Promise.resolve().then(t.bind(t,42226))},61135:()=>{},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>n});var r=t(31658);let n=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},78162:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>n});var r=t(31658);let n=async e=>[{type:"image/png",sizes:"192x192",url:(0,r.fillMetadataSegment)(".",await e.params,"icon.png")+"?d84b06edc1baa420"}]},79551:e=>{"use strict";e.exports=require("url")},80996:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>h});var r=t(60687),n=t(43210),a=t(90317),i=t(48710),l=t(92388),o=t(31889),c=t(25865);let d=({category:e,onSave:s,onCancel:t})=>{let[o,c]=(0,n.useState)({nom:e?.nom||"",budget_annuel:e?.budget_annuel||0,description:e?.description||"",ordre_affichage:e?.ordre_affichage||0});(0,n.useEffect)(()=>{c({nom:e?.nom||"",budget_annuel:e?.budget_annuel||0,description:e?.description||"",ordre_affichage:e?.ordre_affichage||0})},[e]);let d=async t=>{t.preventDefault();let r=e?.id?"put":"post",n=e?.id?`/budget/categories/${e.id}`:"/budget/categories";try{await a.A[r](n,o),s()}catch(s){let e=s instanceof Error?s.message:"Unknown error";console.error("Erreur lors de la sauvegarde de la cat\xe9gorie",e),alert(`Erreur: ${e}`)}};return(0,r.jsxs)(i.A,{onSubmit:d,children:[(0,r.jsxs)(i.A.Group,{className:"mb-3",children:[(0,r.jsx)(i.A.Label,{children:"Nom de la cat\xe9gorie"}),(0,r.jsx)(i.A.Control,{type:"text",value:o.nom,onChange:e=>c({...o,nom:e.target.value}),required:!0,placeholder:"Ex: Alimentation, Assurance Maison..."})]}),(0,r.jsxs)(i.A.Group,{className:"mb-3",children:[(0,r.jsx)(i.A.Label,{children:"Budget annuel (€)"}),(0,r.jsx)(i.A.Control,{type:"number",step:"0.01",value:o.budget_annuel,onChange:e=>c({...o,budget_annuel:parseFloat(e.target.value)||0}),required:!0})]}),(0,r.jsxs)(i.A.Group,{className:"mb-3",children:[(0,r.jsx)(i.A.Label,{children:"Description"}),(0,r.jsx)(i.A.Control,{type:"text",value:o.description,onChange:e=>c({...o,description:e.target.value}),placeholder:"Description de la cat\xe9gorie..."})]}),(0,r.jsxs)(i.A.Group,{className:"mb-3",children:[(0,r.jsx)(i.A.Label,{children:"Ordre d'affichage"}),(0,r.jsx)(i.A.Control,{type:"number",value:o.ordre_affichage,onChange:e=>c({...o,ordre_affichage:parseInt(e.target.value)||0}),placeholder:"Ordre d'affichage (1, 2, 3...)"}),(0,r.jsx)(i.A.Text,{className:"text-muted",children:"Plus le nombre est petit, plus la cat\xe9gorie appara\xeetra en haut de la liste"})]}),(0,r.jsxs)("div",{className:"d-flex justify-content-end gap-2",children:[(0,r.jsx)(l.A,{variant:"secondary",onClick:t,children:"Annuler"}),(0,r.jsx)(l.A,{variant:"primary",type:"submit",children:"Sauvegarder"})]})]})},h=()=>{let[e,s]=(0,n.useState)([]),[t,i]=(0,n.useState)(null),[h,u]=(0,n.useState)(!0),[m,x]=(0,n.useState)(null),[p,j]=(0,n.useState)(!1),[v,b]=(0,n.useState)(null),[f,g]=(0,n.useState)(0),[N,C]=(0,n.useState)(!1),A=async()=>{u(!0),x(null);try{let[e,t]=await Promise.all([a.A.get("/budget/categories"),a.A.get("/budget/summary")]);s(e.data.sort((e,s)=>e.ordre_affichage-s.ordre_affichage)),i(t.data),u(!1),g(0)}catch(e){console.error("Budget API error:",e),f<2?(g(e=>e+1),setTimeout(()=>A(),1e3)):(x("Erreur lors de la r\xe9cup\xe9ration des donn\xe9es budg\xe9taires."),u(!1))}};(0,n.useEffect)(()=>{A()},[]);let k=async(e,s)=>{if(window.confirm(`\xcates-vous s\xfbr de vouloir supprimer la cat\xe9gorie "${s}" ? Cette action est irr\xe9versible.`))try{await a.A.delete(`/budget/categories/${e}`),A()}catch(s){let e=s instanceof Error?s.message:"Unknown error";console.error("Erreur lors de la suppression de la cat\xe9gorie",e),alert(`Erreur lors de la suppression: ${e}`)}},y=e=>new Intl.NumberFormat("fr-FR",{style:"currency",currency:"EUR"}).format(e);return h&&!p&&0===f?(0,r.jsx)("p",{children:"Chargement du budget..."}):m&&f>=2?(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-danger",children:m}),(0,r.jsx)("button",{className:"btn btn-primary",onClick:()=>{g(0),A()},children:"R\xe9essayer"})]}):h||t||m?h&&!p?(0,r.jsxs)("p",{children:["Chargement du budget, tentative ",f+1,"..."]}):(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"d-flex justify-content-between align-items-center mb-4",children:[(0,r.jsx)("h1",{children:"Calculateur Budget FIRE"}),(0,r.jsxs)("div",{className:"d-flex gap-2",children:[(0,r.jsxs)(l.A,{variant:"outline-secondary",onClick:()=>C(!N),children:[N?"Masquer":"G\xe9rer"," Cat\xe9gories"]}),(0,r.jsx)(l.A,{variant:"success",onClick:()=>{b({}),j(!0)},children:"Nouvelle Cat\xe9gorie"})]})]}),t&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"row mb-4",children:[(0,r.jsx)("div",{className:"col-md-4",children:(0,r.jsx)("div",{className:"card text-white bg-primary",children:(0,r.jsxs)("div",{className:"card-body text-center",children:[(0,r.jsx)("h5",{children:"Budget Annuel FIRE"}),(0,r.jsx)("h4",{children:y(t.total_budget_annuel)}),(0,r.jsx)("small",{children:"D\xe9penses nettes cibles"})]})})}),(0,r.jsx)("div",{className:"col-md-4",children:(0,r.jsx)("div",{className:"card text-white bg-warning",children:(0,r.jsxs)("div",{className:"card-body text-center",children:[(0,r.jsx)("h5",{children:"Retrait Brut N\xe9cessaire"}),(0,r.jsx)("h4",{children:y(t.retrait_brut_necessaire)}),(0,r.jsx)("small",{children:"+30% imp\xf4ts/PS"})]})})}),(0,r.jsx)("div",{className:"col-md-4",children:(0,r.jsx)("div",{className:"card text-white bg-success",children:(0,r.jsxs)("div",{className:"card-body text-center",children:[(0,r.jsx)("h5",{children:"Capital FIRE Requis"}),(0,r.jsx)("h4",{children:y(910150)}),(0,r.jsx)("small",{children:"Objectif document\xe9"})]})})})]}),(0,r.jsxs)(o.A,{variant:"info",className:"mb-4",children:[(0,r.jsx)(o.A.Heading,{children:"Objectif FIRE 2038"}),(0,r.jsxs)("p",{className:"mb-0",children:["Avec ce budget de ",(0,r.jsx)("strong",{children:y(t.total_budget_annuel)})," par an, votre objectif FIRE document\xe9 est de ",(0,r.jsx)("strong",{children:y(910150)})," (retrait brut de ",(0,r.jsx)("strong",{children:y(t.retrait_brut_necessaire)}),"/an)."]})]})]}),N&&(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("h3",{children:"Gestion des Cat\xe9gories Budg\xe9taires"}),(0,r.jsx)("div",{className:"table-responsive",children:(0,r.jsxs)("table",{className:"table table-striped table-hover",children:[(0,r.jsx)("thead",{className:"table-secondary",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{children:"Ordre"}),(0,r.jsx)("th",{children:"Nom"}),(0,r.jsx)("th",{className:"text-end",children:"Budget Annuel"}),(0,r.jsx)("th",{children:"Description"}),(0,r.jsx)("th",{className:"text-center",children:"Actions"})]})}),(0,r.jsx)("tbody",{children:e.map(e=>(0,r.jsxs)("tr",{children:[(0,r.jsx)("td",{children:e.ordre_affichage}),(0,r.jsx)("td",{children:(0,r.jsx)("strong",{children:e.nom})}),(0,r.jsx)("td",{className:"text-end",children:y(e.budget_annuel)}),(0,r.jsx)("td",{children:e.description}),(0,r.jsxs)("td",{className:"text-center",children:[(0,r.jsx)(l.A,{variant:"outline-primary",size:"sm",className:"me-2",onClick:()=>{b(e),j(!0)},children:"Modifier"}),(0,r.jsx)(l.A,{variant:"outline-danger",size:"sm",onClick:()=>k(e.id,e.nom),children:"Supprimer"})]})]},e.id))}),(0,r.jsx)("tfoot",{children:(0,r.jsxs)("tr",{className:"table-info",children:[(0,r.jsx)("th",{colSpan:2,children:"TOTAL"}),(0,r.jsx)("th",{className:"text-end",children:y(e.reduce((e,s)=>e+s.budget_annuel,0))}),(0,r.jsx)("th",{colSpan:2})]})})]})})]}),(0,r.jsx)("div",{className:"table-responsive",children:(0,r.jsxs)("table",{className:"table table-striped table-hover",children:[(0,r.jsx)("thead",{className:"table-dark",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{children:"Cat\xe9gorie"}),(0,r.jsx)("th",{className:"text-end",children:"Budget Annuel"}),(0,r.jsx)("th",{className:"text-end",children:"Budget Mensuel"})]})}),(0,r.jsx)("tbody",{children:e.map(e=>(0,r.jsxs)("tr",{children:[(0,r.jsxs)("td",{children:[(0,r.jsx)("strong",{children:e.nom}),e.description&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("br",{}),(0,r.jsx)("small",{className:"text-muted",children:e.description})]})]}),(0,r.jsx)("td",{className:"text-end",children:y(e.budget_annuel)}),(0,r.jsx)("td",{className:"text-end",children:y(e.budget_annuel/12)})]},e.id))}),(0,r.jsx)("tfoot",{children:(0,r.jsxs)("tr",{className:"table-info",children:[(0,r.jsx)("th",{children:"TOTAL"}),(0,r.jsx)("th",{className:"text-end",children:y(e.reduce((e,s)=>e+s.budget_annuel,0))}),(0,r.jsx)("th",{className:"text-end",children:y(e.reduce((e,s)=>e+s.budget_annuel,0)/12)})]})})]})}),(0,r.jsxs)(c.A,{show:p,onHide:()=>j(!1),size:"lg",children:[(0,r.jsx)(c.A.Header,{closeButton:!0,children:(0,r.jsx)(c.A.Title,{children:v?.id?`Modifier "${v.nom}"`:"Nouvelle cat\xe9gorie budg\xe9taire"})}),(0,r.jsx)(c.A.Body,{children:(0,r.jsx)(d,{category:v,onSave:()=>{j(!1),b(null),A()},onCancel:()=>j(!1)})})]})]}):(0,r.jsx)("p",{children:"Aucune donn\xe9e budg\xe9taire trouv\xe9e."})}},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},85461:(e,s,t)=>{"use strict";function r(){return null}t.d(s,{default:()=>r}),t(43210)},87983:(e,s,t)=>{Promise.resolve().then(t.bind(t,80996))},88649:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>h,pages:()=>d,routeModule:()=>u,tree:()=>c});var r=t(65239),n=t(48088),a=t(88170),i=t.n(a),l=t(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);t.d(s,o);let c={children:["",{children:["budget",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,42226)),"D:\\AI-Code\\fire_UI\\frontnextjs\\src\\app\\budget\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e),async e=>(await Promise.resolve().then(t.bind(t,78162))).default(e)],apple:[async e=>(await Promise.resolve().then(t.bind(t,94650))).default(e)],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"D:\\AI-Code\\fire_UI\\frontnextjs\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e),async e=>(await Promise.resolve().then(t.bind(t,78162))).default(e)],apple:[async e=>(await Promise.resolve().then(t.bind(t,94650))).default(e)],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["D:\\AI-Code\\fire_UI\\frontnextjs\\src\\app\\budget\\page.tsx"],h={require:t,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/budget/page",pathname:"/budget",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},90317:(e,s,t)=>{"use strict";t.d(s,{A:()=>n});let r=t(51060).A.create({baseURL:"http://localhost:8000/api",headers:{"Content-Type":"application/json"},timeout:1e4});r.interceptors.response.use(e=>e,e=>(console.error("API Error:",e.response?.status,e.config?.url,e.message),Promise.reject(e)));let n=r},93991:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,16444,23)),Promise.resolve().then(t.t.bind(t,16042,23)),Promise.resolve().then(t.t.bind(t,88170,23)),Promise.resolve().then(t.t.bind(t,49477,23)),Promise.resolve().then(t.t.bind(t,29345,23)),Promise.resolve().then(t.t.bind(t,12089,23)),Promise.resolve().then(t.t.bind(t,46577,23)),Promise.resolve().then(t.t.bind(t,31307,23))},94431:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>o,metadata:()=>i,viewport:()=>l});var r=t(37413);t(61135);var n=t(30004),a=t(11227);let i={title:"FIRE Dashboard",description:"Application pour le suivi de l'objectif FIRE (Financial Independence, Retire Early)",manifest:"/manifest.json",icons:{icon:"/icon.png",apple:"/apple-icon.png"}},l={themeColor:"#000000"};function o({children:e}){return(0,r.jsx)("html",{lang:"en",children:(0,r.jsxs)("body",{children:[(0,r.jsxs)("div",{className:"App",children:[(0,r.jsx)(n.default,{}),(0,r.jsx)("main",{className:"container mt-4",children:e})]}),(0,r.jsx)(a.default,{})]})})}},94650:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>n});var r=t(31658);let n=async e=>[{type:"image/png",sizes:"192x192",url:(0,r.fillMetadataSegment)(".",await e.params,"apple-icon.png")+"?d84b06edc1baa420"}]},94735:e=>{"use strict";e.exports=require("events")}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[447,412,517,315,36,889],()=>t(88649));module.exports=r})();