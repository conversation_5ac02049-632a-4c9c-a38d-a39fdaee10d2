(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[606],{1689:(e,r,t)=>{Promise.resolve().then(t.bind(t,5720))},2365:(e,r,t)=>{"use strict";t.d(r,{A:()=>l});let a=t(3464).A.create({baseURL:"http://localhost:8000/api",headers:{"Content-Type":"application/json"},timeout:1e4});a.interceptors.response.use(e=>e,e=>{var r,t;return console.error("API Error:",null==(r=e.response)?void 0:r.status,null==(t=e.config)?void 0:t.url,e.message),Promise.reject(e)});let l=a},5720:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>o});var a=t(5155),l=t(2115),s=t(2365),n=t(8827),i=t(902),c=t(9986);let d=e=>{let{scpi:r,onSave:t,onCancel:c}=e,[d,o]=(0,l.useState)({name:(null==r?void 0:r.name)||"",price_per_share:(null==r?void 0:r.price_per_share)||0,number_of_shares:(null==r?void 0:r.number_of_shares)||0,total_value:(null==r?void 0:r.total_value)||0,update_date:(null==r?void 0:r.update_date)?new Date(r.update_date).toISOString().split("T")[0]:new Date().toISOString().split("T")[0],primaliance_url:(null==r?void 0:r.primaliance_url)||""});(0,l.useEffect)(()=>{let e=d.price_per_share*d.number_of_shares;e!==d.total_value&&o(r=>({...r,total_value:e}))},[d.price_per_share,d.number_of_shares,d.total_value]),(0,l.useEffect)(()=>{o({name:(null==r?void 0:r.name)||"",price_per_share:(null==r?void 0:r.price_per_share)||0,number_of_shares:(null==r?void 0:r.number_of_shares)||0,total_value:(null==r?void 0:r.total_value)||0,update_date:(null==r?void 0:r.update_date)?new Date(r.update_date).toISOString().split("T")[0]:new Date().toISOString().split("T")[0],primaliance_url:(null==r?void 0:r.primaliance_url)||""})},[r]);let u=async e=>{e.preventDefault();let a=(null==r?void 0:r.id)?"put":"post",l=(null==r?void 0:r.id)?"/scpi/".concat(r.id):"/scpi";try{await s.A[a](l,d),t()}catch(e){console.error("Erreur lors de la sauvegarde de la SCPI",e instanceof Error?e.message:"Unknown error")}};return(0,a.jsxs)(n.A,{onSubmit:u,children:[(0,a.jsxs)(n.A.Group,{className:"mb-3",children:[(0,a.jsx)(n.A.Label,{children:"Nom de la SCPI"}),(0,a.jsx)(n.A.Control,{type:"text",value:d.name,onChange:e=>o({...d,name:e.target.value}),required:!0})]}),(0,a.jsxs)(n.A.Group,{className:"mb-3",children:[(0,a.jsx)(n.A.Label,{children:"Prix par part (€)"}),(0,a.jsx)(n.A.Control,{type:"number",step:"0.01",value:d.price_per_share,onChange:e=>o({...d,price_per_share:parseFloat(e.target.value)||0}),required:!0})]}),(0,a.jsxs)(n.A.Group,{className:"mb-3",children:[(0,a.jsx)(n.A.Label,{children:"Nombre de parts"}),(0,a.jsx)(n.A.Control,{type:"number",value:d.number_of_shares,onChange:e=>o({...d,number_of_shares:parseInt(e.target.value)||0}),required:!0})]}),(0,a.jsxs)(n.A.Group,{className:"mb-3",children:[(0,a.jsx)(n.A.Label,{children:"Valeur totale (€)"}),(0,a.jsx)(n.A.Control,{type:"number",step:"0.01",value:d.total_value,onChange:e=>o({...d,total_value:parseFloat(e.target.value)||0}),required:!0}),(0,a.jsxs)(n.A.Text,{className:"text-muted",children:["Calcul\xe9 automatiquement : ",(d.price_per_share*d.number_of_shares).toFixed(2)," €"]})]}),(0,a.jsxs)(n.A.Group,{className:"mb-3",children:[(0,a.jsx)(n.A.Label,{children:"Date de mise \xe0 jour"}),(0,a.jsx)(n.A.Control,{type:"date",value:d.update_date,onChange:e=>o({...d,update_date:e.target.value}),required:!0})]}),(0,a.jsxs)(n.A.Group,{className:"mb-3",children:[(0,a.jsx)(n.A.Label,{children:"URL Primaliance (Optionnel)"}),(0,a.jsx)(n.A.Control,{type:"url",value:d.primaliance_url,onChange:e=>o({...d,primaliance_url:e.target.value}),placeholder:"https://www.primaliance.com/..."}),(0,a.jsx)(n.A.Text,{className:"text-muted",children:"Lien vers la page de la SCPI sur Primaliance pour r\xe9cup\xe9rer des informations d\xe9taill\xe9es."})]}),(0,a.jsxs)("div",{className:"d-flex justify-content-end gap-2",children:[(0,a.jsx)(i.A,{variant:"secondary",onClick:c,children:"Annuler"}),(0,a.jsx)(i.A,{variant:"primary",type:"submit",children:"Sauvegarder"})]})]})},o=()=>{let[e,r]=(0,l.useState)([]),[t,n]=(0,l.useState)(!0),[o,u]=(0,l.useState)(null),[m,h]=(0,l.useState)(!1),[p,x]=(0,l.useState)(null),[j,_]=(0,l.useState)(0),[v,b]=(0,l.useState)(null),[f,g]=(0,l.useState)(null),[A,C]=(0,l.useState)(!1),[S,N]=(0,l.useState)(null),y=()=>{n(!0),u(null),s.A.get("/scpi").then(e=>{r(e.data),n(!1),_(0)}).catch(e=>{console.error("SCPI API error:",e),j<2?(_(e=>e+1),setTimeout(()=>y(),1e3)):(u("Erreur lors de la r\xe9cup\xe9ration des SCPI."),n(!1))})};(0,l.useEffect)(()=>{y()},[]);let P=async e=>{if(window.confirm("\xcates-vous s\xfbr de vouloir supprimer cette SCPI ?"))try{await s.A.delete("/scpi/".concat(e)),y()}catch(e){console.error("Erreur lors de la suppression de la SCPI",e)}},I=async e=>{if(!e.primaliance_url){N("Aucune URL Primaliance n'est configur\xe9e pour cette SCPI."),b(null),g(e.id);return}C(!0),N(null),b(null),g(e.id);try{let r=await s.A.get("/scpi/scrape/?url=".concat(encodeURIComponent(e.primaliance_url)));b(r.data)}catch(e){console.error("Erreur lors du scraping SCPI",e instanceof Error?e.message:"Unknown error"),N("Erreur lors de la r\xe9cup\xe9ration des donn\xe9es."),b(null)}finally{C(!1)}};if(t&&!m&&0===j)return(0,a.jsx)("p",{children:"Chargement des SCPI..."});if(o&&j>=2)return(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-danger",children:o}),(0,a.jsx)("button",{className:"btn btn-primary",onClick:()=>{_(0),y()},children:"R\xe9essayer"})]});if(!t&&!e.length&&!o)return(0,a.jsx)("p",{children:"Aucune SCPI trouv\xe9e."});if(t&&!m)return(0,a.jsxs)("p",{children:["Chargement des SCPI, tentative ",j+1,"..."]});let w=e.reduce((e,r)=>e+r.total_value,0);return(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"d-flex justify-content-between align-items-center mb-4",children:[(0,a.jsx)("h1",{children:"Mes SCPI"}),(0,a.jsx)(i.A,{variant:"primary",onClick:()=>{x({}),h(!0)},children:"Ajouter une SCPI"})]}),e.length>0&&(0,a.jsxs)("table",{className:"table table-striped table-hover",children:[(0,a.jsx)("thead",{className:"table-dark",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{children:"Nom"}),(0,a.jsx)("th",{className:"text-end",children:"Prix par part"}),(0,a.jsx)("th",{className:"text-end",children:"Nombre de parts"}),(0,a.jsx)("th",{className:"text-end",children:"Valeur totale"}),(0,a.jsx)("th",{className:"text-center",children:"Date M\xe0J"}),(0,a.jsx)("th",{className:"text-center",style:{minWidth:"220px"},children:"Actions"})]})}),(0,a.jsx)("tbody",{children:e.map(e=>(0,a.jsxs)(l.Fragment,{children:[(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{children:e.name}),(0,a.jsx)("td",{className:"text-end",children:new Intl.NumberFormat("fr-FR",{style:"currency",currency:"EUR"}).format(e.price_per_share)}),(0,a.jsx)("td",{className:"text-end",children:e.number_of_shares}),(0,a.jsx)("td",{className:"text-end",children:new Intl.NumberFormat("fr-FR",{style:"currency",currency:"EUR"}).format(e.total_value)}),(0,a.jsx)("td",{className:"text-center",children:new Date(e.update_date).toLocaleDateString("fr-FR")}),(0,a.jsxs)("td",{className:"text-center",children:[(0,a.jsx)(i.A,{variant:"outline-info",size:"sm",className:"me-1",onClick:()=>I(e),disabled:A&&f===e.id,children:A&&f===e.id?"Chargt...":"Primaliance"}),(0,a.jsx)(i.A,{variant:"outline-primary",size:"sm",className:"me-1",onClick:()=>{x(e),h(!0)},children:"Modifier"}),(0,a.jsx)(i.A,{variant:"outline-danger",size:"sm",onClick:()=>P(e.id),children:"Supprimer"})]})]}),f===e.id&&(0,a.jsx)("tr",{children:(0,a.jsxs)("td",{colSpan:6,children:[A&&(0,a.jsx)("p",{className:"text-info m-2",children:"Chargement des donn\xe9es depuis Primaliance..."}),S&&(0,a.jsx)("p",{className:"text-danger m-2",children:S}),v&&!A&&(0,a.jsxs)("div",{className:"p-3 my-2 bg-light border rounded",children:[(0,a.jsxs)("h5",{children:["D\xe9tails de ",v.nom||e.name," (Primaliance)"]}),(0,a.jsx)("ul",{className:"list-unstyled",children:Object.entries(v).map(e=>{let[r,t]=e;if(t&&"nom"!==r){let e=r.replace(/_/g," ").replace(/\b\w/g,e=>e.toUpperCase());return(0,a.jsxs)("li",{children:[(0,a.jsxs)("strong",{children:[e,":"]})," ",t]},r)}return null})})]})]})})]},e.id))}),(0,a.jsx)("tfoot",{children:(0,a.jsxs)("tr",{className:"table-info",children:[(0,a.jsx)("th",{colSpan:3,children:"TOTAL SCPI"}),(0,a.jsx)("th",{className:"text-end",children:new Intl.NumberFormat("fr-FR",{style:"currency",currency:"EUR"}).format(w)}),(0,a.jsx)("th",{colSpan:2})]})})]}),(0,a.jsxs)(c.A,{show:m,onHide:()=>h(!1),size:"lg",children:[(0,a.jsx)(c.A.Header,{closeButton:!0,children:(0,a.jsxs)(c.A.Title,{children:[(null==p?void 0:p.id)?"Modifier":"Ajouter"," une SCPI"]})}),(0,a.jsx)(c.A.Body,{children:(0,a.jsx)(d,{scpi:p,onSave:()=>{h(!1),x(null),y()},onCancel:()=>h(!1)})})]})]})}}},e=>{var r=r=>e(e.s=r);e.O(0,[464,160,405,441,684,358],()=>r(1689)),_N_E=e.O()}]);