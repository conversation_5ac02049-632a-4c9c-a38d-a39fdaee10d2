{"version": 3, "sources": [], "sections": [{"offset": {"line": 111, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/AI-Code/fire_UI/frontnextjs/src/components/ApiClient.ts"], "sourcesContent": ["import axios from 'axios';\r\n\r\nconst apiClient = axios.create({\r\n  baseURL: 'http://localhost:8000/api',\r\n  headers: {\r\n    'Content-Type': 'application/json',\r\n  },\r\n  timeout: 10000, // 10 second timeout\r\n});\r\n\r\n// Add response interceptor for error handling\r\napiClient.interceptors.response.use(\r\n  (response) => response,\r\n  (error) => {\r\n    console.error('API Error:', error.response?.status, error.config?.url, error.message);\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\nexport default apiClient;\r\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,YAAY,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAC7B,SAAS;IACT,SAAS;QACP,gBAAgB;IAClB;IACA,SAAS;AACX;AAEA,8CAA8C;AAC9C,UAAU,YAAY,CAAC,QAAQ,CAAC,GAAG,CACjC,CAAC,WAAa,UACd,CAAC;IACC,QAAQ,KAAK,CAAC,cAAc,MAAM,QAAQ,EAAE,QAAQ,MAAM,MAAM,EAAE,KAAK,MAAM,OAAO;IACpF,OAAO,QAAQ,MAAM,CAAC;AACxB;uCAGa", "debugId": null}}, {"offset": {"line": 135, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/AI-Code/fire_UI/frontnextjs/src/app/assets/AssetsClientPart.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useEffect, useState, FormEvent, useCallback } from 'react';\r\nimport apiClient from '../../components/ApiClient';\r\nimport { Modal, Button, Form, InputGroup, FormControl } from 'react-bootstrap';\r\n\r\n// --- Types ---\r\ninterface CategoryValue {\r\n  name: string;\r\n  value: number;\r\n}\r\n\r\ninterface ApiCategoryValue {\r\n  category_name: string;\r\n  value: number;\r\n}\r\n\r\nexport interface Asset { // Exporté pour être utilisé par le Server Component page.tsx\r\n  id: number;\r\n  name: string;\r\n  value: number;\r\n  annual_interest: number | null;\r\n  notes: string | null;\r\n  update_date: string;\r\n  categories: ApiCategoryValue[];\r\n  liabilities: unknown[];\r\n}\r\n// --- Fin des Types ---\r\n\r\ninterface AssetsClientPartProps {\r\n  initialAssets: Asset[];\r\n  fetchError?: string;\r\n}\r\n\r\n// AssetForm reste un sous-composant défini ici\r\nconst AssetForm: React.FC<{ asset: Partial<Asset> | null, onSave: () => void, onCancel: () => void }> = ({ asset, onSave, onCancel }) => {\r\n    const convertApiCategoriesToForm = (apiCategories: ApiCategoryValue[] | undefined): CategoryValue[] => {\r\n        if (!apiCategories || apiCategories.length === 0) return [{ name: 'Liquidité', value: 0 }];\r\n        return apiCategories.map(cat => ({ name: cat.category_name, value: cat.value }));\r\n    };\r\n\r\n    const initializeFormData = useCallback(() => {\r\n        if (asset && asset.id) {\r\n            const convertedCategories = convertApiCategoriesToForm(asset.categories);\r\n            return {\r\n                name: asset.name || '', value: asset.value || 0, annual_interest: asset.annual_interest || null,\r\n                notes: asset.notes || '', categories: convertedCategories,\r\n                update_date: asset.update_date ? new Date(asset.update_date).toISOString().split('T')[0] : new Date().toISOString().split('T')[0],\r\n            };\r\n        }\r\n        return {\r\n            name: '', value: 0, annual_interest: null, notes: '',\r\n            categories: [{ name: 'Liquidité', value: 0 }], update_date: new Date().toISOString().split('T')[0],\r\n        };\r\n    }, [asset]);\r\n    const [formData, setFormData] = useState(initializeFormData);\r\n\r\n    useEffect(() => { setFormData(initializeFormData()); }, [asset, initializeFormData]);\r\n\r\n    useEffect(() => {\r\n        const totalValue = formData.categories.reduce((sum, cat) => sum + (Number(cat.value) || 0), 0);\r\n        if (totalValue !== formData.value) {\r\n            setFormData(prevData => ({ ...prevData, value: totalValue }));\r\n        }\r\n    }, [formData.categories, formData.value]);\r\n\r\n    const handleCategoryChange = (index: number, field: string, value: string | number) => {\r\n        const newCategories = [...formData.categories];\r\n        newCategories[index] = { ...newCategories[index], [field]: field === 'value' ? parseFloat(value) || 0 : value };\r\n        setFormData({ ...formData, categories: newCategories });\r\n    };\r\n    const addCategory = () => setFormData({ ...formData, categories: [...formData.categories, { name: 'Bourse', value: 0 }] });\r\n    const removeCategory = (index: number) => {\r\n        const newCategories = [...formData.categories]; newCategories.splice(index, 1);\r\n        setFormData({ ...formData, categories: newCategories });\r\n    };\r\n    const handleSubmit = async (e: FormEvent) => {\r\n        e.preventDefault();\r\n        const method = asset?.id ? 'put' : 'post';\r\n        const url = asset?.id ? `/assets/${asset.id}` : '/assets';\r\n        try { await apiClient[method](url, formData); onSave(); }\r\n        catch (error: unknown) {\r\n            const errorMessage = error instanceof Error ? error.message : 'Unknown error';\r\n            console.error(\"Erreur sauvegarde actif\", errorMessage);\r\n        }\r\n    };\r\n\r\n    return (\r\n        <Form onSubmit={handleSubmit}>\r\n            <Form.Group className=\"mb-3\"><Form.Label>Nom</Form.Label><Form.Control type=\"text\" value={formData.name} onChange={e => setFormData({ ...formData, name: e.target.value })} required /></Form.Group>\r\n            <Form.Group className=\"mb-3\"><Form.Label>Valeur</Form.Label><Form.Control type=\"number\" value={formData.value} readOnly disabled /></Form.Group>\r\n            <Form.Group className=\"mb-3\"><Form.Label>Intérêt Annuel</Form.Label><Form.Control type=\"number\" step=\"0.01\" value={formData.annual_interest || ''} onChange={e => setFormData({ ...formData, annual_interest: parseFloat(e.target.value) || null })} /></Form.Group>\r\n            <Form.Group className=\"mb-3\"><Form.Label>Notes</Form.Label><Form.Control type=\"text\" value={formData.notes || ''} onChange={e => setFormData({ ...formData, notes: e.target.value })} /></Form.Group>\r\n            <h5>Catégories</h5>\r\n            {formData.categories.map((category, index) => (\r\n                <InputGroup className=\"mb-3\" key={index}>\r\n                    <Form.Select value={category.name} onChange={e => handleCategoryChange(index, 'name', e.target.value)}><option>Liquidité</option><option>Bourse</option><option>Crypto-Actifs</option><option>Fonds sécurisés</option><option>Immobilier</option><option>Prêts participatifs</option></Form.Select>\r\n                    <FormControl type=\"number\" value={category.value} onChange={e => handleCategoryChange(index, 'value', e.target.value)} />\r\n                    <Button variant=\"outline-danger\" onClick={() => removeCategory(index)}>X</Button>\r\n                </InputGroup>\r\n            ))}\r\n            <Button variant=\"outline-primary\" onClick={addCategory} className=\"mb-3\">Ajouter une catégorie</Button>\r\n            <Form.Group className=\"mb-3 mt-3\"><Form.Label>Date de mise à jour</Form.Label><Form.Control type=\"date\" value={formData.update_date} onChange={e => setFormData({ ...formData, update_date: e.target.value })} required /></Form.Group>\r\n            <Button variant=\"primary\" type=\"submit\">Sauvegarder</Button><Button variant=\"secondary\" onClick={onCancel} className=\"ms-2\">Annuler</Button>\r\n        </Form>\r\n    );\r\n};\r\n\r\nexport const AssetsClientPart: React.FC<AssetsClientPartProps> = ({ initialAssets, fetchError }) => {\r\n  const [assets, setAssets] = useState<Asset[]>(initialAssets || []);\r\n  const [loading, setLoading] = useState(false);\r\n  const [error, setError] = useState<string | null>(fetchError || null);\r\n  const [showModal, setShowModal] = useState(false);\r\n  const [selectedAsset, setSelectedAsset] = useState<Partial<Asset> | null>(null);\r\n  const [sortConfig, setSortConfig] = useState<{ key: keyof Asset | null; direction: 'ascending' | 'descending' }>({ key: 'value', direction: 'descending' });\r\n\r\n  useEffect(() => {\r\n    setAssets(initialAssets || []);\r\n    setError(fetchError || null);\r\n  }, [initialAssets, fetchError]);\r\n\r\n  const fetchAssetsClient = () => {\r\n    setLoading(true); setError(null);\r\n    apiClient.get('/assets')\r\n      .then(response => { setAssets(response.data); })\r\n      .catch(err => { console.error('Assets API error (client fetch):', err); setError('Erreur lors du rechargement des actifs.'); })\r\n      .finally(() => setLoading(false));\r\n  };\r\n\r\n  const requestSort = (key: keyof Asset) => {\r\n    let direction: 'ascending' | 'descending' = 'ascending';\r\n    if (sortConfig.key === key && sortConfig.direction === 'ascending') { direction = 'descending'; }\r\n    setSortConfig({ key, direction });\r\n  };\r\n\r\n  const sortedAssets = React.useMemo(() => {\r\n    const sortableItems = [...assets];\r\n    if (sortConfig.key) {\r\n      sortableItems.sort((a, b) => {\r\n        const aValue = a[sortConfig.key!]; const bValue = b[sortConfig.key!];\r\n        if (aValue === null || aValue === undefined) return 1; if (bValue === null || bValue === undefined) return -1;\r\n        if (aValue < bValue) return sortConfig.direction === 'ascending' ? -1 : 1;\r\n        if (aValue > bValue) return sortConfig.direction === 'ascending' ? 1 : -1;\r\n        return 0;\r\n      });\r\n    }\r\n    return sortableItems;\r\n  }, [assets, sortConfig]);\r\n\r\n  const handleSave = () => { setShowModal(false); setSelectedAsset(null); fetchAssetsClient(); };\r\n  const handleDelete = async (id: number) => {\r\n    if (window.confirm(\"Êtes-vous sûr de vouloir supprimer cet actif ?\")) {\r\n      try { await apiClient.delete(`/assets/${id}`); fetchAssetsClient(); }\r\n      catch (err) { console.error(\"Erreur suppression actif\", err); }\r\n    }\r\n  };\r\n\r\n  if (error && assets.length === 0) return (<div><p className=\"text-danger\">{error}</p><button className=\"btn btn-primary\" onClick={fetchAssetsClient}>Réessayer</button></div>);\r\n  if (loading) return <p>Rechargement des actifs...</p>;\r\n\r\n  return (\r\n    <div>\r\n      <div className=\"d-flex justify-content-between align-items-center mb-4\"><h1>Mon Patrimoine (Actifs)</h1><Button variant=\"primary\" onClick={() => { setSelectedAsset({}); setShowModal(true); }}>Ajouter un Actif</Button></div>\r\n      {assets.length === 0 && !error && <p>Aucun actif trouvé.</p>}\r\n      {assets.length > 0 && (\r\n        <table className=\"table table-striped table-hover\">\r\n          <thead className=\"table-dark\"><tr>\r\n              <th onClick={() => requestSort('name')} style={{ cursor: 'pointer' }}>Nom {sortConfig.key === 'name' ? (sortConfig.direction === 'ascending' ? '▲' : '▼') : ''}</th>\r\n              <th>Catégories</th><th className=\"text-end\">Intérêt Annuel</th>\r\n              <th className=\"text-end\" onClick={() => requestSort('value')} style={{ cursor: 'pointer' }}>Valeur {sortConfig.key === 'value' ? (sortConfig.direction === 'ascending' ? '▲' : '▼') : ''}</th>\r\n              <th className=\"text-center\" onClick={() => requestSort('update_date')} style={{ cursor: 'pointer' }}>Dernière MàJ {sortConfig.key === 'update_date' ? (sortConfig.direction === 'ascending' ? '▲' : '▼') : ''}</th>\r\n              <th className=\"text-center\">Actions</th></tr></thead>\r\n          <tbody>{sortedAssets.map(asset => (<tr key={asset.id}><td>{asset.name}</td><td>{asset.categories.map(c => `${c.category_name} (${new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR' }).format(c.value)})`).join(', ')}</td><td className=\"text-end\">{asset.annual_interest ? new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR' }).format(asset.annual_interest) : 'N/A'}</td><td className=\"text-end\">{new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR' }).format(asset.value)}</td><td className=\"text-center\">{new Date(asset.update_date).toLocaleDateString('fr-FR')}</td><td className=\"text-center\"><Button variant=\"outline-primary\" size=\"sm\" onClick={() => { setSelectedAsset(asset); setShowModal(true); }}>Modifier</Button>{' '}<Button variant=\"outline-danger\" size=\"sm\" onClick={() => handleDelete(asset.id)}>Supprimer</Button></td></tr>))}</tbody>\r\n        </table>\r\n      )}\r\n      <Modal show={showModal} onHide={() => setShowModal(false)}><Modal.Header closeButton><Modal.Title>{selectedAsset?.id ? 'Modifier' : 'Ajouter'} un Actif</Modal.Title></Modal.Header><Modal.Body><AssetForm asset={selectedAsset} onSave={handleSave} onCancel={() => setShowModal(false)} /></Modal.Body></Modal>\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;AAkCA,+CAA+C;AAC/C,MAAM,YAAkG,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE;IAChI,MAAM,6BAA6B,CAAC;QAChC,IAAI,CAAC,iBAAiB,cAAc,MAAM,KAAK,GAAG,OAAO;YAAC;gBAAE,MAAM;gBAAa,OAAO;YAAE;SAAE;QAC1F,OAAO,cAAc,GAAG,CAAC,CAAA,MAAO,CAAC;gBAAE,MAAM,IAAI,aAAa;gBAAE,OAAO,IAAI,KAAK;YAAC,CAAC;IAClF;IAEA,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACnC,IAAI,SAAS,MAAM,EAAE,EAAE;YACnB,MAAM,sBAAsB,2BAA2B,MAAM,UAAU;YACvE,OAAO;gBACH,MAAM,MAAM,IAAI,IAAI;gBAAI,OAAO,MAAM,KAAK,IAAI;gBAAG,iBAAiB,MAAM,eAAe,IAAI;gBAC3F,OAAO,MAAM,KAAK,IAAI;gBAAI,YAAY;gBACtC,aAAa,MAAM,WAAW,GAAG,IAAI,KAAK,MAAM,WAAW,EAAE,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,GAAG,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;YACrI;QACJ;QACA,OAAO;YACH,MAAM;YAAI,OAAO;YAAG,iBAAiB;YAAM,OAAO;YAClD,YAAY;gBAAC;oBAAE,MAAM;oBAAa,OAAO;gBAAE;aAAE;YAAE,aAAa,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QACtG;IACJ,GAAG;QAAC;KAAM;IACV,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QAAQ,YAAY;IAAuB,GAAG;QAAC;QAAO;KAAmB;IAEnF,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,MAAM,aAAa,SAAS,UAAU,CAAC,MAAM,CAAC,CAAC,KAAK,MAAQ,MAAM,CAAC,OAAO,IAAI,KAAK,KAAK,CAAC,GAAG;QAC5F,IAAI,eAAe,SAAS,KAAK,EAAE;YAC/B,YAAY,CAAA,WAAY,CAAC;oBAAE,GAAG,QAAQ;oBAAE,OAAO;gBAAW,CAAC;QAC/D;IACJ,GAAG;QAAC,SAAS,UAAU;QAAE,SAAS,KAAK;KAAC;IAExC,MAAM,uBAAuB,CAAC,OAAe,OAAe;QACxD,MAAM,gBAAgB;eAAI,SAAS,UAAU;SAAC;QAC9C,aAAa,CAAC,MAAM,GAAG;YAAE,GAAG,aAAa,CAAC,MAAM;YAAE,CAAC,MAAM,EAAE,UAAU,UAAU,WAAW,UAAU,IAAI;QAAM;QAC9G,YAAY;YAAE,GAAG,QAAQ;YAAE,YAAY;QAAc;IACzD;IACA,MAAM,cAAc,IAAM,YAAY;YAAE,GAAG,QAAQ;YAAE,YAAY;mBAAI,SAAS,UAAU;gBAAE;oBAAE,MAAM;oBAAU,OAAO;gBAAE;aAAE;QAAC;IACxH,MAAM,iBAAiB,CAAC;QACpB,MAAM,gBAAgB;eAAI,SAAS,UAAU;SAAC;QAAE,cAAc,MAAM,CAAC,OAAO;QAC5E,YAAY;YAAE,GAAG,QAAQ;YAAE,YAAY;QAAc;IACzD;IACA,MAAM,eAAe,OAAO;QACxB,EAAE,cAAc;QAChB,MAAM,SAAS,OAAO,KAAK,QAAQ;QACnC,MAAM,MAAM,OAAO,KAAK,CAAC,QAAQ,EAAE,MAAM,EAAE,EAAE,GAAG;QAChD,IAAI;YAAE,MAAM,8HAAA,CAAA,UAAS,CAAC,OAAO,CAAC,KAAK;YAAW;QAAU,EACxD,OAAO,OAAgB;YACnB,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC9D,QAAQ,KAAK,CAAC,2BAA2B;QAC7C;IACJ;IAEA,qBACI,8OAAC,oLAAA,CAAA,OAAI;QAAC,UAAU;;0BACZ,8OAAC,oLAAA,CAAA,OAAI,CAAC,KAAK;gBAAC,WAAU;;kCAAO,8OAAC,oLAAA,CAAA,OAAI,CAAC,KAAK;kCAAC;;;;;;kCAAgB,8OAAC,oLAAA,CAAA,OAAI,CAAC,OAAO;wBAAC,MAAK;wBAAO,OAAO,SAAS,IAAI;wBAAE,UAAU,CAAA,IAAK,YAAY;gCAAE,GAAG,QAAQ;gCAAE,MAAM,EAAE,MAAM,CAAC,KAAK;4BAAC;wBAAI,QAAQ;;;;;;;;;;;;0BACpL,8OAAC,oLAAA,CAAA,OAAI,CAAC,KAAK;gBAAC,WAAU;;kCAAO,8OAAC,oLAAA,CAAA,OAAI,CAAC,KAAK;kCAAC;;;;;;kCAAmB,8OAAC,oLAAA,CAAA,OAAI,CAAC,OAAO;wBAAC,MAAK;wBAAS,OAAO,SAAS,KAAK;wBAAE,QAAQ;wBAAC,QAAQ;;;;;;;;;;;;0BAChI,8OAAC,oLAAA,CAAA,OAAI,CAAC,KAAK;gBAAC,WAAU;;kCAAO,8OAAC,oLAAA,CAAA,OAAI,CAAC,KAAK;kCAAC;;;;;;kCAA2B,8OAAC,oLAAA,CAAA,OAAI,CAAC,OAAO;wBAAC,MAAK;wBAAS,MAAK;wBAAO,OAAO,SAAS,eAAe,IAAI;wBAAI,UAAU,CAAA,IAAK,YAAY;gCAAE,GAAG,QAAQ;gCAAE,iBAAiB,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;4BAAK;;;;;;;;;;;;0BACjP,8OAAC,oLAAA,CAAA,OAAI,CAAC,KAAK;gBAAC,WAAU;;kCAAO,8OAAC,oLAAA,CAAA,OAAI,CAAC,KAAK;kCAAC;;;;;;kCAAkB,8OAAC,oLAAA,CAAA,OAAI,CAAC,OAAO;wBAAC,MAAK;wBAAO,OAAO,SAAS,KAAK,IAAI;wBAAI,UAAU,CAAA,IAAK,YAAY;gCAAE,GAAG,QAAQ;gCAAE,OAAO,EAAE,MAAM,CAAC,KAAK;4BAAC;;;;;;;;;;;;0BAClL,8OAAC;0BAAG;;;;;;YACH,SAAS,UAAU,CAAC,GAAG,CAAC,CAAC,UAAU,sBAChC,8OAAC,gMAAA,CAAA,aAAU;oBAAC,WAAU;;sCAClB,8OAAC,oLAAA,CAAA,OAAI,CAAC,MAAM;4BAAC,OAAO,SAAS,IAAI;4BAAE,UAAU,CAAA,IAAK,qBAAqB,OAAO,QAAQ,EAAE,MAAM,CAAC,KAAK;;8CAAG,8OAAC;8CAAO;;;;;;8CAAkB,8OAAC;8CAAO;;;;;;8CAAe,8OAAC;8CAAO;;;;;;8CAAsB,8OAAC;8CAAO;;;;;;8CAAwB,8OAAC;8CAAO;;;;;;8CAAmB,8OAAC;8CAAO;;;;;;;;;;;;sCACzP,8OAAC,kMAAA,CAAA,cAAW;4BAAC,MAAK;4BAAS,OAAO,SAAS,KAAK;4BAAE,UAAU,CAAA,IAAK,qBAAqB,OAAO,SAAS,EAAE,MAAM,CAAC,KAAK;;;;;;sCACpH,8OAAC,wLAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAiB,SAAS,IAAM,eAAe;sCAAQ;;;;;;;mBAHzC;;;;;0BAMtC,8OAAC,wLAAA,CAAA,SAAM;gBAAC,SAAQ;gBAAkB,SAAS;gBAAa,WAAU;0BAAO;;;;;;0BACzE,8OAAC,oLAAA,CAAA,OAAI,CAAC,KAAK;gBAAC,WAAU;;kCAAY,8OAAC,oLAAA,CAAA,OAAI,CAAC,KAAK;kCAAC;;;;;;kCAAgC,8OAAC,oLAAA,CAAA,OAAI,CAAC,OAAO;wBAAC,MAAK;wBAAO,OAAO,SAAS,WAAW;wBAAE,UAAU,CAAA,IAAK,YAAY;gCAAE,GAAG,QAAQ;gCAAE,aAAa,EAAE,MAAM,CAAC,KAAK;4BAAC;wBAAI,QAAQ;;;;;;;;;;;;0BACvN,8OAAC,wLAAA,CAAA,SAAM;gBAAC,SAAQ;gBAAU,MAAK;0BAAS;;;;;;0BAAoB,8OAAC,wLAAA,CAAA,SAAM;gBAAC,SAAQ;gBAAY,SAAS;gBAAU,WAAU;0BAAO;;;;;;;;;;;;AAGxI;AAEO,MAAM,mBAAoD,CAAC,EAAE,aAAa,EAAE,UAAU,EAAE;IAC7F,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW,iBAAiB,EAAE;IACjE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB,cAAc;IAChE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAyB;IAC1E,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsE;QAAE,KAAK;QAAS,WAAW;IAAa;IAEzJ,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,UAAU,iBAAiB,EAAE;QAC7B,SAAS,cAAc;IACzB,GAAG;QAAC;QAAe;KAAW;IAE9B,MAAM,oBAAoB;QACxB,WAAW;QAAO,SAAS;QAC3B,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAC,WACX,IAAI,CAAC,CAAA;YAAc,UAAU,SAAS,IAAI;QAAG,GAC7C,KAAK,CAAC,CAAA;YAAS,QAAQ,KAAK,CAAC,oCAAoC;YAAM,SAAS;QAA4C,GAC5H,OAAO,CAAC,IAAM,WAAW;IAC9B;IAEA,MAAM,cAAc,CAAC;QACnB,IAAI,YAAwC;QAC5C,IAAI,WAAW,GAAG,KAAK,OAAO,WAAW,SAAS,KAAK,aAAa;YAAE,YAAY;QAAc;QAChG,cAAc;YAAE;YAAK;QAAU;IACjC;IAEA,MAAM,eAAe,qMAAA,CAAA,UAAK,CAAC,OAAO,CAAC;QACjC,MAAM,gBAAgB;eAAI;SAAO;QACjC,IAAI,WAAW,GAAG,EAAE;YAClB,cAAc,IAAI,CAAC,CAAC,GAAG;gBACrB,MAAM,SAAS,CAAC,CAAC,WAAW,GAAG,CAAE;gBAAE,MAAM,SAAS,CAAC,CAAC,WAAW,GAAG,CAAE;gBACpE,IAAI,WAAW,QAAQ,WAAW,WAAW,OAAO;gBAAG,IAAI,WAAW,QAAQ,WAAW,WAAW,OAAO,CAAC;gBAC5G,IAAI,SAAS,QAAQ,OAAO,WAAW,SAAS,KAAK,cAAc,CAAC,IAAI;gBACxE,IAAI,SAAS,QAAQ,OAAO,WAAW,SAAS,KAAK,cAAc,IAAI,CAAC;gBACxE,OAAO;YACT;QACF;QACA,OAAO;IACT,GAAG;QAAC;QAAQ;KAAW;IAEvB,MAAM,aAAa;QAAQ,aAAa;QAAQ,iBAAiB;QAAO;IAAqB;IAC7F,MAAM,eAAe,OAAO;QAC1B,IAAI,OAAO,OAAO,CAAC,mDAAmD;YACpE,IAAI;gBAAE,MAAM,8HAAA,CAAA,UAAS,CAAC,MAAM,CAAC,CAAC,QAAQ,EAAE,IAAI;gBAAG;YAAqB,EACpE,OAAO,KAAK;gBAAE,QAAQ,KAAK,CAAC,4BAA4B;YAAM;QAChE;IACF;IAEA,IAAI,SAAS,OAAO,MAAM,KAAK,GAAG,qBAAQ,8OAAC;;0BAAI,8OAAC;gBAAE,WAAU;0BAAe;;;;;;0BAAU,8OAAC;gBAAO,WAAU;gBAAkB,SAAS;0BAAmB;;;;;;;;;;;;IACrJ,IAAI,SAAS,qBAAO,8OAAC;kBAAE;;;;;;IAEvB,qBACE,8OAAC;;0BACC,8OAAC;gBAAI,WAAU;;kCAAyD,8OAAC;kCAAG;;;;;;kCAA4B,8OAAC,wLAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAU,SAAS;4BAAQ,iBAAiB,CAAC;4BAAI,aAAa;wBAAO;kCAAG;;;;;;;;;;;;YAC/L,OAAO,MAAM,KAAK,KAAK,CAAC,uBAAS,8OAAC;0BAAE;;;;;;YACpC,OAAO,MAAM,GAAG,mBACf,8OAAC;gBAAM,WAAU;;kCACf,8OAAC;wBAAM,WAAU;kCAAa,cAAA,8OAAC;;8CAC3B,8OAAC;oCAAG,SAAS,IAAM,YAAY;oCAAS,OAAO;wCAAE,QAAQ;oCAAU;;wCAAG;wCAAK,WAAW,GAAG,KAAK,SAAU,WAAW,SAAS,KAAK,cAAc,MAAM,MAAO;;;;;;;8CAC5J,8OAAC;8CAAG;;;;;;8CAAe,8OAAC;oCAAG,WAAU;8CAAW;;;;;;8CAC5C,8OAAC;oCAAG,WAAU;oCAAW,SAAS,IAAM,YAAY;oCAAU,OAAO;wCAAE,QAAQ;oCAAU;;wCAAG;wCAAQ,WAAW,GAAG,KAAK,UAAW,WAAW,SAAS,KAAK,cAAc,MAAM,MAAO;;;;;;;8CACtL,8OAAC;oCAAG,WAAU;oCAAc,SAAS,IAAM,YAAY;oCAAgB,OAAO;wCAAE,QAAQ;oCAAU;;wCAAG;wCAAc,WAAW,GAAG,KAAK,gBAAiB,WAAW,SAAS,KAAK,cAAc,MAAM,MAAO;;;;;;;8CAC3M,8OAAC;oCAAG,WAAU;8CAAc;;;;;;;;;;;;;;;;;kCAChC,8OAAC;kCAAO,aAAa,GAAG,CAAC,CAAA,sBAAU,8OAAC;;kDAAkB,8OAAC;kDAAI,MAAM,IAAI;;;;;;kDAAM,8OAAC;kDAAI,MAAM,UAAU,CAAC,GAAG,CAAC,CAAA,IAAK,GAAG,EAAE,aAAa,CAAC,EAAE,EAAE,IAAI,KAAK,YAAY,CAAC,SAAS;gDAAE,OAAO;gDAAY,UAAU;4CAAM,GAAG,MAAM,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC;;;;;;kDAAW,8OAAC;wCAAG,WAAU;kDAAY,MAAM,eAAe,GAAG,IAAI,KAAK,YAAY,CAAC,SAAS;4CAAE,OAAO;4CAAY,UAAU;wCAAM,GAAG,MAAM,CAAC,MAAM,eAAe,IAAI;;;;;;kDAAW,8OAAC;wCAAG,WAAU;kDAAY,IAAI,KAAK,YAAY,CAAC,SAAS;4CAAE,OAAO;4CAAY,UAAU;wCAAM,GAAG,MAAM,CAAC,MAAM,KAAK;;;;;;kDAAO,8OAAC;wCAAG,WAAU;kDAAe,IAAI,KAAK,MAAM,WAAW,EAAE,kBAAkB,CAAC;;;;;;kDAAc,8OAAC;wCAAG,WAAU;;0DAAc,8OAAC,wLAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAkB,MAAK;gDAAK,SAAS;oDAAQ,iBAAiB;oDAAQ,aAAa;gDAAO;0DAAG;;;;;;4CAAkB;0DAAI,8OAAC,wLAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAiB,MAAK;gDAAK,SAAS,IAAM,aAAa,MAAM,EAAE;0DAAG;;;;;;;;;;;;;+BAAzyB,MAAM,EAAE;;;;;;;;;;;;;;;;0BAGxD,8OAAC,sLAAA,CAAA,QAAK;gBAAC,MAAM;gBAAW,QAAQ,IAAM,aAAa;;kCAAQ,8OAAC,sLAAA,CAAA,QAAK,CAAC,MAAM;wBAAC,WAAW;kCAAC,cAAA,8OAAC,sLAAA,CAAA,QAAK,CAAC,KAAK;;gCAAE,eAAe,KAAK,aAAa;gCAAU;;;;;;;;;;;;kCAAsC,8OAAC,sLAAA,CAAA,QAAK,CAAC,IAAI;kCAAC,cAAA,8OAAC;4BAAU,OAAO;4BAAe,QAAQ;4BAAY,UAAU,IAAM,aAAa;;;;;;;;;;;;;;;;;;;;;;;AAGxR", "debugId": null}}]}