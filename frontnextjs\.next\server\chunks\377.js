exports.id=377,exports.ids=[377],exports.modules={11227:(e,s,n)=>{"use strict";n.d(s,{default:()=>a});let a=(0,n(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\AI-Code\\\\fire_UI\\\\frontnextjs\\\\src\\\\components\\\\BootstrapClient.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\AI-Code\\fire_UI\\frontnextjs\\src\\components\\BootstrapClient.tsx","default")},24255:(e,s,n)=>{Promise.resolve().then(n.t.bind(n,86346,23)),Promise.resolve().then(n.t.bind(n,27924,23)),Promise.resolve().then(n.t.bind(n,35656,23)),Promise.resolve().then(n.t.bind(n,40099,23)),Promise.resolve().then(n.t.bind(n,38243,23)),Promise.resolve().then(n.t.bind(n,28827,23)),Promise.resolve().then(n.t.bind(n,62763,23)),Promise.resolve().then(n.t.bind(n,97173,23))},29190:(e,s,n)=>{"use strict";n.d(s,{default:()=>t});var a=n(60687),i=n(43210),l=n(85814),r=n.n(l);let t=()=>{let[e,s]=(0,i.useState)(!1),n=()=>{s(!1)};return(0,a.jsx)("nav",{className:"navbar navbar-expand-lg navbar-dark bg-dark",children:(0,a.jsxs)("div",{className:"container-fluid d-flex",children:[(0,a.jsx)(r(),{className:"navbar-brand me-3",href:"/",onClick:n,children:"FIRE Dashboard"}),(0,a.jsxs)("ul",{className:"navbar-nav d-none d-lg-flex flex-row",children:[(0,a.jsx)("li",{className:"nav-item",children:(0,a.jsx)(r(),{className:"nav-link",href:"/",onClick:n,children:"Dashboard"})}),(0,a.jsx)("li",{className:"nav-item",children:(0,a.jsx)(r(),{className:"nav-link",href:"/assets",onClick:n,children:"Patrimoine"})}),(0,a.jsx)("li",{className:"nav-item",children:(0,a.jsx)(r(),{className:"nav-link",href:"/liabilities",onClick:n,children:"Emprunts"})}),(0,a.jsx)("li",{className:"nav-item",children:(0,a.jsx)(r(),{className:"nav-link",href:"/scpi",onClick:n,children:"SCPI"})}),(0,a.jsx)("li",{className:"nav-item",children:(0,a.jsx)(r(),{className:"nav-link",href:"/evolution",onClick:n,children:"\xc9volution"})}),(0,a.jsx)("li",{className:"nav-item",children:(0,a.jsx)(r(),{className:"nav-link",href:"/budget",onClick:n,children:"Budget FIRE"})}),(0,a.jsx)("li",{className:"nav-item",children:(0,a.jsx)(r(),{className:"nav-link",href:"/scenarios",onClick:n,children:"Sc\xe9narios"})}),(0,a.jsx)("li",{className:"nav-item",children:(0,a.jsx)(r(),{className:"nav-link",href:"/fire",onClick:n,children:"Objectif FIRE"})}),(0,a.jsx)("li",{className:"nav-item",children:(0,a.jsx)(r(),{className:"nav-link",href:"/test",onClick:n,children:"Test API"})})]}),(0,a.jsx)("button",{className:"navbar-toggler d-lg-none",type:"button",onClick:()=>{s(!e)},"aria-controls":"navbarNavContent","aria-expanded":e,"aria-label":"Toggle navigation",children:(0,a.jsx)("span",{className:"navbar-toggler-icon"})}),e&&(0,a.jsx)("div",{className:"navbar-collapse show d-lg-none position-absolute top-100 start-0 w-100 bg-dark",children:(0,a.jsxs)("ul",{className:"navbar-nav",children:[(0,a.jsx)("li",{className:"nav-item",children:(0,a.jsx)(r(),{className:"nav-link",href:"/",onClick:n,children:"Dashboard"})}),(0,a.jsx)("li",{className:"nav-item",children:(0,a.jsx)(r(),{className:"nav-link",href:"/assets",onClick:n,children:"Patrimoine"})}),(0,a.jsx)("li",{className:"nav-item",children:(0,a.jsx)(r(),{className:"nav-link",href:"/liabilities",onClick:n,children:"Emprunts"})}),(0,a.jsx)("li",{className:"nav-item",children:(0,a.jsx)(r(),{className:"nav-link",href:"/scpi",onClick:n,children:"SCPI"})}),(0,a.jsx)("li",{className:"nav-item",children:(0,a.jsx)(r(),{className:"nav-link",href:"/evolution",onClick:n,children:"\xc9volution"})}),(0,a.jsx)("li",{className:"nav-item",children:(0,a.jsx)(r(),{className:"nav-link",href:"/budget",onClick:n,children:"Budget FIRE"})}),(0,a.jsx)("li",{className:"nav-item",children:(0,a.jsx)(r(),{className:"nav-link",href:"/scenarios",onClick:n,children:"Sc\xe9narios"})}),(0,a.jsx)("li",{className:"nav-item",children:(0,a.jsx)(r(),{className:"nav-link",href:"/fire",onClick:n,children:"Objectif FIRE"})}),(0,a.jsx)("li",{className:"nav-item",children:(0,a.jsx)(r(),{className:"nav-link",href:"/test",onClick:n,children:"Test API"})})]})})]})})}},30004:(e,s,n)=>{"use strict";n.d(s,{default:()=>a});let a=(0,n(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\AI-Code\\\\fire_UI\\\\frontnextjs\\\\src\\\\components\\\\Navbar.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\AI-Code\\fire_UI\\frontnextjs\\src\\components\\Navbar.tsx","default")},39522:(e,s,n)=>{"use strict";n.d(s,{A:()=>m});var a=n(69662),i=n.n(a),l=n(43210),r=n(98466),t=n(22057),o=n(60687);function c({min:e,now:s,max:n,label:a,visuallyHidden:l,striped:r,animated:t,className:c,style:d,variant:m,bsPrefix:h,...v},f){return(0,o.jsx)("div",{ref:f,...v,role:"progressbar",className:i()(c,`${h}-bar`,{[`bg-${m}`]:m,[`${h}-bar-animated`]:t,[`${h}-bar-striped`]:t||r}),style:{width:`${Math.round((s-e)/(n-e)*1e5)/1e3}%`,...d},"aria-valuenow":s,"aria-valuemin":e,"aria-valuemax":n,children:l?(0,o.jsx)("span",{className:"visually-hidden",children:a}):a})}let d=l.forwardRef(({isChild:e=!1,...s},n)=>{let a={min:0,max:100,animated:!1,visuallyHidden:!1,striped:!1,...s};if(a.bsPrefix=(0,r.oU)(a.bsPrefix,"progress"),e)return c(a,n);let{min:d,now:m,max:h,label:v,visuallyHidden:f,striped:x,animated:p,bsPrefix:u,variant:b,className:j,children:N,...k}=a;return(0,o.jsx)("div",{ref:n,...k,className:i()(j,u),children:N?(0,t.Tj)(N,e=>(0,l.cloneElement)(e,{isChild:!0})):c({min:d,now:m,max:h,label:v,visuallyHidden:f,striped:x,animated:p,bsPrefix:u,variant:b},n)})});d.displayName="ProgressBar";let m=d},51832:(e,s,n)=>{Promise.resolve().then(n.bind(n,11227)),Promise.resolve().then(n.bind(n,30004))},52504:(e,s,n)=>{Promise.resolve().then(n.bind(n,85461)),Promise.resolve().then(n.bind(n,29190))},61135:()=>{},70440:(e,s,n)=>{"use strict";n.r(s),n.d(s,{default:()=>i});var a=n(31658);let i=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,a.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},78162:(e,s,n)=>{"use strict";n.r(s),n.d(s,{default:()=>i});var a=n(31658);let i=async e=>[{type:"image/png",sizes:"192x192",url:(0,a.fillMetadataSegment)(".",await e.params,"icon.png")+"?d84b06edc1baa420"}]},85461:(e,s,n)=>{"use strict";function a(){return null}n.d(s,{default:()=>a}),n(43210)},90317:(e,s,n)=>{"use strict";n.d(s,{A:()=>i});let a=n(51060).A.create({baseURL:"http://localhost:8000/api",headers:{"Content-Type":"application/json"},timeout:1e4});a.interceptors.response.use(e=>e,e=>(console.error("API Error:",e.response?.status,e.config?.url,e.message),Promise.reject(e)));let i=a},93991:(e,s,n)=>{Promise.resolve().then(n.t.bind(n,16444,23)),Promise.resolve().then(n.t.bind(n,16042,23)),Promise.resolve().then(n.t.bind(n,88170,23)),Promise.resolve().then(n.t.bind(n,49477,23)),Promise.resolve().then(n.t.bind(n,29345,23)),Promise.resolve().then(n.t.bind(n,12089,23)),Promise.resolve().then(n.t.bind(n,46577,23)),Promise.resolve().then(n.t.bind(n,31307,23))},94431:(e,s,n)=>{"use strict";n.r(s),n.d(s,{default:()=>o,metadata:()=>r,viewport:()=>t});var a=n(37413);n(61135);var i=n(30004),l=n(11227);let r={title:"FIRE Dashboard",description:"Application pour le suivi de l'objectif FIRE (Financial Independence, Retire Early)",manifest:"/manifest.json",icons:{icon:"/icon.png",apple:"/apple-icon.png"}},t={themeColor:"#000000"};function o({children:e}){return(0,a.jsx)("html",{lang:"en",children:(0,a.jsxs)("body",{children:[(0,a.jsxs)("div",{className:"App",children:[(0,a.jsx)(i.default,{}),(0,a.jsx)("main",{className:"container mt-4",children:e})]}),(0,a.jsx)(l.default,{})]})})}},94650:(e,s,n)=>{"use strict";n.r(s),n.d(s,{default:()=>i});var a=n(31658);let i=async e=>[{type:"image/png",sizes:"192x192",url:(0,a.fillMetadataSegment)(".",await e.params,"apple-icon.png")+"?d84b06edc1baa420"}]}};