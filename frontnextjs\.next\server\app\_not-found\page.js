(()=>{var e={};e.id=492,e.ids=[492],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},24255:(e,n,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},29190:(e,n,r)=>{"use strict";r.d(n,{default:()=>o});var s=r(60687),t=r(43210),i=r(85814),a=r.n(i);let o=()=>{let[e,n]=(0,t.useState)(!1),r=()=>{n(!1)};return(0,s.jsx)("nav",{className:"navbar navbar-expand-lg navbar-dark bg-dark",children:(0,s.jsxs)("div",{className:"container-fluid",children:[(0,s.jsx)(a(),{className:"navbar-brand",href:"/",onClick:r,children:"FIRE Dashboard"}),(0,s.jsx)("button",{className:"navbar-toggler",type:"button",onClick:()=>{n(!e)},"aria-controls":"navbarNavContent","aria-expanded":e,"aria-label":"Toggle navigation",children:(0,s.jsx)("span",{className:"navbar-toggler-icon"})}),(0,s.jsx)("div",{className:`collapse navbar-collapse ${e?"show":""}`,id:"navbarNavContent",children:(0,s.jsxs)("ul",{className:"navbar-nav me-auto mb-2 mb-lg-0",children:[(0,s.jsx)("li",{className:"nav-item",children:(0,s.jsx)(a(),{className:"nav-link",href:"/",onClick:r,children:"Dashboard"})}),(0,s.jsx)("li",{className:"nav-item",children:(0,s.jsx)(a(),{className:"nav-link",href:"/assets",onClick:r,children:"Patrimoine"})}),(0,s.jsx)("li",{className:"nav-item",children:(0,s.jsx)(a(),{className:"nav-link",href:"/liabilities",onClick:r,children:"Emprunts"})}),(0,s.jsx)("li",{className:"nav-item",children:(0,s.jsx)(a(),{className:"nav-link",href:"/scpi",onClick:r,children:"SCPI"})}),(0,s.jsx)("li",{className:"nav-item",children:(0,s.jsx)(a(),{className:"nav-link",href:"/evolution",onClick:r,children:"\xc9volution"})}),(0,s.jsx)("li",{className:"nav-item",children:(0,s.jsx)(a(),{className:"nav-link",href:"/budget",onClick:r,children:"Budget FIRE"})}),(0,s.jsx)("li",{className:"nav-item",children:(0,s.jsx)(a(),{className:"nav-link",href:"/scenarios",onClick:r,children:"Sc\xe9narios"})}),(0,s.jsx)("li",{className:"nav-item",children:(0,s.jsx)(a(),{className:"nav-link",href:"/fire",onClick:r,children:"Objectif FIRE"})}),(0,s.jsx)("li",{className:"nav-item",children:(0,s.jsx)(a(),{className:"nav-link",href:"/test",onClick:r,children:"Test API"})})]})})]})})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30004:(e,n,r)=>{"use strict";r.d(n,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\AI-Code\\\\fire_UI\\\\frontnextjs\\\\src\\\\components\\\\Navbar.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\AI-Code\\fire_UI\\frontnextjs\\src\\components\\Navbar.tsx","default")},33873:e=>{"use strict";e.exports=require("path")},39253:(e,n,r)=>{"use strict";r.r(n),r.d(n,{GlobalError:()=>a.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>h,tree:()=>d});var s=r(65239),t=r(48088),i=r(88170),a=r.n(i),o=r(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(n,l);let d={children:["",{children:["/_not-found",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"D:\\AI-Code\\fire_UI\\frontnextjs\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=[],m={require:r,loadChunk:()=>Promise.resolve()},h=new s.AppPageRouteModule({definition:{kind:t.RouteKind.APP_PAGE,page:"/_not-found/page",pathname:"/_not-found",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},61135:()=>{},62907:(e,n,r)=>{Promise.resolve().then(r.bind(r,30004))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73523:(e,n,r)=>{Promise.resolve().then(r.bind(r,29190))},93991:(e,n,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},94431:(e,n,r)=>{"use strict";r.r(n),r.d(n,{default:()=>o,metadata:()=>i,viewport:()=>a});var s=r(37413);r(61135),r(27209);var t=r(30004);let i={title:"FIRE Dashboard",description:"Application pour le suivi de l'objectif FIRE (Financial Independence, Retire Early)",manifest:"/manifest.json",icons:{icon:"/icon.png",apple:"/apple-icon.png"}},a={themeColor:"#000000"};function o({children:e}){return(0,s.jsx)("html",{lang:"en",children:(0,s.jsx)("body",{children:(0,s.jsxs)("div",{className:"App",children:[(0,s.jsx)(t.default,{}),(0,s.jsx)("main",{className:"container mt-4",children:e})]})})})}}};var n=require("../../webpack-runtime.js");n.C(e);var r=e=>n(n.s=e),s=n.X(0,[447,989],()=>r(39253));module.exports=s})();