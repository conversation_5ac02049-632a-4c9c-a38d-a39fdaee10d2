(()=>{var e={};e.id=492,e.ids=[492],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11227:(e,n,s)=>{"use strict";s.d(n,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\AI-Code\\\\fire_UI\\\\frontnextjs\\\\src\\\\components\\\\BootstrapClient.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\AI-Code\\fire_UI\\frontnextjs\\src\\components\\BootstrapClient.tsx","default")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},24255:(e,n,s)=>{Promise.resolve().then(s.t.bind(s,86346,23)),Promise.resolve().then(s.t.bind(s,27924,23)),Promise.resolve().then(s.t.bind(s,35656,23)),Promise.resolve().then(s.t.bind(s,40099,23)),Promise.resolve().then(s.t.bind(s,38243,23)),Promise.resolve().then(s.t.bind(s,28827,23)),Promise.resolve().then(s.t.bind(s,62763,23)),Promise.resolve().then(s.t.bind(s,97173,23))},29190:(e,n,s)=>{"use strict";s.d(n,{default:()=>l});var r=s(60687),i=s(43210),t=s(85814),a=s.n(t);let l=()=>{let[e,n]=(0,i.useState)(!1),s=()=>{n(!1)};return(0,r.jsx)("nav",{className:"navbar navbar-expand-lg navbar-dark bg-dark",children:(0,r.jsxs)("div",{className:"container-fluid d-flex",children:[(0,r.jsx)(a(),{className:"navbar-brand me-3",href:"/",onClick:s,children:"FIRE Dashboard"}),(0,r.jsxs)("ul",{className:"navbar-nav d-none d-lg-flex flex-row",children:[(0,r.jsx)("li",{className:"nav-item",children:(0,r.jsx)(a(),{className:"nav-link",href:"/",onClick:s,children:"Dashboard"})}),(0,r.jsx)("li",{className:"nav-item",children:(0,r.jsx)(a(),{className:"nav-link",href:"/assets",onClick:s,children:"Patrimoine"})}),(0,r.jsx)("li",{className:"nav-item",children:(0,r.jsx)(a(),{className:"nav-link",href:"/liabilities",onClick:s,children:"Emprunts"})}),(0,r.jsx)("li",{className:"nav-item",children:(0,r.jsx)(a(),{className:"nav-link",href:"/scpi",onClick:s,children:"SCPI"})}),(0,r.jsx)("li",{className:"nav-item",children:(0,r.jsx)(a(),{className:"nav-link",href:"/evolution",onClick:s,children:"\xc9volution"})}),(0,r.jsx)("li",{className:"nav-item",children:(0,r.jsx)(a(),{className:"nav-link",href:"/budget",onClick:s,children:"Budget FIRE"})}),(0,r.jsx)("li",{className:"nav-item",children:(0,r.jsx)(a(),{className:"nav-link",href:"/scenarios",onClick:s,children:"Sc\xe9narios"})}),(0,r.jsx)("li",{className:"nav-item",children:(0,r.jsx)(a(),{className:"nav-link",href:"/fire",onClick:s,children:"Objectif FIRE"})}),(0,r.jsx)("li",{className:"nav-item",children:(0,r.jsx)(a(),{className:"nav-link",href:"/test",onClick:s,children:"Test API"})})]}),(0,r.jsx)("button",{className:"navbar-toggler d-lg-none",type:"button",onClick:()=>{n(!e)},"aria-controls":"navbarNavContent","aria-expanded":e,"aria-label":"Toggle navigation",children:(0,r.jsx)("span",{className:"navbar-toggler-icon"})}),e&&(0,r.jsx)("div",{className:"navbar-collapse show d-lg-none position-absolute top-100 start-0 w-100 bg-dark",children:(0,r.jsxs)("ul",{className:"navbar-nav",children:[(0,r.jsx)("li",{className:"nav-item",children:(0,r.jsx)(a(),{className:"nav-link",href:"/",onClick:s,children:"Dashboard"})}),(0,r.jsx)("li",{className:"nav-item",children:(0,r.jsx)(a(),{className:"nav-link",href:"/assets",onClick:s,children:"Patrimoine"})}),(0,r.jsx)("li",{className:"nav-item",children:(0,r.jsx)(a(),{className:"nav-link",href:"/liabilities",onClick:s,children:"Emprunts"})}),(0,r.jsx)("li",{className:"nav-item",children:(0,r.jsx)(a(),{className:"nav-link",href:"/scpi",onClick:s,children:"SCPI"})}),(0,r.jsx)("li",{className:"nav-item",children:(0,r.jsx)(a(),{className:"nav-link",href:"/evolution",onClick:s,children:"\xc9volution"})}),(0,r.jsx)("li",{className:"nav-item",children:(0,r.jsx)(a(),{className:"nav-link",href:"/budget",onClick:s,children:"Budget FIRE"})}),(0,r.jsx)("li",{className:"nav-item",children:(0,r.jsx)(a(),{className:"nav-link",href:"/scenarios",onClick:s,children:"Sc\xe9narios"})}),(0,r.jsx)("li",{className:"nav-item",children:(0,r.jsx)(a(),{className:"nav-link",href:"/fire",onClick:s,children:"Objectif FIRE"})}),(0,r.jsx)("li",{className:"nav-item",children:(0,r.jsx)(a(),{className:"nav-link",href:"/test",onClick:s,children:"Test API"})})]})})]})})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30004:(e,n,s)=>{"use strict";s.d(n,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\AI-Code\\\\fire_UI\\\\frontnextjs\\\\src\\\\components\\\\Navbar.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\AI-Code\\fire_UI\\frontnextjs\\src\\components\\Navbar.tsx","default")},33873:e=>{"use strict";e.exports=require("path")},39253:(e,n,s)=>{"use strict";s.r(n),s.d(n,{GlobalError:()=>a.a,__next_app__:()=>m,pages:()=>d,routeModule:()=>h,tree:()=>c});var r=s(65239),i=s(48088),t=s(88170),a=s.n(t),l=s(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(n,o);let c={children:["",{children:["/_not-found",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"D:\\AI-Code\\fire_UI\\frontnextjs\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=[],m={require:s,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/_not-found/page",pathname:"/_not-found",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},51832:(e,n,s)=>{Promise.resolve().then(s.bind(s,11227)),Promise.resolve().then(s.bind(s,30004))},52504:(e,n,s)=>{Promise.resolve().then(s.bind(s,85461)),Promise.resolve().then(s.bind(s,29190))},61135:()=>{},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},85461:(e,n,s)=>{"use strict";function r(){return null}s.d(n,{default:()=>r}),s(43210)},93991:(e,n,s)=>{Promise.resolve().then(s.t.bind(s,16444,23)),Promise.resolve().then(s.t.bind(s,16042,23)),Promise.resolve().then(s.t.bind(s,88170,23)),Promise.resolve().then(s.t.bind(s,49477,23)),Promise.resolve().then(s.t.bind(s,29345,23)),Promise.resolve().then(s.t.bind(s,12089,23)),Promise.resolve().then(s.t.bind(s,46577,23)),Promise.resolve().then(s.t.bind(s,31307,23))},94431:(e,n,s)=>{"use strict";s.r(n),s.d(n,{default:()=>o,metadata:()=>a,viewport:()=>l});var r=s(37413);s(61135);var i=s(30004),t=s(11227);let a={title:"FIRE Dashboard",description:"Application pour le suivi de l'objectif FIRE (Financial Independence, Retire Early)",manifest:"/manifest.json",icons:{icon:"/icon.png",apple:"/apple-icon.png"}},l={themeColor:"#000000"};function o({children:e}){return(0,r.jsx)("html",{lang:"en",children:(0,r.jsxs)("body",{children:[(0,r.jsxs)("div",{className:"App",children:[(0,r.jsx)(i.default,{}),(0,r.jsx)("main",{className:"container mt-4",children:e})]}),(0,r.jsx)(t.default,{})]})})}}};var n=require("../../webpack-runtime.js");n.C(e);var s=e=>n(n.s=e),r=n.X(0,[447,412],()=>s(39253));module.exports=r})();