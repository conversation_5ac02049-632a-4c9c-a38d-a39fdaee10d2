from fastapi import FastAPI, Depends, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from sqlalchemy.orm import Session
from sqlalchemy import func
from typing import List

import crud, models, schemas
from database import SessionLocal, engine, DATABASE_URL

print(f"Database URL: {DATABASE_URL}")



app = FastAPI()

origins = [
    "http://localhost:3000",
    "http://127.0.0.1:3000",
    "http://localhost",
    "http://127.0.0.1",
]

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Dependency
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

@app.get("/api/assets/")
def read_assets(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    try:
        assets = db.query(models.Asset).offset(skip).limit(limit).all()
        result = []
        for asset in assets:
            asset_data = {
                "id": asset.id,
                "name": asset.name,
                "value": asset.value,
                "annual_interest": asset.annual_interest,
                "notes": asset.notes,
                "update_date": asset.update_date.isoformat(),
                "categories": [],
                "liabilities": []
            }
            # Add categories
            for asset_category in asset.categories:
                asset_data["categories"].append({
                    "category_name": asset_category.category.name,
                    "value": asset_category.value
                })
            result.append(asset_data)
        return result
    except Exception as e:
        print(f"Error in read_assets: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/assets/")
def create_asset(asset: schemas.AssetCreate, db: Session = Depends(get_db)):
    try:
        # Create the asset
        db_asset = models.Asset(
            name=asset.name,
            value=asset.value,
            annual_interest=asset.annual_interest,
            notes=asset.notes,
            update_date=asset.update_date
        )
        db.add(db_asset)
        db.flush()  # Get the asset ID

        # Add categories
        for category_value in asset.categories:
            category = db.query(models.Category).filter(models.Category.name == category_value.name).first()
            if not category:
                category = models.Category(name=category_value.name)
                db.add(category)
                db.flush()

            asset_category = models.AssetCategory(
                asset_id=db_asset.id,
                category_id=category.id,
                value=category_value.value
            )
            db.add(asset_category)

        db.commit()
        db.refresh(db_asset)

        # Return the same format as GET
        result = {
            "id": db_asset.id,
            "name": db_asset.name,
            "value": db_asset.value,
            "annual_interest": db_asset.annual_interest,
            "notes": db_asset.notes,
            "update_date": db_asset.update_date.isoformat(),
            "categories": [],
            "liabilities": []
        }
        for asset_category in db_asset.categories:
            result["categories"].append({
                "category_name": asset_category.category.name,
                "value": asset_category.value
            })
        return result
    except Exception as e:
        db.rollback()
        print(f"Error in create_asset: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.put("/api/assets/{asset_id}")
def update_asset(asset_id: int, asset: schemas.AssetCreate, db: Session = Depends(get_db)):
    try:
        # Find the existing asset
        db_asset = db.query(models.Asset).filter(models.Asset.id == asset_id).first()
        if db_asset is None:
            raise HTTPException(status_code=404, detail="Asset not found")

        # Update asset fields
        db_asset.name = asset.name
        db_asset.value = asset.value
        db_asset.annual_interest = asset.annual_interest
        db_asset.notes = asset.notes
        db_asset.update_date = asset.update_date

        # Delete existing categories
        db.query(models.AssetCategory).filter(models.AssetCategory.asset_id == asset_id).delete()

        # Add new categories
        for category_value in asset.categories:
            category = db.query(models.Category).filter(models.Category.name == category_value.name).first()
            if not category:
                category = models.Category(name=category_value.name)
                db.add(category)
                db.flush()

            asset_category = models.AssetCategory(
                asset_id=db_asset.id,
                category_id=category.id,
                value=category_value.value
            )
            db.add(asset_category)

        db.commit()
        db.refresh(db_asset)

        # Return the same format as GET
        result = {
            "id": db_asset.id,
            "name": db_asset.name,
            "value": db_asset.value,
            "annual_interest": db_asset.annual_interest,
            "notes": db_asset.notes,
            "update_date": db_asset.update_date.isoformat(),
            "categories": [],
            "liabilities": []
        }
        for asset_category in db_asset.categories:
            result["categories"].append({
                "category_name": asset_category.category.name,
                "value": asset_category.value
            })
        return result
    except Exception as e:
        db.rollback()
        print(f"Error in update_asset: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.delete("/api/assets/{asset_id}")
def delete_asset(asset_id: int, db: Session = Depends(get_db)):
    try:
        # Find the existing asset
        db_asset = db.query(models.Asset).filter(models.Asset.id == asset_id).first()
        if db_asset is None:
            raise HTTPException(status_code=404, detail="Asset not found")

        # Store asset data before deletion
        result = {
            "id": db_asset.id,
            "name": db_asset.name,
            "value": db_asset.value,
            "annual_interest": db_asset.annual_interest,
            "notes": db_asset.notes,
            "update_date": db_asset.update_date.isoformat(),
            "categories": [],
            "liabilities": []
        }
        for asset_category in db_asset.categories:
            result["categories"].append({
                "category_name": asset_category.category.name,
                "value": asset_category.value
            })

        # Delete categories first
        db.query(models.AssetCategory).filter(models.AssetCategory.asset_id == asset_id).delete()

        # Delete the asset
        db.delete(db_asset)
        db.commit()

        return result
    except Exception as e:
        db.rollback()
        print(f"Error in delete_asset: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/liabilities/", response_model=List[schemas.Liability])
def read_liabilities(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    liabilities = crud.get_liabilities(db, skip=skip, limit=limit)
    return liabilities

@app.post("/api/liabilities/", response_model=schemas.Liability)
def create_liability(liability: schemas.LiabilityCreate, db: Session = Depends(get_db)):
    return crud.create_liability(db=db, liability=liability)

@app.put("/api/liabilities/{liability_id}", response_model=schemas.Liability)
def update_liability(liability_id: int, liability: schemas.LiabilityCreate, db: Session = Depends(get_db)):
    db_liability = crud.update_liability(db, liability_id=liability_id, liability=liability)
    if db_liability is None:
        raise HTTPException(status_code=404, detail="Liability not found")
    return db_liability

@app.delete("/api/liabilities/{liability_id}", response_model=schemas.Liability)
def delete_liability(liability_id: int, db: Session = Depends(get_db)):
    db_liability = crud.delete_liability(db, liability_id=liability_id)
    if db_liability is None:
        raise HTTPException(status_code=404, detail="Liability not found")
    return db_liability

@app.get("/api/patrimoine-history/", response_model=List[schemas.PatrimoineHistory])
def read_patrimoine_history(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    history = crud.get_patrimoine_history(db, skip=skip, limit=limit)
    return history

@app.get("/api/scpi/", response_model=List[schemas.SCPI])
def read_scpis(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    scpis = crud.get_scpis(db, skip=skip, limit=limit)
    return scpis

@app.post("/api/scpi/", response_model=schemas.SCPI)
def create_scpi(scpi: schemas.SCPICreate, db: Session = Depends(get_db)):
    return crud.create_scpi(db=db, scpi=scpi)

@app.put("/api/scpi/{scpi_id}", response_model=schemas.SCPI)
def update_scpi(scpi_id: int, scpi: schemas.SCPICreate, db: Session = Depends(get_db)):
    db_scpi = crud.update_scpi(db, scpi_id=scpi_id, scpi=scpi)
    if db_scpi is None:
        raise HTTPException(status_code=404, detail="SCPI not found")
    return db_scpi

@app.delete("/api/scpi/{scpi_id}", response_model=schemas.SCPI)
def delete_scpi(scpi_id: int, db: Session = Depends(get_db)):
    db_scpi = crud.delete_scpi(db, scpi_id=scpi_id)
    if db_scpi is None:
        raise HTTPException(status_code=404, detail="SCPI not found")
    return db_scpi

@app.get("/api/dashboard")
def get_dashboard_data(db: Session = Depends(get_db)):
    # Calculate total assets (including SCPI)
    assets_total = db.query(func.sum(models.Asset.value)).scalar() or 0
    scpi_total = db.query(func.sum(models.SCPI.total_value)).scalar() or 0
    total_assets = assets_total + scpi_total

    total_liabilities = db.query(func.sum(models.Liability.remaining_capital)).scalar() or 0
    net_patrimoine = total_assets - total_liabilities

    # Get allocation from assets
    allocation_data = db.query(
        models.Category.name,
        func.sum(models.AssetCategory.value).label('total_value')
    ).select_from(models.Category).join(models.AssetCategory).group_by(models.Category.name).all()

    allocation = {category: value for category, value in allocation_data}

    # Add SCPI to Immobilier category
    if scpi_total > 0:
        if "Immobilier" in allocation:
            allocation["Immobilier"] += scpi_total
        else:
            allocation["Immobilier"] = scpi_total

    return {
        "net_patrimoine": net_patrimoine,
        "total_assets": total_assets,
        "total_liabilities": total_liabilities,
        "allocation": allocation
    }

@app.get("/api/fire-target")
def get_fire_target(db: Session = Depends(get_db)):
    # Get FIRE settings from database, or use defaults
    fire_settings = crud.get_fire_settings(db)
    if fire_settings:
        fire_target_amount = fire_settings.fire_target_amount
        swr = fire_settings.secure_withdrawal_rate
    else:
        # Default values if no settings exist
        fire_target_amount = 910150
        swr = 0.04

    # Calculate total assets (including SCPI) - same logic as dashboard
    assets_total = db.query(func.sum(models.Asset.value)).scalar() or 0
    scpi_total = db.query(func.sum(models.SCPI.total_value)).scalar() or 0
    total_assets = assets_total + scpi_total

    total_liabilities = db.query(func.sum(models.Liability.remaining_capital)).scalar() or 0
    net_patrimoine = total_assets - total_liabilities

    remaining_to_invest = fire_target_amount - net_patrimoine
    potential_passive_income = net_patrimoine * swr
    progress_percentage = (net_patrimoine / fire_target_amount) * 100 if fire_target_amount > 0 else 0

    return {
        "fire_target_amount": fire_target_amount,
        "secure_withdrawal_rate": swr,
        "current_net_patrimoine": net_patrimoine,
        "total_assets": total_assets,
        "total_liabilities": total_liabilities,
        "remaining_to_invest": remaining_to_invest,
        "potential_passive_income": potential_passive_income,
        "progress_percentage": progress_percentage
    }

@app.get("/api/fire-settings", response_model=schemas.FireSettings)
def get_fire_settings(db: Session = Depends(get_db)):
    settings = crud.get_fire_settings(db)
    if settings is None:
        raise HTTPException(status_code=404, detail="Fire settings not found")
    return settings

@app.post("/api/fire-settings", response_model=schemas.FireSettings)
def create_fire_settings(settings: schemas.FireSettingsCreate, db: Session = Depends(get_db)):
    existing_settings = crud.get_fire_settings(db)
    if existing_settings:
        raise HTTPException(status_code=400, detail="Fire settings already exist. Use PUT to update.")
    return crud.create_fire_settings(db=db, settings=settings)

@app.put("/api/fire-settings", response_model=schemas.FireSettings)
def update_fire_settings(settings: schemas.FireSettingsUpdate, db: Session = Depends(get_db)):
    db_settings = crud.update_fire_settings(db, settings=settings)
    if db_settings is None:
        # Create default settings if none exist
        from datetime import date
        default_settings = schemas.FireSettingsCreate(
            fire_target_amount=settings.fire_target_amount or 910150,
            secure_withdrawal_rate=settings.secure_withdrawal_rate or 0.04,
            update_date=date.today()
        )
        return crud.create_fire_settings(db=db, settings=default_settings)
    return db_settings

@app.get("/api/evolution/", response_model=List[schemas.PatrimoineEvolution])
def read_patrimoine_evolution(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    evolutions = crud.get_patrimoine_evolution(db, skip=skip, limit=limit)
    return evolutions

@app.get("/api/evolution/{annee}", response_model=schemas.PatrimoineEvolution)
def read_patrimoine_evolution_by_year(annee: int, db: Session = Depends(get_db)):
    db_evolution = crud.get_patrimoine_evolution_by_year(db, annee=annee)
    if db_evolution is None:
        raise HTTPException(status_code=404, detail="Evolution data not found for this year")
    return db_evolution

@app.post("/api/evolution/", response_model=schemas.PatrimoineEvolution)
def create_patrimoine_evolution(evolution: schemas.PatrimoineEvolutionCreate, db: Session = Depends(get_db)):
    # Vérifier si l'année existe déjà
    existing = crud.get_patrimoine_evolution_by_year(db, evolution.annee)
    if existing:
        raise HTTPException(status_code=400, detail="Evolution data for this year already exists")

    db_evolution = crud.create_patrimoine_evolution(db=db, evolution=evolution)
    # Recalculer les métriques après ajout
    crud.calculate_evolution_metrics(db)
    return db_evolution

@app.put("/api/evolution/{annee}", response_model=schemas.PatrimoineEvolution)
def update_patrimoine_evolution(annee: int, evolution: schemas.PatrimoineEvolutionUpdate, db: Session = Depends(get_db)):
    db_evolution = crud.update_patrimoine_evolution(db, annee=annee, evolution=evolution)
    if db_evolution is None:
        raise HTTPException(status_code=404, detail="Evolution data not found for this year")
    # Recalculer les métriques après modification
    crud.calculate_evolution_metrics(db)
    return db_evolution

@app.delete("/api/evolution/{annee}", response_model=schemas.PatrimoineEvolution)
def delete_patrimoine_evolution(annee: int, db: Session = Depends(get_db)):
    db_evolution = crud.delete_patrimoine_evolution(db, annee=annee)
    if db_evolution is None:
        raise HTTPException(status_code=404, detail="Evolution data not found for this year")
    # Recalculer les métriques après suppression
    crud.calculate_evolution_metrics(db)
    return db_evolution

@app.post("/api/evolution/recalculate")
def recalculate_evolution_metrics(db: Session = Depends(get_db)):
    """Recalcule toutes les métriques d'évolution"""
    evolutions = crud.calculate_evolution_metrics(db)
    return {"message": "Evolution metrics recalculated successfully", "count": len(evolutions) if evolutions else 0}

# Budget Categories Endpoints
@app.get("/api/budget/categories/", response_model=List[schemas.BudgetCategory])
def read_budget_categories(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    categories = crud.get_budget_categories(db, skip=skip, limit=limit)
    return categories

@app.get("/api/budget/categories/{category_id}", response_model=schemas.BudgetCategory)
def read_budget_category(category_id: int, db: Session = Depends(get_db)):
    db_category = crud.get_budget_category(db, category_id=category_id)
    if db_category is None:
        raise HTTPException(status_code=404, detail="Budget category not found")
    return db_category

@app.post("/api/budget/categories/", response_model=schemas.BudgetCategory)
def create_budget_category(category: schemas.BudgetCategoryCreate, db: Session = Depends(get_db)):
    # Vérifier si la catégorie existe déjà
    existing = crud.get_budget_category_by_name(db, category.nom)
    if existing:
        raise HTTPException(status_code=400, detail="Budget category with this name already exists")

    return crud.create_budget_category(db=db, category=category)

@app.put("/api/budget/categories/{category_id}", response_model=schemas.BudgetCategory)
def update_budget_category(category_id: int, category: schemas.BudgetCategoryUpdate, db: Session = Depends(get_db)):
    db_category = crud.update_budget_category(db, category_id=category_id, category=category)
    if db_category is None:
        raise HTTPException(status_code=404, detail="Budget category not found")
    return db_category

@app.delete("/api/budget/categories/{category_id}", response_model=schemas.BudgetCategory)
def delete_budget_category(category_id: int, db: Session = Depends(get_db)):
    try:
        db_category = crud.delete_budget_category(db, category_id=category_id)
        if db_category is None:
            raise HTTPException(status_code=404, detail="Budget category not found")
        return db_category
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))

# Dépenses Réelles Endpoints
@app.get("/api/budget/depenses/", response_model=List[schemas.DepenseReelle])
def read_depenses_reelles(skip: int = 0, limit: int = 100, annee: int = None, mois: int = None, db: Session = Depends(get_db)):
    depenses = crud.get_depenses_reelles(db, skip=skip, limit=limit, annee=annee, mois=mois)
    return depenses

@app.get("/api/budget/depenses/{depense_id}", response_model=schemas.DepenseReelle)
def read_depense_reelle(depense_id: int, db: Session = Depends(get_db)):
    db_depense = crud.get_depense_reelle(db, depense_id=depense_id)
    if db_depense is None:
        raise HTTPException(status_code=404, detail="Expense not found")
    return db_depense

@app.post("/api/budget/depenses/", response_model=schemas.DepenseReelle)
def create_depense_reelle(depense: schemas.DepenseReelleCreate, db: Session = Depends(get_db)):
    return crud.create_depense_reelle(db=db, depense=depense)

@app.put("/api/budget/depenses/{depense_id}", response_model=schemas.DepenseReelle)
def update_depense_reelle(depense_id: int, depense: schemas.DepenseReelleUpdate, db: Session = Depends(get_db)):
    db_depense = crud.update_depense_reelle(db, depense_id=depense_id, depense=depense)
    if db_depense is None:
        raise HTTPException(status_code=404, detail="Expense not found")
    return db_depense

@app.delete("/api/budget/depenses/{depense_id}", response_model=schemas.DepenseReelle)
def delete_depense_reelle(depense_id: int, db: Session = Depends(get_db)):
    db_depense = crud.delete_depense_reelle(db, depense_id=depense_id)
    if db_depense is None:
        raise HTTPException(status_code=404, detail="Expense not found")
    return db_depense

# Analyses Budgétaires Endpoints
@app.get("/api/budget/analysis/", response_model=List[schemas.BudgetAnalysis])
def get_budget_analysis(annee: int = None, db: Session = Depends(get_db)):
    """Analyse détaillée budget vs réalisé par catégorie"""
    return crud.get_budget_analysis(db, annee=annee)

@app.get("/api/budget/summary/", response_model=schemas.BudgetSummary)
def get_budget_summary(annee: int = None, db: Session = Depends(get_db)):
    """Résumé global du budget"""
    return crud.get_budget_summary(db, annee=annee)

# SCPI Scraper Endpoint
from scpi_scraper import get_scpi_data, ScpiData # Import the scraper function and dataclass

@app.get("/api/scpi/scrape/", response_model=ScpiData)
async def scrape_scpi_data(url: str):
    # Basic URL validation (optional, but good practice)
    if not url.startswith("http://") and not url.startswith("https://"):
        raise HTTPException(status_code=400, detail="Invalid URL format. Must start with http:// or https://")

    scpi_data = get_scpi_data(url)
    if scpi_data is None:
        raise HTTPException(status_code=404, detail="Could not retrieve SCPI data. Check URL or website structure.")
    return scpi_data

# --- Fire Allocation Targets Endpoints ---
@app.get("/api/fire-allocation-targets/", response_model=List[schemas.FireAllocationTargetInDB])
def read_fire_allocation_targets(db: Session = Depends(get_db)):
    """
    Retrieve all FIRE allocation targets.
    """
    targets = crud.get_fire_allocation_targets(db)
    return targets

@app.post("/api/fire-allocation-targets/batch_update/", response_model=List[schemas.FireAllocationTargetInDB])
def update_fire_allocation_targets_batch_endpoint(
    targets_in: List[schemas.FireAllocationTargetCreate],
    db: Session = Depends(get_db)
):
    """
    Update a batch of FIRE allocation targets.
    Creates new entries if they don't exist based on category_key.
    """
    # Basic validation: ensure total target percentage does not exceed a reasonable limit (e.g. 100%, or a bit more for flexibility if desired)
    # More complex validation (e.g. ensuring it sums to exactly 100%) could be done on the frontend or as a separate check.
    # total_percentage = sum(t.target_percentage for t in targets_in)
    # if total_percentage > 101: # Allowing a small margin for rounding, adjust as needed
    #     raise HTTPException(status_code=400, detail=f"Total target percentage ({total_percentage}%) exceeds 100%.")

    updated_targets = crud.update_fire_allocation_targets_batch(db=db, targets=targets_in)
    return updated_targets
