'use client';

import React, { useEffect, useState, FormEvent } from 'react';
import apiClient from '../../components/ApiClient';
import { Modal, Button, Form, InputGroup, FormControl } from 'react-bootstrap';

// --- Types ---
interface CategoryValue {
  name: string;
  value: number;
}

interface ApiCategoryValue {
  category_name: string;
  value: number;
}

export interface Asset { // Exporté pour être utilisé par le Server Component page.tsx
  id: number;
  name: string;
  value: number;
  annual_interest: number | null;
  notes: string | null;
  update_date: string;
  categories: ApiCategoryValue[];
  liabilities: any[];
}
// --- Fin des Types ---

interface AssetsClientPartProps {
  initialAssets: Asset[];
  fetchError?: string;
}

// AssetForm reste un sous-composant défini ici
const AssetForm: React.FC<{ asset: Partial<Asset> | null, onSave: () => void, onCancel: () => void }> = ({ asset, onSave, onCancel }) => {
    const convertApiCategoriesToForm = (apiCategories: ApiCategoryValue[] | undefined): CategoryValue[] => {
        if (!apiCategories || apiCategories.length === 0) return [{ name: 'Liquidité', value: 0 }];
        return apiCategories.map(cat => ({ name: cat.category_name, value: cat.value }));
    };

    const initializeFormData = () => {
        if (asset && asset.id) {
            const convertedCategories = convertApiCategoriesToForm(asset.categories);
            return {
                name: asset.name || '', value: asset.value || 0, annual_interest: asset.annual_interest || null,
                notes: asset.notes || '', categories: convertedCategories,
                update_date: asset.update_date ? new Date(asset.update_date).toISOString().split('T')[0] : new Date().toISOString().split('T')[0],
            };
        }
        return {
            name: '', value: 0, annual_interest: null, notes: '',
            categories: [{ name: 'Liquidité', value: 0 }], update_date: new Date().toISOString().split('T')[0],
        };
    };
    const [formData, setFormData] = useState(initializeFormData);

    useEffect(() => { setFormData(initializeFormData()); }, [asset]);

    useEffect(() => {
        const totalValue = formData.categories.reduce((sum, cat) => sum + (Number(cat.value) || 0), 0);
        if (totalValue !== formData.value) {
            setFormData(prevData => ({ ...prevData, value: totalValue }));
        }
    }, [formData.categories, formData.value]);

    const handleCategoryChange = (index: number, field: string, value: any) => {
        const newCategories = [...formData.categories];
        newCategories[index] = { ...newCategories[index], [field]: field === 'value' ? parseFloat(value) || 0 : value };
        setFormData({ ...formData, categories: newCategories });
    };
    const addCategory = () => setFormData({ ...formData, categories: [...formData.categories, { name: 'Bourse', value: 0 }] });
    const removeCategory = (index: number) => {
        const newCategories = [...formData.categories]; newCategories.splice(index, 1);
        setFormData({ ...formData, categories: newCategories });
    };
    const handleSubmit = async (e: FormEvent) => {
        e.preventDefault();
        const method = asset?.id ? 'put' : 'post';
        const url = asset?.id ? `/assets/${asset.id}` : '/assets';
        try { await apiClient[method](url, formData); onSave(); }
        catch (error: any) { console.error("Erreur sauvegarde actif", error.response?.data || error.message); }
    };

    return (
        <Form onSubmit={handleSubmit}>
            <Form.Group className="mb-3"><Form.Label>Nom</Form.Label><Form.Control type="text" value={formData.name} onChange={e => setFormData({ ...formData, name: e.target.value })} required /></Form.Group>
            <Form.Group className="mb-3"><Form.Label>Valeur</Form.Label><Form.Control type="number" value={formData.value} readOnly disabled /></Form.Group>
            <Form.Group className="mb-3"><Form.Label>Intérêt Annuel</Form.Label><Form.Control type="number" step="0.01" value={formData.annual_interest || ''} onChange={e => setFormData({ ...formData, annual_interest: parseFloat(e.target.value) || null })} /></Form.Group>
            <Form.Group className="mb-3"><Form.Label>Notes</Form.Label><Form.Control type="text" value={formData.notes || ''} onChange={e => setFormData({ ...formData, notes: e.target.value })} /></Form.Group>
            <h5>Catégories</h5>
            {formData.categories.map((category, index) => (
                <InputGroup className="mb-3" key={index}>
                    <Form.Select value={category.name} onChange={e => handleCategoryChange(index, 'name', e.target.value)}><option>Liquidité</option><option>Bourse</option><option>Crypto-Actifs</option><option>Fonds sécurisés</option><option>Immobilier</option><option>Prêts participatifs</option></Form.Select>
                    <FormControl type="number" value={category.value} onChange={e => handleCategoryChange(index, 'value', e.target.value)} />
                    <Button variant="outline-danger" onClick={() => removeCategory(index)}>X</Button>
                </InputGroup>
            ))}
            <Button variant="outline-primary" onClick={addCategory} className="mb-3">Ajouter une catégorie</Button>
            <Form.Group className="mb-3 mt-3"><Form.Label>Date de mise à jour</Form.Label><Form.Control type="date" value={formData.update_date} onChange={e => setFormData({ ...formData, update_date: e.target.value })} required /></Form.Group>
            <Button variant="primary" type="submit">Sauvegarder</Button><Button variant="secondary" onClick={onCancel} className="ms-2">Annuler</Button>
        </Form>
    );
};

export const AssetsClientPart: React.FC<AssetsClientPartProps> = ({ initialAssets, fetchError }) => {
  const [assets, setAssets] = useState<Asset[]>(initialAssets || []);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(fetchError || null);
  const [showModal, setShowModal] = useState(false);
  const [selectedAsset, setSelectedAsset] = useState<Partial<Asset> | null>(null);
  const [sortConfig, setSortConfig] = useState<{ key: keyof Asset | null; direction: 'ascending' | 'descending' }>({ key: 'value', direction: 'descending' });

  useEffect(() => {
    setAssets(initialAssets || []);
    setError(fetchError || null);
  }, [initialAssets, fetchError]);

  const fetchAssetsClient = () => {
    setLoading(true); setError(null);
    apiClient.get('/assets')
      .then(response => { setAssets(response.data); })
      .catch(err => { console.error('Assets API error (client fetch):', err); setError('Erreur lors du rechargement des actifs.'); })
      .finally(() => setLoading(false));
  };

  const requestSort = (key: keyof Asset) => {
    let direction: 'ascending' | 'descending' = 'ascending';
    if (sortConfig.key === key && sortConfig.direction === 'ascending') { direction = 'descending'; }
    setSortConfig({ key, direction });
  };

  const sortedAssets = React.useMemo(() => {
    let sortableItems = [...assets];
    if (sortConfig.key) {
      sortableItems.sort((a, b) => {
        const aValue = a[sortConfig.key!]; const bValue = b[sortConfig.key!];
        if (aValue === null || aValue === undefined) return 1; if (bValue === null || bValue === undefined) return -1;
        if (aValue < bValue) return sortConfig.direction === 'ascending' ? -1 : 1;
        if (aValue > bValue) return sortConfig.direction === 'ascending' ? 1 : -1;
        return 0;
      });
    }
    return sortableItems;
  }, [assets, sortConfig]);

  const handleSave = () => { setShowModal(false); setSelectedAsset(null); fetchAssetsClient(); };
  const handleDelete = async (id: number) => {
    if (window.confirm("Êtes-vous sûr de vouloir supprimer cet actif ?")) {
      try { await apiClient.delete(`/assets/${id}`); fetchAssetsClient(); }
      catch (err) { console.error("Erreur suppression actif", err); }
    }
  };

  if (error && assets.length === 0) return (<div><p className="text-danger">{error}</p><button className="btn btn-primary" onClick={fetchAssetsClient}>Réessayer</button></div>);
  if (loading) return <p>Rechargement des actifs...</p>;

  return (
    <div>
      <div className="d-flex justify-content-between align-items-center mb-4"><h1>Mon Patrimoine (Actifs)</h1><Button variant="primary" onClick={() => { setSelectedAsset({}); setShowModal(true); }}>Ajouter un Actif</Button></div>
      {assets.length === 0 && !error && <p>Aucun actif trouvé.</p>}
      {assets.length > 0 && (
        <table className="table table-striped table-hover">
          <thead className="table-dark"><tr>
              <th onClick={() => requestSort('name')} style={{ cursor: 'pointer' }}>Nom {sortConfig.key === 'name' ? (sortConfig.direction === 'ascending' ? '▲' : '▼') : ''}</th>
              <th>Catégories</th><th className="text-end">Intérêt Annuel</th>
              <th className="text-end" onClick={() => requestSort('value')} style={{ cursor: 'pointer' }}>Valeur {sortConfig.key === 'value' ? (sortConfig.direction === 'ascending' ? '▲' : '▼') : ''}</th>
              <th className="text-center" onClick={() => requestSort('update_date')} style={{ cursor: 'pointer' }}>Dernière MàJ {sortConfig.key === 'update_date' ? (sortConfig.direction === 'ascending' ? '▲' : '▼') : ''}</th>
              <th className="text-center">Actions</th></tr></thead>
          <tbody>{sortedAssets.map(asset => (<tr key={asset.id}><td>{asset.name}</td><td>{asset.categories.map(c => `${c.category_name} (${new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR' }).format(c.value)})`).join(', ')}</td><td className="text-end">{asset.annual_interest ? new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR' }).format(asset.annual_interest) : 'N/A'}</td><td className="text-end">{new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR' }).format(asset.value)}</td><td className="text-center">{new Date(asset.update_date).toLocaleDateString('fr-FR')}</td><td className="text-center"><Button variant="outline-primary" size="sm" onClick={() => { setSelectedAsset(asset); setShowModal(true); }}>Modifier</Button>{' '}<Button variant="outline-danger" size="sm" onClick={() => handleDelete(asset.id)}>Supprimer</Button></td></tr>))}</tbody>
        </table>
      )}
      <Modal show={showModal} onHide={() => setShowModal(false)}><Modal.Header closeButton><Modal.Title>{selectedAsset?.id ? 'Modifier' : 'Ajouter'} un Actif</Modal.Title></Modal.Header><Modal.Body><AssetForm asset={selectedAsset} onSave={handleSave} onCancel={() => setShowModal(false)} /></Modal.Body></Modal>
    </div>
  );
};
