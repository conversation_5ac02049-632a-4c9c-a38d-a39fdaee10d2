import requests
import json
import datetime
import os # Added

# Configuration des URLs de l'API
BASE_URL = "http://127.0.0.1:8000/api"
ENDPOINTS = {
    "assets": "/assets/",
    "scpi": "/scpi/",
    "liabilities": "/liabilities/",
    "patrimoine_history": "/patrimoine-history/",
    "evolution": "/evolution/",
    "budget_categories": "/budget/categories/",
    "depenses_reelles": "/budget/depenses/",
    "fire_settings": "/fire-settings",
    "fire_allocation_targets": "/fire-allocation-targets/"
}

# OUTPUT_SEED_FILE = "backend/generated_seed.py" # Original plan
SEED_FILE_PATH = "backend/seed.py" # New target
BACKUP_SEED_FILE_PATH = "backend/seed.py.bak" # Backup path

def fetch_data(endpoint):
    """Récupère les données depuis un endpoint de l'API."""
    try:
        url = f"{BASE_URL}{endpoint}"
        response = requests.get(url)
        response.raise_for_status()  # Lève une exception pour les erreurs HTTP
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"Erreur lors de la récupération des données pour {endpoint}: {e}")
        return None

def format_assets(assets):
    """Formate les données des actifs pour le fichier seed."""
    asset_lines = []
    for asset in assets:
        categories = []
        for cat in asset.get("categories", []):
            category_name = cat.get('category_name', '').replace("'", "\'")
            value = cat.get('value')
            categories.append(f"schemas.CategoryValue(name='{category_name}', value={value})")
        
        categories_str = f"[{', '.join(categories)}]"
        
        # Gestion des valeurs None
        name = asset.get('name', '').replace("'", "\'")
        value = asset.get('value')
        annual_interest = asset.get('annual_interest')
        notes = asset.get('notes')
        update_date_str = asset.get('update_date')
        
        # Conversion de la date
        update_date = datetime.datetime.fromisoformat(update_date_str).date()

        line = (
            f"        schemas.AssetCreate(name='{name}', value={value}, "
            f"annual_interest={annual_interest}, notes={repr(notes)}, "
            f"update_date=datetime.date({update_date.year}, {update_date.month}, {update_date.day}), "
            f"categories={categories_str})"
        )
        asset_lines.append(line)
    return ",\n".join(asset_lines)

# Define hardcoded Primaliance URLs for specific SCPIs
HARDCODED_SCPI_URLS = {
    "PFO2": "https://www.primaliance.com/scpi-de-rendement/9-scpi-pfo2",
    "EPARGNE FONCIERE": "https://www.primaliance.com/scpi-de-rendement/49-scpi-epargne-fonciere",
    "AESTIAM PIERRE RENDEMENT": "https://www.primaliance.com/scpi-de-rendement/84-scpi-aestiam-pierre-rendement",
    "Moniwan : LF OPPORTUNITE IMMO": "https://www.primaliance.com/scpi-de-rendement/159-scpi-lf-opportunite-immo"
}

def format_simple_data(items, model_name):
    """Formate les données simples (SCPI, Liabilities, etc.) pour le fichier seed."""
    item_lines = []
    for item in items:
        # For SCPIs, check if we need to override the primaliance_url
        if model_name == 'SCPI':
            scpi_name = item.get('name')
            if scpi_name in HARDCODED_SCPI_URLS:
                item['primaliance_url'] = HARDCODED_SCPI_URLS[scpi_name]
            # Ensure primaliance_url key exists even if not in hardcoded list and not returned by API, to avoid KeyError during json.dumps if other SCPIs don't have it
            elif 'primaliance_url' not in item:
                 item['primaliance_url'] = None


        # Gérer les dates
        if 'update_date' in item and item['update_date']:
            update_date = datetime.datetime.fromisoformat(item['update_date']).date()
            item['update_date'] = f"datetime.date({update_date.year}, {update_date.month}, {update_date.day})"
        if 'end_date' in item and item['end_date']:
            end_date = datetime.datetime.fromisoformat(item['end_date']).date()
            item['end_date'] = f"datetime.date({end_date.year}, {end_date.month}, {end_date.day})"

        # Supprimer l'ID et autres champs non nécessaires pour la création
        item.pop('id', None)
        if model_name != 'PatrimoineEvolution':
            item.pop('evolution_pourcentage', None)
            item.pop('evolution_euros', None)
            item.pop('croissance_moyenne', None)
            item.pop('tcam', None)

        # Formatter en dictionnaire Python
        item_str = json.dumps(item, ensure_ascii=False, indent=8)
        
        # Remplacer les chaînes de date formatées
        if 'update_date' in item:
            item_str = item_str.replace(f"\"{item['update_date']}\"", item['update_date'])
        if 'end_date' in item:
            item_str = item_str.replace(f"\"{item['end_date']}\"", item['end_date'])

        # Replace JSON null with Python None
        item_str = item_str.replace(": null", ": None")
            
        item_lines.append(item_str)
        
    return f",\n".join(item_lines)
    
def format_patrimoine_history(history_items):
    """Formate l'historique du patrimoine."""
    lines = []
    for item in history_items:
        date_str = item.get('date')
        date_obj = datetime.datetime.fromisoformat(date_str).date()
        net_patrimoine = item.get('net_patrimoine')
        line = (
            f"        schemas.PatrimoineHistoryCreate("
            f"date=datetime.date({date_obj.year}, {date_obj.month}, {date_obj.day}), "
            f"net_patrimoine={net_patrimoine})"
        )
        lines.append(line)
    return ",\n".join(lines)

def format_fire_settings(settings):
    """Formate les données FireSettings pour le fichier seed."""
    if not settings:
        return ""
    # Remove 'id' and format date for FireSettingsCreate schema
    # The API GET /api/fire-settings returns an object that includes 'id' and 'update_date'
    # The seed script typically uses a Create schema or direct model instantiation.
    # Let's assume the seed script will use models.FireSettings directly for simplicity here.

    update_date_str = settings.get('update_date')
    update_date_formatted = "None"
    if update_date_str:
        try:
            date_obj = datetime.datetime.fromisoformat(update_date_str).date()
            update_date_formatted = f"datetime.date({date_obj.year}, {date_obj.month}, {date_obj.day})"
        except ValueError:
            print(f"Warning: Could not parse update_date '{update_date_str}' for FireSettings.")

    # Constructing the string for models.FireSettings, excluding id
    return (
        f"    fire_setting_data = models.FireSettings(\n"
        f"        fire_target_amount={settings.get('fire_target_amount', 0)},\n"
        f"        secure_withdrawal_rate={settings.get('secure_withdrawal_rate', 0.04)},\n"
        f"        update_date={update_date_formatted}\n"
        f"    )\n"
        f"    db.add(fire_setting_data)"
    )

def format_fire_allocation_targets(targets):
    """Formate les données FireAllocationTarget pour le fichier seed."""
    target_lines = []
    if not targets:
        return ""

    for target in targets:
        category_key = target.get('category_key', '').replace("'", "\\'")
        target_percentage = target.get('target_percentage', 0)
        # This will be part of a list of objects to be added to the DB session
        line = (
            f"        models.FireAllocationTarget(category_key='{category_key}', target_percentage={target_percentage})"
        )
        target_lines.append(line)

    if not target_lines:
        return ""

    return "    fire_allocation_targets_to_create = [\n" + ",\n".join(target_lines) + "\n    ]\n\n" \
           "    for fat_data in fire_allocation_targets_to_create:\n" \
           "        db.add(fat_data)"

def format_depenses_reelles(depenses):
    """Formate les données DepenseReelle pour le fichier seed."""
    depense_lines = []
    if not depenses:
        return ""

    for depense in depenses:
        # The API for DepenseReelle returns 'id', 'categorie_id', 'montant',
        # 'date_depense', 'description', 'mois', 'annee'.
        # For seeding, we'd typically use DepenseReelleCreate schema which needs:
        # categorie_id, montant, date_depense, description.
        # The model itself will derive mois/annee if not provided, or they can be set.
        # The create_depense_reelle CRUD in main.py derives mois/annee.

        categorie_id = depense.get('categorie_id')
        montant = depense.get('montant')
        date_depense_str = depense.get('date_depense')
        description = depense.get('description')

        date_depense_formatted = "None"
        if date_depense_str:
            try:
                date_obj = datetime.datetime.fromisoformat(date_depense_str).date()
                date_depense_formatted = f"datetime.date({date_obj.year}, {date_obj.month}, {date_obj.day})"
            except ValueError:
                print(f"Warning: Could not parse date_depense '{date_depense_str}' for DepenseReelle.")

        # We will create DepenseReelle model instances directly.
        # The CRUD create_depense_reelle in main.py handles deriving mois and annee.
        # So the generated seed should mimic that by providing the necessary fields for the model.
        # Or, if the generated seed calls a CRUD function, it would use the Create schema.
        # For now, let's assume direct model instantiation in the generated seed.
        # The model itself doesn't automatically derive mois/annee, the CRUD op does.
        # So, we should include mois and annee from the fetched data.
        mois = depense.get('mois')
        annee = depense.get('annee')

        line = (
            f"        models.DepenseReelle(categorie_id={categorie_id}, montant={montant}, "
            f"date_depense={date_depense_formatted}, description={repr(description)}, "
            f"mois={mois}, annee={annee})"
        )
        depense_lines.append(line)

    if not depense_lines:
        return ""

    return "    depenses_reelles_to_create = [\n" + ",\n".join(depense_lines) + "\n    ]\n\n" \
           "    for dr_data in depenses_reelles_to_create:\n" \
           "        db.add(dr_data)"


def generate_seed_content(all_fetched_data):
    """Génère le contenu complet du fichier seed.py."""

    # Collect all unique category names first
    all_category_names = set()
    # Corrected to use all_fetched_data consistently
    for asset in all_fetched_data.get('assets', []):
        for cat in asset.get("categories", []):
            if cat.get('category_name'):
                all_category_names.add(cat.get('category_name'))
    for fat in all_fetched_data.get('fire_allocation_targets', []):
        if fat.get('category_key'):
            all_category_names.add(fat.get('category_key'))

    unique_categories_to_seed_code = []
    for cat_name in sorted(list(all_category_names)):
        unique_categories_to_seed_code.append(f"        models.Category(name='{cat_name.replace("'", "\\'")}')")

    categories_creation_str = "    # --- Ensure All Categories Exist ---\n"
    if unique_categories_to_seed_code:
        categories_creation_str += "    categories_to_seed = [\n" + ",\n".join(unique_categories_to_seed_code) + "\n    ]\n"
        categories_creation_str += "    for cat_model_instance in categories_to_seed:\n"
        categories_creation_str += "        existing_category = db.query(models.Category).filter(models.Category.name == cat_model_instance.name).first()\n"
        categories_creation_str += "        if not existing_category:\n"
        categories_creation_str += "            db.add(cat_model_instance)\n"
        categories_creation_str += "    db.commit() # Commit categories before linking\n"
    else:
        categories_creation_str += "    # No categories found to pre-seed.\n"

    assets_str = format_assets(all_fetched_data.get('assets', []))
    scpi_str = format_simple_data(all_fetched_data.get('scpi', []), 'SCPI')
    liabilities_str = format_simple_data(all_fetched_data.get('liabilities', []), 'Liability')
    patrimoine_history_str = format_patrimoine_history(all_fetched_data.get('patrimoine_history', []))
    evolution_str = format_simple_data(all_fetched_data.get('evolution', []), 'PatrimoineEvolution')
    budget_categories_str = format_simple_data(all_fetched_data.get('budget_categories', []), 'BudgetCategory')
    depenses_reelles_str = format_depenses_reelles(all_fetched_data.get('depenses_reelles', []))
    fire_settings_str = format_fire_settings(all_fetched_data.get('fire_settings', {}))
    fire_allocation_targets_str = format_fire_allocation_targets(all_fetched_data.get('fire_allocation_targets', []))


    # Template du fichier seed.py
    seed_template = f"""import datetime
from sqlalchemy.orm import Session
from database import SessionLocal, engine
import models
import crud, schemas

def seed_data():
    db = SessionLocal()
    models.Base.metadata.drop_all(bind=engine)
    models.Base.metadata.create_all(bind=engine)

    # Clear existing data
    db.query(models.DepenseReelle).delete() # Already here, good
    db.query(models.BudgetCategory).delete()
    db.query(models.FireAllocationTarget).delete()
    db.query(models.Liability).delete()
    db.query(models.SCPI).delete()
    db.query(models.AssetCategory).delete() # Ensuring this is before Asset and Category
    db.query(models.Category).delete() # Ensuring this is before Asset
    db.query(models.Asset).delete()
    db.query(models.PatrimoineHistory).delete()
    db.query(models.PatrimoineEvolution).delete()
    db.query(models.FireSettings).delete()
    db.commit()

{categories_creation_str}

    # --- Assets ---
    assets_to_create = [
{assets_str}
    ]

    # --- Assets (Generated Script Logic) ---
    # This assumes assets_to_create is a list of objects that have .name, .value, .categories etc.
    # (i.e. format_assets produces strings that result in a list of schemas.AssetCreate instances)
    for asset_data_item in assets_to_create:
        asset_db = models.Asset(
            name=asset_data_item.name,
            value=asset_data_item.value,
            annual_interest=asset_data_item.annual_interest,
            notes=asset_data_item.notes,
            update_date=asset_data_item.update_date
        )
        db.add(asset_db)
        db.flush() # Get asset_db.id

        for category_value_item in asset_data_item.categories:
            category_db_instance = db.query(models.Category).filter(models.Category.name == category_value_item.name).first()
            if category_db_instance: # Should exist due to pre-seeding
                asset_category_link = models.AssetCategory(
                    asset_id=asset_db.id,
                    category_id=category_db_instance.id,
                    value=category_value_item.value
                )
                db.add(asset_category_link)
            else:
                print(f"WARNING: Category '{{category_value_item.name}}' not found for asset '{{asset_data_item.name}}' during generated seed.")
    db.commit() # Commit assets and their category links

    # --- SCPI ---
    scpi_to_create = [
{scpi_str}
    ]

    for scpi_data in scpi_to_create:
        scpi = models.SCPI(**scpi_data)
        db.add(scpi)

    # --- Liabilities ---
    liabilities_to_create = [
{liabilities_str}
    ]

    for liability_data in liabilities_to_create:
        liability = models.Liability(**liability_data)
        db.add(liability)

    # --- Patrimoine History ---
    patrimoine_history_to_create = [
{patrimoine_history_str}
    ]

    for history_data in patrimoine_history_to_create:
        history = models.PatrimoineHistory(**history_data.model_dump())
        db.add(history)

    # --- Patrimoine Evolution (2015-2024) ---
    evolution_to_create = [
{evolution_str}
    ]

    for evolution_data in evolution_to_create:
        evolution = models.PatrimoineEvolution(**evolution_data)
        db.add(evolution)

    # --- Budget Categories (25 catégories FIRE) ---
    budget_categories_to_create = [
{budget_categories_str}
    ]

    for category_data in budget_categories_to_create:
        category = models.BudgetCategory(**category_data)
        db.add(category)

    # --- Dépenses Réelles ---
{depenses_reelles_str}

    # --- Fire Settings ---
{fire_settings_str}

    # --- Fire Allocation Targets ---
{fire_allocation_targets_str}

    db.commit()

    # Calculate and display totals
    total_scpi_value = sum(scpi_data["total_value"] for scpi_data in scpi_to_create)
    total_liabilities = sum(liability_data["remaining_capital"] for liability_data in liabilities_to_create)
    total_budget_annuel = sum(cat["budget_annuel"] for cat in budget_categories_to_create)

    print("Database has been seeded successfully.")
    print(f"Assets created: {{len(assets_to_create)}}")
    print(f"SCPI created: {{len(scpi_to_create)}}")
    print(f"Total SCPI value: {{total_scpi_value:,.2f}} €")
    print(f"Liabilities created: {{len(liabilities_to_create)}}")
    print(f"Total liabilities: {{total_liabilities:,.2f}} €")
    print(f"Patrimoine history entries: {{len(patrimoine_history_to_create)}}")
    print(f"Evolution entries: {{len(evolution_to_create)}} (2015-2024)")
    print(f"Budget categories: {{len(budget_categories_to_create)}}")
    print(f"Total budget annuel FIRE: {{total_budget_annuel:,.2f}} €")
    print(f"Retrait brut nécessaire (avec 30% impôts/PS): {{total_budget_annuel * 1.3:,.2f}} €")
    print("Evolution metrics calculated automatically.")

    db.close()
    engine.dispose()

if __name__ == "__main__":
    seed_data()
"""
    return seed_template

def main():
    """Fonction principale du script."""
    all_data = {}
    print("Démarrage de la récupération des données depuis l'API...")
    has_errors = False
    for name, endpoint in ENDPOINTS.items():
        print(f"Récupération de: {name}...")
        data = fetch_data(endpoint)
        if data is not None:
            all_data[name] = data
            # Handle single dict items (like fire_settings) vs lists for length calculation
            count = 0
            if isinstance(data, list):
                count = len(data)
            elif isinstance(data, dict) and data: # Check if dict is not empty
                count = 1
            print(f"-> {count} enregistrements trouvés.")
        else:
            print(f"-> Échec de la récupération pour {name}.")
            # Decide if script should stop or continue. For now, let's mark error and continue.
            # This allows generating a partial seed if some non-critical endpoints fail.
            has_errors = True
            # all_data[name] will be missing, formatters should handle empty lists/dicts.

    if has_errors:
        print("\nAttention: Des erreurs sont survenues lors de la récupération de certaines données.")
        user_choice = input("Voulez-vous continuer la génération du script avec les données partielles? (o/N): ")
        if user_choice.lower() != 'o':
            print("Génération du script annulée.")
            return

    print("\nGénération du nouveau contenu pour seed.py...")
    seed_content = generate_seed_content(all_data)

    try:
        # Backup existing seed.py
        if os.path.exists(SEED_FILE_PATH):
            if os.path.exists(BACKUP_SEED_FILE_PATH):
                os.remove(BACKUP_SEED_FILE_PATH)
                print(f"Ancien backup '{BACKUP_SEED_FILE_PATH}' supprimé.")
            os.rename(SEED_FILE_PATH, BACKUP_SEED_FILE_PATH)
            print(f"Fichier '{SEED_FILE_PATH}' sauvegardé en '{BACKUP_SEED_FILE_PATH}'.")

        # Write the new seed.py
        with open(SEED_FILE_PATH, "w", encoding="utf-8") as f:
            f.write(seed_content)
        print(f"Le fichier '{SEED_FILE_PATH}' a été mis à jour avec succès.")
        print("Vous pouvez maintenant l'exécuter pour peupler la base de données.")
    except IOError as e:
        print(f"Erreur lors de l'écriture du fichier '{SEED_FILE_PATH}': {e}")
    except OSError as e:
        print(f"Erreur lors de la manipulation des fichiers (backup/rename): {e}")

if __name__ == "__main__":
    main()
