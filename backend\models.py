from typing import List, Optional
from sqlalchemy import Column, Integer, String, Float, Date, ForeignKey
from sqlalchemy.orm import relationship, Mapped, mapped_column
from database import Base

class AssetCategory(Base):
    __tablename__ = 'asset_category'
    asset_id: Mapped[int] = mapped_column(Integer, ForeignKey('assets.id'), primary_key=True)
    category_id: Mapped[int] = mapped_column(Integer, ForeignKey('categories.id'), primary_key=True)
    value: Mapped[Optional[float]] = mapped_column(Float, nullable=True) # Assuming value can be optional or default to 0
    asset: Mapped["Asset"] = relationship(back_populates="categories")
    category: Mapped["Category"] = relationship(back_populates="assets")

class Category(Base):
    __tablename__ = "categories"
    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    name: Mapped[str] = mapped_column(String, unique=True, index=True)
    assets: Mapped[List["AssetCategory"]] = relationship(back_populates="category")

class Asset(Base):
    __tablename__ = "assets"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    name: Mapped[str] = mapped_column(String, index=True)
    value: Mapped[float] = mapped_column(Float)
    annual_interest: Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    notes: Mapped[Optional[str]] = mapped_column(String, nullable=True)
    update_date: Mapped[Date] = mapped_column(Date) # Assuming datetime.date, direct map to Date

    categories: Mapped[List["AssetCategory"]] = relationship(back_populates="asset")
    liabilities: Mapped[List["Liability"]] = relationship(back_populates="asset")

class Liability(Base):
    __tablename__ = "liabilities"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    name: Mapped[str] = mapped_column(String, index=True)
    initial_amount: Mapped[float] = mapped_column(Float)
    remaining_capital: Mapped[float] = mapped_column(Float)
    interest_rate: Mapped[float] = mapped_column(Float)
    end_date: Mapped[Date] = mapped_column(Date) # Assuming datetime.date
    monthly_payment: Mapped[float] = mapped_column(Float)
    asset_id: Mapped[Optional[int]] = mapped_column(Integer, ForeignKey("assets.id"), nullable=True)

    asset: Mapped[Optional["Asset"]] = relationship(back_populates="liabilities") # Optional if asset_id is nullable

class PatrimoineHistory(Base):
    __tablename__ = "patrimoine_history"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    date: Mapped[Date] = mapped_column(Date, unique=True) # Assuming datetime.date
    net_patrimoine: Mapped[float] = mapped_column(Float)

class SCPI(Base):
    __tablename__ = "scpi"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    name: Mapped[str] = mapped_column(String, index=True)
    price_per_share: Mapped[float] = mapped_column(Float)
    number_of_shares: Mapped[int] = mapped_column(Integer)
    total_value: Mapped[float] = mapped_column(Float)
    update_date: Mapped[Date] = mapped_column(Date) # Assuming datetime.date
    primaliance_url: Mapped[Optional[str]] = mapped_column(String, nullable=True)

class FireSettings(Base):
    __tablename__ = "fire_settings"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    fire_target_amount: Mapped[float] = mapped_column(Float)
    secure_withdrawal_rate: Mapped[float] = mapped_column(Float)
    update_date: Mapped[Date] = mapped_column(Date) # Assuming datetime.date

class PatrimoineEvolution(Base):
    __tablename__ = "patrimoine_evolution"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    annee: Mapped[int] = mapped_column(Integer, unique=True, index=True)
    investissement: Mapped[float] = mapped_column(Float)
    prix_part_scpi: Mapped[float] = mapped_column(Float)
    remboursement_credit: Mapped[float] = mapped_column(Float)
    valeur_reelle_scpi: Mapped[float] = mapped_column(Float)
    total_patrimoine: Mapped[float] = mapped_column(Float)
    evolution_pourcentage: Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    evolution_euros: Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    croissance_moyenne: Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    tcam: Mapped[Optional[float]] = mapped_column(Float, nullable=True)

class BudgetCategory(Base):
    __tablename__ = "budget_categories"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    nom: Mapped[str] = mapped_column(String, unique=True, index=True)
    budget_annuel: Mapped[float] = mapped_column(Float)
    description: Mapped[Optional[str]] = mapped_column(String, nullable=True)
    ordre_affichage: Mapped[int] = mapped_column(Integer, default=0)

    depenses: Mapped[List["DepenseReelle"]] = relationship(back_populates="categorie")

class DepenseReelle(Base):
    __tablename__ = "depenses_reelles"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    categorie_id: Mapped[int] = mapped_column(Integer, ForeignKey("budget_categories.id"))
    montant: Mapped[float] = mapped_column(Float)
    date_depense: Mapped[Date] = mapped_column(Date) # Assuming datetime.date
    description: Mapped[Optional[str]] = mapped_column(String, nullable=True)
    mois: Mapped[int] = mapped_column(Integer)
    annee: Mapped[int] = mapped_column(Integer)

    categorie: Mapped["BudgetCategory"] = relationship(back_populates="depenses")

class FireAllocationTarget(Base):
    __tablename__ = "fire_allocation_targets"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    category_key: Mapped[str] = mapped_column(String, unique=True, index=True, nullable=False)
    target_percentage: Mapped[float] = mapped_column(Float, nullable=False)
