"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[405],{902:(e,t,n)=>{n.d(t,{A:()=>u});var r=n(9300),l=n.n(r),o=n(2115),a=n(2960),i=n(7390),s=n(5155);let c=o.forwardRef((e,t)=>{let{as:n,bsPrefix:r,variant:o="primary",size:c,active:u=!1,disabled:d=!1,className:f,...m}=e,h=(0,i.oU)(r,"btn"),[g,{tagName:p}]=(0,a.Am)({tagName:n,disabled:d,...m});return(0,s.jsx)(p,{...g,...m,ref:t,disabled:d,className:l()(f,h,u&&"active",o&&"".concat(h,"-").concat(o),c&&"".concat(h,"-").concat(c),m.href&&d&&"disabled")})});c.displayName="Button";let u=c},9986:(e,t,n)=>{let r,l;n.d(t,{A:()=>eu});var o,a=n(9300),i=n.n(a),s=n(6603),c=n(317),u=n(5352),d=n(2906);function f(e){if((!o&&0!==o||e)&&c.A){var t=document.createElement("div");t.style.position="absolute",t.style.top="-9999px",t.style.width="50px",t.style.height="50px",t.style.overflow="scroll",document.body.appendChild(t),o=t.offsetWidth-t.clientWidth,document.body.removeChild(t)}return o}var m=n(2115),h=n(7150),g=n(8621),p=n(9172);function v(e){void 0===e&&(e=(0,u.A)());try{var t=e.activeElement;if(!t||!t.nodeName)return null;return t}catch(t){return e.body}}function b(e,t){return e.contains?e.contains(t):e.compareDocumentPosition?e===t||!!(16&e.compareDocumentPosition(t)):void 0}var E=n(2405),y=n(7650),A=n(8141),x=n(18),N=n(1730),R=n(3666);let w="data-rr-ui-modal-open";class C{constructor({ownerDocument:e,handleContainerOverflow:t=!0,isRTL:n=!1}={}){this.handleContainerOverflow=t,this.isRTL=n,this.modals=[],this.ownerDocument=e}getScrollbarWidth(){return function(e=document){return Math.abs(e.defaultView.innerWidth-e.documentElement.clientWidth)}(this.ownerDocument)}getElement(){return(this.ownerDocument||document).body}setModalAttributes(e){}removeModalAttributes(e){}setContainerStyle(e){let t={overflow:"hidden"},n=this.isRTL?"paddingLeft":"paddingRight",r=this.getElement();e.style={overflow:r.style.overflow,[n]:r.style[n]},e.scrollBarWidth&&(t[n]=`${parseInt((0,R.A)(r,n)||"0",10)+e.scrollBarWidth}px`),r.setAttribute(w,""),(0,R.A)(r,t)}reset(){[...this.modals].forEach(e=>this.remove(e))}removeContainerStyle(e){let t=this.getElement();t.removeAttribute(w),Object.assign(t.style,e.style)}add(e){let t=this.modals.indexOf(e);return -1!==t||(t=this.modals.length,this.modals.push(e),this.setModalAttributes(e),0!==t||(this.state={scrollBarWidth:this.getScrollbarWidth(),style:{}},this.handleContainerOverflow&&this.setContainerStyle(this.state))),t}remove(e){let t=this.modals.indexOf(e);-1!==t&&(this.modals.splice(t,1),!this.modals.length&&this.handleContainerOverflow&&this.removeContainerStyle(this.state),this.removeModalAttributes(e))}isTopModal(e){return!!this.modals.length&&this.modals[this.modals.length-1]===e}}let T=C,O=(0,m.createContext)(c.A?window:void 0);function j(){return(0,m.useContext)(O)}O.Provider;let k=(e,t)=>c.A?null==e?(t||(0,u.A)()).body:("function"==typeof e&&(e=e()),e&&"current"in e&&(e=e.current),e&&("nodeType"in e||e.getBoundingClientRect))?e:null:null,S=e=>e&&"function"!=typeof e?t=>{e.current=t}:e,L=function(e,t){return(0,m.useMemo)(()=>(function(e,t){let n=S(e),r=S(t);return e=>{n&&n(e),r&&r(e)}})(e,t),[e,t])};var D=n(4583),M=n(2489);let B=function({children:e,in:t,onExited:n,mountOnEnter:r,unmountOnExit:l}){let o=(0,m.useRef)(null),a=(0,m.useRef)(t),i=(0,N.A)(n);(0,m.useEffect)(()=>{t?a.current=!0:i(o.current)},[t,i]);let s=L(o,(0,M.am)(e)),c=(0,m.cloneElement)(e,{ref:s});return t?c:l||!a.current&&r?null:c},F=["onEnter","onEntering","onEntered","onExit","onExiting","onExited","addEndListener","children"];var _=n(5155);let I=["component"],W=m.forwardRef((e,t)=>{let{component:n}=e,r=function(e){let{onEnter:t,onEntering:n,onEntered:r,onExit:l,onExiting:o,onExited:a,addEndListener:i,children:s}=e,c=function(e,t){if(null==e)return{};var n={};for(var r in e)if(({}).hasOwnProperty.call(e,r)){if(t.indexOf(r)>=0)continue;n[r]=e[r]}return n}(e,F),u=(0,m.useRef)(null),d=L(u,(0,M.am)(s)),f=e=>t=>{e&&u.current&&e(u.current,t)},h=(0,m.useCallback)(f(t),[t]),g=(0,m.useCallback)(f(n),[n]),p=(0,m.useCallback)(f(r),[r]),v=(0,m.useCallback)(f(l),[l]),b=(0,m.useCallback)(f(o),[o]),E=(0,m.useCallback)(f(a),[a]),y=(0,m.useCallback)(f(i),[i]);return Object.assign({},c,{nodeRef:u},t&&{onEnter:h},n&&{onEntering:g},r&&{onEntered:p},l&&{onExit:v},o&&{onExiting:b},a&&{onExited:E},i&&{addEndListener:y},{children:"function"==typeof s?(e,t)=>s(e,Object.assign({},t,{ref:d})):(0,m.cloneElement)(s,{ref:d})})}(function(e,t){if(null==e)return{};var n={};for(var r in e)if(({}).hasOwnProperty.call(e,r)){if(t.indexOf(r)>=0)continue;n[r]=e[r]}return n}(e,I));return(0,_.jsx)(n,Object.assign({ref:t},r))});function H({children:e,in:t,onExited:n,onEntered:r,transition:l}){let[o,a]=(0,m.useState)(!t);t&&o&&a(!1);let i=L(function({in:e,onTransition:t}){let n=(0,m.useRef)(null),r=(0,m.useRef)(!0),l=(0,N.A)(t);return(0,D.A)(()=>{if(!n.current)return;let t=!1;return l({in:e,element:n.current,initial:r.current,isStale:()=>t}),()=>{t=!0}},[e,l]),(0,D.A)(()=>(r.current=!1,()=>{r.current=!0}),[]),n}({in:!!t,onTransition:e=>{Promise.resolve(l(e)).then(()=>{e.isStale()||(e.in?null==r||r(e.element,e.initial):(a(!0),null==n||n(e.element)))},t=>{throw e.in||a(!0),t})}}),(0,M.am)(e));return o&&!t?null:(0,m.cloneElement)(e,{ref:i})}function P(e,t,n){return e?(0,_.jsx)(W,Object.assign({},n,{component:e})):t?(0,_.jsx)(H,Object.assign({},n,{transition:t})):(0,_.jsx)(B,Object.assign({},n))}let U=["show","role","className","style","children","backdrop","keyboard","onBackdropClick","onEscapeKeyDown","transition","runTransition","backdropTransition","runBackdropTransition","autoFocus","enforceFocus","restoreFocus","restoreFocusOptions","renderDialog","renderBackdrop","manager","container","onShow","onHide","onExit","onExited","onExiting","onEnter","onEntering","onEntered"],V=(0,m.forwardRef)((e,t)=>{let{show:n=!1,role:l="dialog",className:o,style:a,children:i,backdrop:s=!0,keyboard:u=!0,onBackdropClick:d,onEscapeKeyDown:f,transition:h,runTransition:g,backdropTransition:p,runBackdropTransition:R,autoFocus:w=!0,enforceFocus:C=!0,restoreFocus:O=!0,restoreFocusOptions:S,renderDialog:L,renderBackdrop:D=e=>(0,_.jsx)("div",Object.assign({},e)),manager:B,container:F,onShow:I,onHide:W=()=>{},onExit:H,onExited:V,onExiting:G,onEnter:K,onEntering:$,onEntered:z}=e,X=function(e,t){if(null==e)return{};var n={};for(var r in e)if(({}).hasOwnProperty.call(e,r)){if(t.indexOf(r)>=0)continue;n[r]=e[r]}return n}(e,U),Y=j(),q=function(e,t){let n=j(),[r,l]=(0,m.useState)(()=>k(e,null==n?void 0:n.document));if(!r){let t=k(e);t&&l(t)}return(0,m.useEffect)(()=>{},[void 0,r]),(0,m.useEffect)(()=>{let t=k(e);t!==r&&l(t)},[e,r]),r}(F),J=function(e){let t=j(),n=e||(r||(r=new T({ownerDocument:null==t?void 0:t.document})),r),l=(0,m.useRef)({dialog:null,backdrop:null});return Object.assign(l.current,{add:()=>n.add(l.current),remove:()=>n.remove(l.current),isTopModal:()=>n.isTopModal(l.current),setDialogRef:(0,m.useCallback)(e=>{l.current.dialog=e},[]),setBackdropRef:(0,m.useCallback)(e=>{l.current.backdrop=e},[])})}(B),Q=(0,A.A)(),Z=(0,x.A)(n),[ee,et]=(0,m.useState)(!n),en=(0,m.useRef)(null);(0,m.useImperativeHandle)(t,()=>J,[J]),c.A&&!Z&&n&&(en.current=v(null==Y?void 0:Y.document)),n&&ee&&et(!1);let er=(0,N.A)(()=>{if(J.add(),ec.current=(0,E.A)(document,"keydown",ei),es.current=(0,E.A)(document,"focus",()=>setTimeout(eo),!0),I&&I(),w){var e,t;let n=v(null!=(e=null==(t=J.dialog)?void 0:t.ownerDocument)?e:null==Y?void 0:Y.document);J.dialog&&n&&!b(J.dialog,n)&&(en.current=n,J.dialog.focus())}}),el=(0,N.A)(()=>{if(J.remove(),null==ec.current||ec.current(),null==es.current||es.current(),O){var e;null==(e=en.current)||null==e.focus||e.focus(S),en.current=null}});(0,m.useEffect)(()=>{n&&q&&er()},[n,q,er]),(0,m.useEffect)(()=>{ee&&el()},[ee,el]),function(e){let t=function(e){let t=(0,m.useRef)(e);return t.current=e,t}(e);(0,m.useEffect)(()=>()=>t.current(),[])}(()=>{el()});let eo=(0,N.A)(()=>{if(!C||!Q()||!J.isTopModal())return;let e=v(null==Y?void 0:Y.document);J.dialog&&e&&!b(J.dialog,e)&&J.dialog.focus()}),ea=(0,N.A)(e=>{e.target===e.currentTarget&&(null==d||d(e),!0===s&&W())}),ei=(0,N.A)(e=>{u&&(0,M.v$)(e)&&J.isTopModal()&&(null==f||f(e),e.defaultPrevented||W())}),es=(0,m.useRef)(),ec=(0,m.useRef)();if(!q)return null;let eu=Object.assign({role:l,ref:J.setDialogRef,"aria-modal":"dialog"===l||void 0},X,{style:a,className:o,tabIndex:-1}),ed=L?L(eu):(0,_.jsx)("div",Object.assign({},eu,{children:m.cloneElement(i,{role:"document"})}));ed=P(h,g,{unmountOnExit:!0,mountOnEnter:!0,appear:!0,in:!!n,onExit:H,onExiting:G,onExited:(...e)=>{et(!0),null==V||V(...e)},onEnter:K,onEntering:$,onEntered:z,children:ed});let ef=null;return s&&(ef=P(p,R,{in:!!n,appear:!0,mountOnEnter:!0,unmountOnExit:!0,children:ef=D({ref:J.setBackdropRef,onClick:ea})})),(0,_.jsx)(_.Fragment,{children:y.createPortal((0,_.jsxs)(_.Fragment,{children:[ef,ed]}),q)})});V.displayName="Modal";let G=Object.assign(V,{Manager:T});var K=Function.prototype.bind.call(Function.prototype.call,[].slice);function $(e,t){return K(e.querySelectorAll(t))}function z(e,t){return e.replace(RegExp("(^|\\s)"+t+"(?:\\s|$)","g"),"$1").replace(/\s+/g," ").replace(/^\s*|\s*$/g,"")}let X={FIXED_CONTENT:".fixed-top, .fixed-bottom, .is-fixed, .sticky-top",STICKY_CONTENT:".sticky-top",NAVBAR_TOGGLER:".navbar-toggler"};class Y extends T{adjustAndStore(e,t,n){let r=t.style[e];t.dataset[e]=r,(0,R.A)(t,{[e]:"".concat(parseFloat((0,R.A)(t,e))+n,"px")})}restore(e,t){let n=t.dataset[e];void 0!==n&&(delete t.dataset[e],(0,R.A)(t,{[e]:n}))}setContainerStyle(e){super.setContainerStyle(e);let t=this.getElement();var n="modal-open";if(t.classList?t.classList.add(n):(t.classList?n&&t.classList.contains(n):-1!==(" "+(t.className.baseVal||t.className)+" ").indexOf(" "+n+" "))||("string"==typeof t.className?t.className=t.className+" "+n:t.setAttribute("class",(t.className&&t.className.baseVal||"")+" "+n)),!e.scrollBarWidth)return;let r=this.isRTL?"paddingLeft":"paddingRight",l=this.isRTL?"marginLeft":"marginRight";$(t,X.FIXED_CONTENT).forEach(t=>this.adjustAndStore(r,t,e.scrollBarWidth)),$(t,X.STICKY_CONTENT).forEach(t=>this.adjustAndStore(l,t,-e.scrollBarWidth)),$(t,X.NAVBAR_TOGGLER).forEach(t=>this.adjustAndStore(l,t,e.scrollBarWidth))}removeContainerStyle(e){var t;super.removeContainerStyle(e);let n=this.getElement();t="modal-open",n.classList?n.classList.remove(t):"string"==typeof n.className?n.className=z(n.className,t):n.setAttribute("class",z(n.className&&n.className.baseVal||"",t));let r=this.isRTL?"paddingLeft":"paddingRight",l=this.isRTL?"marginLeft":"marginRight";$(n,X.FIXED_CONTENT).forEach(e=>this.restore(r,e)),$(n,X.STICKY_CONTENT).forEach(e=>this.restore(l,e)),$(n,X.NAVBAR_TOGGLER).forEach(e=>this.restore(l,e))}}var q=n(4748),J=n(7390);let Q=m.forwardRef((e,t)=>{let{className:n,bsPrefix:r,as:l="div",...o}=e;return r=(0,J.oU)(r,"modal-body"),(0,_.jsx)(l,{ref:t,className:i()(n,r),...o})});Q.displayName="ModalBody";let Z=m.createContext({onHide(){}}),ee=m.forwardRef((e,t)=>{let{bsPrefix:n,className:r,contentClassName:l,centered:o,size:a,fullscreen:s,children:c,scrollable:u,...d}=e;n=(0,J.oU)(n,"modal");let f="".concat(n,"-dialog"),m="string"==typeof s?"".concat(n,"-fullscreen-").concat(s):"".concat(n,"-fullscreen");return(0,_.jsx)("div",{...d,ref:t,className:i()(f,r,a&&"".concat(n,"-").concat(a),o&&"".concat(f,"-centered"),u&&"".concat(f,"-scrollable"),s&&m),children:(0,_.jsx)("div",{className:i()("".concat(n,"-content"),l),children:c})})});ee.displayName="ModalDialog";let et=m.forwardRef((e,t)=>{let{className:n,bsPrefix:r,as:l="div",...o}=e;return r=(0,J.oU)(r,"modal-footer"),(0,_.jsx)(l,{ref:t,className:i()(n,r),...o})});et.displayName="ModalFooter";var en=n(7706);let er=m.forwardRef((e,t)=>{let{closeLabel:n="Close",closeVariant:r,closeButton:l=!1,onHide:o,children:a,...i}=e,s=(0,m.useContext)(Z),c=(0,h.A)(()=>{null==s||s.onHide(),null==o||o()});return(0,_.jsxs)("div",{ref:t,...i,children:[a,l&&(0,_.jsx)(en.A,{"aria-label":n,variant:r,onClick:c})]})});er.displayName="AbstractModalHeader";let el=m.forwardRef((e,t)=>{let{bsPrefix:n,className:r,closeLabel:l="Close",closeButton:o=!1,...a}=e;return n=(0,J.oU)(n,"modal-header"),(0,_.jsx)(er,{ref:t,...a,className:i()(r,n),closeLabel:l,closeButton:o})});el.displayName="ModalHeader";let eo=(0,n(8724).A)("h4"),ea=m.forwardRef((e,t)=>{let{className:n,bsPrefix:r,as:l=eo,...o}=e;return r=(0,J.oU)(r,"modal-title"),(0,_.jsx)(l,{ref:t,className:i()(n,r),...o})});function ei(e){return(0,_.jsx)(q.A,{...e,timeout:null})}function es(e){return(0,_.jsx)(q.A,{...e,timeout:null})}ea.displayName="ModalTitle";let ec=m.forwardRef((e,t)=>{let{bsPrefix:n,className:r,style:o,dialogClassName:a,contentClassName:v,children:b,dialogAs:E=ee,"data-bs-theme":y,"aria-labelledby":A,"aria-describedby":x,"aria-label":N,show:R=!1,animation:w=!0,backdrop:C=!0,keyboard:T=!0,onEscapeKeyDown:O,onShow:j,onHide:k,container:S,autoFocus:L=!0,enforceFocus:D=!0,restoreFocus:M=!0,restoreFocusOptions:B,onEntered:F,onExit:I,onExiting:W,onEnter:H,onEntering:P,onExited:U,backdropClassName:V,manager:K,...$}=e,[z,X]=(0,m.useState)({}),[q,Q]=(0,m.useState)(!1),et=(0,m.useRef)(!1),en=(0,m.useRef)(!1),er=(0,m.useRef)(null),[el,eo]=(0,m.useState)(null),ea=(0,g.A)(t,eo),ec=(0,h.A)(k),eu=(0,J.Wz)();n=(0,J.oU)(n,"modal");let ed=(0,m.useMemo)(()=>({onHide:ec}),[ec]);function ef(){var e;return K?K:(e={isRTL:eu},l||(l=new Y(e)),l)}function em(e){if(!c.A)return;let t=ef().getScrollbarWidth()>0,n=e.scrollHeight>(0,u.A)(e).documentElement.clientHeight;X({paddingRight:t&&!n?f():void 0,paddingLeft:!t&&n?f():void 0})}let eh=(0,h.A)(()=>{el&&em(el.dialog)});!function(e){let t=function(e){let t=(0,m.useRef)(e);return t.current=e,t}(e);(0,m.useEffect)(()=>()=>t.current(),[])}(()=>{(0,d.A)(window,"resize",eh),null==er.current||er.current()});let eg=()=>{et.current=!0},ep=e=>{et.current&&el&&e.target===el.dialog&&(en.current=!0),et.current=!1},ev=()=>{Q(!0),er.current=(0,p.A)(el.dialog,()=>{Q(!1)})},eb=e=>{e.target===e.currentTarget&&ev()},eE=e=>{if("static"===C)return void eb(e);if(en.current||e.target!==e.currentTarget){en.current=!1;return}null==k||k()},ey=(0,m.useCallback)(e=>(0,_.jsx)("div",{...e,className:i()("".concat(n,"-backdrop"),V,!w&&"show")}),[w,V,n]),eA={...o,...z};return eA.display="block",(0,_.jsx)(Z.Provider,{value:ed,children:(0,_.jsx)(G,{show:R,ref:ea,backdrop:C,container:S,keyboard:!0,autoFocus:L,enforceFocus:D,restoreFocus:M,restoreFocusOptions:B,onEscapeKeyDown:e=>{T?null==O||O(e):(e.preventDefault(),"static"===C&&ev())},onShow:j,onHide:k,onEnter:(e,t)=>{e&&em(e),null==H||H(e,t)},onEntering:(e,t)=>{null==P||P(e,t),(0,s.Ay)(window,"resize",eh)},onEntered:F,onExit:e=>{null==er.current||er.current(),null==I||I(e)},onExiting:W,onExited:e=>{e&&(e.style.display=""),null==U||U(e),(0,d.A)(window,"resize",eh)},manager:ef(),transition:w?ei:void 0,backdropTransition:w?es:void 0,renderBackdrop:ey,renderDialog:e=>(0,_.jsx)("div",{role:"dialog",...e,style:eA,className:i()(r,n,q&&"".concat(n,"-static"),!w&&"show"),onClick:C?eE:void 0,onMouseUp:ep,"data-bs-theme":y,"aria-label":N,"aria-labelledby":A,"aria-describedby":x,children:(0,_.jsx)(E,{...$,onMouseDown:eg,className:a,contentClassName:v,children:b})})})})});ec.displayName="Modal";let eu=Object.assign(ec,{Body:Q,Header:el,Title:ea,Footer:et,Dialog:ee,TRANSITION_DURATION:300,BACKDROP_TRANSITION_DURATION:150})}}]);