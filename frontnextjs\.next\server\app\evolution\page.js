(()=>{var e={};e.id=76,e.ids=[76],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11227:(e,t,n)=>{"use strict";n.d(t,{default:()=>r});let r=(0,n(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\AI-Code\\\\fire_UI\\\\frontnextjs\\\\src\\\\components\\\\BootstrapClient.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\AI-Code\\fire_UI\\frontnextjs\\src\\components\\BootstrapClient.tsx","default")},12412:e=>{"use strict";e.exports=require("assert")},13461:(e,t,n)=>{"use strict";n.r(t),n.d(t,{GlobalError:()=>a.a,__next_app__:()=>h,pages:()=>c,routeModule:()=>u,tree:()=>d});var r=n(65239),i=n(48088),o=n(88170),a=n.n(o),s=n(30893),l={};for(let e in s)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>s[e]);n.d(t,l);let d={children:["",{children:["evolution",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(n.bind(n,91406)),"D:\\AI-Code\\fire_UI\\frontnextjs\\src\\app\\evolution\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(n.bind(n,70440))).default(e),async e=>(await Promise.resolve().then(n.bind(n,78162))).default(e)],apple:[async e=>(await Promise.resolve().then(n.bind(n,94650))).default(e)],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(n.bind(n,94431)),"D:\\AI-Code\\fire_UI\\frontnextjs\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(n.t.bind(n,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(n.t.bind(n,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(n.t.bind(n,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(n.bind(n,70440))).default(e),async e=>(await Promise.resolve().then(n.bind(n,78162))).default(e)],apple:[async e=>(await Promise.resolve().then(n.bind(n,94650))).default(e)],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["D:\\AI-Code\\fire_UI\\frontnextjs\\src\\app\\evolution\\page.tsx"],h={require:n,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/evolution/page",pathname:"/evolution",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},24255:(e,t,n)=>{Promise.resolve().then(n.t.bind(n,86346,23)),Promise.resolve().then(n.t.bind(n,27924,23)),Promise.resolve().then(n.t.bind(n,35656,23)),Promise.resolve().then(n.t.bind(n,40099,23)),Promise.resolve().then(n.t.bind(n,38243,23)),Promise.resolve().then(n.t.bind(n,28827,23)),Promise.resolve().then(n.t.bind(n,62763,23)),Promise.resolve().then(n.t.bind(n,97173,23))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29190:(e,t,n)=>{"use strict";n.d(t,{default:()=>s});var r=n(60687),i=n(43210),o=n(85814),a=n.n(o);let s=()=>{let[e,t]=(0,i.useState)(!1),n=()=>{t(!1)};return(0,r.jsx)("nav",{className:"navbar navbar-expand-lg navbar-dark bg-dark",children:(0,r.jsxs)("div",{className:"container-fluid d-flex",children:[(0,r.jsx)(a(),{className:"navbar-brand me-3",href:"/",onClick:n,children:"FIRE Dashboard"}),(0,r.jsxs)("ul",{className:"navbar-nav d-none d-lg-flex flex-row",children:[(0,r.jsx)("li",{className:"nav-item",children:(0,r.jsx)(a(),{className:"nav-link",href:"/",onClick:n,children:"Dashboard"})}),(0,r.jsx)("li",{className:"nav-item",children:(0,r.jsx)(a(),{className:"nav-link",href:"/assets",onClick:n,children:"Patrimoine"})}),(0,r.jsx)("li",{className:"nav-item",children:(0,r.jsx)(a(),{className:"nav-link",href:"/liabilities",onClick:n,children:"Emprunts"})}),(0,r.jsx)("li",{className:"nav-item",children:(0,r.jsx)(a(),{className:"nav-link",href:"/scpi",onClick:n,children:"SCPI"})}),(0,r.jsx)("li",{className:"nav-item",children:(0,r.jsx)(a(),{className:"nav-link",href:"/evolution",onClick:n,children:"\xc9volution"})}),(0,r.jsx)("li",{className:"nav-item",children:(0,r.jsx)(a(),{className:"nav-link",href:"/budget",onClick:n,children:"Budget FIRE"})}),(0,r.jsx)("li",{className:"nav-item",children:(0,r.jsx)(a(),{className:"nav-link",href:"/scenarios",onClick:n,children:"Sc\xe9narios"})}),(0,r.jsx)("li",{className:"nav-item",children:(0,r.jsx)(a(),{className:"nav-link",href:"/fire",onClick:n,children:"Objectif FIRE"})}),(0,r.jsx)("li",{className:"nav-item",children:(0,r.jsx)(a(),{className:"nav-link",href:"/test",onClick:n,children:"Test API"})})]}),(0,r.jsx)("button",{className:"navbar-toggler d-lg-none",type:"button",onClick:()=>{t(!e)},"aria-controls":"navbarNavContent","aria-expanded":e,"aria-label":"Toggle navigation",children:(0,r.jsx)("span",{className:"navbar-toggler-icon"})}),e&&(0,r.jsx)("div",{className:"navbar-collapse show d-lg-none position-absolute top-100 start-0 w-100 bg-dark",children:(0,r.jsxs)("ul",{className:"navbar-nav",children:[(0,r.jsx)("li",{className:"nav-item",children:(0,r.jsx)(a(),{className:"nav-link",href:"/",onClick:n,children:"Dashboard"})}),(0,r.jsx)("li",{className:"nav-item",children:(0,r.jsx)(a(),{className:"nav-link",href:"/assets",onClick:n,children:"Patrimoine"})}),(0,r.jsx)("li",{className:"nav-item",children:(0,r.jsx)(a(),{className:"nav-link",href:"/liabilities",onClick:n,children:"Emprunts"})}),(0,r.jsx)("li",{className:"nav-item",children:(0,r.jsx)(a(),{className:"nav-link",href:"/scpi",onClick:n,children:"SCPI"})}),(0,r.jsx)("li",{className:"nav-item",children:(0,r.jsx)(a(),{className:"nav-link",href:"/evolution",onClick:n,children:"\xc9volution"})}),(0,r.jsx)("li",{className:"nav-item",children:(0,r.jsx)(a(),{className:"nav-link",href:"/budget",onClick:n,children:"Budget FIRE"})}),(0,r.jsx)("li",{className:"nav-item",children:(0,r.jsx)(a(),{className:"nav-link",href:"/scenarios",onClick:n,children:"Sc\xe9narios"})}),(0,r.jsx)("li",{className:"nav-item",children:(0,r.jsx)(a(),{className:"nav-link",href:"/fire",onClick:n,children:"Objectif FIRE"})}),(0,r.jsx)("li",{className:"nav-item",children:(0,r.jsx)(a(),{className:"nav-link",href:"/test",onClick:n,children:"Test API"})})]})})]})})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30004:(e,t,n)=>{"use strict";n.d(t,{default:()=>r});let r=(0,n(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\AI-Code\\\\fire_UI\\\\frontnextjs\\\\src\\\\components\\\\Navbar.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\AI-Code\\fire_UI\\frontnextjs\\src\\components\\Navbar.tsx","default")},33337:(e,t,n)=>{Promise.resolve().then(n.bind(n,44999))},33873:e=>{"use strict";e.exports=require("path")},44999:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>e0});var r=n(60687),i=n(43210),o=n(90317),a=n(48710),s=n(31889),l=n(92388),d=n(25865),c=n(29947),h=n(54758),u=n(58022);let p={modes:{point:(e,t)=>m(e,t,{intersect:!0}),nearest:(e,t,n)=>(function(e,t,n){let r=Number.POSITIVE_INFINITY;return m(e,t,n).reduce((e,i)=>{var o;let a=i.getCenterPoint(),s="x"===(o=n.axis)?{x:t.x,y:a.y}:"y"===o?{x:a.x,y:t.y}:a,l=(0,u.aF)(t,s);return l<r?(e=[i],r=l):l===r&&e.push(i),e},[]).sort((e,t)=>e._index-t._index).slice(0,1)})(e,t,n),x:(e,t,n)=>m(e,t,{intersect:n.intersect,axis:"x"}),y:(e,t,n)=>m(e,t,{intersect:n.intersect,axis:"y"})}};function x(e,t,n){return(p.modes[n.mode]||p.modes.nearest)(e,t,n)}function m(e,t,n){return e.filter(e=>{var r;return n.intersect?e.inRange(t.x,t.y):"x"!==(r=n.axis)&&"y"!==r?e.inRange(t.x,t.y,"x",!0)||e.inRange(t.x,t.y,"y",!0):e.inRange(t.x,t.y,r,!0)})}function f(e,t,n){let r=Math.cos(n),i=Math.sin(n),o=t.x,a=t.y;return{x:o+r*(e.x-o)-i*(e.y-a),y:a+i*(e.x-o)+r*(e.y-a)}}let b=(e,t)=>t>e||e.length>t.length&&e.slice(0,t.length)===t,v=(e,t,n)=>Math.min(n,Math.max(t,e)),g=(e,t)=>e.value>=e.start-t&&e.value<=e.end+t;function y(e,{x:t,y:n,x2:r,y2:i},o,{borderWidth:a,hitTolerance:s}){let l=(a+s)/2,d=e.x>=t-l-.001&&e.x<=r+l+.001,c=e.y>=n-l-.001&&e.y<=i+l+.001;return"x"===o?d:"y"===o?c:d&&c}function j(e,{rect:t,center:n},r,{rotation:i,borderWidth:o,hitTolerance:a}){return y(f(e,n,(0,u.t)(-i)),t,r,{borderWidth:o,hitTolerance:a})}function w(e,t){let{centerX:n,centerY:r}=e.getProps(["centerX","centerY"],t);return{x:n,y:r}}let C=e=>"string"==typeof e&&e.endsWith("%"),M=e=>parseFloat(e)/100,k=e=>v(M(e),0,1),P=(e,t)=>({x:e,y:t,x2:e,y2:t,width:0,height:0}),A={box:e=>P(e.centerX,e.centerY),doughnutLabel:e=>P(e.centerX,e.centerY),ellipse:e=>({centerX:e.centerX,centerY:e.centerX,radius:0,width:0,height:0}),label:e=>P(e.centerX,e.centerY),line:e=>P(e.x,e.y),point:e=>({centerX:e.centerX,centerY:e.centerY,radius:0,width:0,height:0}),polygon:e=>P(e.centerX,e.centerY)};function N(e,t){return"start"===t?0:"end"===t?e:C(t)?k(t)*e:e/2}function S(e,t,n=!0){return"number"==typeof t?t:C(t)?(n?k(t):M(t))*e:e}function _(e,t,{borderWidth:n,position:r,xAdjust:i,yAdjust:o},a){let s=(0,u.i)(a),l=t.width+(s?a.width:0)+n,d=t.height+(s?a.height:0)+n,c=T(r),h=R(e.x,l,i,c.x),p=R(e.y,d,o,c.y);return{x:h,y:p,x2:h+l,y2:p+d,width:l,height:d,centerX:h+l/2,centerY:p+d/2}}function T(e,t="center"){return(0,u.i)(e)?{x:(0,u.v)(e.x,t),y:(0,u.v)(e.y,t)}:{x:e=(0,u.v)(e,t),y:e}}let D=(e,t)=>e&&e.autoFit&&t<1;function I(e,t){let n=e.font,r=(0,u.b)(n)?n:[n];return D(e,t)?r.map(function(e){let n=(0,u.a0)(e);return n.size=Math.floor(e.size*t),n.lineHeight=e.lineHeight,(0,u.a0)(n)}):r.map(e=>(0,u.a0)(e))}function E(e){return e&&((0,u.h)(e.xValue)||(0,u.h)(e.yValue))}function R(e,t,n=0,r){return e-N(t,r)+n}function O(e,t,n){let r=n.init;return r?!0===r?W(t,n):function(e,t,n){let r=(0,u.Q)(n.init,[{chart:e,properties:t,options:n}]);return!0===r?W(t,n):(0,u.i)(r)?r:void 0}(e,t,n):void 0}function Y(e,t,n){let r=!1;return t.forEach(t=>{(0,u.a7)(e[t])?(r=!0,n[t]=e[t]):(0,u.h)(n[t])&&delete n[t]}),r}function W(e,t){return A[t.type||"line"](e)}let F=new Map,X=e=>isNaN(e)||e<=0,q=e=>e.reduce(function(e,t){return e+t.string},"");function z(e){if(e&&"object"==typeof e){let t=e.toString();return"[object HTMLImageElement]"===t||"[object HTMLCanvasElement]"===t}}function H(e,{x:t,y:n},r){r&&(e.translate(t,n),e.rotate((0,u.t)(r)),e.translate(-t,-n))}function L(e,t){if(t&&t.borderWidth)return e.lineCap=t.borderCapStyle||"butt",e.setLineDash(t.borderDash),e.lineDashOffset=t.borderDashOffset,e.lineJoin=t.borderJoinStyle||"miter",e.lineWidth=t.borderWidth,e.strokeStyle=t.borderColor,!0}function V(e,t){e.shadowColor=t.backgroundShadowColor,e.shadowBlur=t.shadowBlur,e.shadowOffsetX=t.shadowOffsetX,e.shadowOffsetY=t.shadowOffsetY}function U(e,t){let n=t.content;if(z(n))return{width:S(n.width,t.width),height:S(n.height,t.height)};let r=I(t),i=t.textStrokeWidth,o=(0,u.b)(n)?n:[n],a=o.join()+q(r)+i+(e._measureText?"-spriting":"");return F.has(a)||F.set(a,function(e,t,n,r){e.save();let i=t.length,o=0,a=r;for(let s=0;s<i;s++){let i=n[Math.min(s,n.length-1)];e.font=i.string;let l=t[s];o=Math.max(o,e.measureText(l).width+r),a+=i.lineHeight}return e.restore(),{width:o,height:a}}(e,o,r,i)),F.get(a)}function $(e,t,n){let{x:r,y:i,width:o,height:a}=t;e.save(),V(e,n);let s=L(e,n);e.fillStyle=n.backgroundColor,e.beginPath(),(0,u.aw)(e,{x:r,y:i,w:o,h:a,radius:function(e,t,n){for(let t of Object.keys(e))e[t]=v(e[t],0,n);return e}((0,u.ay)(n.borderRadius),0,Math.min(o,a)/2)}),e.closePath(),e.fill(),s&&(e.shadowColor=n.borderShadowColor,e.stroke()),e.restore()}function B(e,t,n,r){let i=n.content;if(z(i)){e.save(),e.globalAlpha=function(e,t){let n=(0,u.x)(e)?e:t;return(0,u.x)(n)?v(n,0,1):1}(n.opacity,i.style.opacity),e.drawImage(i,t.x,t.y,t.width,t.height),e.restore();return}let o=(0,u.b)(i)?i:[i],a=I(n,r),s=n.color,l=(0,u.b)(s)?s:[s],d=function(e,t){let{x:n,width:r}=e,i=t.textAlign;return"center"===i?n+r/2:"end"===i||"right"===i?n+r:n}(t,n),c=t.y+n.textStrokeWidth/2;e.save(),e.textBaseline="middle",e.textAlign=n.textAlign,function(e,t){if(t.textStrokeWidth>0)return e.lineJoin="round",e.miterLimit=2,e.lineWidth=t.textStrokeWidth,e.strokeStyle=t.textStrokeColor,!0}(e,n)&&function(e,{x:t,y:n},r,i){e.beginPath();let o=0;r.forEach(function(r,a){let s=i[Math.min(a,i.length-1)],l=s.lineHeight;e.font=s.string,e.strokeText(r,t,n+l/2+o),o+=l}),e.stroke()}(e,{x:d,y:c},o,a),function(e,{x:t,y:n},r,{fonts:i,colors:o}){let a=0;r.forEach(function(r,s){let l=o[Math.min(s,o.length-1)],d=i[Math.min(s,i.length-1)],c=d.lineHeight;e.beginPath(),e.font=d.string,e.fillStyle=l,e.fillText(r,t,n+c/2+a),a+=c,e.fill()})}(e,{x:d,y:c},o,{fonts:a,colors:l}),e.restore()}let G=["left","bottom","top","right"],J={xScaleID:{min:"xMin",max:"xMax",start:"left",end:"right",startProp:"x",endProp:"x2"},yScaleID:{min:"yMin",max:"yMax",start:"bottom",end:"top",startProp:"y",endProp:"y2"}};function Q(e,t,n){return t="number"==typeof t?t:e.parse(t),(0,u.g)(t)?e.getPixelForValue(t):n}function K(e,t,n){let r=t[n];if(r||"scaleID"===n)return r;let i=n.charAt(0),o=Object.values(e).filter(e=>e.axis&&e.axis===i);return o.length?o[0].id:i}function Z(e,t){if(e){let n=e.options.reverse;return{start:Q(e,t.min,n?t.end:t.start),end:Q(e,t.max,n?t.start:t.end)}}}function ee(e,t){let{chartArea:n,scales:r}=e,i=r[K(r,t,"xScaleID")],o=r[K(r,t,"yScaleID")],a=n.width/2,s=n.height/2;return i&&(a=Q(i,t.xValue,i.left+i.width/2)),o&&(s=Q(o,t.yValue,o.top+o.height/2)),{x:a,y:s}}function et(e,t){let n=e.scales,r=n[K(n,t,"xScaleID")],i=n[K(n,t,"yScaleID")];if(!r&&!i)return{};let{left:o,right:a}=r||e.chartArea,{top:s,bottom:l}=i||e.chartArea,d=ei(r,{min:t.xMin,max:t.xMax,start:o,end:a});o=d.start,a=d.end;let c=ei(i,{min:t.yMin,max:t.yMax,start:l,end:s});return{x:o,y:s=c.start,x2:a,y2:l=c.end,width:a-o,height:l-s,centerX:o+(a-o)/2,centerY:s+(l-s)/2}}function en(e,t){if(!E(t)){let n=et(e,t),r=t.radius;(!r||isNaN(r))&&(t.radius=r=Math.min(n.width,n.height)/2);let i=2*r,o=n.centerX+t.xAdjust,a=n.centerY+t.yAdjust;return{x:o-r,y:a-r,x2:o+r,y2:a+r,centerX:o,centerY:a,width:i,height:i,radius:r}}var n=e,r=t;let i=ee(n,r),o=2*r.radius;return{x:i.x-r.radius+r.xAdjust,y:i.y-r.radius+r.yAdjust,x2:i.x+r.radius+r.xAdjust,y2:i.y+r.radius+r.yAdjust,centerX:i.x+r.xAdjust,centerY:i.y+r.yAdjust,radius:r.radius,width:o,height:o}}function er(e,t){let n=et(e,t);return n.initProperties=O(e,n,t),n.elements=[{type:"label",optionScope:"label",properties:function(e,t,n){let r=n.label;r.backgroundColor="transparent",r.callout.display=!1;let i=T(r.position),o=(0,u.E)(r.padding),a=U(e.ctx,r),s=function({properties:e,options:t},n,r,i){let{x:o,x2:a,width:s}=e;return eo({start:o,end:a,size:s,borderWidth:t.borderWidth},{position:r.x,padding:{start:i.left,end:i.right},adjust:t.label.xAdjust,size:n.width})}({properties:t,options:n},a,i,o),l=function({properties:e,options:t},n,r,i){let{y:o,y2:a,height:s}=e;return eo({start:o,end:a,size:s,borderWidth:t.borderWidth},{position:r.y,padding:{start:i.top,end:i.bottom},adjust:t.label.yAdjust,size:n.height})}({properties:t,options:n},a,i,o),d=a.width+o.width,c=a.height+o.height;return{x:s,y:l,x2:s+d,y2:l+c,width:d,height:c,centerX:s+d/2,centerY:l+c/2,rotation:r.rotation}}(e,n,t),initProperties:n.initProperties}],n}function ei(e,t){let n=Z(e,t)||t;return{start:Math.min(n.start,n.end),end:Math.max(n.start,n.end)}}function eo(e,t){let{start:n,end:r,borderWidth:i}=e,{position:o,padding:{start:a,end:s},adjust:l}=t,d=r-i-n-a-s-t.size;return n+i/2+l+N(d,o)}let ea=["enter","leave"],es=ea.concat("click");function el({state:e,event:t},n,r,i){let o;for(let a of r)0>i.indexOf(a)&&(o=ed(a.options[n]||e.listeners[n],a,t)||o);return o}function ed(e,t,n){return!0===(0,u.Q)(e,[t.$context,n])}let ec=["afterDraw","beforeDraw"];function eh(e,t,n){if(e.hooked){let r=t.options[n]||e.hooks[n];return(0,u.Q)(r,[t.$context])}}function eu(e,t,n,r){var i,o,a;if((0,u.g)(t[n])&&(i=e.options,o=n,a=r,!((0,u.h)(i[o])||(0,u.h)(i[a])))){let r=e[n]!==t[n];return e[n]=t[n],r}}function ep(e,t,n,r){for(let i of n){let n=e[i];if((0,u.h)(n)){let e=t.parse(n);r.min=Math.min(r.min,e),r.max=Math.max(r.max,e)}}}class ex extends h.Hg{inRange(e,t,n,r){let{x:i,y:o}=f({x:e,y:t},this.getCenterPoint(r),(0,u.t)(-this.options.rotation));return y({x:i,y:o},this.getProps(["x","y","x2","y2"],r),n,this.options)}getCenterPoint(e){return w(this,e)}draw(e){e.save(),H(e,this.getCenterPoint(),this.options.rotation),$(e,this,this.options),e.restore()}get label(){return this.elements&&this.elements[0]}resolveElementProperties(e,t){return er(e,t)}}ex.id="boxAnnotation",ex.defaults={adjustScaleRange:!0,backgroundShadowColor:"transparent",borderCapStyle:"butt",borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",borderRadius:0,borderShadowColor:"transparent",borderWidth:1,display:!0,init:void 0,hitTolerance:0,label:{backgroundColor:"transparent",borderWidth:0,callout:{display:!1},color:"black",content:null,display:!1,drawTime:void 0,font:{family:void 0,lineHeight:void 0,size:void 0,style:void 0,weight:"bold"},height:void 0,hitTolerance:void 0,opacity:void 0,padding:6,position:"center",rotation:void 0,textAlign:"start",textStrokeColor:void 0,textStrokeWidth:0,width:void 0,xAdjust:0,yAdjust:0,z:void 0},rotation:0,shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,xMax:void 0,xMin:void 0,xScaleID:void 0,yMax:void 0,yMin:void 0,yScaleID:void 0,z:0},ex.defaultRoutes={borderColor:"color",backgroundColor:"color"},ex.descriptors={label:{_fallback:!0}};class em extends h.Hg{inRange(e,t,n,r){return j({x:e,y:t},{rect:this.getProps(["x","y","x2","y2"],r),center:this.getCenterPoint(r)},n,{rotation:this.rotation,borderWidth:0,hitTolerance:this.options.hitTolerance})}getCenterPoint(e){return w(this,e)}draw(e){let t=this.options;t.display&&t.content&&(function(e,t){let{_centerX:n,_centerY:r,_radius:i,_startAngle:o,_endAngle:a,_counterclockwise:s,options:l}=t;e.save();let d=L(e,l);e.fillStyle=l.backgroundColor,e.beginPath(),e.arc(n,r,i,o,a,s),e.closePath(),e.fill(),d&&e.stroke(),e.restore()}(e,this),e.save(),H(e,this.getCenterPoint(),this.rotation),B(e,this,t,this._fitRatio),e.restore())}resolveElementProperties(e,t){var n,r;let i=(n=e,r=t,n.getSortedVisibleDatasetMetas().reduce(function(e,t){let i=t.controller;return i instanceof h.ju&&function(e,t,n){if(!t.autoHide)return!0;for(let t=0;t<n.length;t++)if(!n[t].hidden&&e.getDataVisibility(t))return!0}(n,r,t.data)&&(!e||i.innerRadius<e.controller.innerRadius)&&i.options.circumference>=90?t:e},void 0));if(!i)return{};let{controllerMeta:o,point:a,radius:s}=function({chartArea:e},t,n){let{left:r,top:i,right:o,bottom:a}=e,{innerRadius:s,offsetX:l,offsetY:d}=n.controller,c=(r+o)/2+l,h=(i+a)/2+d,p={left:Math.max(c-s,r),right:Math.min(c+s,o),top:Math.max(h-s,i),bottom:Math.min(h+s,a)},x={x:(p.left+p.right)/2,y:(p.top+p.bottom)/2},m=t.spacing+t.borderWidth/2,f=s-m,b=x.y>h,v=function(e,t,n,r){let i=-2*t,o=Math.pow(i,2)-4*(Math.pow(t,2)+Math.pow(n-e,2)-Math.pow(r,2));if(o<=0)return{_startAngle:0,_endAngle:u.T};let a=(-i-Math.sqrt(o))/2,s=(-i+Math.sqrt(o))/2;return{_startAngle:(0,u.D)({x:t,y:n},{x:a,y:e}).angle,_endAngle:(0,u.D)({x:t,y:n},{x:s,y:e}).angle}}(b?i+m:a-m,c,h,f);return{controllerMeta:{_centerX:c,_centerY:h,_radius:f,_counterclockwise:b,...v},point:x,radius:Math.min(s,Math.min(p.right-p.left,p.bottom-p.top)/2)}}(e,t,i),l=U(e.ctx,t),d=function({width:e,height:t},n){return 2*n/Math.sqrt(Math.pow(e,2)+Math.pow(t,2))}(l,s);D(t,d)&&(l={width:l.width*d,height:l.height*d});let{position:c,xAdjust:p,yAdjust:x}=t,m=_(a,l,{borderWidth:0,position:c,xAdjust:p,yAdjust:x});return{initProperties:O(e,m,t),...m,...o,rotation:t.rotation,_fitRatio:d}}}em.id="doughnutLabelAnnotation",em.defaults={autoFit:!0,autoHide:!0,backgroundColor:"transparent",backgroundShadowColor:"transparent",borderColor:"transparent",borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",borderShadowColor:"transparent",borderWidth:0,color:"black",content:null,display:!0,font:{family:void 0,lineHeight:void 0,size:void 0,style:void 0,weight:void 0},height:void 0,hitTolerance:0,init:void 0,opacity:void 0,position:"center",rotation:0,shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,spacing:1,textAlign:"center",textStrokeColor:void 0,textStrokeWidth:0,width:void 0,xAdjust:0,yAdjust:0},em.defaultRoutes={};class ef extends h.Hg{inRange(e,t,n,r){return j({x:e,y:t},{rect:this.getProps(["x","y","x2","y2"],r),center:this.getCenterPoint(r)},n,{rotation:this.rotation,borderWidth:this.options.borderWidth,hitTolerance:this.options.hitTolerance})}getCenterPoint(e){return w(this,e)}draw(e){let t=this.options,n=!(0,u.h)(this._visible)||this._visible;t.display&&t.content&&n&&(e.save(),H(e,this.getCenterPoint(),this.rotation),function(e,t){let{pointX:n,pointY:r,options:i}=t,o=i.callout,a=o&&o.display&&function(e,t){let n=t.position;return G.includes(n)?n:function(e,t){let{x:n,y:r,x2:i,y2:o,width:a,height:s,pointX:l,pointY:d,centerX:c,centerY:h,rotation:p}=e,x={x:c,y:h},m=t.start,b=S(a,m),v=S(s,m),g=[n,n+b,n+b,i],y=[r+v,o,r,o],j=[];for(let e=0;e<4;e++){let t=f({x:g[e],y:y[e]},x,(0,u.t)(p));j.push({position:G[e],distance:(0,u.aF)(t,{x:l,y:d})})}return j.sort((e,t)=>e.distance-t.distance)[0].position}(e,t)}(t,o);if(!a||function(e,t,n){let{pointX:r,pointY:i}=e,o=t.margin,a=r,s=i;return"left"===n?a+=o:"right"===n?a-=o:"top"===n?s+=o:"bottom"===n&&(s-=o),e.inRange(a,s)}(t,o,a))return;if(e.save(),e.beginPath(),!L(e,o))return e.restore();let{separatorStart:s,separatorEnd:l}=function(e,t){let n,r,{x:i,y:o,x2:a,y2:s}=e,l=function(e,t){let{width:n,height:r,options:i}=e,o=i.callout.margin+i.borderWidth/2;return"right"===t?n+o:"bottom"===t?r+o:-o}(e,t);return r="left"===t||"right"===t?{x:(n={x:i+l,y:o}).x,y:s}:{x:a,y:(n={x:i,y:o+l}).y},{separatorStart:n,separatorEnd:r}}(t,a),{sideStart:d,sideEnd:c}=function(e,t,n){let r,i,{y:o,width:a,height:s,options:l}=e,d=l.callout.start,c=function(e,t){let n=t.side;return"left"===e||"top"===e?-n:n}(t,l.callout);return i="left"===t||"right"===t?{x:(r={x:n.x,y:o+S(s,d)}).x+c,y:r.y}:{x:(r={x:n.x+S(a,d),y:n.y}).x,y:r.y+c},{sideStart:r,sideEnd:i}}(t,a,s);(o.margin>0||0===i.borderWidth)&&(e.moveTo(s.x,s.y),e.lineTo(l.x,l.y)),e.moveTo(d.x,d.y),e.lineTo(c.x,c.y);let h=f({x:n,y:r},t.getCenterPoint(),(0,u.t)(-t.rotation));e.lineTo(h.x,h.y),e.stroke(),e.restore()}(e,this),$(e,this,t),B(e,function({x:e,y:t,width:n,height:r,options:i}){let o=i.borderWidth/2,a=(0,u.E)(i.padding);return{x:e+a.left+o,y:t+a.top+o,width:n-a.left-a.right-i.borderWidth,height:r-a.top-a.bottom-i.borderWidth}}(this),t),e.restore())}resolveElementProperties(e,t){let n;if(E(t))n=ee(e,t);else{let{centerX:r,centerY:i}=et(e,t);n={x:r,y:i}}let r=(0,u.E)(t.padding),i=_(n,U(e.ctx,t),t,r);return{initProperties:O(e,i,t),pointX:n.x,pointY:n.y,...i,rotation:t.rotation}}}ef.id="labelAnnotation",ef.defaults={adjustScaleRange:!0,backgroundColor:"transparent",backgroundShadowColor:"transparent",borderCapStyle:"butt",borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",borderRadius:0,borderShadowColor:"transparent",borderWidth:0,callout:{borderCapStyle:"butt",borderColor:void 0,borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",borderWidth:1,display:!1,margin:5,position:"auto",side:5,start:"50%"},color:"black",content:null,display:!0,font:{family:void 0,lineHeight:void 0,size:void 0,style:void 0,weight:void 0},height:void 0,hitTolerance:0,init:void 0,opacity:void 0,padding:6,position:"center",rotation:0,shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,textAlign:"center",textStrokeColor:void 0,textStrokeWidth:0,width:void 0,xAdjust:0,xMax:void 0,xMin:void 0,xScaleID:void 0,xValue:void 0,yAdjust:0,yMax:void 0,yMin:void 0,yScaleID:void 0,yValue:void 0,z:0},ef.defaultRoutes={borderColor:"color"};let eb=(e,t,n)=>({x:e.x+n*(t.x-e.x),y:e.y+n*(t.y-e.y)}),ev=(e,t,n)=>eb(t,n,Math.abs((e-t.y)/(n.y-t.y))).x,eg=(e,t,n)=>eb(t,n,Math.abs((e-t.x)/(n.x-t.x))).y,ey=e=>e*e,ej=(e,t,{x:n,y:r,x2:i,y2:o},a)=>"y"===a?{start:Math.min(r,o),end:Math.max(r,o),value:t}:{start:Math.min(n,i),end:Math.max(n,i),value:e},ew=(e,t,n,r)=>(1-r)*(1-r)*e+2*(1-r)*r*t+r*r*n,eC=(e,t,n,r)=>({x:ew(e.x,t.x,n.x,r),y:ew(e.y,t.y,n.y,r)}),eM=(e,t,n,r)=>2*(1-r)*(t-e)+2*r*(n-t),ek=(e,t,n,r)=>-Math.atan2(eM(e.x,t.x,n.x,r),eM(e.y,t.y,n.y,r))+.5*u.P;class eP extends h.Hg{inRange(e,t,n,r){let i=(this.options.borderWidth+this.options.hitTolerance)/2;if("x"!==n&&"y"!==n){let n={mouseX:e,mouseY:t},{path:o,ctx:a}=this;if(o){L(a,this.options),a.lineWidth+=this.options.hitTolerance;let{chart:i}=this.$context,s=e*i.currentDevicePixelRatio,l=t*i.currentDevicePixelRatio,d=a.isPointInStroke(o,s,l)||eS(this,n,r);return a.restore(),d}return function(e,{mouseX:t,mouseY:n},r=.001,i){let o,a,{x:s,y:l,x2:d,y2:c}=e.getProps(["x","y","x2","y2"],i),h=d-s,u=c-l,p=ey(h)+ey(u),x=0===p?-1:((t-s)*h+(n-l)*u)/p;return x<0?(o=s,a=l):x>1?(o=d,a=c):(o=s+x*h,a=l+x*u),ey(t-o)+ey(n-a)<=r}(this,n,ey(i),r)||eS(this,n,r)}return function(e,{mouseX:t,mouseY:n},r,{hitSize:i,useFinalPosition:o}){return g(ej(t,n,e.getProps(["x","y","x2","y2"],o),r),i)||eS(e,{mouseX:t,mouseY:n},o,r)}(this,{mouseX:e,mouseY:t},n,{hitSize:i,useFinalPosition:r})}getCenterPoint(e){return w(this,e)}draw(e){let{x:t,y:n,x2:r,y2:i,cp:o,options:a}=this;if(e.save(),!L(e,a))return e.restore();V(e,a);let s=Math.sqrt(Math.pow(r-t,2)+Math.pow(i-n,2));if(a.curve&&o)return function(e,t,n,r){let{x:i,y:o,x2:a,y2:s,options:l}=t,{startOpts:d,endOpts:c,startAdjust:h,endAdjust:p}=eD(t),x={x:i,y:o},m={x:a,y:s},f=ek(x,n,m,0),b=ek(x,n,m,1)-u.P,v=eC(x,n,m,h/r),g=eC(x,n,m,1-p/r),y=new Path2D;e.beginPath(),y.moveTo(v.x,v.y),y.quadraticCurveTo(n.x,n.y,g.x,g.y),e.shadowColor=l.borderShadowColor,e.stroke(y),t.path=y,t.ctx=e,eR(e,v,{angle:f,adjust:h},d),eR(e,g,{angle:b,adjust:p},c)}(e,this,o,s),e.restore();let{startOpts:l,endOpts:d,startAdjust:c,endAdjust:h}=eD(this),p=Math.atan2(i-n,r-t);e.translate(t,n),e.rotate(p),e.beginPath(),e.moveTo(0+c,0),e.lineTo(s-h,0),e.shadowColor=a.borderShadowColor,e.stroke(),eE(e,0,c,l),eE(e,s,-h,d),e.restore()}get label(){return this.elements&&this.elements[0]}resolveElementProperties(e,t){let n=function(e,t){let{scales:n,chartArea:r}=e,i=n[t.scaleID],o={x:r.left,y:r.top,x2:r.right,y2:r.bottom};return i?function(e,t,n){let r=Q(e,n.value,NaN),i=Q(e,n.endValue,r);e.isHorizontal()?(t.x=r,t.x2=i):(t.y=r,t.y2=i)}(i,o,t):function(e,t,n){for(let r of Object.keys(J)){let i=e[K(e,n,r)];if(i){let{min:e,max:o,start:a,end:s,startProp:l,endProp:d}=J[r],c=Z(i,{min:n[e],max:n[o],start:i[a],end:i[s]});t[l]=c.start,t[d]=c.end}}}(n,o,t),o}(e,t),{x:r,y:i,x2:o,y2:a}=n,s=function({x:e,y:t,x2:n,y2:r},{top:i,right:o,bottom:a,left:s}){return!(e<s&&n<s||e>o&&n>o||t<i&&r<i||t>a&&r>a)}(n,e.chartArea),l=s?function(e,t,n){let{x:r,y:i}=eN(e,t,n),{x:o,y:a}=eN(t,e,n);return{x:r,y:i,x2:o,y2:a,width:Math.abs(o-r),height:Math.abs(a-i)}}({x:r,y:i},{x:o,y:a},e.chartArea):{x:r,y:i,x2:o,y2:a,width:Math.abs(o-r),height:Math.abs(a-i)};if(l.centerX=(o+r)/2,l.centerY=(a+i)/2,l.initProperties=O(e,l,t),t.curve){let e={x:l.x,y:l.y},n={x:l.x2,y:l.y2};l.cp=function(e,t,n){let{x:r,y:i,x2:o,y2:a,centerX:s,centerY:l}=e,d=Math.atan2(a-i,o-r),c=T(t.controlPoint,0);return f({x:s+S(n,c.x,!1),y:l+S(n,c.y,!1)},{x:s,y:l},d)}(l,t,(0,u.aF)(e,n))}let d=function(e,t,n){let r=n.borderWidth,i=(0,u.E)(n.padding),o=U(e.ctx,n);return function(e,t,n,r){let{width:i,height:o,padding:a}=n,{xAdjust:s,yAdjust:l}=t,d={x:e.x,y:e.y},c={x:e.x2,y:e.y2},h="auto"===t.rotation?function(e){let{x:t,y:n,x2:r,y2:i}=e,o=Math.atan2(i-n,r-t);return o>u.P/2?o-u.P:o<-(u.P/2)?o+u.P:o}(e):(0,u.t)(t.rotation),p=function(e,t,n){let r=Math.cos(n),i=Math.sin(n);return{w:Math.abs(e*r)+Math.abs(t*i),h:Math.abs(e*i)+Math.abs(t*r)}}(i,o,h),x=function(e,t,n,r){let i,o=function(e,t){let{x:n,x2:r,y:i,y2:o}=e,a=Math.min(i,o)-t.top,s=Math.min(n,r)-t.left,l=t.bottom-Math.max(i,o),d=t.right-Math.max(n,r);return{x:Math.min(s,d),y:Math.min(a,l),dx:s<=d?1:-1,dy:a<=l?1:-1}}(e,r);return"start"===t.position?e_({w:e.x2-e.x,h:e.y2-e.y},n,t,o):"end"===t.position?1-e_({w:e.x-e.x2,h:e.y-e.y2},n,t,o):N(1,t.position)}(e,t,{labelSize:p,padding:a},r),m=e.cp?eC(d,e.cp,c,x):eb(d,c,x),f={size:p.w,min:r.left,max:r.right,padding:a.left},b={size:p.h,min:r.top,max:r.bottom,padding:a.top},v=eT(m.x,f)+s,g=eT(m.y,b)+l;return{x:v-i/2,y:g-o/2,x2:v+i/2,y2:g+o/2,centerX:v,centerY:g,pointX:m.x,pointY:m.y,width:i,height:o,rotation:(0,u.U)(h)}}(t,n,{width:o.width+i.width+r,height:o.height+i.height+r,padding:i},e.chartArea)}(e,l,t.label);return d._visible=s,l.elements=[{type:"label",optionScope:"label",properties:d,initProperties:l.initProperties}],l}}eP.id="lineAnnotation";let eA={backgroundColor:void 0,backgroundShadowColor:void 0,borderColor:void 0,borderDash:void 0,borderDashOffset:void 0,borderShadowColor:void 0,borderWidth:void 0,display:void 0,fill:void 0,length:void 0,shadowBlur:void 0,shadowOffsetX:void 0,shadowOffsetY:void 0,width:void 0};function eN({x:e,y:t},n,{top:r,right:i,bottom:o,left:a}){return e<a&&(t=eg(a,{x:e,y:t},n),e=a),e>i&&(t=eg(i,{x:e,y:t},n),e=i),t<r&&(e=ev(r,{x:e,y:t},n),t=r),t>o&&(e=ev(o,{x:e,y:t},n),t=o),{x:e,y:t}}function eS(e,{mouseX:t,mouseY:n},r,i){let o=e.label;return o.options.display&&o.inRange(t,n,i,r)}function e_(e,t,n,r){let{labelSize:i,padding:o}=t,a=e.w*r.dx,s=e.h*r.dy;return v(Math.max(a>0&&(i.w/2+o.left-r.x)/a,s>0&&(i.h/2+o.top-r.y)/s),0,.25)}function eT(e,t){let{size:n,min:r,max:i,padding:o}=t,a=n/2;return n>i-r?(i+r)/2:(r>=e-o-a&&(e=r+o+a),i<=e+o+a&&(e=i-o-a),e)}function eD(e){let t=e.options,n=t.arrowHeads&&t.arrowHeads.start,r=t.arrowHeads&&t.arrowHeads.end;return{startOpts:n,endOpts:r,startAdjust:eI(e,n),endAdjust:eI(e,r)}}function eI(e,t){if(!t||!t.display)return 0;let{length:n,width:r}=t,i=e.options.borderWidth/2;return Math.abs(ev(0,{x:n,y:r+i},{x:0,y:i}))}function eE(e,t,n,r){if(!r||!r.display)return;let{length:i,width:o,fill:a,backgroundColor:s,borderColor:l}=r,d=Math.abs(t-i)+n;e.beginPath(),V(e,r),L(e,r),e.moveTo(d,-o),e.lineTo(t+n,0),e.lineTo(d,o),!0===a?(e.fillStyle=s||l,e.closePath(),e.fill(),e.shadowColor="transparent"):e.shadowColor=r.borderShadowColor,e.stroke()}function eR(e,{x:t,y:n},{angle:r,adjust:i},o){o&&o.display&&(e.save(),e.translate(t,n),e.rotate(r),eE(e,0,-i,o),e.restore())}eP.defaults={adjustScaleRange:!0,arrowHeads:{display:!1,end:Object.assign({},eA),fill:!1,length:12,start:Object.assign({},eA),width:6},borderDash:[],borderDashOffset:0,borderShadowColor:"transparent",borderWidth:2,curve:!1,controlPoint:{y:"-50%"},display:!0,endValue:void 0,init:void 0,hitTolerance:0,label:{backgroundColor:"rgba(0,0,0,0.8)",backgroundShadowColor:"transparent",borderCapStyle:"butt",borderColor:"black",borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",borderRadius:6,borderShadowColor:"transparent",borderWidth:0,callout:Object.assign({},ef.defaults.callout),color:"#fff",content:null,display:!1,drawTime:void 0,font:{family:void 0,lineHeight:void 0,size:void 0,style:void 0,weight:"bold"},height:void 0,hitTolerance:void 0,opacity:void 0,padding:6,position:"center",rotation:0,shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,textAlign:"center",textStrokeColor:void 0,textStrokeWidth:0,width:void 0,xAdjust:0,yAdjust:0,z:void 0},scaleID:void 0,shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,value:void 0,xMax:void 0,xMin:void 0,xScaleID:void 0,yMax:void 0,yMin:void 0,yScaleID:void 0,z:0},eP.descriptors={arrowHeads:{start:{_fallback:!0},end:{_fallback:!0},_fallback:!0}},eP.defaultRoutes={borderColor:"color"};class eO extends h.Hg{inRange(e,t,n,r){let i=this.options.rotation,o=(this.options.borderWidth+this.options.hitTolerance)/2;if("x"!==n&&"y"!==n)return function(e,t,n,r){let{width:i,height:o,centerX:a,centerY:s}=t,l=i/2,d=o/2;if(l<=0||d<=0)return!1;let c=(0,u.t)(n||0),h=Math.cos(c),p=Math.sin(c);return Math.pow(h*(e.x-a)+p*(e.y-s),2)/Math.pow(l+r,2)+Math.pow(p*(e.x-a)-h*(e.y-s),2)/Math.pow(d+r,2)<=1.0001}({x:e,y:t},this.getProps(["width","height","centerX","centerY"],r),i,o);let{x:a,y:s,x2:l,y2:d}=this.getProps(["x","y","x2","y2"],r),c="y"===n?{start:s,end:d}:{start:a,end:l},h=f({x:e,y:t},this.getCenterPoint(r),(0,u.t)(-i));return h[n]>=c.start-o-.001&&h[n]<=c.end+o+.001}getCenterPoint(e){return w(this,e)}draw(e){let{width:t,height:n,centerX:r,centerY:i,options:o}=this;e.save(),H(e,this.getCenterPoint(),o.rotation),V(e,this.options),e.beginPath(),e.fillStyle=o.backgroundColor;let a=L(e,o);e.ellipse(r,i,n/2,t/2,u.P/2,0,2*u.P),e.fill(),a&&(e.shadowColor=o.borderShadowColor,e.stroke()),e.restore()}get label(){return this.elements&&this.elements[0]}resolveElementProperties(e,t){return er(e,t)}}eO.id="ellipseAnnotation",eO.defaults={adjustScaleRange:!0,backgroundShadowColor:"transparent",borderDash:[],borderDashOffset:0,borderShadowColor:"transparent",borderWidth:1,display:!0,hitTolerance:0,init:void 0,label:Object.assign({},ex.defaults.label),rotation:0,shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,xMax:void 0,xMin:void 0,xScaleID:void 0,yMax:void 0,yMin:void 0,yScaleID:void 0,z:0},eO.defaultRoutes={borderColor:"color",backgroundColor:"color"},eO.descriptors={label:{_fallback:!0}};class eY extends h.Hg{inRange(e,t,n,r){let{x:i,y:o,x2:a,y2:s,width:l}=this.getProps(["x","y","x2","y2","width"],r),d=(this.options.borderWidth+this.options.hitTolerance)/2;if("x"!==n&&"y"!==n){var c,h,u;return c={x:e,y:t},h=this.getCenterPoint(r),u=l/2,!!c&&!!h&&!(u<=0)&&Math.pow(c.x-h.x,2)+Math.pow(c.y-h.y,2)<=Math.pow(u+d,2)}return g("y"===n?{start:o,end:s,value:t}:{start:i,end:a,value:e},d)}getCenterPoint(e){return w(this,e)}draw(e){let t=this.options,n=t.borderWidth;if(t.radius<.1)return;e.save(),e.fillStyle=t.backgroundColor,V(e,t);let r=L(e,t);!function(e,t,n,r){let{radius:i,options:o}=t,a=o.pointStyle,s=o.rotation,l=(s||0)*u.b4;if(z(a)){e.save(),e.translate(n,r),e.rotate(l),e.drawImage(a,-a.width/2,-a.height/2,a.width,a.height),e.restore();return}X(i)||function(e,{x:t,y:n,radius:r,rotation:i,style:o,rad:a}){let s,l,d,c;switch(e.beginPath(),o){default:e.arc(t,n,r,0,u.T),e.closePath();break;case"triangle":e.moveTo(t+Math.sin(a)*r,n-Math.cos(a)*r),a+=u.b6,e.lineTo(t+Math.sin(a)*r,n-Math.cos(a)*r),a+=u.b6,e.lineTo(t+Math.sin(a)*r,n-Math.cos(a)*r),e.closePath();break;case"rectRounded":c=.516*r,d=r-c,s=Math.cos(a+u.b5)*d,l=Math.sin(a+u.b5)*d,e.arc(t-s,n-l,c,a-u.P,a-u.H),e.arc(t+l,n-s,c,a-u.H,a),e.arc(t+s,n+l,c,a,a+u.H),e.arc(t-l,n+s,c,a+u.H,a+u.P),e.closePath();break;case"rect":if(!i){d=Math.SQRT1_2*r,e.rect(t-d,n-d,2*d,2*d);break}a+=u.b5;case"rectRot":s=Math.cos(a)*r,l=Math.sin(a)*r,e.moveTo(t-s,n-l),e.lineTo(t+l,n-s),e.lineTo(t+s,n+l),e.lineTo(t-l,n+s),e.closePath();break;case"crossRot":a+=u.b5;case"cross":s=Math.cos(a)*r,l=Math.sin(a)*r,e.moveTo(t-s,n-l),e.lineTo(t+s,n+l),e.moveTo(t+l,n-s),e.lineTo(t-l,n+s);break;case"star":s=Math.cos(a)*r,l=Math.sin(a)*r,e.moveTo(t-s,n-l),e.lineTo(t+s,n+l),e.moveTo(t+l,n-s),e.lineTo(t-l,n+s),a+=u.b5,s=Math.cos(a)*r,l=Math.sin(a)*r,e.moveTo(t-s,n-l),e.lineTo(t+s,n+l),e.moveTo(t+l,n-s),e.lineTo(t-l,n+s);break;case"line":s=Math.cos(a)*r,l=Math.sin(a)*r,e.moveTo(t-s,n-l),e.lineTo(t+s,n+l);break;case"dash":e.moveTo(t,n),e.lineTo(t+Math.cos(a)*r,n+Math.sin(a)*r)}e.fill()}(e,{x:n,y:r,radius:i,rotation:s,style:a,rad:l})}(e,this,this.centerX,this.centerY),r&&!z(t.pointStyle)&&(e.shadowColor=t.borderShadowColor,e.stroke()),e.restore(),t.borderWidth=n}resolveElementProperties(e,t){let n=en(e,t);return n.initProperties=O(e,n,t),n}}eY.id="pointAnnotation",eY.defaults={adjustScaleRange:!0,backgroundShadowColor:"transparent",borderDash:[],borderDashOffset:0,borderShadowColor:"transparent",borderWidth:1,display:!0,hitTolerance:0,init:void 0,pointStyle:"circle",radius:10,rotation:0,shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,xAdjust:0,xMax:void 0,xMin:void 0,xScaleID:void 0,xValue:void 0,yAdjust:0,yMax:void 0,yMin:void 0,yScaleID:void 0,yValue:void 0,z:0},eY.defaultRoutes={borderColor:"color",backgroundColor:"color"};class eW extends h.Hg{inRange(e,t,n,r){if("x"!==n&&"y"!==n)return this.options.radius>=.1&&this.elements.length>1&&function(e,t,n,r){let i=!1,o=e[e.length-1].getProps(["bX","bY"],r);for(let a of e){let e=a.getProps(["bX","bY"],r);e.bY>n!=o.bY>n&&t<(o.bX-e.bX)*(n-e.bY)/(o.bY-e.bY)+e.bX&&(i=!i),o=e}return i}(this.elements,e,t,r);let i=f({x:e,y:t},this.getCenterPoint(r),(0,u.t)(-this.options.rotation)),o=this.elements.map(e=>"y"===n?e.bY:e.bX),a=Math.min(...o),s=Math.max(...o);return i[n]>=a&&i[n]<=s}getCenterPoint(e){return w(this,e)}draw(e){let{elements:t,options:n}=this;e.save(),e.beginPath(),e.fillStyle=n.backgroundColor,V(e,n);let r=L(e,n),i=!0;for(let n of t)i?(e.moveTo(n.x,n.y),i=!1):e.lineTo(n.x,n.y);e.closePath(),e.fill(),r&&(e.shadowColor=n.borderShadowColor,e.stroke()),e.restore()}resolveElementProperties(e,t){let n=en(e,t),{sides:r,rotation:i}=t,o=[],a=2*u.P/r,s=i*u.b4;for(let i=0;i<r;i++,s+=a){let r=function({centerX:e,centerY:t},{radius:n,borderWidth:r,hitTolerance:i},o){let a=(r+i)/2,s=Math.sin(o),l=Math.cos(o),d={x:e+s*n,y:t-l*n};return{type:"point",optionScope:"point",properties:{x:d.x,y:d.y,centerX:d.x,centerY:d.y,bX:e+s*(n+a),bY:t-l*(n+a)}}}(n,t,s);r.initProperties=O(e,n,t),o.push(r)}return n.elements=o,n}}eW.id="polygonAnnotation",eW.defaults={adjustScaleRange:!0,backgroundShadowColor:"transparent",borderCapStyle:"butt",borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",borderShadowColor:"transparent",borderWidth:1,display:!0,hitTolerance:0,init:void 0,point:{radius:0},radius:10,rotation:0,shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,sides:3,xAdjust:0,xMax:void 0,xMin:void 0,xScaleID:void 0,xValue:void 0,yAdjust:0,yMax:void 0,yMin:void 0,yScaleID:void 0,yValue:void 0,z:0},eW.defaultRoutes={borderColor:"color",backgroundColor:"color"};let eF={box:ex,doughnutLabel:em,ellipse:eO,label:ef,line:eP,point:eY,polygon:eW};Object.keys(eF).forEach(e=>{u.d.describe(`elements.${eF[e].id}`,{_fallback:"plugins.annotation.common"})});let eX={update:Object.assign},eq=es.concat(ec),ez=(e,t)=>(0,u.i)(t)?e$(e,t):e,eH=e=>"color"===e||"font"===e;function eL(e="line"){return eF[e]?e:(console.warn(`Unknown annotation type: '${e}', defaulting to 'line'`),"line")}function eV(e,t,n,r){let i=eF[eL(n)],o=e[t];return o&&o instanceof i||Object.assign(o=e[t]=new i,r),o}function eU(e){let t=eF[eL(e.type)],n={};for(let r of(n.id=e.id,n.type=e.type,n.drawTime=e.drawTime,Object.assign(n,e$(e,t.defaults),e$(e,t.defaultRoutes)),eq))n[r]=e[r];return n}function e$(e,t){let n={};for(let r of Object.keys(t)){let i=t[r],o=e[r];eH(r)&&(0,u.b)(o)?n[r]=o.map(e=>ez(e,i)):n[r]=ez(o,i)}return n}let eB=new Map,eG=e=>"doughnutLabel"!==e.type,eJ=es.concat(ec);function eQ(e,t,n){let{ctx:r,chartArea:i}=e,o=eB.get(e);for(let e of(n&&(0,u.Y)(r,i),(function(e,t){let n=[];for(let r of e)if(r.options.drawTime===t&&n.push({element:r,main:!0}),r.elements&&r.elements.length)for(let e of r.elements)e.options.display&&e.options.drawTime===t&&n.push({element:e});return n})(o.visibleElements,t).sort((e,t)=>e.element.options.z-t.element.options.z))){var a=r,s=i,l=o,d=e;let t=d.element;d.main?(eh(l,t,"beforeDraw"),t.draw(a,s),eh(l,t,"afterDraw")):t.draw(a,s)}n&&(0,u.$)(r)}h.t1.register(h.PP,h.kc,h.FN,h.No,h.hE,h.m_,h.s$,h.dN,{id:"annotation",version:"3.1.0",beforeRegister(){!function(e,t,n,r=!0){let i=n.split("."),o=0;for(let t of"4.0".split(".")){let a=i[o++];if(parseInt(t,10)<parseInt(a,10))break;if(b(a,t))if(!r)return!1;else throw Error(`${e} v${n} is not supported. v4.0 or newer is required.`)}}("chart.js","4.0",h.t1.version)},afterRegister(){h.t1.register(eF)},afterUnregister(){h.t1.unregister(eF)},beforeInit(e){eB.set(e,{annotations:[],elements:[],visibleElements:[],listeners:{},listened:!1,moveListened:!1,hooks:{},hooked:!1,hovered:[]})},beforeUpdate(e,t,n){let r=eB.get(e).annotations=[],i=n.annotations;(0,u.i)(i)?Object.keys(i).forEach(e=>{let t=i[e];(0,u.i)(t)&&(t.id=e,r.push(t))}):(0,u.b)(i)&&r.push(...i);var o=r.filter(eG),a=e.scales;for(let e of o){var s=e,l=a;for(let e of["scaleID","xScaleID","yScaleID"]){let t=K(l,s,e);t&&!l[t]&&function(e,t){if("scaleID"===t)return!0;let n=t.charAt(0);for(let t of["Min","Max","Value"])if((0,u.h)(e[n+t]))return!0;return!1}(s,e)&&console.warn(`No scale found with id '${t}' for annotation '${s.id}'`)}}},afterDataLimits(e,t){let n=eB.get(e);!function(e,t,n){let r=function(e,t,n){let r=t.axis,i=t.id,o=r+"ScaleID",a={min:(0,u.v)(t.min,Number.NEGATIVE_INFINITY),max:(0,u.v)(t.max,Number.POSITIVE_INFINITY)};for(let s of n)s.scaleID===i?ep(s,t,["value","endValue"],a):K(e,s,o)===i&&ep(s,t,[r+"Min",r+"Max",r+"Value"],a);return a}(e.scales,t,n),i=eu(t,r,"min","suggestedMin");(i=eu(t,r,"max","suggestedMax")||i)&&(0,u.a7)(t.handleTickRangeOptions)&&t.handleTickRangeOptions()}(e,t.scale,n.annotations.filter(eG).filter(e=>e.display&&e.adjustScaleRange))},afterUpdate(e,t,n){let r=eB.get(e);r.listened=Y(n,es,r.listeners),r.moveListened=!1,ea.forEach(e=>{(0,u.a7)(n[e])&&(r.moveListened=!0)}),r.listened&&r.moveListened||r.annotations.forEach(e=>{!r.listened&&(0,u.a7)(e.click)&&(r.listened=!0),r.moveListened||ea.forEach(t=>{(0,u.a7)(e[t])&&(r.listened=!0,r.moveListened=!0)})}),function(e,t,n,r){var i,o,a,s,l,d,c,p;let x=(i=e,o=n.animations,"reset"===(a=r)||"none"===a||"resize"===a?eX:new h.Qw(i,o)),m=t.annotations,f=function(e,t){let n=t.length,r=e.length;return r<n?e.splice(r,0,...Array(n-r)):r>n&&e.splice(n,r-n),e}(t.elements,m);for(let t=0;t<m.length;t++){let n=m[t],r=eV(f,t,n.type),i=n.setContext((s=e,l=r,d=f,c=n,l.$context||(l.$context=Object.assign(Object.create(s.getContext()),{element:l,get elements(){return d.filter(e=>e&&e.options)},id:c.id,type:"annotation"})))),o=r.resolveElementProperties(e,i);o.skip=isNaN((p=o).x)||isNaN(p.y),"elements"in o&&(function(e,t,n,r){let i=e.elements||(e.elements=[]);i.length=t.length;for(let e=0;e<t.length;e++){let o=t[e],a=o.properties,s=eV(i,e,o.type,o.initProperties);a.options=eU(n[o.optionScope].override(o)),r.update(s,a)}}(r,o.elements,i,x),delete o.elements),(0,u.h)(r.x)||Object.assign(r,o),Object.assign(r,o.initProperties),o.options=eU(i),x.update(r,o)}}(e,r,n,t.mode),r.visibleElements=r.elements.filter(e=>!e.skip&&e.options.display);let i=r.visibleElements;r.hooked=Y(n,ec,r.hooks),r.hooked||i.forEach(e=>{r.hooked||ec.forEach(t=>{(0,u.a7)(e.options[t])&&(r.hooked=!0)})})},beforeDatasetsDraw(e,t,n){eQ(e,"beforeDatasetsDraw",n.clip)},afterDatasetsDraw(e,t,n){eQ(e,"afterDatasetsDraw",n.clip)},beforeDatasetDraw(e,t,n){eQ(e,t.index,n.clip)},beforeDraw(e,t,n){eQ(e,"beforeDraw",n.clip)},afterDraw(e,t,n){eQ(e,"afterDraw",n.clip)},beforeEvent(e,t,n){(function(e,t,n){if(e.listened)switch(t.type){case"mousemove":case"mouseout":let r;var i=e,o=t,a=n;if(!i.moveListened)return;r="mousemove"===o.type?x(i.visibleElements,o,a.interaction):[];let s=i.hovered;i.hovered=r;let l={state:i,event:o},d=el(l,"leave",s,r);return el(l,"enter",r,s)||d;case"click":let c;var h=e,u=t,p=n;let m=h.listeners;for(let e of x(h.visibleElements,u,p.interaction))c=ed(e.options.click||m.click,e,u)||c;return c}})(eB.get(e),t.event,n)&&(t.changed=!0)},afterDestroy(e){eB.delete(e)},getAnnotations(e){let t=eB.get(e);return t?t.elements:[]},_getAnnotationElementsAtEventForMode:(e,t,n)=>x(e,t,n),defaults:{animations:{numbers:{properties:["x","y","x2","y2","width","height","centerX","centerY","pointX","pointY","radius"],type:"number"},colors:{properties:["backgroundColor","borderColor"],type:"color"}},clip:!0,interaction:{mode:void 0,axis:void 0,intersect:void 0},common:{drawTime:"afterDatasetsDraw",init:!1,label:{}}},descriptors:{_indexable:!1,_scriptable:e=>!eJ.includes(e)&&"init"!==e,annotations:{_allKeys:!1,_fallback:(e,t)=>`elements.${eF[eL(t.type)].id}`},interaction:{_fallback:!0},common:{label:{_indexable:eH,_fallback:!0},_indexable:eH}},additionalOptionScopes:[""]});let eK=({evolutionData:e,fireTargetAmount:t})=>{let n={labels:e.map(e=>e.annee.toString()),datasets:[{label:"Patrimoine Total (€)",data:e.map(e=>e.total_patrimoine),borderColor:"rgb(75, 192, 192)",backgroundColor:"rgba(75, 192, 192, 0.2)",tension:.1,fill:!0}]},i={};return null!==t&&(i.line1={type:"line",yMin:t,yMax:t,borderColor:"rgb(255, 99, 132)",borderWidth:2,borderDash:[6,6],label:{content:`Objectif FIRE: ${t.toLocaleString("fr-FR",{style:"currency",currency:"EUR"})}`,display:!0,position:"end",backgroundColor:"rgba(255, 99, 132, 0.8)",font:{weight:"bold"},color:"white",padding:{x:6,y:3},yAdjust:-10}}),(0,r.jsxs)("div",{style:{height:"400px",marginTop:"20px",marginBottom:"20px"},children:[(0,r.jsx)(c.N1,{data:n,options:{responsive:!0,maintainAspectRatio:!1,scales:{y:{beginAtZero:!0,ticks:{callback:function(e){return"number"==typeof e?e.toLocaleString("fr-FR",{style:"currency",currency:"EUR"}):e}}}},plugins:{legend:{position:"top"},title:{display:!0,text:"\xc9volution du Patrimoine Total et Objectif FIRE",font:{size:18}},tooltip:{callbacks:{label:function(e){let t=e.dataset.label||"";return t&&(t+=": "),null!==e.parsed.y&&(t+=new Intl.NumberFormat("fr-FR",{style:"currency",currency:"EUR"}).format(e.parsed.y)),t}}},annotation:{annotations:i}},interaction:{mode:"index",intersect:!1}}})," "]})},eZ=({evolution:e,onSave:t,onCancel:n})=>{let[d,c]=(0,i.useState)({annee:e?.annee||new Date().getFullYear(),investissement:e?.investissement||0,prix_part_scpi:e?.prix_part_scpi||0,remboursement_credit:e?.remboursement_credit||0,valeur_reelle_scpi:e?.valeur_reelle_scpi||0,total_patrimoine:e?.total_patrimoine||0});(0,i.useEffect)(()=>{c({annee:e?.annee||new Date().getFullYear(),investissement:e?.investissement||0,prix_part_scpi:e?.prix_part_scpi||0,remboursement_credit:e?.remboursement_credit||0,valeur_reelle_scpi:e?.valeur_reelle_scpi||0,total_patrimoine:e?.total_patrimoine||0})},[e]);let h=async n=>{n.preventDefault();let r=e?.id?"put":"post",i=e?.id?`/evolution/${e.annee}`:"/evolution";try{await o.A[r](i,d),t()}catch(e){console.error("Erreur lors de la sauvegarde des donn\xe9es d&apos;\xe9volution",e instanceof Error?e.message:"Unknown error")}};return(0,r.jsxs)(a.A,{onSubmit:h,children:[(0,r.jsxs)(a.A.Group,{className:"mb-3",children:[(0,r.jsx)(a.A.Label,{children:"Ann\xe9e"}),(0,r.jsx)(a.A.Control,{type:"number",min:"2000",max:new Date().getFullYear()+5,value:d.annee,onChange:e=>c({...d,annee:parseInt(e.target.value)||0}),required:!0,disabled:!!e?.id})]}),(0,r.jsxs)(a.A.Group,{className:"mb-3",children:[(0,r.jsx)(a.A.Label,{children:"Investissement total (€)"}),(0,r.jsx)(a.A.Control,{type:"number",step:"0.01",value:d.investissement,onChange:e=>c({...d,investissement:parseFloat(e.target.value)||0}),required:!0})]}),(0,r.jsxs)(a.A.Group,{className:"mb-3",children:[(0,r.jsx)(a.A.Label,{children:"Prix moyen des parts SCPI (€)"}),(0,r.jsx)(a.A.Control,{type:"number",step:"0.01",value:d.prix_part_scpi,onChange:e=>c({...d,prix_part_scpi:parseFloat(e.target.value)||0}),required:!0})]}),(0,r.jsxs)(a.A.Group,{className:"mb-3",children:[(0,r.jsx)(a.A.Label,{children:"Remboursement cr\xe9dit (€)"}),(0,r.jsx)(a.A.Control,{type:"number",step:"0.01",value:d.remboursement_credit,onChange:e=>c({...d,remboursement_credit:parseFloat(e.target.value)||0}),required:!0})]}),(0,r.jsxs)(a.A.Group,{className:"mb-3",children:[(0,r.jsx)(a.A.Label,{children:"Valeur r\xe9elle SCPI (€)"}),(0,r.jsx)(a.A.Control,{type:"number",step:"0.01",value:d.valeur_reelle_scpi,onChange:e=>c({...d,valeur_reelle_scpi:parseFloat(e.target.value)||0}),required:!0})]}),(0,r.jsxs)(a.A.Group,{className:"mb-3",children:[(0,r.jsx)(a.A.Label,{children:"Total patrimoine (€)"}),(0,r.jsx)(a.A.Control,{type:"number",step:"0.01",value:d.total_patrimoine,onChange:e=>c({...d,total_patrimoine:parseFloat(e.target.value)||0}),required:!0})]}),(0,r.jsx)(s.A,{variant:"info",children:(0,r.jsx)("small",{children:"Les m\xe9triques d'\xe9volution (%, €, TCAM) seront calcul\xe9es automatiquement apr\xe8s la sauvegarde."})}),(0,r.jsxs)("div",{className:"d-flex justify-content-end gap-2",children:[(0,r.jsx)(l.A,{variant:"secondary",onClick:n,children:"Annuler"}),(0,r.jsx)(l.A,{variant:"primary",type:"submit",children:"Sauvegarder"})]})]})},e0=()=>{let[e,t]=(0,i.useState)([]),[n,a]=(0,i.useState)(!0),[s,c]=(0,i.useState)(null),[h,u]=(0,i.useState)(!1),[p,x]=(0,i.useState)(null),[m,f]=(0,i.useState)(0),[b,v]=(0,i.useState)(null),g=async()=>{try{let e=await o.A.get("/fire-settings");if(e.data&&void 0!==e.data.fire_target_amount)v(e.data.fire_target_amount);else{let e=await o.A.get("/fire-target");v(e.data.fire_target_amount)}}catch(e){console.error("Erreur lors de la r\xe9cup\xe9ration de l'objectif FIRE",e),v(910150)}},y=()=>{a(!0),c(null),o.A.get("/evolution").then(e=>{t(e.data),a(!1),f(0)}).catch(e=>{console.error("Evolution API error:",e),m<2?(f(e=>e+1),setTimeout(()=>y(),1e3)):(c("Erreur lors de la r\xe9cup\xe9ration des donn\xe9es d'\xe9volution."),a(!1))})};(0,i.useEffect)(()=>{y(),g()},[]);let j=async e=>{if(window.confirm(`\xcates-vous s\xfbr de vouloir supprimer les donn\xe9es de ${e} ?`))try{await o.A.delete(`/evolution/${e}`),y()}catch(e){console.error("Erreur lors de la suppression des donn\xe9es d'\xe9volution",e)}},w=e=>null==e?"N/A":new Intl.NumberFormat("fr-FR",{style:"currency",currency:"EUR"}).format(e),C=e=>null==e?"N/A":new Intl.NumberFormat("fr-FR",{style:"percent",minimumFractionDigits:2,maximumFractionDigits:2}).format(e/100);return n&&!h&&0===m?(0,r.jsx)("p",{children:"Chargement des donn\xe9es d'\xe9volution..."}):s&&m>=2?(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-danger",children:s}),(0,r.jsx)("button",{className:"btn btn-primary",onClick:()=>{f(0),y()},children:"R\xe9essayer"})]}):n||e.length||s?n&&!h?(0,r.jsxs)("p",{children:["Chargement des donn\xe9es d'\xe9volution, tentative ",m+1,"..."]}):(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"d-flex justify-content-between align-items-center mb-4",children:[(0,r.jsx)("h1",{children:"\xc9volution Annuelle du Patrimoine"}),(0,r.jsx)(l.A,{variant:"primary",onClick:()=>{x({}),u(!0)},children:"Ajouter une ann\xe9e"})]}),e.length>0&&(0,r.jsx)("div",{className:"mb-4",children:(0,r.jsxs)("div",{className:"row",children:[(0,r.jsx)("div",{className:"col-md-3",children:(0,r.jsx)("div",{className:"card text-white bg-primary",children:(0,r.jsxs)("div",{className:"card-body text-center",children:[(0,r.jsx)("h5",{children:"P\xe9riode"}),(0,r.jsxs)("h4",{children:[e[0]?.annee," - ",e[e.length-1]?.annee]})]})})}),(0,r.jsx)("div",{className:"col-md-3",children:(0,r.jsx)("div",{className:"card text-white bg-success",children:(0,r.jsxs)("div",{className:"card-body text-center",children:[(0,r.jsx)("h5",{children:"Croissance Totale"}),(0,r.jsx)("h4",{children:w(e[e.length-1]?.total_patrimoine-e[0]?.total_patrimoine)})]})})}),(0,r.jsx)("div",{className:"col-md-3",children:(0,r.jsx)("div",{className:"card text-white bg-info",children:(0,r.jsxs)("div",{className:"card-body text-center",children:[(0,r.jsx)("h5",{children:"TCAM Moyen"}),(0,r.jsx)("h4",{children:C(e[e.length-1]?.tcam)})]})})}),(0,r.jsx)("div",{className:"col-md-3",children:(0,r.jsx)("div",{className:"card text-white bg-warning",children:(0,r.jsxs)("div",{className:"card-body text-center",children:[(0,r.jsx)("h5",{children:"Patrimoine Actuel"}),(0,r.jsx)("h4",{children:w(e[e.length-1]?.total_patrimoine)})]})})})]})}),e.length>0&&(0,r.jsx)("div",{className:"table-responsive",children:(0,r.jsxs)("table",{className:"table table-striped table-hover",children:[(0,r.jsx)("thead",{className:"table-dark",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{children:"Ann\xe9e"}),(0,r.jsx)("th",{className:"text-end",children:"Total Patrimoine"}),(0,r.jsx)("th",{className:"text-end",children:"Evolution en %"}),(0,r.jsx)("th",{className:"text-end",children:"Evolution en €"}),(0,r.jsx)("th",{className:"text-end",children:"Croissance Annuel Moyen (€)"}),(0,r.jsx)("th",{className:"text-end",children:"TCAM (%)"}),(0,r.jsx)("th",{className:"text-center",children:"Actions"})]})}),(0,r.jsx)("tbody",{children:e.map(e=>(0,r.jsxs)("tr",{children:[(0,r.jsx)("td",{children:(0,r.jsx)("strong",{children:e.annee})}),(0,r.jsx)("td",{className:"text-end",children:(0,r.jsx)("strong",{children:w(e.total_patrimoine)})}),(0,r.jsx)("td",{className:`text-end ${(e.evolution_pourcentage||0)>=0?"text-success":"text-danger"}`,children:C(e.evolution_pourcentage)}),(0,r.jsx)("td",{className:`text-end ${(e.evolution_euros||0)>=0?"text-success":"text-danger"}`,children:w(e.evolution_euros)}),(0,r.jsx)("td",{className:"text-end",children:w(e.croissance_moyenne)}),(0,r.jsx)("td",{className:"text-end text-info",children:C(e.tcam)}),(0,r.jsxs)("td",{className:"text-center",children:[(0,r.jsx)(l.A,{variant:"outline-primary",size:"sm",className:"me-2",onClick:()=>{x(e),u(!0)},children:"Modifier"}),(0,r.jsx)(l.A,{variant:"outline-danger",size:"sm",onClick:()=>j(e.annee),children:"Supprimer"})]})]},e.id))})]})}),(0,r.jsxs)(d.A,{show:h,onHide:()=>u(!1),size:"lg",children:[(0,r.jsx)(d.A.Header,{closeButton:!0,children:(0,r.jsx)(d.A.Title,{children:p?.id?`Modifier ${p.annee}`:"Ajouter une ann\xe9e"})}),(0,r.jsx)(d.A.Body,{children:(0,r.jsx)(eZ,{evolution:p,onSave:()=>{u(!1),x(null),y()},onCancel:()=>u(!1)})})]}),e.length>0&&null!==b&&(0,r.jsx)(eK,{evolutionData:e,fireTargetAmount:b}),(0,r.jsxs)("div",{className:"mt-5 p-4 bg-light rounded",children:[(0,r.jsx)("h4",{children:"Comprendre les m\xe9triques d'\xe9volution"}),(0,r.jsx)("hr",{}),(0,r.jsxs)("dl",{className:"row",children:[(0,r.jsx)("dt",{className:"col-sm-4",children:"TCAM (Taux de Croissance Annuel Moyen)"}),(0,r.jsxs)("dd",{className:"col-sm-8",children:[(0,r.jsx)("p",{children:"Le TCAM repr\xe9sente le taux de croissance annuel compos\xe9 moyen de votre patrimoine sur une p\xe9riode donn\xe9e (depuis 2015 dans ce tableau). Il lisse les fluctuations annuelles pour donner une id\xe9e de la performance moyenne \xe0 long terme."}),(0,r.jsxs)("p",{children:[(0,r.jsx)("em",{children:"Formule :"})," ",(0,r.jsx)("code",{children:"((Valeur Finale / Valeur Initiale)^(1 / Nombre d'Ann\xe9es)) - 1"})]}),(0,r.jsx)("p",{children:"Un TCAM \xe9lev\xe9 indique une croissance robuste et constante de votre patrimoine au fil du temps."})]}),(0,r.jsx)("dt",{className:"col-sm-4 mt-3",children:"Evolution en %"}),(0,r.jsxs)("dd",{className:"col-sm-8 mt-3",children:[(0,r.jsx)("p",{children:"Cette valeur montre la variation en pourcentage du patrimoine total par rapport \xe0 l'ann\xe9e pr\xe9c\xe9dente."}),(0,r.jsxs)("p",{children:[(0,r.jsx)("em",{children:"Formule :"})," ",(0,r.jsx)("code",{children:"((Total Patrimoine Ann\xe9e N - Total Patrimoine Ann\xe9e N-1) / Total Patrimoine Ann\xe9e N-1) * 100"})]}),(0,r.jsx)("p",{children:"Elle permet d'identifier rapidement les ann\xe9es de forte croissance ou de stagnation."})]}),(0,r.jsx)("dt",{className:"col-sm-4 mt-3",children:"Evolution en €"}),(0,r.jsxs)("dd",{className:"col-sm-8 mt-3",children:[(0,r.jsx)("p",{children:"Indique l'augmentation (ou la diminution) absolue du patrimoine total en euros par rapport \xe0 l'ann\xe9e pr\xe9c\xe9dente."}),(0,r.jsxs)("p",{children:[(0,r.jsx)("em",{children:"Formule :"})," ",(0,r.jsx)("code",{children:"Total Patrimoine Ann\xe9e N - Total Patrimoine Ann\xe9e N-1"})]}),(0,r.jsx)("p",{children:"Cette m\xe9trique donne une mesure concr\xe8te de la richesse cr\xe9\xe9e (ou perdue) chaque ann\xe9e."})]}),(0,r.jsx)("dt",{className:"col-sm-4 mt-3",children:"Croissance Annuel Moyen (€)"}),(0,r.jsxs)("dd",{className:"col-sm-8 mt-3",children:[(0,r.jsx)("p",{children:"Repr\xe9sente la moyenne simple de l'augmentation annuelle de votre patrimoine en euros depuis l'ann\xe9e de base (2015)."}),(0,r.jsxs)("p",{children:[(0,r.jsx)("em",{children:"Formule :"})," ",(0,r.jsx)("code",{children:"(Total Patrimoine Ann\xe9e N - Total Patrimoine 2015) / (Ann\xe9e N - 2015)"})]}),(0,r.jsx)("p",{children:"Elle donne une indication de la contribution mon\xe9taire moyenne annuelle \xe0 la croissance de votre patrimoine depuis le d\xe9but de la p\xe9riode de suivi."})]})]})]})]}):(0,r.jsx)("p",{children:"Aucune donn\xe9e d'\xe9volution trouv\xe9e."})}},51832:(e,t,n)=>{Promise.resolve().then(n.bind(n,11227)),Promise.resolve().then(n.bind(n,30004))},52504:(e,t,n)=>{Promise.resolve().then(n.bind(n,85461)),Promise.resolve().then(n.bind(n,29190))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},61135:()=>{},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>i});var r=n(31658);let i=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},78162:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>i});var r=n(31658);let i=async e=>[{type:"image/png",sizes:"192x192",url:(0,r.fillMetadataSegment)(".",await e.params,"icon.png")+"?d84b06edc1baa420"}]},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},85461:(e,t,n)=>{"use strict";function r(){return null}n.d(t,{default:()=>r}),n(43210)},90317:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});let r=n(51060).A.create({baseURL:"http://localhost:8000/api",headers:{"Content-Type":"application/json"},timeout:1e4});r.interceptors.response.use(e=>e,e=>(console.error("API Error:",e.response?.status,e.config?.url,e.message),Promise.reject(e)));let i=r},91406:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>r});let r=(0,n(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\AI-Code\\\\fire_UI\\\\frontnextjs\\\\src\\\\app\\\\evolution\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\AI-Code\\fire_UI\\frontnextjs\\src\\app\\evolution\\page.tsx","default")},93991:(e,t,n)=>{Promise.resolve().then(n.t.bind(n,16444,23)),Promise.resolve().then(n.t.bind(n,16042,23)),Promise.resolve().then(n.t.bind(n,88170,23)),Promise.resolve().then(n.t.bind(n,49477,23)),Promise.resolve().then(n.t.bind(n,29345,23)),Promise.resolve().then(n.t.bind(n,12089,23)),Promise.resolve().then(n.t.bind(n,46577,23)),Promise.resolve().then(n.t.bind(n,31307,23))},94431:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>l,metadata:()=>a,viewport:()=>s});var r=n(37413);n(61135);var i=n(30004),o=n(11227);let a={title:"FIRE Dashboard",description:"Application pour le suivi de l'objectif FIRE (Financial Independence, Retire Early)",manifest:"/manifest.json",icons:{icon:"/icon.png",apple:"/apple-icon.png"}},s={themeColor:"#000000"};function l({children:e}){return(0,r.jsx)("html",{lang:"en",children:(0,r.jsxs)("body",{children:[(0,r.jsxs)("div",{className:"App",children:[(0,r.jsx)(i.default,{}),(0,r.jsx)("main",{className:"container mt-4",children:e})]}),(0,r.jsx)(o.default,{})]})})}},94650:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>i});var r=n(31658);let i=async e=>[{type:"image/png",sizes:"192x192",url:(0,r.fillMetadataSegment)(".",await e.params,"apple-icon.png")+"?d84b06edc1baa420"}]},94735:e=>{"use strict";e.exports=require("events")},96889:(e,t,n)=>{Promise.resolve().then(n.bind(n,91406))}};var t=require("../../webpack-runtime.js");t.C(e);var n=e=>t(t.s=e),r=t.X(0,[447,412,517,315,36,889,947],()=>n(13461));module.exports=r})();