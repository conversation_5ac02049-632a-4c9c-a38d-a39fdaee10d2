(()=>{var e={};e.id=76,e.ids=[76],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},13461:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>h,pages:()=>c,routeModule:()=>u,tree:()=>d});var n=r(65239),o=r(48088),i=r(88170),a=r.n(i),s=r(30893),l={};for(let e in s)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>s[e]);r.d(t,l);let d={children:["",{children:["evolution",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,91406)),"D:\\AI-Code\\fire_UI\\frontnextjs\\src\\app\\evolution\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e),async e=>(await Promise.resolve().then(r.bind(r,78162))).default(e)],apple:[async e=>(await Promise.resolve().then(r.bind(r,94650))).default(e)],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"D:\\AI-Code\\fire_UI\\frontnextjs\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e),async e=>(await Promise.resolve().then(r.bind(r,78162))).default(e)],apple:[async e=>(await Promise.resolve().then(r.bind(r,94650))).default(e)],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["D:\\AI-Code\\fire_UI\\frontnextjs\\src\\app\\evolution\\page.tsx"],h={require:r,loadChunk:()=>Promise.resolve()},u=new n.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/evolution/page",pathname:"/evolution",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},24255:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29190:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});var n=r(60687),o=r(43210),i=r(85814),a=r.n(i);let s=()=>{let[e,t]=(0,o.useState)(!1),r=()=>{t(!1)};return(0,n.jsx)("nav",{className:"navbar navbar-expand-lg navbar-dark bg-dark",children:(0,n.jsxs)("div",{className:"container-fluid",children:[(0,n.jsx)(a(),{className:"navbar-brand",href:"/",onClick:r,children:"FIRE Dashboard"}),(0,n.jsx)("button",{className:"navbar-toggler",type:"button",onClick:()=>{t(!e)},"aria-controls":"navbarNavContent","aria-expanded":e,"aria-label":"Toggle navigation",children:(0,n.jsx)("span",{className:"navbar-toggler-icon"})}),(0,n.jsx)("div",{className:`collapse navbar-collapse ${e?"show":""}`,id:"navbarNavContent",children:(0,n.jsxs)("ul",{className:"navbar-nav me-auto mb-2 mb-lg-0",children:[(0,n.jsx)("li",{className:"nav-item",children:(0,n.jsx)(a(),{className:"nav-link",href:"/",onClick:r,children:"Dashboard"})}),(0,n.jsx)("li",{className:"nav-item",children:(0,n.jsx)(a(),{className:"nav-link",href:"/assets",onClick:r,children:"Patrimoine"})}),(0,n.jsx)("li",{className:"nav-item",children:(0,n.jsx)(a(),{className:"nav-link",href:"/liabilities",onClick:r,children:"Emprunts"})}),(0,n.jsx)("li",{className:"nav-item",children:(0,n.jsx)(a(),{className:"nav-link",href:"/scpi",onClick:r,children:"SCPI"})}),(0,n.jsx)("li",{className:"nav-item",children:(0,n.jsx)(a(),{className:"nav-link",href:"/evolution",onClick:r,children:"\xc9volution"})}),(0,n.jsx)("li",{className:"nav-item",children:(0,n.jsx)(a(),{className:"nav-link",href:"/budget",onClick:r,children:"Budget FIRE"})}),(0,n.jsx)("li",{className:"nav-item",children:(0,n.jsx)(a(),{className:"nav-link",href:"/scenarios",onClick:r,children:"Sc\xe9narios"})}),(0,n.jsx)("li",{className:"nav-item",children:(0,n.jsx)(a(),{className:"nav-link",href:"/fire",onClick:r,children:"Objectif FIRE"})}),(0,n.jsx)("li",{className:"nav-item",children:(0,n.jsx)(a(),{className:"nav-link",href:"/test",onClick:r,children:"Test API"})})]})})]})})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30004:(e,t,r)=>{"use strict";r.d(t,{default:()=>n});let n=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\AI-Code\\\\fire_UI\\\\frontnextjs\\\\src\\\\components\\\\Navbar.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\AI-Code\\fire_UI\\frontnextjs\\src\\components\\Navbar.tsx","default")},33337:(e,t,r)=>{Promise.resolve().then(r.bind(r,44999))},33873:e=>{"use strict";e.exports=require("path")},44999:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>e0});var n=r(60687),o=r(43210),i=r(90317),a=r(48710),s=r(31889),l=r(92388),d=r(25865),c=r(29947),h=r(54758),u=r(58022);let p={modes:{point:(e,t)=>m(e,t,{intersect:!0}),nearest:(e,t,r)=>(function(e,t,r){let n=Number.POSITIVE_INFINITY;return m(e,t,r).reduce((e,o)=>{var i;let a=o.getCenterPoint(),s="x"===(i=r.axis)?{x:t.x,y:a.y}:"y"===i?{x:a.x,y:t.y}:a,l=(0,u.aF)(t,s);return l<n?(e=[o],n=l):l===n&&e.push(o),e},[]).sort((e,t)=>e._index-t._index).slice(0,1)})(e,t,r),x:(e,t,r)=>m(e,t,{intersect:r.intersect,axis:"x"}),y:(e,t,r)=>m(e,t,{intersect:r.intersect,axis:"y"})}};function x(e,t,r){return(p.modes[r.mode]||p.modes.nearest)(e,t,r)}function m(e,t,r){return e.filter(e=>{var n;return r.intersect?e.inRange(t.x,t.y):"x"!==(n=r.axis)&&"y"!==n?e.inRange(t.x,t.y,"x",!0)||e.inRange(t.x,t.y,"y",!0):e.inRange(t.x,t.y,n,!0)})}function f(e,t,r){let n=Math.cos(r),o=Math.sin(r),i=t.x,a=t.y;return{x:i+n*(e.x-i)-o*(e.y-a),y:a+o*(e.x-i)+n*(e.y-a)}}let b=(e,t)=>t>e||e.length>t.length&&e.slice(0,t.length)===t,v=(e,t,r)=>Math.min(r,Math.max(t,e)),g=(e,t)=>e.value>=e.start-t&&e.value<=e.end+t;function y(e,{x:t,y:r,x2:n,y2:o},i,{borderWidth:a,hitTolerance:s}){let l=(a+s)/2,d=e.x>=t-l-.001&&e.x<=n+l+.001,c=e.y>=r-l-.001&&e.y<=o+l+.001;return"x"===i?d:"y"===i?c:d&&c}function j(e,{rect:t,center:r},n,{rotation:o,borderWidth:i,hitTolerance:a}){return y(f(e,r,(0,u.t)(-o)),t,n,{borderWidth:i,hitTolerance:a})}function w(e,t){let{centerX:r,centerY:n}=e.getProps(["centerX","centerY"],t);return{x:r,y:n}}let C=e=>"string"==typeof e&&e.endsWith("%"),M=e=>parseFloat(e)/100,P=e=>v(M(e),0,1),k=(e,t)=>({x:e,y:t,x2:e,y2:t,width:0,height:0}),A={box:e=>k(e.centerX,e.centerY),doughnutLabel:e=>k(e.centerX,e.centerY),ellipse:e=>({centerX:e.centerX,centerY:e.centerX,radius:0,width:0,height:0}),label:e=>k(e.centerX,e.centerY),line:e=>k(e.x,e.y),point:e=>({centerX:e.centerX,centerY:e.centerY,radius:0,width:0,height:0}),polygon:e=>k(e.centerX,e.centerY)};function S(e,t){return"start"===t?0:"end"===t?e:C(t)?P(t)*e:e/2}function N(e,t,r=!0){return"number"==typeof t?t:C(t)?(r?P(t):M(t))*e:e}function _(e,t,{borderWidth:r,position:n,xAdjust:o,yAdjust:i},a){let s=(0,u.i)(a),l=t.width+(s?a.width:0)+r,d=t.height+(s?a.height:0)+r,c=T(n),h=R(e.x,l,o,c.x),p=R(e.y,d,i,c.y);return{x:h,y:p,x2:h+l,y2:p+d,width:l,height:d,centerX:h+l/2,centerY:p+d/2}}function T(e,t="center"){return(0,u.i)(e)?{x:(0,u.v)(e.x,t),y:(0,u.v)(e.y,t)}:{x:e=(0,u.v)(e,t),y:e}}let D=(e,t)=>e&&e.autoFit&&t<1;function I(e,t){let r=e.font,n=(0,u.b)(r)?r:[r];return D(e,t)?n.map(function(e){let r=(0,u.a0)(e);return r.size=Math.floor(e.size*t),r.lineHeight=e.lineHeight,(0,u.a0)(r)}):n.map(e=>(0,u.a0)(e))}function E(e){return e&&((0,u.h)(e.xValue)||(0,u.h)(e.yValue))}function R(e,t,r=0,n){return e-S(t,n)+r}function O(e,t,r){let n=r.init;return n?!0===n?W(t,r):function(e,t,r){let n=(0,u.Q)(r.init,[{chart:e,properties:t,options:r}]);return!0===n?W(t,r):(0,u.i)(n)?n:void 0}(e,t,r):void 0}function Y(e,t,r){let n=!1;return t.forEach(t=>{(0,u.a7)(e[t])?(n=!0,r[t]=e[t]):(0,u.h)(r[t])&&delete r[t]}),n}function W(e,t){return A[t.type||"line"](e)}let F=new Map,X=e=>isNaN(e)||e<=0,q=e=>e.reduce(function(e,t){return e+t.string},"");function z(e){if(e&&"object"==typeof e){let t=e.toString();return"[object HTMLImageElement]"===t||"[object HTMLCanvasElement]"===t}}function H(e,{x:t,y:r},n){n&&(e.translate(t,r),e.rotate((0,u.t)(n)),e.translate(-t,-r))}function L(e,t){if(t&&t.borderWidth)return e.lineCap=t.borderCapStyle||"butt",e.setLineDash(t.borderDash),e.lineDashOffset=t.borderDashOffset,e.lineJoin=t.borderJoinStyle||"miter",e.lineWidth=t.borderWidth,e.strokeStyle=t.borderColor,!0}function V(e,t){e.shadowColor=t.backgroundShadowColor,e.shadowBlur=t.shadowBlur,e.shadowOffsetX=t.shadowOffsetX,e.shadowOffsetY=t.shadowOffsetY}function $(e,t){let r=t.content;if(z(r))return{width:N(r.width,t.width),height:N(r.height,t.height)};let n=I(t),o=t.textStrokeWidth,i=(0,u.b)(r)?r:[r],a=i.join()+q(n)+o+(e._measureText?"-spriting":"");return F.has(a)||F.set(a,function(e,t,r,n){e.save();let o=t.length,i=0,a=n;for(let s=0;s<o;s++){let o=r[Math.min(s,r.length-1)];e.font=o.string;let l=t[s];i=Math.max(i,e.measureText(l).width+n),a+=o.lineHeight}return e.restore(),{width:i,height:a}}(e,i,n,o)),F.get(a)}function U(e,t,r){let{x:n,y:o,width:i,height:a}=t;e.save(),V(e,r);let s=L(e,r);e.fillStyle=r.backgroundColor,e.beginPath(),(0,u.aw)(e,{x:n,y:o,w:i,h:a,radius:function(e,t,r){for(let t of Object.keys(e))e[t]=v(e[t],0,r);return e}((0,u.ay)(r.borderRadius),0,Math.min(i,a)/2)}),e.closePath(),e.fill(),s&&(e.shadowColor=r.borderShadowColor,e.stroke()),e.restore()}function B(e,t,r,n){let o=r.content;if(z(o)){e.save(),e.globalAlpha=function(e,t){let r=(0,u.x)(e)?e:t;return(0,u.x)(r)?v(r,0,1):1}(r.opacity,o.style.opacity),e.drawImage(o,t.x,t.y,t.width,t.height),e.restore();return}let i=(0,u.b)(o)?o:[o],a=I(r,n),s=r.color,l=(0,u.b)(s)?s:[s],d=function(e,t){let{x:r,width:n}=e,o=t.textAlign;return"center"===o?r+n/2:"end"===o||"right"===o?r+n:r}(t,r),c=t.y+r.textStrokeWidth/2;e.save(),e.textBaseline="middle",e.textAlign=r.textAlign,function(e,t){if(t.textStrokeWidth>0)return e.lineJoin="round",e.miterLimit=2,e.lineWidth=t.textStrokeWidth,e.strokeStyle=t.textStrokeColor,!0}(e,r)&&function(e,{x:t,y:r},n,o){e.beginPath();let i=0;n.forEach(function(n,a){let s=o[Math.min(a,o.length-1)],l=s.lineHeight;e.font=s.string,e.strokeText(n,t,r+l/2+i),i+=l}),e.stroke()}(e,{x:d,y:c},i,a),function(e,{x:t,y:r},n,{fonts:o,colors:i}){let a=0;n.forEach(function(n,s){let l=i[Math.min(s,i.length-1)],d=o[Math.min(s,o.length-1)],c=d.lineHeight;e.beginPath(),e.font=d.string,e.fillStyle=l,e.fillText(n,t,r+c/2+a),a+=c,e.fill()})}(e,{x:d,y:c},i,{fonts:a,colors:l}),e.restore()}let G=["left","bottom","top","right"],J={xScaleID:{min:"xMin",max:"xMax",start:"left",end:"right",startProp:"x",endProp:"x2"},yScaleID:{min:"yMin",max:"yMax",start:"bottom",end:"top",startProp:"y",endProp:"y2"}};function Q(e,t,r){return t="number"==typeof t?t:e.parse(t),(0,u.g)(t)?e.getPixelForValue(t):r}function K(e,t,r){let n=t[r];if(n||"scaleID"===r)return n;let o=r.charAt(0),i=Object.values(e).filter(e=>e.axis&&e.axis===o);return i.length?i[0].id:o}function Z(e,t){if(e){let r=e.options.reverse;return{start:Q(e,t.min,r?t.end:t.start),end:Q(e,t.max,r?t.start:t.end)}}}function ee(e,t){let{chartArea:r,scales:n}=e,o=n[K(n,t,"xScaleID")],i=n[K(n,t,"yScaleID")],a=r.width/2,s=r.height/2;return o&&(a=Q(o,t.xValue,o.left+o.width/2)),i&&(s=Q(i,t.yValue,i.top+i.height/2)),{x:a,y:s}}function et(e,t){let r=e.scales,n=r[K(r,t,"xScaleID")],o=r[K(r,t,"yScaleID")];if(!n&&!o)return{};let{left:i,right:a}=n||e.chartArea,{top:s,bottom:l}=o||e.chartArea,d=eo(n,{min:t.xMin,max:t.xMax,start:i,end:a});i=d.start,a=d.end;let c=eo(o,{min:t.yMin,max:t.yMax,start:l,end:s});return{x:i,y:s=c.start,x2:a,y2:l=c.end,width:a-i,height:l-s,centerX:i+(a-i)/2,centerY:s+(l-s)/2}}function er(e,t){if(!E(t)){let r=et(e,t),n=t.radius;(!n||isNaN(n))&&(t.radius=n=Math.min(r.width,r.height)/2);let o=2*n,i=r.centerX+t.xAdjust,a=r.centerY+t.yAdjust;return{x:i-n,y:a-n,x2:i+n,y2:a+n,centerX:i,centerY:a,width:o,height:o,radius:n}}var r=e,n=t;let o=ee(r,n),i=2*n.radius;return{x:o.x-n.radius+n.xAdjust,y:o.y-n.radius+n.yAdjust,x2:o.x+n.radius+n.xAdjust,y2:o.y+n.radius+n.yAdjust,centerX:o.x+n.xAdjust,centerY:o.y+n.yAdjust,radius:n.radius,width:i,height:i}}function en(e,t){let r=et(e,t);return r.initProperties=O(e,r,t),r.elements=[{type:"label",optionScope:"label",properties:function(e,t,r){let n=r.label;n.backgroundColor="transparent",n.callout.display=!1;let o=T(n.position),i=(0,u.E)(n.padding),a=$(e.ctx,n),s=function({properties:e,options:t},r,n,o){let{x:i,x2:a,width:s}=e;return ei({start:i,end:a,size:s,borderWidth:t.borderWidth},{position:n.x,padding:{start:o.left,end:o.right},adjust:t.label.xAdjust,size:r.width})}({properties:t,options:r},a,o,i),l=function({properties:e,options:t},r,n,o){let{y:i,y2:a,height:s}=e;return ei({start:i,end:a,size:s,borderWidth:t.borderWidth},{position:n.y,padding:{start:o.top,end:o.bottom},adjust:t.label.yAdjust,size:r.height})}({properties:t,options:r},a,o,i),d=a.width+i.width,c=a.height+i.height;return{x:s,y:l,x2:s+d,y2:l+c,width:d,height:c,centerX:s+d/2,centerY:l+c/2,rotation:n.rotation}}(e,r,t),initProperties:r.initProperties}],r}function eo(e,t){let r=Z(e,t)||t;return{start:Math.min(r.start,r.end),end:Math.max(r.start,r.end)}}function ei(e,t){let{start:r,end:n,borderWidth:o}=e,{position:i,padding:{start:a,end:s},adjust:l}=t,d=n-o-r-a-s-t.size;return r+o/2+l+S(d,i)}let ea=["enter","leave"],es=ea.concat("click");function el({state:e,event:t},r,n,o){let i;for(let a of n)0>o.indexOf(a)&&(i=ed(a.options[r]||e.listeners[r],a,t)||i);return i}function ed(e,t,r){return!0===(0,u.Q)(e,[t.$context,r])}let ec=["afterDraw","beforeDraw"];function eh(e,t,r){if(e.hooked){let n=t.options[r]||e.hooks[r];return(0,u.Q)(n,[t.$context])}}function eu(e,t,r,n){var o,i,a;if((0,u.g)(t[r])&&(o=e.options,i=r,a=n,!((0,u.h)(o[i])||(0,u.h)(o[a])))){let n=e[r]!==t[r];return e[r]=t[r],n}}function ep(e,t,r,n){for(let o of r){let r=e[o];if((0,u.h)(r)){let e=t.parse(r);n.min=Math.min(n.min,e),n.max=Math.max(n.max,e)}}}class ex extends h.Hg{inRange(e,t,r,n){let{x:o,y:i}=f({x:e,y:t},this.getCenterPoint(n),(0,u.t)(-this.options.rotation));return y({x:o,y:i},this.getProps(["x","y","x2","y2"],n),r,this.options)}getCenterPoint(e){return w(this,e)}draw(e){e.save(),H(e,this.getCenterPoint(),this.options.rotation),U(e,this,this.options),e.restore()}get label(){return this.elements&&this.elements[0]}resolveElementProperties(e,t){return en(e,t)}}ex.id="boxAnnotation",ex.defaults={adjustScaleRange:!0,backgroundShadowColor:"transparent",borderCapStyle:"butt",borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",borderRadius:0,borderShadowColor:"transparent",borderWidth:1,display:!0,init:void 0,hitTolerance:0,label:{backgroundColor:"transparent",borderWidth:0,callout:{display:!1},color:"black",content:null,display:!1,drawTime:void 0,font:{family:void 0,lineHeight:void 0,size:void 0,style:void 0,weight:"bold"},height:void 0,hitTolerance:void 0,opacity:void 0,padding:6,position:"center",rotation:void 0,textAlign:"start",textStrokeColor:void 0,textStrokeWidth:0,width:void 0,xAdjust:0,yAdjust:0,z:void 0},rotation:0,shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,xMax:void 0,xMin:void 0,xScaleID:void 0,yMax:void 0,yMin:void 0,yScaleID:void 0,z:0},ex.defaultRoutes={borderColor:"color",backgroundColor:"color"},ex.descriptors={label:{_fallback:!0}};class em extends h.Hg{inRange(e,t,r,n){return j({x:e,y:t},{rect:this.getProps(["x","y","x2","y2"],n),center:this.getCenterPoint(n)},r,{rotation:this.rotation,borderWidth:0,hitTolerance:this.options.hitTolerance})}getCenterPoint(e){return w(this,e)}draw(e){let t=this.options;t.display&&t.content&&(function(e,t){let{_centerX:r,_centerY:n,_radius:o,_startAngle:i,_endAngle:a,_counterclockwise:s,options:l}=t;e.save();let d=L(e,l);e.fillStyle=l.backgroundColor,e.beginPath(),e.arc(r,n,o,i,a,s),e.closePath(),e.fill(),d&&e.stroke(),e.restore()}(e,this),e.save(),H(e,this.getCenterPoint(),this.rotation),B(e,this,t,this._fitRatio),e.restore())}resolveElementProperties(e,t){var r,n;let o=(r=e,n=t,r.getSortedVisibleDatasetMetas().reduce(function(e,t){let o=t.controller;return o instanceof h.ju&&function(e,t,r){if(!t.autoHide)return!0;for(let t=0;t<r.length;t++)if(!r[t].hidden&&e.getDataVisibility(t))return!0}(r,n,t.data)&&(!e||o.innerRadius<e.controller.innerRadius)&&o.options.circumference>=90?t:e},void 0));if(!o)return{};let{controllerMeta:i,point:a,radius:s}=function({chartArea:e},t,r){let{left:n,top:o,right:i,bottom:a}=e,{innerRadius:s,offsetX:l,offsetY:d}=r.controller,c=(n+i)/2+l,h=(o+a)/2+d,p={left:Math.max(c-s,n),right:Math.min(c+s,i),top:Math.max(h-s,o),bottom:Math.min(h+s,a)},x={x:(p.left+p.right)/2,y:(p.top+p.bottom)/2},m=t.spacing+t.borderWidth/2,f=s-m,b=x.y>h,v=function(e,t,r,n){let o=-2*t,i=Math.pow(o,2)-4*(Math.pow(t,2)+Math.pow(r-e,2)-Math.pow(n,2));if(i<=0)return{_startAngle:0,_endAngle:u.T};let a=(-o-Math.sqrt(i))/2,s=(-o+Math.sqrt(i))/2;return{_startAngle:(0,u.D)({x:t,y:r},{x:a,y:e}).angle,_endAngle:(0,u.D)({x:t,y:r},{x:s,y:e}).angle}}(b?o+m:a-m,c,h,f);return{controllerMeta:{_centerX:c,_centerY:h,_radius:f,_counterclockwise:b,...v},point:x,radius:Math.min(s,Math.min(p.right-p.left,p.bottom-p.top)/2)}}(e,t,o),l=$(e.ctx,t),d=function({width:e,height:t},r){return 2*r/Math.sqrt(Math.pow(e,2)+Math.pow(t,2))}(l,s);D(t,d)&&(l={width:l.width*d,height:l.height*d});let{position:c,xAdjust:p,yAdjust:x}=t,m=_(a,l,{borderWidth:0,position:c,xAdjust:p,yAdjust:x});return{initProperties:O(e,m,t),...m,...i,rotation:t.rotation,_fitRatio:d}}}em.id="doughnutLabelAnnotation",em.defaults={autoFit:!0,autoHide:!0,backgroundColor:"transparent",backgroundShadowColor:"transparent",borderColor:"transparent",borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",borderShadowColor:"transparent",borderWidth:0,color:"black",content:null,display:!0,font:{family:void 0,lineHeight:void 0,size:void 0,style:void 0,weight:void 0},height:void 0,hitTolerance:0,init:void 0,opacity:void 0,position:"center",rotation:0,shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,spacing:1,textAlign:"center",textStrokeColor:void 0,textStrokeWidth:0,width:void 0,xAdjust:0,yAdjust:0},em.defaultRoutes={};class ef extends h.Hg{inRange(e,t,r,n){return j({x:e,y:t},{rect:this.getProps(["x","y","x2","y2"],n),center:this.getCenterPoint(n)},r,{rotation:this.rotation,borderWidth:this.options.borderWidth,hitTolerance:this.options.hitTolerance})}getCenterPoint(e){return w(this,e)}draw(e){let t=this.options,r=!(0,u.h)(this._visible)||this._visible;t.display&&t.content&&r&&(e.save(),H(e,this.getCenterPoint(),this.rotation),function(e,t){let{pointX:r,pointY:n,options:o}=t,i=o.callout,a=i&&i.display&&function(e,t){let r=t.position;return G.includes(r)?r:function(e,t){let{x:r,y:n,x2:o,y2:i,width:a,height:s,pointX:l,pointY:d,centerX:c,centerY:h,rotation:p}=e,x={x:c,y:h},m=t.start,b=N(a,m),v=N(s,m),g=[r,r+b,r+b,o],y=[n+v,i,n,i],j=[];for(let e=0;e<4;e++){let t=f({x:g[e],y:y[e]},x,(0,u.t)(p));j.push({position:G[e],distance:(0,u.aF)(t,{x:l,y:d})})}return j.sort((e,t)=>e.distance-t.distance)[0].position}(e,t)}(t,i);if(!a||function(e,t,r){let{pointX:n,pointY:o}=e,i=t.margin,a=n,s=o;return"left"===r?a+=i:"right"===r?a-=i:"top"===r?s+=i:"bottom"===r&&(s-=i),e.inRange(a,s)}(t,i,a))return;if(e.save(),e.beginPath(),!L(e,i))return e.restore();let{separatorStart:s,separatorEnd:l}=function(e,t){let r,n,{x:o,y:i,x2:a,y2:s}=e,l=function(e,t){let{width:r,height:n,options:o}=e,i=o.callout.margin+o.borderWidth/2;return"right"===t?r+i:"bottom"===t?n+i:-i}(e,t);return n="left"===t||"right"===t?{x:(r={x:o+l,y:i}).x,y:s}:{x:a,y:(r={x:o,y:i+l}).y},{separatorStart:r,separatorEnd:n}}(t,a),{sideStart:d,sideEnd:c}=function(e,t,r){let n,o,{y:i,width:a,height:s,options:l}=e,d=l.callout.start,c=function(e,t){let r=t.side;return"left"===e||"top"===e?-r:r}(t,l.callout);return o="left"===t||"right"===t?{x:(n={x:r.x,y:i+N(s,d)}).x+c,y:n.y}:{x:(n={x:r.x+N(a,d),y:r.y}).x,y:n.y+c},{sideStart:n,sideEnd:o}}(t,a,s);(i.margin>0||0===o.borderWidth)&&(e.moveTo(s.x,s.y),e.lineTo(l.x,l.y)),e.moveTo(d.x,d.y),e.lineTo(c.x,c.y);let h=f({x:r,y:n},t.getCenterPoint(),(0,u.t)(-t.rotation));e.lineTo(h.x,h.y),e.stroke(),e.restore()}(e,this),U(e,this,t),B(e,function({x:e,y:t,width:r,height:n,options:o}){let i=o.borderWidth/2,a=(0,u.E)(o.padding);return{x:e+a.left+i,y:t+a.top+i,width:r-a.left-a.right-o.borderWidth,height:n-a.top-a.bottom-o.borderWidth}}(this),t),e.restore())}resolveElementProperties(e,t){let r;if(E(t))r=ee(e,t);else{let{centerX:n,centerY:o}=et(e,t);r={x:n,y:o}}let n=(0,u.E)(t.padding),o=_(r,$(e.ctx,t),t,n);return{initProperties:O(e,o,t),pointX:r.x,pointY:r.y,...o,rotation:t.rotation}}}ef.id="labelAnnotation",ef.defaults={adjustScaleRange:!0,backgroundColor:"transparent",backgroundShadowColor:"transparent",borderCapStyle:"butt",borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",borderRadius:0,borderShadowColor:"transparent",borderWidth:0,callout:{borderCapStyle:"butt",borderColor:void 0,borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",borderWidth:1,display:!1,margin:5,position:"auto",side:5,start:"50%"},color:"black",content:null,display:!0,font:{family:void 0,lineHeight:void 0,size:void 0,style:void 0,weight:void 0},height:void 0,hitTolerance:0,init:void 0,opacity:void 0,padding:6,position:"center",rotation:0,shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,textAlign:"center",textStrokeColor:void 0,textStrokeWidth:0,width:void 0,xAdjust:0,xMax:void 0,xMin:void 0,xScaleID:void 0,xValue:void 0,yAdjust:0,yMax:void 0,yMin:void 0,yScaleID:void 0,yValue:void 0,z:0},ef.defaultRoutes={borderColor:"color"};let eb=(e,t,r)=>({x:e.x+r*(t.x-e.x),y:e.y+r*(t.y-e.y)}),ev=(e,t,r)=>eb(t,r,Math.abs((e-t.y)/(r.y-t.y))).x,eg=(e,t,r)=>eb(t,r,Math.abs((e-t.x)/(r.x-t.x))).y,ey=e=>e*e,ej=(e,t,{x:r,y:n,x2:o,y2:i},a)=>"y"===a?{start:Math.min(n,i),end:Math.max(n,i),value:t}:{start:Math.min(r,o),end:Math.max(r,o),value:e},ew=(e,t,r,n)=>(1-n)*(1-n)*e+2*(1-n)*n*t+n*n*r,eC=(e,t,r,n)=>({x:ew(e.x,t.x,r.x,n),y:ew(e.y,t.y,r.y,n)}),eM=(e,t,r,n)=>2*(1-n)*(t-e)+2*n*(r-t),eP=(e,t,r,n)=>-Math.atan2(eM(e.x,t.x,r.x,n),eM(e.y,t.y,r.y,n))+.5*u.P;class ek extends h.Hg{inRange(e,t,r,n){let o=(this.options.borderWidth+this.options.hitTolerance)/2;if("x"!==r&&"y"!==r){let r={mouseX:e,mouseY:t},{path:i,ctx:a}=this;if(i){L(a,this.options),a.lineWidth+=this.options.hitTolerance;let{chart:o}=this.$context,s=e*o.currentDevicePixelRatio,l=t*o.currentDevicePixelRatio,d=a.isPointInStroke(i,s,l)||eN(this,r,n);return a.restore(),d}return function(e,{mouseX:t,mouseY:r},n=.001,o){let i,a,{x:s,y:l,x2:d,y2:c}=e.getProps(["x","y","x2","y2"],o),h=d-s,u=c-l,p=ey(h)+ey(u),x=0===p?-1:((t-s)*h+(r-l)*u)/p;return x<0?(i=s,a=l):x>1?(i=d,a=c):(i=s+x*h,a=l+x*u),ey(t-i)+ey(r-a)<=n}(this,r,ey(o),n)||eN(this,r,n)}return function(e,{mouseX:t,mouseY:r},n,{hitSize:o,useFinalPosition:i}){return g(ej(t,r,e.getProps(["x","y","x2","y2"],i),n),o)||eN(e,{mouseX:t,mouseY:r},i,n)}(this,{mouseX:e,mouseY:t},r,{hitSize:o,useFinalPosition:n})}getCenterPoint(e){return w(this,e)}draw(e){let{x:t,y:r,x2:n,y2:o,cp:i,options:a}=this;if(e.save(),!L(e,a))return e.restore();V(e,a);let s=Math.sqrt(Math.pow(n-t,2)+Math.pow(o-r,2));if(a.curve&&i)return function(e,t,r,n){let{x:o,y:i,x2:a,y2:s,options:l}=t,{startOpts:d,endOpts:c,startAdjust:h,endAdjust:p}=eD(t),x={x:o,y:i},m={x:a,y:s},f=eP(x,r,m,0),b=eP(x,r,m,1)-u.P,v=eC(x,r,m,h/n),g=eC(x,r,m,1-p/n),y=new Path2D;e.beginPath(),y.moveTo(v.x,v.y),y.quadraticCurveTo(r.x,r.y,g.x,g.y),e.shadowColor=l.borderShadowColor,e.stroke(y),t.path=y,t.ctx=e,eR(e,v,{angle:f,adjust:h},d),eR(e,g,{angle:b,adjust:p},c)}(e,this,i,s),e.restore();let{startOpts:l,endOpts:d,startAdjust:c,endAdjust:h}=eD(this),p=Math.atan2(o-r,n-t);e.translate(t,r),e.rotate(p),e.beginPath(),e.moveTo(0+c,0),e.lineTo(s-h,0),e.shadowColor=a.borderShadowColor,e.stroke(),eE(e,0,c,l),eE(e,s,-h,d),e.restore()}get label(){return this.elements&&this.elements[0]}resolveElementProperties(e,t){let r=function(e,t){let{scales:r,chartArea:n}=e,o=r[t.scaleID],i={x:n.left,y:n.top,x2:n.right,y2:n.bottom};return o?function(e,t,r){let n=Q(e,r.value,NaN),o=Q(e,r.endValue,n);e.isHorizontal()?(t.x=n,t.x2=o):(t.y=n,t.y2=o)}(o,i,t):function(e,t,r){for(let n of Object.keys(J)){let o=e[K(e,r,n)];if(o){let{min:e,max:i,start:a,end:s,startProp:l,endProp:d}=J[n],c=Z(o,{min:r[e],max:r[i],start:o[a],end:o[s]});t[l]=c.start,t[d]=c.end}}}(r,i,t),i}(e,t),{x:n,y:o,x2:i,y2:a}=r,s=function({x:e,y:t,x2:r,y2:n},{top:o,right:i,bottom:a,left:s}){return!(e<s&&r<s||e>i&&r>i||t<o&&n<o||t>a&&n>a)}(r,e.chartArea),l=s?function(e,t,r){let{x:n,y:o}=eS(e,t,r),{x:i,y:a}=eS(t,e,r);return{x:n,y:o,x2:i,y2:a,width:Math.abs(i-n),height:Math.abs(a-o)}}({x:n,y:o},{x:i,y:a},e.chartArea):{x:n,y:o,x2:i,y2:a,width:Math.abs(i-n),height:Math.abs(a-o)};if(l.centerX=(i+n)/2,l.centerY=(a+o)/2,l.initProperties=O(e,l,t),t.curve){let e={x:l.x,y:l.y},r={x:l.x2,y:l.y2};l.cp=function(e,t,r){let{x:n,y:o,x2:i,y2:a,centerX:s,centerY:l}=e,d=Math.atan2(a-o,i-n),c=T(t.controlPoint,0);return f({x:s+N(r,c.x,!1),y:l+N(r,c.y,!1)},{x:s,y:l},d)}(l,t,(0,u.aF)(e,r))}let d=function(e,t,r){let n=r.borderWidth,o=(0,u.E)(r.padding),i=$(e.ctx,r);return function(e,t,r,n){let{width:o,height:i,padding:a}=r,{xAdjust:s,yAdjust:l}=t,d={x:e.x,y:e.y},c={x:e.x2,y:e.y2},h="auto"===t.rotation?function(e){let{x:t,y:r,x2:n,y2:o}=e,i=Math.atan2(o-r,n-t);return i>u.P/2?i-u.P:i<-(u.P/2)?i+u.P:i}(e):(0,u.t)(t.rotation),p=function(e,t,r){let n=Math.cos(r),o=Math.sin(r);return{w:Math.abs(e*n)+Math.abs(t*o),h:Math.abs(e*o)+Math.abs(t*n)}}(o,i,h),x=function(e,t,r,n){let o,i=function(e,t){let{x:r,x2:n,y:o,y2:i}=e,a=Math.min(o,i)-t.top,s=Math.min(r,n)-t.left,l=t.bottom-Math.max(o,i),d=t.right-Math.max(r,n);return{x:Math.min(s,d),y:Math.min(a,l),dx:s<=d?1:-1,dy:a<=l?1:-1}}(e,n);return"start"===t.position?e_({w:e.x2-e.x,h:e.y2-e.y},r,t,i):"end"===t.position?1-e_({w:e.x-e.x2,h:e.y-e.y2},r,t,i):S(1,t.position)}(e,t,{labelSize:p,padding:a},n),m=e.cp?eC(d,e.cp,c,x):eb(d,c,x),f={size:p.w,min:n.left,max:n.right,padding:a.left},b={size:p.h,min:n.top,max:n.bottom,padding:a.top},v=eT(m.x,f)+s,g=eT(m.y,b)+l;return{x:v-o/2,y:g-i/2,x2:v+o/2,y2:g+i/2,centerX:v,centerY:g,pointX:m.x,pointY:m.y,width:o,height:i,rotation:(0,u.U)(h)}}(t,r,{width:i.width+o.width+n,height:i.height+o.height+n,padding:o},e.chartArea)}(e,l,t.label);return d._visible=s,l.elements=[{type:"label",optionScope:"label",properties:d,initProperties:l.initProperties}],l}}ek.id="lineAnnotation";let eA={backgroundColor:void 0,backgroundShadowColor:void 0,borderColor:void 0,borderDash:void 0,borderDashOffset:void 0,borderShadowColor:void 0,borderWidth:void 0,display:void 0,fill:void 0,length:void 0,shadowBlur:void 0,shadowOffsetX:void 0,shadowOffsetY:void 0,width:void 0};function eS({x:e,y:t},r,{top:n,right:o,bottom:i,left:a}){return e<a&&(t=eg(a,{x:e,y:t},r),e=a),e>o&&(t=eg(o,{x:e,y:t},r),e=o),t<n&&(e=ev(n,{x:e,y:t},r),t=n),t>i&&(e=ev(i,{x:e,y:t},r),t=i),{x:e,y:t}}function eN(e,{mouseX:t,mouseY:r},n,o){let i=e.label;return i.options.display&&i.inRange(t,r,o,n)}function e_(e,t,r,n){let{labelSize:o,padding:i}=t,a=e.w*n.dx,s=e.h*n.dy;return v(Math.max(a>0&&(o.w/2+i.left-n.x)/a,s>0&&(o.h/2+i.top-n.y)/s),0,.25)}function eT(e,t){let{size:r,min:n,max:o,padding:i}=t,a=r/2;return r>o-n?(o+n)/2:(n>=e-i-a&&(e=n+i+a),o<=e+i+a&&(e=o-i-a),e)}function eD(e){let t=e.options,r=t.arrowHeads&&t.arrowHeads.start,n=t.arrowHeads&&t.arrowHeads.end;return{startOpts:r,endOpts:n,startAdjust:eI(e,r),endAdjust:eI(e,n)}}function eI(e,t){if(!t||!t.display)return 0;let{length:r,width:n}=t,o=e.options.borderWidth/2;return Math.abs(ev(0,{x:r,y:n+o},{x:0,y:o}))}function eE(e,t,r,n){if(!n||!n.display)return;let{length:o,width:i,fill:a,backgroundColor:s,borderColor:l}=n,d=Math.abs(t-o)+r;e.beginPath(),V(e,n),L(e,n),e.moveTo(d,-i),e.lineTo(t+r,0),e.lineTo(d,i),!0===a?(e.fillStyle=s||l,e.closePath(),e.fill(),e.shadowColor="transparent"):e.shadowColor=n.borderShadowColor,e.stroke()}function eR(e,{x:t,y:r},{angle:n,adjust:o},i){i&&i.display&&(e.save(),e.translate(t,r),e.rotate(n),eE(e,0,-o,i),e.restore())}ek.defaults={adjustScaleRange:!0,arrowHeads:{display:!1,end:Object.assign({},eA),fill:!1,length:12,start:Object.assign({},eA),width:6},borderDash:[],borderDashOffset:0,borderShadowColor:"transparent",borderWidth:2,curve:!1,controlPoint:{y:"-50%"},display:!0,endValue:void 0,init:void 0,hitTolerance:0,label:{backgroundColor:"rgba(0,0,0,0.8)",backgroundShadowColor:"transparent",borderCapStyle:"butt",borderColor:"black",borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",borderRadius:6,borderShadowColor:"transparent",borderWidth:0,callout:Object.assign({},ef.defaults.callout),color:"#fff",content:null,display:!1,drawTime:void 0,font:{family:void 0,lineHeight:void 0,size:void 0,style:void 0,weight:"bold"},height:void 0,hitTolerance:void 0,opacity:void 0,padding:6,position:"center",rotation:0,shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,textAlign:"center",textStrokeColor:void 0,textStrokeWidth:0,width:void 0,xAdjust:0,yAdjust:0,z:void 0},scaleID:void 0,shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,value:void 0,xMax:void 0,xMin:void 0,xScaleID:void 0,yMax:void 0,yMin:void 0,yScaleID:void 0,z:0},ek.descriptors={arrowHeads:{start:{_fallback:!0},end:{_fallback:!0},_fallback:!0}},ek.defaultRoutes={borderColor:"color"};class eO extends h.Hg{inRange(e,t,r,n){let o=this.options.rotation,i=(this.options.borderWidth+this.options.hitTolerance)/2;if("x"!==r&&"y"!==r)return function(e,t,r,n){let{width:o,height:i,centerX:a,centerY:s}=t,l=o/2,d=i/2;if(l<=0||d<=0)return!1;let c=(0,u.t)(r||0),h=Math.cos(c),p=Math.sin(c);return Math.pow(h*(e.x-a)+p*(e.y-s),2)/Math.pow(l+n,2)+Math.pow(p*(e.x-a)-h*(e.y-s),2)/Math.pow(d+n,2)<=1.0001}({x:e,y:t},this.getProps(["width","height","centerX","centerY"],n),o,i);let{x:a,y:s,x2:l,y2:d}=this.getProps(["x","y","x2","y2"],n),c="y"===r?{start:s,end:d}:{start:a,end:l},h=f({x:e,y:t},this.getCenterPoint(n),(0,u.t)(-o));return h[r]>=c.start-i-.001&&h[r]<=c.end+i+.001}getCenterPoint(e){return w(this,e)}draw(e){let{width:t,height:r,centerX:n,centerY:o,options:i}=this;e.save(),H(e,this.getCenterPoint(),i.rotation),V(e,this.options),e.beginPath(),e.fillStyle=i.backgroundColor;let a=L(e,i);e.ellipse(n,o,r/2,t/2,u.P/2,0,2*u.P),e.fill(),a&&(e.shadowColor=i.borderShadowColor,e.stroke()),e.restore()}get label(){return this.elements&&this.elements[0]}resolveElementProperties(e,t){return en(e,t)}}eO.id="ellipseAnnotation",eO.defaults={adjustScaleRange:!0,backgroundShadowColor:"transparent",borderDash:[],borderDashOffset:0,borderShadowColor:"transparent",borderWidth:1,display:!0,hitTolerance:0,init:void 0,label:Object.assign({},ex.defaults.label),rotation:0,shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,xMax:void 0,xMin:void 0,xScaleID:void 0,yMax:void 0,yMin:void 0,yScaleID:void 0,z:0},eO.defaultRoutes={borderColor:"color",backgroundColor:"color"},eO.descriptors={label:{_fallback:!0}};class eY extends h.Hg{inRange(e,t,r,n){let{x:o,y:i,x2:a,y2:s,width:l}=this.getProps(["x","y","x2","y2","width"],n),d=(this.options.borderWidth+this.options.hitTolerance)/2;if("x"!==r&&"y"!==r){var c,h,u;return c={x:e,y:t},h=this.getCenterPoint(n),u=l/2,!!c&&!!h&&!(u<=0)&&Math.pow(c.x-h.x,2)+Math.pow(c.y-h.y,2)<=Math.pow(u+d,2)}return g("y"===r?{start:i,end:s,value:t}:{start:o,end:a,value:e},d)}getCenterPoint(e){return w(this,e)}draw(e){let t=this.options,r=t.borderWidth;if(t.radius<.1)return;e.save(),e.fillStyle=t.backgroundColor,V(e,t);let n=L(e,t);!function(e,t,r,n){let{radius:o,options:i}=t,a=i.pointStyle,s=i.rotation,l=(s||0)*u.b4;if(z(a)){e.save(),e.translate(r,n),e.rotate(l),e.drawImage(a,-a.width/2,-a.height/2,a.width,a.height),e.restore();return}X(o)||function(e,{x:t,y:r,radius:n,rotation:o,style:i,rad:a}){let s,l,d,c;switch(e.beginPath(),i){default:e.arc(t,r,n,0,u.T),e.closePath();break;case"triangle":e.moveTo(t+Math.sin(a)*n,r-Math.cos(a)*n),a+=u.b6,e.lineTo(t+Math.sin(a)*n,r-Math.cos(a)*n),a+=u.b6,e.lineTo(t+Math.sin(a)*n,r-Math.cos(a)*n),e.closePath();break;case"rectRounded":c=.516*n,d=n-c,s=Math.cos(a+u.b5)*d,l=Math.sin(a+u.b5)*d,e.arc(t-s,r-l,c,a-u.P,a-u.H),e.arc(t+l,r-s,c,a-u.H,a),e.arc(t+s,r+l,c,a,a+u.H),e.arc(t-l,r+s,c,a+u.H,a+u.P),e.closePath();break;case"rect":if(!o){d=Math.SQRT1_2*n,e.rect(t-d,r-d,2*d,2*d);break}a+=u.b5;case"rectRot":s=Math.cos(a)*n,l=Math.sin(a)*n,e.moveTo(t-s,r-l),e.lineTo(t+l,r-s),e.lineTo(t+s,r+l),e.lineTo(t-l,r+s),e.closePath();break;case"crossRot":a+=u.b5;case"cross":s=Math.cos(a)*n,l=Math.sin(a)*n,e.moveTo(t-s,r-l),e.lineTo(t+s,r+l),e.moveTo(t+l,r-s),e.lineTo(t-l,r+s);break;case"star":s=Math.cos(a)*n,l=Math.sin(a)*n,e.moveTo(t-s,r-l),e.lineTo(t+s,r+l),e.moveTo(t+l,r-s),e.lineTo(t-l,r+s),a+=u.b5,s=Math.cos(a)*n,l=Math.sin(a)*n,e.moveTo(t-s,r-l),e.lineTo(t+s,r+l),e.moveTo(t+l,r-s),e.lineTo(t-l,r+s);break;case"line":s=Math.cos(a)*n,l=Math.sin(a)*n,e.moveTo(t-s,r-l),e.lineTo(t+s,r+l);break;case"dash":e.moveTo(t,r),e.lineTo(t+Math.cos(a)*n,r+Math.sin(a)*n)}e.fill()}(e,{x:r,y:n,radius:o,rotation:s,style:a,rad:l})}(e,this,this.centerX,this.centerY),n&&!z(t.pointStyle)&&(e.shadowColor=t.borderShadowColor,e.stroke()),e.restore(),t.borderWidth=r}resolveElementProperties(e,t){let r=er(e,t);return r.initProperties=O(e,r,t),r}}eY.id="pointAnnotation",eY.defaults={adjustScaleRange:!0,backgroundShadowColor:"transparent",borderDash:[],borderDashOffset:0,borderShadowColor:"transparent",borderWidth:1,display:!0,hitTolerance:0,init:void 0,pointStyle:"circle",radius:10,rotation:0,shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,xAdjust:0,xMax:void 0,xMin:void 0,xScaleID:void 0,xValue:void 0,yAdjust:0,yMax:void 0,yMin:void 0,yScaleID:void 0,yValue:void 0,z:0},eY.defaultRoutes={borderColor:"color",backgroundColor:"color"};class eW extends h.Hg{inRange(e,t,r,n){if("x"!==r&&"y"!==r)return this.options.radius>=.1&&this.elements.length>1&&function(e,t,r,n){let o=!1,i=e[e.length-1].getProps(["bX","bY"],n);for(let a of e){let e=a.getProps(["bX","bY"],n);e.bY>r!=i.bY>r&&t<(i.bX-e.bX)*(r-e.bY)/(i.bY-e.bY)+e.bX&&(o=!o),i=e}return o}(this.elements,e,t,n);let o=f({x:e,y:t},this.getCenterPoint(n),(0,u.t)(-this.options.rotation)),i=this.elements.map(e=>"y"===r?e.bY:e.bX),a=Math.min(...i),s=Math.max(...i);return o[r]>=a&&o[r]<=s}getCenterPoint(e){return w(this,e)}draw(e){let{elements:t,options:r}=this;e.save(),e.beginPath(),e.fillStyle=r.backgroundColor,V(e,r);let n=L(e,r),o=!0;for(let r of t)o?(e.moveTo(r.x,r.y),o=!1):e.lineTo(r.x,r.y);e.closePath(),e.fill(),n&&(e.shadowColor=r.borderShadowColor,e.stroke()),e.restore()}resolveElementProperties(e,t){let r=er(e,t),{sides:n,rotation:o}=t,i=[],a=2*u.P/n,s=o*u.b4;for(let o=0;o<n;o++,s+=a){let n=function({centerX:e,centerY:t},{radius:r,borderWidth:n,hitTolerance:o},i){let a=(n+o)/2,s=Math.sin(i),l=Math.cos(i),d={x:e+s*r,y:t-l*r};return{type:"point",optionScope:"point",properties:{x:d.x,y:d.y,centerX:d.x,centerY:d.y,bX:e+s*(r+a),bY:t-l*(r+a)}}}(r,t,s);n.initProperties=O(e,r,t),i.push(n)}return r.elements=i,r}}eW.id="polygonAnnotation",eW.defaults={adjustScaleRange:!0,backgroundShadowColor:"transparent",borderCapStyle:"butt",borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",borderShadowColor:"transparent",borderWidth:1,display:!0,hitTolerance:0,init:void 0,point:{radius:0},radius:10,rotation:0,shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,sides:3,xAdjust:0,xMax:void 0,xMin:void 0,xScaleID:void 0,xValue:void 0,yAdjust:0,yMax:void 0,yMin:void 0,yScaleID:void 0,yValue:void 0,z:0},eW.defaultRoutes={borderColor:"color",backgroundColor:"color"};let eF={box:ex,doughnutLabel:em,ellipse:eO,label:ef,line:ek,point:eY,polygon:eW};Object.keys(eF).forEach(e=>{u.d.describe(`elements.${eF[e].id}`,{_fallback:"plugins.annotation.common"})});let eX={update:Object.assign},eq=es.concat(ec),ez=(e,t)=>(0,u.i)(t)?eU(e,t):e,eH=e=>"color"===e||"font"===e;function eL(e="line"){return eF[e]?e:(console.warn(`Unknown annotation type: '${e}', defaulting to 'line'`),"line")}function eV(e,t,r,n){let o=eF[eL(r)],i=e[t];return i&&i instanceof o||Object.assign(i=e[t]=new o,n),i}function e$(e){let t=eF[eL(e.type)],r={};for(let n of(r.id=e.id,r.type=e.type,r.drawTime=e.drawTime,Object.assign(r,eU(e,t.defaults),eU(e,t.defaultRoutes)),eq))r[n]=e[n];return r}function eU(e,t){let r={};for(let n of Object.keys(t)){let o=t[n],i=e[n];eH(n)&&(0,u.b)(i)?r[n]=i.map(e=>ez(e,o)):r[n]=ez(i,o)}return r}let eB=new Map,eG=e=>"doughnutLabel"!==e.type,eJ=es.concat(ec);function eQ(e,t,r){let{ctx:n,chartArea:o}=e,i=eB.get(e);for(let e of(r&&(0,u.Y)(n,o),(function(e,t){let r=[];for(let n of e)if(n.options.drawTime===t&&r.push({element:n,main:!0}),n.elements&&n.elements.length)for(let e of n.elements)e.options.display&&e.options.drawTime===t&&r.push({element:e});return r})(i.visibleElements,t).sort((e,t)=>e.element.options.z-t.element.options.z))){var a=n,s=o,l=i,d=e;let t=d.element;d.main?(eh(l,t,"beforeDraw"),t.draw(a,s),eh(l,t,"afterDraw")):t.draw(a,s)}r&&(0,u.$)(n)}h.t1.register(h.PP,h.kc,h.FN,h.No,h.hE,h.m_,h.s$,h.dN,{id:"annotation",version:"3.1.0",beforeRegister(){!function(e,t,r,n=!0){let o=r.split("."),i=0;for(let t of"4.0".split(".")){let a=o[i++];if(parseInt(t,10)<parseInt(a,10))break;if(b(a,t))if(!n)return!1;else throw Error(`${e} v${r} is not supported. v4.0 or newer is required.`)}}("chart.js","4.0",h.t1.version)},afterRegister(){h.t1.register(eF)},afterUnregister(){h.t1.unregister(eF)},beforeInit(e){eB.set(e,{annotations:[],elements:[],visibleElements:[],listeners:{},listened:!1,moveListened:!1,hooks:{},hooked:!1,hovered:[]})},beforeUpdate(e,t,r){let n=eB.get(e).annotations=[],o=r.annotations;(0,u.i)(o)?Object.keys(o).forEach(e=>{let t=o[e];(0,u.i)(t)&&(t.id=e,n.push(t))}):(0,u.b)(o)&&n.push(...o);var i=n.filter(eG),a=e.scales;for(let e of i){var s=e,l=a;for(let e of["scaleID","xScaleID","yScaleID"]){let t=K(l,s,e);t&&!l[t]&&function(e,t){if("scaleID"===t)return!0;let r=t.charAt(0);for(let t of["Min","Max","Value"])if((0,u.h)(e[r+t]))return!0;return!1}(s,e)&&console.warn(`No scale found with id '${t}' for annotation '${s.id}'`)}}},afterDataLimits(e,t){let r=eB.get(e);!function(e,t,r){let n=function(e,t,r){let n=t.axis,o=t.id,i=n+"ScaleID",a={min:(0,u.v)(t.min,Number.NEGATIVE_INFINITY),max:(0,u.v)(t.max,Number.POSITIVE_INFINITY)};for(let s of r)s.scaleID===o?ep(s,t,["value","endValue"],a):K(e,s,i)===o&&ep(s,t,[n+"Min",n+"Max",n+"Value"],a);return a}(e.scales,t,r),o=eu(t,n,"min","suggestedMin");(o=eu(t,n,"max","suggestedMax")||o)&&(0,u.a7)(t.handleTickRangeOptions)&&t.handleTickRangeOptions()}(e,t.scale,r.annotations.filter(eG).filter(e=>e.display&&e.adjustScaleRange))},afterUpdate(e,t,r){let n=eB.get(e);n.listened=Y(r,es,n.listeners),n.moveListened=!1,ea.forEach(e=>{(0,u.a7)(r[e])&&(n.moveListened=!0)}),n.listened&&n.moveListened||n.annotations.forEach(e=>{!n.listened&&(0,u.a7)(e.click)&&(n.listened=!0),n.moveListened||ea.forEach(t=>{(0,u.a7)(e[t])&&(n.listened=!0,n.moveListened=!0)})}),function(e,t,r,n){var o,i,a,s,l,d,c,p;let x=(o=e,i=r.animations,"reset"===(a=n)||"none"===a||"resize"===a?eX:new h.Qw(o,i)),m=t.annotations,f=function(e,t){let r=t.length,n=e.length;return n<r?e.splice(n,0,...Array(r-n)):n>r&&e.splice(r,n-r),e}(t.elements,m);for(let t=0;t<m.length;t++){let r=m[t],n=eV(f,t,r.type),o=r.setContext((s=e,l=n,d=f,c=r,l.$context||(l.$context=Object.assign(Object.create(s.getContext()),{element:l,get elements(){return d.filter(e=>e&&e.options)},id:c.id,type:"annotation"})))),i=n.resolveElementProperties(e,o);i.skip=isNaN((p=i).x)||isNaN(p.y),"elements"in i&&(function(e,t,r,n){let o=e.elements||(e.elements=[]);o.length=t.length;for(let e=0;e<t.length;e++){let i=t[e],a=i.properties,s=eV(o,e,i.type,i.initProperties);a.options=e$(r[i.optionScope].override(i)),n.update(s,a)}}(n,i.elements,o,x),delete i.elements),(0,u.h)(n.x)||Object.assign(n,i),Object.assign(n,i.initProperties),i.options=e$(o),x.update(n,i)}}(e,n,r,t.mode),n.visibleElements=n.elements.filter(e=>!e.skip&&e.options.display);let o=n.visibleElements;n.hooked=Y(r,ec,n.hooks),n.hooked||o.forEach(e=>{n.hooked||ec.forEach(t=>{(0,u.a7)(e.options[t])&&(n.hooked=!0)})})},beforeDatasetsDraw(e,t,r){eQ(e,"beforeDatasetsDraw",r.clip)},afterDatasetsDraw(e,t,r){eQ(e,"afterDatasetsDraw",r.clip)},beforeDatasetDraw(e,t,r){eQ(e,t.index,r.clip)},beforeDraw(e,t,r){eQ(e,"beforeDraw",r.clip)},afterDraw(e,t,r){eQ(e,"afterDraw",r.clip)},beforeEvent(e,t,r){(function(e,t,r){if(e.listened)switch(t.type){case"mousemove":case"mouseout":let n;var o=e,i=t,a=r;if(!o.moveListened)return;n="mousemove"===i.type?x(o.visibleElements,i,a.interaction):[];let s=o.hovered;o.hovered=n;let l={state:o,event:i},d=el(l,"leave",s,n);return el(l,"enter",n,s)||d;case"click":let c;var h=e,u=t,p=r;let m=h.listeners;for(let e of x(h.visibleElements,u,p.interaction))c=ed(e.options.click||m.click,e,u)||c;return c}})(eB.get(e),t.event,r)&&(t.changed=!0)},afterDestroy(e){eB.delete(e)},getAnnotations(e){let t=eB.get(e);return t?t.elements:[]},_getAnnotationElementsAtEventForMode:(e,t,r)=>x(e,t,r),defaults:{animations:{numbers:{properties:["x","y","x2","y2","width","height","centerX","centerY","pointX","pointY","radius"],type:"number"},colors:{properties:["backgroundColor","borderColor"],type:"color"}},clip:!0,interaction:{mode:void 0,axis:void 0,intersect:void 0},common:{drawTime:"afterDatasetsDraw",init:!1,label:{}}},descriptors:{_indexable:!1,_scriptable:e=>!eJ.includes(e)&&"init"!==e,annotations:{_allKeys:!1,_fallback:(e,t)=>`elements.${eF[eL(t.type)].id}`},interaction:{_fallback:!0},common:{label:{_indexable:eH,_fallback:!0},_indexable:eH}},additionalOptionScopes:[""]});let eK=({evolutionData:e,fireTargetAmount:t})=>{let r={labels:e.map(e=>e.annee.toString()),datasets:[{label:"Patrimoine Total (€)",data:e.map(e=>e.total_patrimoine),borderColor:"rgb(75, 192, 192)",backgroundColor:"rgba(75, 192, 192, 0.2)",tension:.1,fill:!0}]},o={};return null!==t&&(o.line1={type:"line",yMin:t,yMax:t,borderColor:"rgb(255, 99, 132)",borderWidth:2,borderDash:[6,6],label:{content:`Objectif FIRE: ${t.toLocaleString("fr-FR",{style:"currency",currency:"EUR"})}`,display:!0,position:"end",backgroundColor:"rgba(255, 99, 132, 0.8)",font:{weight:"bold"},color:"white",padding:{x:6,y:3},yAdjust:-10}}),(0,n.jsxs)("div",{style:{height:"400px",marginTop:"20px",marginBottom:"20px"},children:[(0,n.jsx)(c.N1,{data:r,options:{responsive:!0,maintainAspectRatio:!1,scales:{y:{beginAtZero:!0,ticks:{callback:function(e){return"number"==typeof e?e.toLocaleString("fr-FR",{style:"currency",currency:"EUR"}):e}}}},plugins:{legend:{position:"top"},title:{display:!0,text:"\xc9volution du Patrimoine Total et Objectif FIRE",font:{size:18}},tooltip:{callbacks:{label:function(e){let t=e.dataset.label||"";return t&&(t+=": "),null!==e.parsed.y&&(t+=new Intl.NumberFormat("fr-FR",{style:"currency",currency:"EUR"}).format(e.parsed.y)),t}}},annotation:{annotations:o}},interaction:{mode:"index",intersect:!1}}})," "]})},eZ=({evolution:e,onSave:t,onCancel:r})=>{let[d,c]=(0,o.useState)({annee:e?.annee||new Date().getFullYear(),investissement:e?.investissement||0,prix_part_scpi:e?.prix_part_scpi||0,remboursement_credit:e?.remboursement_credit||0,valeur_reelle_scpi:e?.valeur_reelle_scpi||0,total_patrimoine:e?.total_patrimoine||0});(0,o.useEffect)(()=>{c({annee:e?.annee||new Date().getFullYear(),investissement:e?.investissement||0,prix_part_scpi:e?.prix_part_scpi||0,remboursement_credit:e?.remboursement_credit||0,valeur_reelle_scpi:e?.valeur_reelle_scpi||0,total_patrimoine:e?.total_patrimoine||0})},[e]);let h=async r=>{r.preventDefault();let n=e?.id?"put":"post",o=e?.id?`/evolution/${e.annee}`:"/evolution";try{await i.A[n](o,d),t()}catch(e){console.error("Erreur lors de la sauvegarde des donn\xe9es d&apos;\xe9volution",e instanceof Error?e.message:"Unknown error")}};return(0,n.jsxs)(a.A,{onSubmit:h,children:[(0,n.jsxs)(a.A.Group,{className:"mb-3",children:[(0,n.jsx)(a.A.Label,{children:"Ann\xe9e"}),(0,n.jsx)(a.A.Control,{type:"number",min:"2000",max:new Date().getFullYear()+5,value:d.annee,onChange:e=>c({...d,annee:parseInt(e.target.value)||0}),required:!0,disabled:!!e?.id})]}),(0,n.jsxs)(a.A.Group,{className:"mb-3",children:[(0,n.jsx)(a.A.Label,{children:"Investissement total (€)"}),(0,n.jsx)(a.A.Control,{type:"number",step:"0.01",value:d.investissement,onChange:e=>c({...d,investissement:parseFloat(e.target.value)||0}),required:!0})]}),(0,n.jsxs)(a.A.Group,{className:"mb-3",children:[(0,n.jsx)(a.A.Label,{children:"Prix moyen des parts SCPI (€)"}),(0,n.jsx)(a.A.Control,{type:"number",step:"0.01",value:d.prix_part_scpi,onChange:e=>c({...d,prix_part_scpi:parseFloat(e.target.value)||0}),required:!0})]}),(0,n.jsxs)(a.A.Group,{className:"mb-3",children:[(0,n.jsx)(a.A.Label,{children:"Remboursement cr\xe9dit (€)"}),(0,n.jsx)(a.A.Control,{type:"number",step:"0.01",value:d.remboursement_credit,onChange:e=>c({...d,remboursement_credit:parseFloat(e.target.value)||0}),required:!0})]}),(0,n.jsxs)(a.A.Group,{className:"mb-3",children:[(0,n.jsx)(a.A.Label,{children:"Valeur r\xe9elle SCPI (€)"}),(0,n.jsx)(a.A.Control,{type:"number",step:"0.01",value:d.valeur_reelle_scpi,onChange:e=>c({...d,valeur_reelle_scpi:parseFloat(e.target.value)||0}),required:!0})]}),(0,n.jsxs)(a.A.Group,{className:"mb-3",children:[(0,n.jsx)(a.A.Label,{children:"Total patrimoine (€)"}),(0,n.jsx)(a.A.Control,{type:"number",step:"0.01",value:d.total_patrimoine,onChange:e=>c({...d,total_patrimoine:parseFloat(e.target.value)||0}),required:!0})]}),(0,n.jsx)(s.A,{variant:"info",children:(0,n.jsx)("small",{children:"Les m\xe9triques d'\xe9volution (%, €, TCAM) seront calcul\xe9es automatiquement apr\xe8s la sauvegarde."})}),(0,n.jsxs)("div",{className:"d-flex justify-content-end gap-2",children:[(0,n.jsx)(l.A,{variant:"secondary",onClick:r,children:"Annuler"}),(0,n.jsx)(l.A,{variant:"primary",type:"submit",children:"Sauvegarder"})]})]})},e0=()=>{let[e,t]=(0,o.useState)([]),[r,a]=(0,o.useState)(!0),[s,c]=(0,o.useState)(null),[h,u]=(0,o.useState)(!1),[p,x]=(0,o.useState)(null),[m,f]=(0,o.useState)(0),[b,v]=(0,o.useState)(null),g=async()=>{try{let e=await i.A.get("/fire-settings");if(e.data&&void 0!==e.data.fire_target_amount)v(e.data.fire_target_amount);else{let e=await i.A.get("/fire-target");v(e.data.fire_target_amount)}}catch(e){console.error("Erreur lors de la r\xe9cup\xe9ration de l'objectif FIRE",e),v(910150)}},y=()=>{a(!0),c(null),i.A.get("/evolution").then(e=>{t(e.data),a(!1),f(0)}).catch(e=>{console.error("Evolution API error:",e),m<2?(f(e=>e+1),setTimeout(()=>y(),1e3)):(c("Erreur lors de la r\xe9cup\xe9ration des donn\xe9es d'\xe9volution."),a(!1))})};(0,o.useEffect)(()=>{y(),g()},[]);let j=async e=>{if(window.confirm(`\xcates-vous s\xfbr de vouloir supprimer les donn\xe9es de ${e} ?`))try{await i.A.delete(`/evolution/${e}`),y()}catch(e){console.error("Erreur lors de la suppression des donn\xe9es d'\xe9volution",e)}},w=e=>null==e?"N/A":new Intl.NumberFormat("fr-FR",{style:"currency",currency:"EUR"}).format(e),C=e=>null==e?"N/A":new Intl.NumberFormat("fr-FR",{style:"percent",minimumFractionDigits:2,maximumFractionDigits:2}).format(e/100);return r&&!h&&0===m?(0,n.jsx)("p",{children:"Chargement des donn\xe9es d'\xe9volution..."}):s&&m>=2?(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"text-danger",children:s}),(0,n.jsx)("button",{className:"btn btn-primary",onClick:()=>{f(0),y()},children:"R\xe9essayer"})]}):r||e.length||s?r&&!h?(0,n.jsxs)("p",{children:["Chargement des donn\xe9es d'\xe9volution, tentative ",m+1,"..."]}):(0,n.jsxs)("div",{children:[(0,n.jsxs)("div",{className:"d-flex justify-content-between align-items-center mb-4",children:[(0,n.jsx)("h1",{children:"\xc9volution Annuelle du Patrimoine"}),(0,n.jsx)(l.A,{variant:"primary",onClick:()=>{x({}),u(!0)},children:"Ajouter une ann\xe9e"})]}),e.length>0&&(0,n.jsx)("div",{className:"mb-4",children:(0,n.jsxs)("div",{className:"row",children:[(0,n.jsx)("div",{className:"col-md-3",children:(0,n.jsx)("div",{className:"card text-white bg-primary",children:(0,n.jsxs)("div",{className:"card-body text-center",children:[(0,n.jsx)("h5",{children:"P\xe9riode"}),(0,n.jsxs)("h4",{children:[e[0]?.annee," - ",e[e.length-1]?.annee]})]})})}),(0,n.jsx)("div",{className:"col-md-3",children:(0,n.jsx)("div",{className:"card text-white bg-success",children:(0,n.jsxs)("div",{className:"card-body text-center",children:[(0,n.jsx)("h5",{children:"Croissance Totale"}),(0,n.jsx)("h4",{children:w(e[e.length-1]?.total_patrimoine-e[0]?.total_patrimoine)})]})})}),(0,n.jsx)("div",{className:"col-md-3",children:(0,n.jsx)("div",{className:"card text-white bg-info",children:(0,n.jsxs)("div",{className:"card-body text-center",children:[(0,n.jsx)("h5",{children:"TCAM Moyen"}),(0,n.jsx)("h4",{children:C(e[e.length-1]?.tcam)})]})})}),(0,n.jsx)("div",{className:"col-md-3",children:(0,n.jsx)("div",{className:"card text-white bg-warning",children:(0,n.jsxs)("div",{className:"card-body text-center",children:[(0,n.jsx)("h5",{children:"Patrimoine Actuel"}),(0,n.jsx)("h4",{children:w(e[e.length-1]?.total_patrimoine)})]})})})]})}),e.length>0&&(0,n.jsx)("div",{className:"table-responsive",children:(0,n.jsxs)("table",{className:"table table-striped table-hover",children:[(0,n.jsx)("thead",{className:"table-dark",children:(0,n.jsxs)("tr",{children:[(0,n.jsx)("th",{children:"Ann\xe9e"}),(0,n.jsx)("th",{className:"text-end",children:"Total Patrimoine"}),(0,n.jsx)("th",{className:"text-end",children:"Evolution en %"}),(0,n.jsx)("th",{className:"text-end",children:"Evolution en €"}),(0,n.jsx)("th",{className:"text-end",children:"Croissance Annuel Moyen (€)"}),(0,n.jsx)("th",{className:"text-end",children:"TCAM (%)"}),(0,n.jsx)("th",{className:"text-center",children:"Actions"})]})}),(0,n.jsx)("tbody",{children:e.map(e=>(0,n.jsxs)("tr",{children:[(0,n.jsx)("td",{children:(0,n.jsx)("strong",{children:e.annee})}),(0,n.jsx)("td",{className:"text-end",children:(0,n.jsx)("strong",{children:w(e.total_patrimoine)})}),(0,n.jsx)("td",{className:`text-end ${(e.evolution_pourcentage||0)>=0?"text-success":"text-danger"}`,children:C(e.evolution_pourcentage)}),(0,n.jsx)("td",{className:`text-end ${(e.evolution_euros||0)>=0?"text-success":"text-danger"}`,children:w(e.evolution_euros)}),(0,n.jsx)("td",{className:"text-end",children:w(e.croissance_moyenne)}),(0,n.jsx)("td",{className:"text-end text-info",children:C(e.tcam)}),(0,n.jsxs)("td",{className:"text-center",children:[(0,n.jsx)(l.A,{variant:"outline-primary",size:"sm",className:"me-2",onClick:()=>{x(e),u(!0)},children:"Modifier"}),(0,n.jsx)(l.A,{variant:"outline-danger",size:"sm",onClick:()=>j(e.annee),children:"Supprimer"})]})]},e.id))})]})}),(0,n.jsxs)(d.A,{show:h,onHide:()=>u(!1),size:"lg",children:[(0,n.jsx)(d.A.Header,{closeButton:!0,children:(0,n.jsx)(d.A.Title,{children:p?.id?`Modifier ${p.annee}`:"Ajouter une ann\xe9e"})}),(0,n.jsx)(d.A.Body,{children:(0,n.jsx)(eZ,{evolution:p,onSave:()=>{u(!1),x(null),y()},onCancel:()=>u(!1)})})]}),e.length>0&&null!==b&&(0,n.jsx)(eK,{evolutionData:e,fireTargetAmount:b}),(0,n.jsxs)("div",{className:"mt-5 p-4 bg-light rounded",children:[(0,n.jsx)("h4",{children:"Comprendre les m\xe9triques d'\xe9volution"}),(0,n.jsx)("hr",{}),(0,n.jsxs)("dl",{className:"row",children:[(0,n.jsx)("dt",{className:"col-sm-4",children:"TCAM (Taux de Croissance Annuel Moyen)"}),(0,n.jsxs)("dd",{className:"col-sm-8",children:[(0,n.jsx)("p",{children:"Le TCAM repr\xe9sente le taux de croissance annuel compos\xe9 moyen de votre patrimoine sur une p\xe9riode donn\xe9e (depuis 2015 dans ce tableau). Il lisse les fluctuations annuelles pour donner une id\xe9e de la performance moyenne \xe0 long terme."}),(0,n.jsxs)("p",{children:[(0,n.jsx)("em",{children:"Formule :"})," ",(0,n.jsx)("code",{children:"((Valeur Finale / Valeur Initiale)^(1 / Nombre d'Ann\xe9es)) - 1"})]}),(0,n.jsx)("p",{children:"Un TCAM \xe9lev\xe9 indique une croissance robuste et constante de votre patrimoine au fil du temps."})]}),(0,n.jsx)("dt",{className:"col-sm-4 mt-3",children:"Evolution en %"}),(0,n.jsxs)("dd",{className:"col-sm-8 mt-3",children:[(0,n.jsx)("p",{children:"Cette valeur montre la variation en pourcentage du patrimoine total par rapport \xe0 l'ann\xe9e pr\xe9c\xe9dente."}),(0,n.jsxs)("p",{children:[(0,n.jsx)("em",{children:"Formule :"})," ",(0,n.jsx)("code",{children:"((Total Patrimoine Ann\xe9e N - Total Patrimoine Ann\xe9e N-1) / Total Patrimoine Ann\xe9e N-1) * 100"})]}),(0,n.jsx)("p",{children:"Elle permet d'identifier rapidement les ann\xe9es de forte croissance ou de stagnation."})]}),(0,n.jsx)("dt",{className:"col-sm-4 mt-3",children:"Evolution en €"}),(0,n.jsxs)("dd",{className:"col-sm-8 mt-3",children:[(0,n.jsx)("p",{children:"Indique l'augmentation (ou la diminution) absolue du patrimoine total en euros par rapport \xe0 l'ann\xe9e pr\xe9c\xe9dente."}),(0,n.jsxs)("p",{children:[(0,n.jsx)("em",{children:"Formule :"})," ",(0,n.jsx)("code",{children:"Total Patrimoine Ann\xe9e N - Total Patrimoine Ann\xe9e N-1"})]}),(0,n.jsx)("p",{children:"Cette m\xe9trique donne une mesure concr\xe8te de la richesse cr\xe9\xe9e (ou perdue) chaque ann\xe9e."})]}),(0,n.jsx)("dt",{className:"col-sm-4 mt-3",children:"Croissance Annuel Moyen (€)"}),(0,n.jsxs)("dd",{className:"col-sm-8 mt-3",children:[(0,n.jsx)("p",{children:"Repr\xe9sente la moyenne simple de l'augmentation annuelle de votre patrimoine en euros depuis l'ann\xe9e de base (2015)."}),(0,n.jsxs)("p",{children:[(0,n.jsx)("em",{children:"Formule :"})," ",(0,n.jsx)("code",{children:"(Total Patrimoine Ann\xe9e N - Total Patrimoine 2015) / (Ann\xe9e N - 2015)"})]}),(0,n.jsx)("p",{children:"Elle donne une indication de la contribution mon\xe9taire moyenne annuelle \xe0 la croissance de votre patrimoine depuis le d\xe9but de la p\xe9riode de suivi."})]})]})]})]}):(0,n.jsx)("p",{children:"Aucune donn\xe9e d'\xe9volution trouv\xe9e."})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},61135:()=>{},62907:(e,t,r)=>{Promise.resolve().then(r.bind(r,30004))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});var n=r(31658);let o=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,n.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},73523:(e,t,r)=>{Promise.resolve().then(r.bind(r,29190))},74075:e=>{"use strict";e.exports=require("zlib")},78162:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});var n=r(31658);let o=async e=>[{type:"image/png",sizes:"192x192",url:(0,n.fillMetadataSegment)(".",await e.params,"icon.png")+"?d84b06edc1baa420"}]},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},90317:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});let n=r(51060).A.create({baseURL:"http://localhost:8000/api",headers:{"Content-Type":"application/json"},timeout:1e4});n.interceptors.response.use(e=>e,e=>(console.error("API Error:",e.response?.status,e.config?.url,e.message),Promise.reject(e)));let o=n},91406:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\AI-Code\\\\fire_UI\\\\frontnextjs\\\\src\\\\app\\\\evolution\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\AI-Code\\fire_UI\\frontnextjs\\src\\app\\evolution\\page.tsx","default")},93991:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},94431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s,metadata:()=>i,viewport:()=>a});var n=r(37413);r(61135),r(27209);var o=r(30004);let i={title:"FIRE Dashboard",description:"Application pour le suivi de l'objectif FIRE (Financial Independence, Retire Early)",manifest:"/manifest.json",icons:{icon:"/icon.png",apple:"/apple-icon.png"}},a={themeColor:"#000000"};function s({children:e}){return(0,n.jsx)("html",{lang:"en",children:(0,n.jsx)("body",{children:(0,n.jsxs)("div",{className:"App",children:[(0,n.jsx)(o.default,{}),(0,n.jsx)("main",{className:"container mt-4",children:e})]})})})}},94650:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});var n=r(31658);let o=async e=>[{type:"image/png",sizes:"192x192",url:(0,n.fillMetadataSegment)(".",await e.params,"apple-icon.png")+"?d84b06edc1baa420"}]},94735:e=>{"use strict";e.exports=require("events")},96889:(e,t,r)=>{Promise.resolve().then(r.bind(r,91406))}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[447,989,517,315,36,889,947],()=>r(13461));module.exports=n})();