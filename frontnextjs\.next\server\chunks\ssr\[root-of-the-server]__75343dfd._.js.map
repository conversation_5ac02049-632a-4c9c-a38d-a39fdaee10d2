{"version": 3, "sources": [], "sections": [{"offset": {"line": 111, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/AI-Code/fire_UI/frontnextjs/src/components/ApiClient.ts"], "sourcesContent": ["import axios from 'axios';\r\n\r\nconst apiClient = axios.create({\r\n  baseURL: 'http://localhost:8000/api',\r\n  headers: {\r\n    'Content-Type': 'application/json',\r\n  },\r\n  timeout: 10000, // 10 second timeout\r\n});\r\n\r\n// Add response interceptor for error handling\r\napiClient.interceptors.response.use(\r\n  (response) => response,\r\n  (error) => {\r\n    console.error('API Error:', error.response?.status, error.config?.url, error.message);\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\nexport default apiClient;\r\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,YAAY,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAC7B,SAAS;IACT,SAAS;QACP,gBAAgB;IAClB;IACA,SAAS;AACX;AAEA,8CAA8C;AAC9C,UAAU,YAAY,CAAC,QAAQ,CAAC,GAAG,CACjC,CAAC,WAAa,UACd,CAAC;IACC,QAAQ,KAAK,CAAC,cAAc,MAAM,QAAQ,EAAE,QAAQ,MAAM,MAAM,EAAE,KAAK,MAAM,OAAO;IACpF,OAAO,QAAQ,MAAM,CAAC;AACxB;uCAGa", "debugId": null}}, {"offset": {"line": 135, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/AI-Code/fire_UI/frontnextjs/src/app/test/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState } from 'react';\r\nimport apiClient from '../../components/ApiClient'; // Ajusté\r\n\r\nconst ApiTestPage: React.FC = () => {\r\n  const [result, setResult] = useState<string>('');\r\n  const [loading, setLoading] = useState(false);\r\n\r\n  const testDashboard = async () => {\r\n    setLoading(true);\r\n    setResult('Testing with Axios apiClient...');\r\n    try {\r\n      const response = await apiClient.get('/dashboard');\r\n      setResult(`Axios Success: ${JSON.stringify(response.data, null, 2)}`);\r\n    } catch (error: unknown) {\r\n      const errorMessage = error instanceof Error ? error.message : 'Unknown error';\r\n      setResult(`Axios Error: ${errorMessage}`);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const testDirectFetchLocalhost = async () => {\r\n    setLoading(true);\r\n    setResult('Testing with fetch (http://localhost:8000/api/dashboard)...');\r\n    try {\r\n      const response = await fetch('http://localhost:8000/api/dashboard');\r\n      if (!response.ok) {\r\n        throw new Error(`HTTP error! status: ${response.status}`);\r\n      }\r\n      const data = await response.json();\r\n      setResult(`Fetch (localhost) Success: ${JSON.stringify(data, null, 2)}`);\r\n    } catch (error: unknown) {\r\n      const errorMessage = error instanceof Error ? error.message : 'Unknown error';\r\n      setResult(`Fetch (localhost) Error: ${errorMessage}`);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const testDirectFetch127 = async () => {\r\n    setLoading(true);\r\n    setResult('Testing with fetch (http://127.0.0.1:8000/api/dashboard)...');\r\n    try {\r\n      const response = await fetch('http://127.0.0.1:8000/api/dashboard');\r\n      if (!response.ok) {\r\n        throw new Error(`HTTP error! status: ${response.status}`);\r\n      }\r\n      const data = await response.json();\r\n      setResult(`Fetch (127.0.0.1) Success: ${JSON.stringify(data, null, 2)}`);\r\n    } catch (error: unknown) {\r\n      const errorMessage = error instanceof Error ? error.message : 'Unknown error';\r\n      setResult(`Fetch (127.0.0.1) Error: ${errorMessage}`);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"container mt-4\">\r\n      <h2>Test des Appels API vers le Backend</h2>\r\n      <p>\r\n        Cet outil permet de vérifier la connectivité et les réponses de base du backend FastAPI.\r\n        Assurez-vous que le serveur backend (<code>cd backend && uvicorn main:app --reload</code>) est en cours d&apos;exécution sur <code>http://localhost:8000</code>.\r\n      </p>\r\n      <div className=\"mb-3\">\r\n        <button\r\n          className=\"btn btn-primary me-2 mb-2\"\r\n          onClick={testDashboard}\r\n          disabled={loading}\r\n        >\r\n          Test /dashboard (via apiClient Axios)\r\n        </button>\r\n        <button\r\n          className=\"btn btn-secondary me-2 mb-2\"\r\n          onClick={testDirectFetchLocalhost}\r\n          disabled={loading}\r\n        >\r\n          Test /dashboard (via fetch direct sur localhost:8000)\r\n        </button>\r\n        <button\r\n          className=\"btn btn-info mb-2\"\r\n          onClick={testDirectFetch127}\r\n          disabled={loading}\r\n        >\r\n          Test /dashboard (via fetch direct sur 127.0.0.1:8000)\r\n        </button>\r\n      </div>\r\n      {loading && <p>Chargement du test...</p>}\r\n      <pre className=\"bg-light p-3 border rounded\" style={{ whiteSpace: 'pre-wrap', wordBreak: 'break-all' }}>\r\n        {result || \"Cliquez sur un bouton pour tester l'API.\"}\r\n      </pre>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ApiTestPage;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA,8NAAoD,SAAS;AAH7D;;;;AAKA,MAAM,cAAwB;IAC5B,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,gBAAgB;QACpB,WAAW;QACX,UAAU;QACV,IAAI;YACF,MAAM,WAAW,MAAM,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAC;YACrC,UAAU,CAAC,eAAe,EAAE,KAAK,SAAS,CAAC,SAAS,IAAI,EAAE,MAAM,IAAI;QACtE,EAAE,OAAO,OAAgB;YACvB,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC9D,UAAU,CAAC,aAAa,EAAE,cAAc;QAC1C,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,2BAA2B;QAC/B,WAAW;QACX,UAAU;QACV,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YACA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,UAAU,CAAC,2BAA2B,EAAE,KAAK,SAAS,CAAC,MAAM,MAAM,IAAI;QACzE,EAAE,OAAO,OAAgB;YACvB,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC9D,UAAU,CAAC,yBAAyB,EAAE,cAAc;QACtD,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,qBAAqB;QACzB,WAAW;QACX,UAAU;QACV,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YACA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,UAAU,CAAC,2BAA2B,EAAE,KAAK,SAAS,CAAC,MAAM,MAAM,IAAI;QACzE,EAAE,OAAO,OAAgB;YACvB,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC9D,UAAU,CAAC,yBAAyB,EAAE,cAAc;QACtD,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;0BAAG;;;;;;0BACJ,8OAAC;;oBAAE;kCAEoC,8OAAC;kCAAK;;;;;;oBAA8C;kCAAoC,8OAAC;kCAAK;;;;;;oBAA4B;;;;;;;0BAEjK,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,WAAU;wBACV,SAAS;wBACT,UAAU;kCACX;;;;;;kCAGD,8OAAC;wBACC,WAAU;wBACV,SAAS;wBACT,UAAU;kCACX;;;;;;kCAGD,8OAAC;wBACC,WAAU;wBACV,SAAS;wBACT,UAAU;kCACX;;;;;;;;;;;;YAIF,yBAAW,8OAAC;0BAAE;;;;;;0BACf,8OAAC;gBAAI,WAAU;gBAA8B,OAAO;oBAAE,YAAY;oBAAY,WAAW;gBAAY;0BAClG,UAAU;;;;;;;;;;;;AAInB;uCAEe", "debugId": null}}]}