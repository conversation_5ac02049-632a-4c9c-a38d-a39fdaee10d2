import type { Metadata, Viewport } from "next"; // Ajoutez Viewport
import "./globals.css";
import "bootstrap/dist/css/bootstrap.min.css";
import Navbar from "../components/Navbar";

export const metadata: Metadata = {
  title: "FIRE Dashboard",
  description: "Application pour le suivi de l'objectif FIRE (Financial Independence, Retire Early)",
  manifest: "/manifest.json",
  icons: {
    icon: "/icon.png",
    apple: "/apple-icon.png",
  },
};

// Ajoutez ce bloc pour résoudre l'erreur themeColor
export const viewport: Viewport = {
  themeColor: "#000000",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body>
        <div className="App">
          <Navbar />
          <main className="container mt-4">
            {children}
          </main>
        </div>
      </body>
    </html>
  );
}