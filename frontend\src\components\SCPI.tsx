import React, { useEffect, useState, FormEvent } from 'react';
import apiClient from './ApiClient';
import { Modal, Button, Form } from 'react-bootstrap';

interface SCPI {
  id: number;
  name: string;
  price_per_share: number;
  number_of_shares: number;
  total_value: number;
  update_date: string;
  // Add the Primaliance URL field, optional
  primaliance_url?: string;
}

// Define a type for the scraped SCPI data
interface ScrapedScpiData {
  nom?: string;
  type_de_capital?: string;
  capitalisation?: string;
  collecte_nette_2024?: string;
  collecte_brute_2024?: string;
  parts_en_attente?: string;
  parts_echange?: string;
  versement_des_dividendes?: string;
  frais_reel_de_souscription?: string;
  frais_de_gestion?: string;
  delai_de_jouissance?: string;
  minimum_1ere_souscription?: string;
  prix_retrait_recent?: string;
  prix_souscription_recent?: string;
  date_prix_recent?: string;
}

const SCPIForm: React.FC<{ scpi: Partial<SCPI> | null, onSave: () => void, onCancel: () => void }> = ({ scpi, onSave, onCancel }) => {
    const [formData, setFormData] = useState({
        name: scpi?.name || '',
        price_per_share: scpi?.price_per_share || 0,
        number_of_shares: scpi?.number_of_shares || 0,
        total_value: scpi?.total_value || 0,
        update_date: scpi?.update_date ? new Date(scpi.update_date).toISOString().split('T')[0] : new Date().toISOString().split('T')[0],
        primaliance_url: scpi?.primaliance_url || '', // Initialize primaliance_url
    });

    // Auto-calculate total_value when price or shares change
    useEffect(() => {
        const calculatedTotal = formData.price_per_share * formData.number_of_shares;
        if (calculatedTotal !== formData.total_value) {
            setFormData(prev => ({ ...prev, total_value: calculatedTotal }));
        }
    }, [formData.price_per_share, formData.number_of_shares]);

    const handleSubmit = async (e: FormEvent) => {
        e.preventDefault();
        const method = scpi?.id ? 'put' : 'post';
        const url = scpi?.id ? `/scpi/${scpi.id}` : '/scpi';
        
        console.log('Submitting SCPI:', { method, url, formData });
        
        try {
            const response = await apiClient[method](url, formData);
            console.log('SCPI saved successfully:', response.data);
            onSave();
        } catch (error: any) {
            console.error("Erreur lors de la sauvegarde de la SCPI", error);
            console.error("Error details:", error.response?.data);
        }
    };

    return (
        <Form onSubmit={handleSubmit}>
            <Form.Group className="mb-3">
                <Form.Label>Nom de la SCPI</Form.Label>
                <Form.Control
                    type="text"
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    required
                />
            </Form.Group>
            
            <Form.Group className="mb-3">
                <Form.Label>Prix par part (€)</Form.Label>
                <Form.Control
                    type="number"
                    step="0.01"
                    value={formData.price_per_share}
                    onChange={(e) => setFormData({ ...formData, price_per_share: parseFloat(e.target.value) || 0 })}
                    required
                />
            </Form.Group>
            
            <Form.Group className="mb-3">
                <Form.Label>Nombre de parts</Form.Label>
                <Form.Control
                    type="number"
                    value={formData.number_of_shares}
                    onChange={(e) => setFormData({ ...formData, number_of_shares: parseInt(e.target.value) || 0 })}
                    required
                />
            </Form.Group>
            
            <Form.Group className="mb-3">
                <Form.Label>Valeur totale (€)</Form.Label>
                <Form.Control
                    type="number"
                    step="0.01"
                    value={formData.total_value}
                    onChange={(e) => setFormData({ ...formData, total_value: parseFloat(e.target.value) || 0 })}
                    required
                />
                <Form.Text className="text-muted">
                    Calculé automatiquement : {(formData.price_per_share * formData.number_of_shares).toFixed(2)} €
                </Form.Text>
            </Form.Group>
            
            <Form.Group className="mb-3">
                <Form.Label>Date de mise à jour</Form.Label>
                <Form.Control
                    type="date"
                    value={formData.update_date}
                    onChange={(e) => setFormData({ ...formData, update_date: e.target.value })}
                    required
                />
            </Form.Group>

            <Form.Group className="mb-3">
                <Form.Label>URL Primaliance (Optionnel)</Form.Label>
                <Form.Control
                    type="url"
                    value={formData.primaliance_url}
                    onChange={(e) => setFormData({ ...formData, primaliance_url: e.target.value })}
                    placeholder="https://www.primaliance.com/..."
                />
                <Form.Text className="text-muted">
                    Lien vers la page de la SCPI sur Primaliance pour récupérer des informations détaillées.
                </Form.Text>
            </Form.Group>
            
            <div className="d-flex justify-content-end gap-2">
                <Button variant="secondary" onClick={onCancel}>Annuler</Button>
                <Button variant="primary" type="submit">Sauvegarder</Button>
            </div>
        </Form>
    );
};

const SCPI: React.FC = () => {
  const [scpis, setSCPIs] = useState<SCPI[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showModal, setShowModal] = useState(false);
  const [selectedSCPI, setSelectedSCPI] = useState<Partial<SCPI> | null>(null);
  const [retryCount, setRetryCount] = useState(0);
  // State for scraped data
  const [scrapedData, setScrapedData] = useState<ScrapedScpiData | null>(null);
  const [scrapingSCPIId, setScrapingSCPIId] = useState<number | null>(null);
  const [isScraping, setIsScraping] = useState(false);
  const [scrapingError, setScrapingError] = useState<string | null>(null);


  const fetchSCPIs = () => {
    setLoading(true);
    setError(null);
    
    apiClient.get('/scpi')
      .then(response => {
        setSCPIs(response.data);
        setLoading(false);
        setRetryCount(0);
      })
      .catch(error => {
        console.error('SCPI API error:', error);
        
        if (retryCount < 2) {
          setRetryCount(prev => prev + 1);
          setTimeout(() => fetchSCPIs(), 1000);
        } else {
          setError('Erreur lors de la récupération des SCPI.');
          setLoading(false);
        }
      });
  };

  useEffect(() => {
    fetchSCPIs();
  }, []);

  const handleSave = () => {
    setShowModal(false);
    setSelectedSCPI(null);
    fetchSCPIs();
  };

  const handleDelete = async (id: number) => {
    if (window.confirm("Êtes-vous sûr de vouloir supprimer cette SCPI ?")) {
        try {
            await apiClient.delete(`/scpi/${id}`);
            fetchSCPIs();
        } catch (error) {
            console.error("Erreur lors de la suppression de la SCPI", error);
        }
    }
  };

  const handleScrapeSCPI = async (scpi: SCPI) => {
    if (!scpi.primaliance_url) {
      setScrapingError("Aucune URL Primaliance n'est configurée pour cette SCPI.");
      setScrapedData(null);
      setScrapingSCPIId(scpi.id); // Show error under this SCPI
      return;
    }

    setIsScraping(true);
    setScrapingError(null);
    setScrapedData(null);
    setScrapingSCPIId(scpi.id);

    try {
      const response = await apiClient.get(`/scpi/scrape/?url=${encodeURIComponent(scpi.primaliance_url)}`);
      setScrapedData(response.data);
    } catch (err: any) {
      console.error("Erreur lors du scraping SCPI", err);
      setScrapingError(err.response?.data?.detail || "Erreur lors de la récupération des données.");
      setScrapedData(null);
    } finally {
      setIsScraping(false);
    }
  };

  if (loading && !showModal) return <p>Chargement...</p>;
  if (error) return (
    <div>
      <p className="text-danger">{error}</p>
      <button className="btn btn-primary" onClick={fetchSCPIs}>
        Réessayer
      </button>
    </div>
  );

  const totalSCPI = scpis.reduce((sum, scpi) => sum + scpi.total_value, 0);

  return (
    <div>
      <div className="d-flex justify-content-between align-items-center mb-4">
        <h1>Mes SCPI</h1>
        <Button variant="primary" onClick={() => { setSelectedSCPI({}); setShowModal(true); }}>Ajouter une SCPI</Button>
      </div>
      
      <table className="table table-striped table-hover">
        <thead className="table-dark">
          <tr>
            <th>Nom</th>
            <th className="text-end">Prix par part</th>
            <th className="text-end">Nombre de parts</th>
            <th className="text-end">Valeur totale</th>
            <th className="text-center">Date MàJ</th>
            <th className="text-center" style={{ minWidth: '220px' }}>Actions</th>
          </tr>
        </thead>
        <tbody>
          {scpis.map(scpi => (
            <React.Fragment key={scpi.id}>
              <tr>
                <td>{scpi.name}</td>
                <td className="text-end">{new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR' }).format(scpi.price_per_share)}</td>
                <td className="text-end">{scpi.number_of_shares}</td>
                <td className="text-end">{new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR' }).format(scpi.total_value)}</td>
                <td className="text-center">{new Date(scpi.update_date).toLocaleDateString('fr-FR')}</td>
                <td className="text-center">
                  <Button
                    variant="outline-info"
                    size="sm"
                    className="me-1"
                    onClick={() => handleScrapeSCPI(scpi)}
                    disabled={isScraping && scrapingSCPIId === scpi.id}
                  >
                    {isScraping && scrapingSCPIId === scpi.id ? 'Chargt...' : 'Primaliance'}
                  </Button>
                  <Button
                    variant="outline-primary"
                    size="sm"
                    className="me-1"
                    onClick={() => { setSelectedSCPI(scpi); setShowModal(true); }}
                  >
                    Modifier
                  </Button>
                  <Button
                    variant="outline-danger"
                    size="sm"
                    onClick={() => handleDelete(scpi.id)}
                  >
                    Supprimer
                  </Button>
                </td>
              </tr>
              {scrapingSCPIId === scpi.id && (
                <tr>
                  <td colSpan={6}>
                    {isScraping && <p className="text-info m-2">Chargement des données depuis Primaliance...</p>}
                    {scrapingError && <p className="text-danger m-2">{scrapingError}</p>}
                    {scrapedData && !isScraping && (
                      <div className="p-3 my-2 bg-light border rounded">
                        <h5>Détails de {scrapedData.nom || scpi.name} (Primaliance)</h5>
                        <ul className="list-unstyled">
                          {Object.entries(scrapedData).map(([key, value]) => {
                            if (value && key !== 'nom') {
                              const fieldName = key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
                              return <li key={key}><strong>{fieldName}:</strong> {value}</li>;
                            }
                            return null;
                          })}
                        </ul>
                      </div>
                    )}
                  </td>
                </tr>
              )}
            </React.Fragment>
          ))}
        </tbody>
        <tfoot>
          <tr className="table-info">
            <th colSpan={3}>TOTAL SCPI</th>
            <th className="text-end">{new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR' }).format(totalSCPI)}</th>
            <th colSpan={2}></th>
          </tr>
        </tfoot>
      </table>

      <Modal show={showModal} onHide={() => setShowModal(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>{selectedSCPI?.id ? 'Modifier' : 'Ajouter'} une SCPI</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <SCPIForm 
            scpi={selectedSCPI} 
            onSave={handleSave} 
            onCancel={() => setShowModal(false)} 
          />
        </Modal.Body>
      </Modal>
    </div>
  );
};

export default SCPI;
