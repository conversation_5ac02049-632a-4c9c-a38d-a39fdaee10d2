'use client';

import React, { useState } from 'react';
import apiClient from '../../components/ApiClient'; // Ajusté

const ApiTestPage: React.FC = () => {
  const [result, setResult] = useState<string>('');
  const [loading, setLoading] = useState(false);

  const testDashboard = async () => {
    setLoading(true);
    setResult('Testing with Axios apiClient...');
    try {
      const response = await apiClient.get('/dashboard');
      setResult(`Axios Success: ${JSON.stringify(response.data, null, 2)}`);
    } catch (error: any) {
      setResult(`Axios Error: ${error.message}\nDetails: ${JSON.stringify({
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        config: {
          url: error.config?.url,
          baseURL: error.config?.baseURL,
          method: error.config?.method
        }
      }, null, 2)}`);
    } finally {
      setLoading(false);
    }
  };

  const testDirectFetchLocalhost = async () => {
    setLoading(true);
    setResult('Testing with fetch (http://localhost:8000/api/dashboard)...');
    try {
      const response = await fetch('http://localhost:8000/api/dashboard');
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data = await response.json();
      setResult(`Fetch (localhost) Success: ${JSON.stringify(data, null, 2)}`);
    } catch (error: any) {
      setResult(`Fetch (localhost) Error: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const testDirectFetch127 = async () => {
    setLoading(true);
    setResult('Testing with fetch (http://127.0.0.1:8000/api/dashboard)...');
    try {
      const response = await fetch('http://127.0.0.1:8000/api/dashboard');
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data = await response.json();
      setResult(`Fetch (127.0.0.1) Success: ${JSON.stringify(data, null, 2)}`);
    } catch (error: any) {
      setResult(`Fetch (127.0.0.1) Error: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mt-4">
      <h2>Test des Appels API vers le Backend</h2>
      <p>
        Cet outil permet de vérifier la connectivité et les réponses de base du backend FastAPI.
        Assurez-vous que le serveur backend (<code>cd backend && uvicorn main:app --reload</code>) est en cours d'exécution sur <code>http://localhost:8000</code>.
      </p>
      <div className="mb-3">
        <button
          className="btn btn-primary me-2 mb-2"
          onClick={testDashboard}
          disabled={loading}
        >
          Test /dashboard (via apiClient Axios)
        </button>
        <button
          className="btn btn-secondary me-2 mb-2"
          onClick={testDirectFetchLocalhost}
          disabled={loading}
        >
          Test /dashboard (via fetch direct sur localhost:8000)
        </button>
        <button
          className="btn btn-info mb-2"
          onClick={testDirectFetch127}
          disabled={loading}
        >
          Test /dashboard (via fetch direct sur 127.0.0.1:8000)
        </button>
      </div>
      {loading && <p>Chargement du test...</p>}
      <pre className="bg-light p-3 border rounded" style={{ whiteSpace: 'pre-wrap', wordBreak: 'break-all' }}>
        {result || "Cliquez sur un bouton pour tester l'API."}
      </pre>
    </div>
  );
};

export default ApiTestPage;
