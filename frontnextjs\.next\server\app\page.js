(()=>{var e={};e.id=974,e.ids=[974],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21204:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\AI-Code\\\\fire_UI\\\\frontnextjs\\\\src\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\AI-Code\\fire_UI\\frontnextjs\\src\\app\\page.tsx","default")},21820:e=>{"use strict";e.exports=require("os")},22057:(e,t,r)=>{"use strict";r.d(t,{Tj:()=>a,mf:()=>n});var s=r(43210);function a(e,t){let r=0;return s.Children.map(e,e=>s.isValidElement(e)?t(e,r++):e)}function n(e,t){return s.Children.toArray(e).some(e=>s.isValidElement(e)&&e.type===t)}},24255:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29190:(e,t,r)=>{"use strict";r.d(t,{default:()=>l});var s=r(60687),a=r(43210),n=r(85814),i=r.n(n);let l=()=>{let[e,t]=(0,a.useState)(!1),r=()=>{t(!1)};return(0,s.jsx)("nav",{className:"navbar navbar-expand-lg navbar-dark bg-dark",children:(0,s.jsxs)("div",{className:"container-fluid",children:[(0,s.jsx)(i(),{className:"navbar-brand",href:"/",onClick:r,children:"FIRE Dashboard"}),(0,s.jsx)("button",{className:"navbar-toggler",type:"button",onClick:()=>{t(!e)},"aria-controls":"navbarNavContent","aria-expanded":e,"aria-label":"Toggle navigation",children:(0,s.jsx)("span",{className:"navbar-toggler-icon"})}),(0,s.jsx)("div",{className:`collapse navbar-collapse ${e?"show":""}`,id:"navbarNavContent",children:(0,s.jsxs)("ul",{className:"navbar-nav me-auto mb-2 mb-lg-0",children:[(0,s.jsx)("li",{className:"nav-item",children:(0,s.jsx)(i(),{className:"nav-link",href:"/",onClick:r,children:"Dashboard"})}),(0,s.jsx)("li",{className:"nav-item",children:(0,s.jsx)(i(),{className:"nav-link",href:"/assets",onClick:r,children:"Patrimoine"})}),(0,s.jsx)("li",{className:"nav-item",children:(0,s.jsx)(i(),{className:"nav-link",href:"/liabilities",onClick:r,children:"Emprunts"})}),(0,s.jsx)("li",{className:"nav-item",children:(0,s.jsx)(i(),{className:"nav-link",href:"/scpi",onClick:r,children:"SCPI"})}),(0,s.jsx)("li",{className:"nav-item",children:(0,s.jsx)(i(),{className:"nav-link",href:"/evolution",onClick:r,children:"\xc9volution"})}),(0,s.jsx)("li",{className:"nav-item",children:(0,s.jsx)(i(),{className:"nav-link",href:"/budget",onClick:r,children:"Budget FIRE"})}),(0,s.jsx)("li",{className:"nav-item",children:(0,s.jsx)(i(),{className:"nav-link",href:"/scenarios",onClick:r,children:"Sc\xe9narios"})}),(0,s.jsx)("li",{className:"nav-item",children:(0,s.jsx)(i(),{className:"nav-link",href:"/fire",onClick:r,children:"Objectif FIRE"})}),(0,s.jsx)("li",{className:"nav-item",children:(0,s.jsx)(i(),{className:"nav-link",href:"/test",onClick:r,children:"Test API"})})]})})]})})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30004:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\AI-Code\\\\fire_UI\\\\frontnextjs\\\\src\\\\components\\\\Navbar.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\AI-Code\\fire_UI\\frontnextjs\\src\\components\\Navbar.tsx","default")},33873:e=>{"use strict";e.exports=require("path")},39522:(e,t,r)=>{"use strict";r.d(t,{A:()=>u});var s=r(69662),a=r.n(s),n=r(43210),i=r(98466),l=r(22057),o=r(60687);function c({min:e,now:t,max:r,label:s,visuallyHidden:n,striped:i,animated:l,className:c,style:d,variant:u,bsPrefix:m,...h},x){return(0,o.jsx)("div",{ref:x,...h,role:"progressbar",className:a()(c,`${m}-bar`,{[`bg-${u}`]:u,[`${m}-bar-animated`]:l,[`${m}-bar-striped`]:l||i}),style:{width:`${Math.round((t-e)/(r-e)*1e5)/1e3}%`,...d},"aria-valuenow":t,"aria-valuemin":e,"aria-valuemax":r,children:n?(0,o.jsx)("span",{className:"visually-hidden",children:s}):s})}let d=n.forwardRef(({isChild:e=!1,...t},r)=>{let s={min:0,max:100,animated:!1,visuallyHidden:!1,striped:!1,...t};if(s.bsPrefix=(0,i.oU)(s.bsPrefix,"progress"),e)return c(s,r);let{min:d,now:u,max:m,label:h,visuallyHidden:x,striped:p,animated:g,bsPrefix:b,variant:f,className:v,children:j,...y}=s;return(0,o.jsx)("div",{ref:r,...y,className:a()(v,b),children:j?(0,l.Tj)(j,e=>(0,n.cloneElement)(e,{isChild:!0})):c({min:d,now:u,max:m,label:h,visuallyHidden:x,striped:p,animated:g,bsPrefix:b,variant:f},r)})});d.displayName="ProgressBar";let u=d},53726:(e,t,r)=>{Promise.resolve().then(r.bind(r,75694))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},58454:(e,t,r)=>{Promise.resolve().then(r.bind(r,21204))},61135:()=>{},62907:(e,t,r)=>{Promise.resolve().then(r.bind(r,30004))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65213:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>c});var s=r(65239),a=r(48088),n=r(88170),i=r.n(n),l=r(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);r.d(t,o);let c={children:["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,21204)),"D:\\AI-Code\\fire_UI\\frontnextjs\\src\\app\\page.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e),async e=>(await Promise.resolve().then(r.bind(r,78162))).default(e)],apple:[async e=>(await Promise.resolve().then(r.bind(r,94650))).default(e)],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"D:\\AI-Code\\fire_UI\\frontnextjs\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e),async e=>(await Promise.resolve().then(r.bind(r,78162))).default(e)],apple:[async e=>(await Promise.resolve().then(r.bind(r,94650))).default(e)],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["D:\\AI-Code\\fire_UI\\frontnextjs\\src\\app\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},69662:(e,t)=>{var r;!function(){"use strict";var s={}.hasOwnProperty;function a(){for(var e="",t=0;t<arguments.length;t++){var r=arguments[t];r&&(e=n(e,function(e){if("string"==typeof e||"number"==typeof e)return e;if("object"!=typeof e)return"";if(Array.isArray(e))return a.apply(null,e);if(e.toString!==Object.prototype.toString&&!e.toString.toString().includes("[native code]"))return e.toString();var t="";for(var r in e)s.call(e,r)&&e[r]&&(t=n(t,r));return t}(r)))}return e}function n(e,t){return t?e?e+" "+t:e+t:e}e.exports?(a.default=a,e.exports=a):void 0===(r=(function(){return a}).apply(t,[]))||(e.exports=r)}()},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},73523:(e,t,r)=>{Promise.resolve().then(r.bind(r,29190))},74075:e=>{"use strict";e.exports=require("zlib")},75694:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d});var s=r(60687),a=r(43210),n=r.n(a),i=r(90317),l=r(29947),o=r(54758),c=r(39522);o.t1.register(o.Bs,o.m_,o.s$);let d=()=>{let[e,t]=(0,a.useState)(null),[r,o]=(0,a.useState)([]),[d,u]=(0,a.useState)({}),[m,h]=(0,a.useState)(!0),[x,p]=(0,a.useState)(null),[g,b]=(0,a.useState)(0),[f,v]=(0,a.useState)(!1),j=[{category:"Liquidit\xe9",categoryKey:"Liquidit\xe9",defaultTargetPercent:2.5},{category:"Bourse",categoryKey:"Bourse",defaultTargetPercent:35.5},{category:"Immobilier",categoryKey:"Immobilier",defaultTargetPercent:35},{category:"Fonds s\xe9curis\xe9s",categoryKey:"Fonds s\xe9curis\xe9s",defaultTargetPercent:20},{category:"Pr\xeats participatifs",categoryKey:"Pr\xeats participatifs",defaultTargetPercent:2},{category:"Crypto-Actifs",categoryKey:"Crypto-Actifs",defaultTargetPercent:5}],y=(e,t)=>e?j.map(r=>{let s=e.allocation[r.categoryKey]||0,a=e.total_assets>0?s/e.total_assets*100:0,n=void 0!==t[r.categoryKey]?t[r.categoryKey]:r.defaultTargetPercent,i=910150*n/100,l=Math.max(0,i-s),o=i>0?Math.min(100,s/i*100):0;return{category:r.category,categoryKey:r.categoryKey,currentValue:s,currentPercent:a,targetPercent:n,targetValue:i,amountToInvest:l,progressPercent:o}}):[],N=()=>{h(!0),p(null),Promise.all([i.A.get("/dashboard"),i.A.get("/fire-allocation-targets/")]).then(([e,r])=>{let s=e.data;t(s);let a=r.data.reduce((e,t)=>(e[t.category_key]=t.target_percentage,e),{});u(a),o(y(s,a)),h(!1),b(0)}).catch(e=>{console.error("Error fetching data for DashboardPage:",e),g<2?(b(e=>e+1),setTimeout(N,1e3)):(p("Erreur lors de la r\xe9cup\xe9ration des donn\xe9es du dashboard."),h(!1))})};(0,a.useEffect)(()=>{N()},[]),(0,a.useEffect)(()=>{if(e){let t=y(e,d),s=r.length>0?r:t,a=t.map(e=>{let t=s.find(t=>t.categoryKey===e.categoryKey);if(t&&t.targetPercent!==e.targetPercent){let r=t.targetPercent,s=910150*r/100,a=Math.max(0,s-e.currentValue),n=s>0?Math.min(100,e.currentValue/s*100):0;return{...e,targetPercent:r,targetValue:s,amountToInvest:a,progressPercent:n}}return e});(0===r.length||C(e,d,P.current?.data,P.current?.initialTargetPercentages))&&o(a)}P.current={data:e,initialTargetPercentages:d}},[e,d]);let P=n().useRef(),C=(e,t,r,s)=>JSON.stringify(e)!==JSON.stringify(r)||JSON.stringify(t)!==JSON.stringify(s),k=(e,t)=>{let r=parseFloat(t);isNaN(r)&&""!==t&&"."!==t||o(s=>s.map(s=>{if(s.categoryKey===e){let e=isNaN(r)?""===t?0:s.targetPercent:r,a=910150*e/100,n=Math.max(0,a-s.currentValue),i=a>0?Math.min(100,s.currentValue/a*100):0;return{...s,targetPercent:e,targetValue:a,amountToInvest:n,progressPercent:i}}return s}))},w=async()=>{v(!0),p(null);let e=r.reduce((e,t)=>e+(t.targetPercent||0),0);if(Math.abs(e-100)>.1&&!window.confirm(`Le total des pourcentages cibles est ${e.toFixed(1)}%, ce qui n'est pas \xe9gal \xe0 100%. Voulez-vous continuer quand m\xeame ?`))return void v(!1);let t=r.map(e=>({category_key:e.categoryKey,target_percentage:e.targetPercent||0}));try{await i.A.post("/fire-allocation-targets/batch_update/",t);let e=t.reduce((e,t)=>(e[t.category_key]=t.target_percentage,e),{});u(e)}catch(e){console.error("Erreur sauvegarde allocations:",e),p("Erreur sauvegarde. R\xe9essayez.")}finally{v(!1)}},A=r.reduce((e,t)=>e+(t.targetPercent||0),0),_=r.some(e=>{let t=d[e.categoryKey];return Math.abs((e.targetPercent||0)-(void 0===t?j.find(t=>t.categoryKey===e.categoryKey)?.defaultTargetPercent||0:t))>.001});if(m&&0===g)return(0,s.jsx)("p",{children:"Chargement initial du dashboard..."});if(x&&g>=2)return(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-danger",children:x}),(0,s.jsx)("button",{className:"btn btn-primary",onClick:()=>{b(0),N()},children:"R\xe9essayer"})]});if(!e&&!m)return(0,s.jsx)("p",{children:"Aucune donn\xe9e de dashboard disponible ou erreur de chargement."});if(m)return(0,s.jsxs)("p",{children:["Chargement du dashboard, tentative ",g+1,"..."]});if(!e)return(0,s.jsx)("p",{children:"Donn\xe9es du dashboard non disponibles."});let I=e=>new Intl.NumberFormat("fr-FR",{style:"currency",currency:"EUR",maximumFractionDigits:0}).format(e),S={Liquidité:{bg:"#17a2b8",border:"#138496",hover:"#20c997"},Bourse:{bg:"#007bff",border:"#0056b3",hover:"#0069d9"},"Crypto-Actifs":{bg:"#fd7e14",border:"#e55a00",hover:"#ff8c42"},"Fonds s\xe9curis\xe9s":{bg:"#28a745",border:"#1e7e34",hover:"#34ce57"},Immobilier:{bg:"#6f42c1",border:"#59359a",hover:"#7952b3"},"Pr\xeats participatifs":{bg:"#dc3545",border:"#bd2130",hover:"#e4606d"}},E=Object.keys(e.allocation),q=Object.values(e.allocation),F=q.reduce((e,t)=>e+t,0),R={labels:E,datasets:[{label:"R\xe9partition du portefeuille",data:q,backgroundColor:E.map(e=>S[e]?.bg||"#6c757d"),borderColor:E.map(e=>S[e]?.border||"#495057"),hoverBackgroundColor:E.map(e=>S[e]?.hover||"#868e96"),borderWidth:3,hoverBorderWidth:4,borderRadius:8,spacing:4}]};return(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"mb-4",children:"Dashboard"}),(0,s.jsxs)("div",{className:"row",children:[(0,s.jsx)("div",{className:"col-md-4",children:(0,s.jsxs)("div",{className:"card text-white bg-primary mb-3",children:[(0,s.jsx)("div",{className:"card-header",children:"Patrimoine Net Total"}),(0,s.jsx)("div",{className:"card-body",children:(0,s.jsx)("h5",{className:"card-title",children:I(e.net_patrimoine)})})]})}),(0,s.jsx)("div",{className:"col-md-4",children:(0,s.jsxs)("div",{className:"card text-white bg-success mb-3",children:[(0,s.jsx)("div",{className:"card-header",children:"Total Actifs"}),(0,s.jsx)("div",{className:"card-body",children:(0,s.jsx)("h5",{className:"card-title",children:I(e.total_assets)})})]})}),(0,s.jsx)("div",{className:"col-md-4",children:(0,s.jsxs)("div",{className:"card text-white bg-danger mb-3",children:[(0,s.jsx)("div",{className:"card-header",children:"Total Passifs"}),(0,s.jsx)("div",{className:"card-body",children:(0,s.jsx)("h5",{className:"card-title",children:I(e.total_liabilities)})})]})})]}),(0,s.jsx)("div",{className:"row mt-4",children:(0,s.jsx)("div",{className:"col-md-8 offset-md-2",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("h2",{className:"mb-4",children:"R\xe9partition des Actifs"}),(0,s.jsx)("div",{style:{maxWidth:"500px",margin:"0 auto"},children:(0,s.jsx)(l.nu,{data:R,options:{responsive:!0,maintainAspectRatio:!0,plugins:{legend:{position:"bottom",labels:{padding:20,usePointStyle:!0,pointStyle:"circle",font:{size:14,weight:"bold"},generateLabels:e=>{let t=e.data;return t.labels&&t.datasets.length?t.labels.map((e,r)=>{let s=t.datasets[0].data[r],a=F>0?(s/F*100).toFixed(1):"0";return{text:`${e} (${a}%)`,fillStyle:Array.isArray(t.datasets[0].backgroundColor)?t.datasets[0].backgroundColor[r]:t.datasets[0].backgroundColor,strokeStyle:Array.isArray(t.datasets[0].borderColor)?t.datasets[0].borderColor[r]:t.datasets[0].borderColor,lineWidth:2,hidden:!1,index:r}}):[]}}},tooltip:{enabled:!0,backgroundColor:"rgba(0, 0, 0, 0.9)",titleColor:"#fff",bodyColor:"#fff",borderColor:"#fff",borderWidth:1,cornerRadius:8,displayColors:!0,callbacks:{title:e=>e[0].label||"",label:e=>{let t=e.parsed,r=F>0?(t/F*100).toFixed(1):"0";return[`Valeur: ${I(t)}`,`Pourcentage: ${r}%`]}}}},animation:{animateRotate:!0,animateScale:!0,duration:1500,easing:"easeInOutQuart"},interaction:{intersect:!1,mode:"index"},onHover:(e,t)=>{e.native?.target&&(e.native.target.style.cursor=t.length>0?"pointer":"default")},onClick:(e,t)=>{if(t.length>0){let e=t[0].index,r=E[e],s={Liquidité:"assets",Bourse:"assets","Crypto-Actifs":"assets","Fonds s\xe9curis\xe9s":"assets",Immobilier:"scpi","Pr\xeats participatifs":"assets"}[r];if(s){let t=q[e],a=F>0?(t/F*100).toFixed(1):"0";alert(`${r}
Valeur: ${I(t)}
Pourcentage: ${a}%

Cliquez sur l'onglet "${"scpi"===s?"SCPI":"Patrimoine"}" pour g\xe9rer cette cat\xe9gorie.`)}}}}})})]})})}),(0,s.jsx)("div",{className:"row mt-5",children:(0,s.jsx)("div",{className:"col-12",children:(0,s.jsxs)("div",{className:"card",children:[(0,s.jsxs)("div",{className:"card-header",children:[(0,s.jsx)("h3",{className:"mb-0",children:"\uD83C\uDFAF Allocation Cible FIRE"}),(0,s.jsxs)("small",{className:"text-muted",children:["Progression vers l'objectif de ",I(910150)," selon la strat\xe9gie document\xe9e"]})]}),(0,s.jsxs)("div",{className:"card-body",children:[x&&0===r.length&&(0,s.jsx)("div",{className:"alert alert-danger",children:x}),x&&r.length>0&&(0,s.jsxs)("div",{className:"alert alert-danger",children:["Erreur lors de la sauvegarde : ",x]}),(0,s.jsx)("div",{className:"table-responsive",children:(0,s.jsxs)("table",{className:"table table-striped table-hover",children:[(0,s.jsx)("thead",{className:"table-dark",children:(0,s.jsxs)("tr",{children:[(0,s.jsx)("th",{children:"Cat\xe9gorie d'Actif"}),(0,s.jsx)("th",{className:"text-end",children:"Valeur Actuelle"}),(0,s.jsx)("th",{className:"text-end",children:"% Actuel"}),(0,s.jsx)("th",{className:"text-end",style:{minWidth:"100px"},children:"% Cible"}),(0,s.jsx)("th",{className:"text-end",children:"Valeur Cible"}),(0,s.jsx)("th",{className:"text-end",children:"Montant \xe0 Investir"}),(0,s.jsx)("th",{className:"text-center",children:"Progression"})]})}),(0,s.jsx)("tbody",{children:r.map(e=>(0,s.jsxs)("tr",{children:[(0,s.jsx)("td",{children:(0,s.jsx)("strong",{children:e.category})}),(0,s.jsx)("td",{className:"text-end",children:I(e.currentValue)}),(0,s.jsxs)("td",{className:"text-end",children:[e.currentPercent.toFixed(1),"%"]}),(0,s.jsxs)("td",{className:"text-end",children:[(0,s.jsx)("input",{type:"number",value:void 0===e.targetPercent?"":e.targetPercent.toString(),onChange:t=>k(e.categoryKey,t.target.value),className:"form-control form-control-sm text-end",style:{width:"70px",display:"inline-block"},step:"0.1",min:"0",max:"100"})," %"]}),(0,s.jsx)("td",{className:"text-end",children:I(e.targetValue)}),(0,s.jsx)("td",{className:"text-end",children:e.amountToInvest>0?(0,s.jsx)("span",{className:"text-warning",children:I(e.amountToInvest)}):(0,s.jsx)("span",{className:"text-success",children:"Objectif atteint"})}),(0,s.jsx)("td",{className:"text-center",style:{width:"150px"},children:(0,s.jsx)(c.A,{now:e.progressPercent,variant:e.progressPercent>=100?"success":e.progressPercent>=75?"info":e.progressPercent>=50?"warning":"danger",style:{height:"20px"},label:`${e.progressPercent.toFixed(0)}%`})})]},e.categoryKey))}),(0,s.jsx)("tfoot",{children:(0,s.jsxs)("tr",{className:"table-info",children:[(0,s.jsx)("th",{children:"TOTAL"}),(0,s.jsx)("th",{className:"text-end",children:I(e.total_assets)}),(0,s.jsx)("th",{className:"text-end",children:"100%"}),(0,s.jsxs)("th",{className:"text-end",children:[(0,s.jsxs)("strong",{children:[A.toFixed(1),"%"]}),Math.abs(A-100)>.1&&(0,s.jsx)("span",{className:"text-danger ms-1",children:"(≠100%)"})]}),(0,s.jsx)("th",{className:"text-end",children:I(910150)}),(0,s.jsx)("th",{className:"text-end",children:(0,s.jsx)("strong",{className:"text-primary",children:I(Math.max(0,910150-e.total_assets))})}),(0,s.jsx)("th",{className:"text-center",children:(0,s.jsxs)("strong",{children:[(e.total_assets/910150*100).toFixed(1),"%"]})})]})})]})}),_&&(0,s.jsx)("div",{className:"alert alert-warning mt-3",children:"Vous avez des modifications non enregistr\xe9es."}),(0,s.jsxs)("div",{className:"mt-3 d-flex justify-content-end",children:[(0,s.jsx)("button",{className:"btn btn-secondary me-2",onClick:()=>{e&&o(y(e,d))},disabled:!_||f,children:"R\xe9initialiser"}),(0,s.jsx)("button",{className:"btn btn-primary",onClick:w,disabled:f||!_,children:f?"Sauvegarde...":"Sauvegarder les % Cibles"})]}),(0,s.jsxs)("div",{className:"row mt-4",children:[(0,s.jsx)("div",{className:"col-md-4",children:(0,s.jsx)("div",{className:"card text-white bg-info",children:(0,s.jsxs)("div",{className:"card-body text-center",children:[(0,s.jsx)("h5",{children:"Progression Globale"}),(0,s.jsxs)("h4",{children:[(e.total_assets/910150*100).toFixed(1),"%"]}),(0,s.jsx)("small",{children:"vers l'objectif FIRE"})]})})}),(0,s.jsx)("div",{className:"col-md-4",children:(0,s.jsx)("div",{className:"card text-white bg-warning",children:(0,s.jsxs)("div",{className:"card-body text-center",children:[(0,s.jsx)("h5",{children:"Capital Restant"}),(0,s.jsx)("h4",{children:I(Math.max(0,910150-e.total_assets))}),(0,s.jsx)("small",{children:"\xe0 investir"})]})})}),(0,s.jsx)("div",{className:"col-md-4",children:(0,s.jsx)("div",{className:"card text-white bg-success",children:(0,s.jsxs)("div",{className:"card-body text-center",children:[(0,s.jsx)("h5",{children:"Estimation"}),(0,s.jsxs)("h4",{children:[Math.max(0,Math.ceil((910150-e.total_assets)/40980))," ans"]}),(0,s.jsx)("small",{children:"\xe0 3 415€/mois"})]})})})]})]})]})})})]})}},78162:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(31658);let a=async e=>[{type:"image/png",sizes:"192x192",url:(0,s.fillMetadataSegment)(".",await e.params,"icon.png")+"?d84b06edc1baa420"}]},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},90317:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let s=r(51060).A.create({baseURL:"http://localhost:8000/api",headers:{"Content-Type":"application/json"},timeout:1e4});s.interceptors.response.use(e=>e,e=>(console.error("API Error:",e.response?.status,e.config?.url,e.message),Promise.reject(e)));let a=s},93991:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},94431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l,metadata:()=>n,viewport:()=>i});var s=r(37413);r(61135),r(27209);var a=r(30004);let n={title:"FIRE Dashboard",description:"Application pour le suivi de l'objectif FIRE (Financial Independence, Retire Early)",manifest:"/manifest.json",icons:{icon:"/icon.png",apple:"/apple-icon.png"}},i={themeColor:"#000000"};function l({children:e}){return(0,s.jsx)("html",{lang:"en",children:(0,s.jsx)("body",{children:(0,s.jsxs)("div",{className:"App",children:[(0,s.jsx)(a.default,{}),(0,s.jsx)("main",{className:"container mt-4",children:e})]})})})}},94650:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(31658);let a=async e=>[{type:"image/png",sizes:"192x192",url:(0,s.fillMetadataSegment)(".",await e.params,"apple-icon.png")+"?d84b06edc1baa420"}]},94735:e=>{"use strict";e.exports=require("events")},98466:(e,t,r)=>{"use strict";r.d(t,{Jm:()=>c,Wz:()=>d,gy:()=>o,oU:()=>l});var s=r(43210);r(60687);let a=s.createContext({prefixes:{},breakpoints:["xxl","xl","lg","md","sm","xs"],minBreakpoint:"xs"}),{Consumer:n,Provider:i}=a;function l(e,t){let{prefixes:r}=(0,s.useContext)(a);return e||r[t]||t}function o(){let{breakpoints:e}=(0,s.useContext)(a);return e}function c(){let{minBreakpoint:e}=(0,s.useContext)(a);return e}function d(){let{dir:e}=(0,s.useContext)(a);return"rtl"===e}}};var t=require("../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,989,517,947],()=>r(65213));module.exports=s})();