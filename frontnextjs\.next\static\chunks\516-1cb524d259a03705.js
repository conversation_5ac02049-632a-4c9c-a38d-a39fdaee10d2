"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[516],{1516:(t,e,o)=>{o.d(e,{A:()=>tL});var r=o(2502),i=o(1351);let n={modes:{point:(t,e)=>s(t,e,{intersect:!0}),nearest:(t,e,o)=>(function(t,e,o){let r=Number.POSITIVE_INFINITY;return s(t,e,o).reduce((t,n)=>{var a;let s=n.getCenterPoint(),l="x"===(a=o.axis)?{x:e.x,y:s.y}:"y"===a?{x:s.x,y:e.y}:s,d=(0,i.aF)(e,l);return d<r?(t=[n],r=d):d===r&&t.push(n),t},[]).sort((t,e)=>t._index-e._index).slice(0,1)})(t,e,o),x:(t,e,o)=>s(t,e,{intersect:o.intersect,axis:"x"}),y:(t,e,o)=>s(t,e,{intersect:o.intersect,axis:"y"})}};function a(t,e,o){return(n.modes[o.mode]||n.modes.nearest)(t,e,o)}function s(t,e,o){return t.filter(t=>{var r;return o.intersect?t.inRange(e.x,e.y):"x"!==(r=o.axis)&&"y"!==r?t.inRange(e.x,e.y,"x",!0)||t.inRange(e.x,e.y,"y",!0):t.inRange(e.x,e.y,r,!0)})}function l(t,e,o){let r=Math.cos(o),i=Math.sin(o),n=e.x,a=e.y;return{x:n+r*(t.x-n)-i*(t.y-a),y:a+i*(t.x-n)+r*(t.y-a)}}let d=(t,e)=>e>t||t.length>e.length&&t.slice(0,e.length)===e,h=(t,e,o)=>Math.min(o,Math.max(e,t)),c=(t,e)=>t.value>=t.start-e&&t.value<=t.end+e;function u(t,{x:e,y:o,x2:r,y2:i},n,{borderWidth:a,hitTolerance:s}){let l=(a+s)/2,d=t.x>=e-l-.001&&t.x<=r+l+.001,h=t.y>=o-l-.001&&t.y<=i+l+.001;return"x"===n?d:"y"===n?h:d&&h}function f(t,{rect:e,center:o},r,{rotation:n,borderWidth:a,hitTolerance:s}){return u(l(t,o,(0,i.t)(-n)),e,r,{borderWidth:a,hitTolerance:s})}function p(t,e){let{centerX:o,centerY:r}=t.getProps(["centerX","centerY"],e);return{x:o,y:r}}let x=t=>"string"==typeof t&&t.endsWith("%"),b=t=>parseFloat(t)/100,y=t=>h(b(t),0,1),g=(t,e)=>({x:t,y:e,x2:t,y2:e,width:0,height:0}),v={box:t=>g(t.centerX,t.centerY),doughnutLabel:t=>g(t.centerX,t.centerY),ellipse:t=>({centerX:t.centerX,centerY:t.centerX,radius:0,width:0,height:0}),label:t=>g(t.centerX,t.centerY),line:t=>g(t.x,t.y),point:t=>({centerX:t.centerX,centerY:t.centerY,radius:0,width:0,height:0}),polygon:t=>g(t.centerX,t.centerY)};function m(t,e){return"start"===e?0:"end"===e?t:x(e)?y(e)*t:t/2}function w(t,e,o=!0){return"number"==typeof e?e:x(e)?(o?y(e):b(e))*t:t}function M(t,e,{borderWidth:o,position:r,xAdjust:n,yAdjust:a},s){let l=(0,i.i)(s),d=e.width+(l?s.width:0)+o,h=e.height+(l?s.height:0)+o,c=S(r),u=D(t.x,d,n,c.x),f=D(t.y,h,a,c.y);return{x:u,y:f,x2:u+d,y2:f+h,width:d,height:h,centerX:u+d/2,centerY:f+h/2}}function S(t,e="center"){return(0,i.i)(t)?{x:(0,i.v)(t.x,e),y:(0,i.v)(t.y,e)}:{x:t=(0,i.v)(t,e),y:t}}let k=(t,e)=>t&&t.autoFit&&e<1;function C(t,e){let o=t.font,r=(0,i.b)(o)?o:[o];return k(t,e)?r.map(function(t){let o=(0,i.a0)(t);return o.size=Math.floor(t.size*e),o.lineHeight=t.lineHeight,(0,i.a0)(o)}):r.map(t=>(0,i.a0)(t))}function P(t){return t&&((0,i.h)(t.xValue)||(0,i.h)(t.yValue))}function D(t,e,o=0,r){return t-m(e,r)+o}function T(t,e,o){let r=o.init;return r?!0===r?j(e,o):function(t,e,o){let r=(0,i.Q)(o.init,[{chart:t,properties:e,options:o}]);return!0===r?j(e,o):(0,i.i)(r)?r:void 0}(t,e,o):void 0}function O(t,e,o){let r=!1;return e.forEach(e=>{(0,i.a7)(t[e])?(r=!0,o[e]=t[e]):(0,i.h)(o[e])&&delete o[e]}),r}function j(t,e){return v[e.type||"line"](t)}let A=new Map,I=t=>isNaN(t)||t<=0,Y=t=>t.reduce(function(t,e){return t+e.string},"");function W(t){if(t&&"object"==typeof t){let e=t.toString();return"[object HTMLImageElement]"===e||"[object HTMLCanvasElement]"===e}}function R(t,{x:e,y:o},r){r&&(t.translate(e,o),t.rotate((0,i.t)(r)),t.translate(-e,-o))}function X(t,e){if(e&&e.borderWidth)return t.lineCap=e.borderCapStyle||"butt",t.setLineDash(e.borderDash),t.lineDashOffset=e.borderDashOffset,t.lineJoin=e.borderJoinStyle||"miter",t.lineWidth=e.borderWidth,t.strokeStyle=e.borderColor,!0}function E(t,e){t.shadowColor=e.backgroundShadowColor,t.shadowBlur=e.shadowBlur,t.shadowOffsetX=e.shadowOffsetX,t.shadowOffsetY=e.shadowOffsetY}function _(t,e){let o=e.content;if(W(o))return{width:w(o.width,e.width),height:w(o.height,e.height)};let r=C(e),n=e.textStrokeWidth,a=(0,i.b)(o)?o:[o],s=a.join()+Y(r)+n+(t._measureText?"-spriting":"");return A.has(s)||A.set(s,function(t,e,o,r){t.save();let i=e.length,n=0,a=r;for(let s=0;s<i;s++){let i=o[Math.min(s,o.length-1)];t.font=i.string;let l=e[s];n=Math.max(n,t.measureText(l).width+r),a+=i.lineHeight}return t.restore(),{width:n,height:a}}(t,a,r,n)),A.get(s)}function H(t,e,o){let{x:r,y:n,width:a,height:s}=e;t.save(),E(t,o);let l=X(t,o);t.fillStyle=o.backgroundColor,t.beginPath(),(0,i.aw)(t,{x:r,y:n,w:a,h:s,radius:function(t,e,o){for(let e of Object.keys(t))t[e]=h(t[e],0,o);return t}((0,i.ay)(o.borderRadius),0,Math.min(a,s)/2)}),t.closePath(),t.fill(),l&&(t.shadowColor=o.borderShadowColor,t.stroke()),t.restore()}function z(t,e,o,r){let n=o.content;if(W(n)){t.save(),t.globalAlpha=function(t,e){let o=(0,i.x)(t)?t:e;return(0,i.x)(o)?h(o,0,1):1}(o.opacity,n.style.opacity),t.drawImage(n,e.x,e.y,e.width,e.height),t.restore();return}let a=(0,i.b)(n)?n:[n],s=C(o,r),l=o.color,d=(0,i.b)(l)?l:[l],c=function(t,e){let{x:o,width:r}=t,i=e.textAlign;return"center"===i?o+r/2:"end"===i||"right"===i?o+r:o}(e,o),u=e.y+o.textStrokeWidth/2;t.save(),t.textBaseline="middle",t.textAlign=o.textAlign,function(t,e){if(e.textStrokeWidth>0)return t.lineJoin="round",t.miterLimit=2,t.lineWidth=e.textStrokeWidth,t.strokeStyle=e.textStrokeColor,!0}(t,o)&&function(t,{x:e,y:o},r,i){t.beginPath();let n=0;r.forEach(function(r,a){let s=i[Math.min(a,i.length-1)],l=s.lineHeight;t.font=s.string,t.strokeText(r,e,o+l/2+n),n+=l}),t.stroke()}(t,{x:c,y:u},a,s),function(t,{x:e,y:o},r,{fonts:i,colors:n}){let a=0;r.forEach(function(r,s){let l=n[Math.min(s,n.length-1)],d=i[Math.min(s,i.length-1)],h=d.lineHeight;t.beginPath(),t.font=d.string,t.fillStyle=l,t.fillText(r,e,o+h/2+a),a+=h,t.fill()})}(t,{x:c,y:u},a,{fonts:s,colors:d}),t.restore()}let N=["left","bottom","top","right"],V={xScaleID:{min:"xMin",max:"xMax",start:"left",end:"right",startProp:"x",endProp:"x2"},yScaleID:{min:"yMin",max:"yMax",start:"bottom",end:"top",startProp:"y",endProp:"y2"}};function L(t,e,o){return e="number"==typeof e?e:t.parse(e),(0,i.g)(e)?t.getPixelForValue(e):o}function $(t,e,o){let r=e[o];if(r||"scaleID"===o)return r;let i=o.charAt(0),n=Object.values(t).filter(t=>t.axis&&t.axis===i);return n.length?n[0].id:i}function B(t,e){if(t){let o=t.options.reverse;return{start:L(t,e.min,o?e.end:e.start),end:L(t,e.max,o?e.start:e.end)}}}function F(t,e){let{chartArea:o,scales:r}=t,i=r[$(r,e,"xScaleID")],n=r[$(r,e,"yScaleID")],a=o.width/2,s=o.height/2;return i&&(a=L(i,e.xValue,i.left+i.width/2)),n&&(s=L(n,e.yValue,n.top+n.height/2)),{x:a,y:s}}function J(t,e){let o=t.scales,r=o[$(o,e,"xScaleID")],i=o[$(o,e,"yScaleID")];if(!r&&!i)return{};let{left:n,right:a}=r||t.chartArea,{top:s,bottom:l}=i||t.chartArea,d=U(r,{min:e.xMin,max:e.xMax,start:n,end:a});n=d.start,a=d.end;let h=U(i,{min:e.yMin,max:e.yMax,start:l,end:s});return{x:n,y:s=h.start,x2:a,y2:l=h.end,width:a-n,height:l-s,centerX:n+(a-n)/2,centerY:s+(l-s)/2}}function q(t,e){if(!P(e)){let o=J(t,e),r=e.radius;(!r||isNaN(r))&&(e.radius=r=Math.min(o.width,o.height)/2);let i=2*r,n=o.centerX+e.xAdjust,a=o.centerY+e.yAdjust;return{x:n-r,y:a-r,x2:n+r,y2:a+r,centerX:n,centerY:a,width:i,height:i,radius:r}}var o=t,r=e;let i=F(o,r),n=2*r.radius;return{x:i.x-r.radius+r.xAdjust,y:i.y-r.radius+r.yAdjust,x2:i.x+r.radius+r.xAdjust,y2:i.y+r.radius+r.yAdjust,centerX:i.x+r.xAdjust,centerY:i.y+r.yAdjust,radius:r.radius,width:n,height:n}}function Q(t,e){let o=J(t,e);return o.initProperties=T(t,o,e),o.elements=[{type:"label",optionScope:"label",properties:function(t,e,o){let r=o.label;r.backgroundColor="transparent",r.callout.display=!1;let n=S(r.position),a=(0,i.E)(r.padding),s=_(t.ctx,r),l=function({properties:t,options:e},o,r,i){let{x:n,x2:a,width:s}=t;return G({start:n,end:a,size:s,borderWidth:e.borderWidth},{position:r.x,padding:{start:i.left,end:i.right},adjust:e.label.xAdjust,size:o.width})}({properties:e,options:o},s,n,a),d=function({properties:t,options:e},o,r,i){let{y:n,y2:a,height:s}=t;return G({start:n,end:a,size:s,borderWidth:e.borderWidth},{position:r.y,padding:{start:i.top,end:i.bottom},adjust:e.label.yAdjust,size:o.height})}({properties:e,options:o},s,n,a),h=s.width+a.width,c=s.height+a.height;return{x:l,y:d,x2:l+h,y2:d+c,width:h,height:c,centerX:l+h/2,centerY:d+c/2,rotation:r.rotation}}(t,o,e),initProperties:o.initProperties}],o}function U(t,e){let o=B(t,e)||e;return{start:Math.min(o.start,o.end),end:Math.max(o.start,o.end)}}function G(t,e){let{start:o,end:r,borderWidth:i}=t,{position:n,padding:{start:a,end:s},adjust:l}=e,d=r-i-o-a-s-e.size;return o+i/2+l+m(d,n)}let K=["enter","leave"],Z=K.concat("click");function tt({state:t,event:e},o,r,i){let n;for(let a of r)0>i.indexOf(a)&&(n=te(a.options[o]||t.listeners[o],a,e)||n);return n}function te(t,e,o){return!0===(0,i.Q)(t,[e.$context,o])}let to=["afterDraw","beforeDraw"];function tr(t,e,o){if(t.hooked){let r=e.options[o]||t.hooks[o];return(0,i.Q)(r,[e.$context])}}function ti(t,e,o,r){var n,a,s;if((0,i.g)(e[o])&&(n=t.options,a=o,s=r,!((0,i.h)(n[a])||(0,i.h)(n[s])))){let r=t[o]!==e[o];return t[o]=e[o],r}}function tn(t,e,o,r){for(let n of o){let o=t[n];if((0,i.h)(o)){let t=e.parse(o);r.min=Math.min(r.min,t),r.max=Math.max(r.max,t)}}}class ta extends r.Hg{inRange(t,e,o,r){let{x:n,y:a}=l({x:t,y:e},this.getCenterPoint(r),(0,i.t)(-this.options.rotation));return u({x:n,y:a},this.getProps(["x","y","x2","y2"],r),o,this.options)}getCenterPoint(t){return p(this,t)}draw(t){t.save(),R(t,this.getCenterPoint(),this.options.rotation),H(t,this,this.options),t.restore()}get label(){return this.elements&&this.elements[0]}resolveElementProperties(t,e){return Q(t,e)}}ta.id="boxAnnotation",ta.defaults={adjustScaleRange:!0,backgroundShadowColor:"transparent",borderCapStyle:"butt",borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",borderRadius:0,borderShadowColor:"transparent",borderWidth:1,display:!0,init:void 0,hitTolerance:0,label:{backgroundColor:"transparent",borderWidth:0,callout:{display:!1},color:"black",content:null,display:!1,drawTime:void 0,font:{family:void 0,lineHeight:void 0,size:void 0,style:void 0,weight:"bold"},height:void 0,hitTolerance:void 0,opacity:void 0,padding:6,position:"center",rotation:void 0,textAlign:"start",textStrokeColor:void 0,textStrokeWidth:0,width:void 0,xAdjust:0,yAdjust:0,z:void 0},rotation:0,shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,xMax:void 0,xMin:void 0,xScaleID:void 0,yMax:void 0,yMin:void 0,yScaleID:void 0,z:0},ta.defaultRoutes={borderColor:"color",backgroundColor:"color"},ta.descriptors={label:{_fallback:!0}};class ts extends r.Hg{inRange(t,e,o,r){return f({x:t,y:e},{rect:this.getProps(["x","y","x2","y2"],r),center:this.getCenterPoint(r)},o,{rotation:this.rotation,borderWidth:0,hitTolerance:this.options.hitTolerance})}getCenterPoint(t){return p(this,t)}draw(t){let e=this.options;e.display&&e.content&&(function(t,e){let{_centerX:o,_centerY:r,_radius:i,_startAngle:n,_endAngle:a,_counterclockwise:s,options:l}=e;t.save();let d=X(t,l);t.fillStyle=l.backgroundColor,t.beginPath(),t.arc(o,r,i,n,a,s),t.closePath(),t.fill(),d&&t.stroke(),t.restore()}(t,this),t.save(),R(t,this.getCenterPoint(),this.rotation),z(t,this,e,this._fitRatio),t.restore())}resolveElementProperties(t,e){var o,n;let a=(o=t,n=e,o.getSortedVisibleDatasetMetas().reduce(function(t,e){let i=e.controller;return i instanceof r.ju&&function(t,e,o){if(!e.autoHide)return!0;for(let e=0;e<o.length;e++)if(!o[e].hidden&&t.getDataVisibility(e))return!0}(o,n,e.data)&&(!t||i.innerRadius<t.controller.innerRadius)&&i.options.circumference>=90?e:t},void 0));if(!a)return{};let{controllerMeta:s,point:l,radius:d}=function({chartArea:t},e,o){let{left:r,top:n,right:a,bottom:s}=t,{innerRadius:l,offsetX:d,offsetY:h}=o.controller,c=(r+a)/2+d,u=(n+s)/2+h,f={left:Math.max(c-l,r),right:Math.min(c+l,a),top:Math.max(u-l,n),bottom:Math.min(u+l,s)},p={x:(f.left+f.right)/2,y:(f.top+f.bottom)/2},x=e.spacing+e.borderWidth/2,b=l-x,y=p.y>u,g=function(t,e,o,r){let n=-2*e,a=Math.pow(n,2)-4*(Math.pow(e,2)+Math.pow(o-t,2)-Math.pow(r,2));if(a<=0)return{_startAngle:0,_endAngle:i.T};let s=(-n-Math.sqrt(a))/2,l=(-n+Math.sqrt(a))/2;return{_startAngle:(0,i.D)({x:e,y:o},{x:s,y:t}).angle,_endAngle:(0,i.D)({x:e,y:o},{x:l,y:t}).angle}}(y?n+x:s-x,c,u,b);return{controllerMeta:{_centerX:c,_centerY:u,_radius:b,_counterclockwise:y,...g},point:p,radius:Math.min(l,Math.min(f.right-f.left,f.bottom-f.top)/2)}}(t,e,a),h=_(t.ctx,e),c=function({width:t,height:e},o){return 2*o/Math.sqrt(Math.pow(t,2)+Math.pow(e,2))}(h,d);k(e,c)&&(h={width:h.width*c,height:h.height*c});let{position:u,xAdjust:f,yAdjust:p}=e,x=M(l,h,{borderWidth:0,position:u,xAdjust:f,yAdjust:p});return{initProperties:T(t,x,e),...x,...s,rotation:e.rotation,_fitRatio:c}}}ts.id="doughnutLabelAnnotation",ts.defaults={autoFit:!0,autoHide:!0,backgroundColor:"transparent",backgroundShadowColor:"transparent",borderColor:"transparent",borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",borderShadowColor:"transparent",borderWidth:0,color:"black",content:null,display:!0,font:{family:void 0,lineHeight:void 0,size:void 0,style:void 0,weight:void 0},height:void 0,hitTolerance:0,init:void 0,opacity:void 0,position:"center",rotation:0,shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,spacing:1,textAlign:"center",textStrokeColor:void 0,textStrokeWidth:0,width:void 0,xAdjust:0,yAdjust:0},ts.defaultRoutes={};class tl extends r.Hg{inRange(t,e,o,r){return f({x:t,y:e},{rect:this.getProps(["x","y","x2","y2"],r),center:this.getCenterPoint(r)},o,{rotation:this.rotation,borderWidth:this.options.borderWidth,hitTolerance:this.options.hitTolerance})}getCenterPoint(t){return p(this,t)}draw(t){let e=this.options,o=!(0,i.h)(this._visible)||this._visible;e.display&&e.content&&o&&(t.save(),R(t,this.getCenterPoint(),this.rotation),function(t,e){let{pointX:o,pointY:r,options:n}=e,a=n.callout,s=a&&a.display&&function(t,e){let o=e.position;return N.includes(o)?o:function(t,e){let{x:o,y:r,x2:n,y2:a,width:s,height:d,pointX:h,pointY:c,centerX:u,centerY:f,rotation:p}=t,x={x:u,y:f},b=e.start,y=w(s,b),g=w(d,b),v=[o,o+y,o+y,n],m=[r+g,a,r,a],M=[];for(let t=0;t<4;t++){let e=l({x:v[t],y:m[t]},x,(0,i.t)(p));M.push({position:N[t],distance:(0,i.aF)(e,{x:h,y:c})})}return M.sort((t,e)=>t.distance-e.distance)[0].position}(t,e)}(e,a);if(!s||function(t,e,o){let{pointX:r,pointY:i}=t,n=e.margin,a=r,s=i;return"left"===o?a+=n:"right"===o?a-=n:"top"===o?s+=n:"bottom"===o&&(s-=n),t.inRange(a,s)}(e,a,s))return;if(t.save(),t.beginPath(),!X(t,a))return t.restore();let{separatorStart:d,separatorEnd:h}=function(t,e){let o,r,{x:i,y:n,x2:a,y2:s}=t,l=function(t,e){let{width:o,height:r,options:i}=t,n=i.callout.margin+i.borderWidth/2;return"right"===e?o+n:"bottom"===e?r+n:-n}(t,e);return r="left"===e||"right"===e?{x:(o={x:i+l,y:n}).x,y:s}:{x:a,y:(o={x:i,y:n+l}).y},{separatorStart:o,separatorEnd:r}}(e,s),{sideStart:c,sideEnd:u}=function(t,e,o){let r,i,{y:n,width:a,height:s,options:l}=t,d=l.callout.start,h=function(t,e){let o=e.side;return"left"===t||"top"===t?-o:o}(e,l.callout);return i="left"===e||"right"===e?{x:(r={x:o.x,y:n+w(s,d)}).x+h,y:r.y}:{x:(r={x:o.x+w(a,d),y:o.y}).x,y:r.y+h},{sideStart:r,sideEnd:i}}(e,s,d);(a.margin>0||0===n.borderWidth)&&(t.moveTo(d.x,d.y),t.lineTo(h.x,h.y)),t.moveTo(c.x,c.y),t.lineTo(u.x,u.y);let f=l({x:o,y:r},e.getCenterPoint(),(0,i.t)(-e.rotation));t.lineTo(f.x,f.y),t.stroke(),t.restore()}(t,this),H(t,this,e),z(t,function({x:t,y:e,width:o,height:r,options:n}){let a=n.borderWidth/2,s=(0,i.E)(n.padding);return{x:t+s.left+a,y:e+s.top+a,width:o-s.left-s.right-n.borderWidth,height:r-s.top-s.bottom-n.borderWidth}}(this),e),t.restore())}resolveElementProperties(t,e){let o;if(P(e))o=F(t,e);else{let{centerX:r,centerY:i}=J(t,e);o={x:r,y:i}}let r=(0,i.E)(e.padding),n=M(o,_(t.ctx,e),e,r);return{initProperties:T(t,n,e),pointX:o.x,pointY:o.y,...n,rotation:e.rotation}}}tl.id="labelAnnotation",tl.defaults={adjustScaleRange:!0,backgroundColor:"transparent",backgroundShadowColor:"transparent",borderCapStyle:"butt",borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",borderRadius:0,borderShadowColor:"transparent",borderWidth:0,callout:{borderCapStyle:"butt",borderColor:void 0,borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",borderWidth:1,display:!1,margin:5,position:"auto",side:5,start:"50%"},color:"black",content:null,display:!0,font:{family:void 0,lineHeight:void 0,size:void 0,style:void 0,weight:void 0},height:void 0,hitTolerance:0,init:void 0,opacity:void 0,padding:6,position:"center",rotation:0,shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,textAlign:"center",textStrokeColor:void 0,textStrokeWidth:0,width:void 0,xAdjust:0,xMax:void 0,xMin:void 0,xScaleID:void 0,xValue:void 0,yAdjust:0,yMax:void 0,yMin:void 0,yScaleID:void 0,yValue:void 0,z:0},tl.defaultRoutes={borderColor:"color"};let td=(t,e,o)=>({x:t.x+o*(e.x-t.x),y:t.y+o*(e.y-t.y)}),th=(t,e,o)=>td(e,o,Math.abs((t-e.y)/(o.y-e.y))).x,tc=(t,e,o)=>td(e,o,Math.abs((t-e.x)/(o.x-e.x))).y,tu=t=>t*t,tf=(t,e,{x:o,y:r,x2:i,y2:n},a)=>"y"===a?{start:Math.min(r,n),end:Math.max(r,n),value:e}:{start:Math.min(o,i),end:Math.max(o,i),value:t},tp=(t,e,o,r)=>(1-r)*(1-r)*t+2*(1-r)*r*e+r*r*o,tx=(t,e,o,r)=>({x:tp(t.x,e.x,o.x,r),y:tp(t.y,e.y,o.y,r)}),tb=(t,e,o,r)=>2*(1-r)*(e-t)+2*r*(o-e),ty=(t,e,o,r)=>-Math.atan2(tb(t.x,e.x,o.x,r),tb(t.y,e.y,o.y,r))+.5*i.P;class tg extends r.Hg{inRange(t,e,o,r){let i=(this.options.borderWidth+this.options.hitTolerance)/2;if("x"!==o&&"y"!==o){let o={mouseX:t,mouseY:e},{path:n,ctx:a}=this;if(n){X(a,this.options),a.lineWidth+=this.options.hitTolerance;let{chart:i}=this.$context,s=t*i.currentDevicePixelRatio,l=e*i.currentDevicePixelRatio,d=a.isPointInStroke(n,s,l)||tw(this,o,r);return a.restore(),d}return function(t,{mouseX:e,mouseY:o},r=.001,i){let n,a,{x:s,y:l,x2:d,y2:h}=t.getProps(["x","y","x2","y2"],i),c=d-s,u=h-l,f=tu(c)+tu(u),p=0===f?-1:((e-s)*c+(o-l)*u)/f;return p<0?(n=s,a=l):p>1?(n=d,a=h):(n=s+p*c,a=l+p*u),tu(e-n)+tu(o-a)<=r}(this,o,tu(i),r)||tw(this,o,r)}return function(t,{mouseX:e,mouseY:o},r,{hitSize:i,useFinalPosition:n}){return c(tf(e,o,t.getProps(["x","y","x2","y2"],n),r),i)||tw(t,{mouseX:e,mouseY:o},n,r)}(this,{mouseX:t,mouseY:e},o,{hitSize:i,useFinalPosition:r})}getCenterPoint(t){return p(this,t)}draw(t){let{x:e,y:o,x2:r,y2:n,cp:a,options:s}=this;if(t.save(),!X(t,s))return t.restore();E(t,s);let l=Math.sqrt(Math.pow(r-e,2)+Math.pow(n-o,2));if(s.curve&&a)return function(t,e,o,r){let{x:n,y:a,x2:s,y2:l,options:d}=e,{startOpts:h,endOpts:c,startAdjust:u,endAdjust:f}=tk(e),p={x:n,y:a},x={x:s,y:l},b=ty(p,o,x,0),y=ty(p,o,x,1)-i.P,g=tx(p,o,x,u/r),v=tx(p,o,x,1-f/r),m=new Path2D;t.beginPath(),m.moveTo(g.x,g.y),m.quadraticCurveTo(o.x,o.y,v.x,v.y),t.shadowColor=d.borderShadowColor,t.stroke(m),e.path=m,e.ctx=t,tD(t,g,{angle:b,adjust:u},h),tD(t,v,{angle:y,adjust:f},c)}(t,this,a,l),t.restore();let{startOpts:d,endOpts:h,startAdjust:c,endAdjust:u}=tk(this),f=Math.atan2(n-o,r-e);t.translate(e,o),t.rotate(f),t.beginPath(),t.moveTo(0+c,0),t.lineTo(l-u,0),t.shadowColor=s.borderShadowColor,t.stroke(),tP(t,0,c,d),tP(t,l,-u,h),t.restore()}get label(){return this.elements&&this.elements[0]}resolveElementProperties(t,e){let o=function(t,e){let{scales:o,chartArea:r}=t,i=o[e.scaleID],n={x:r.left,y:r.top,x2:r.right,y2:r.bottom};return i?function(t,e,o){let r=L(t,o.value,NaN),i=L(t,o.endValue,r);t.isHorizontal()?(e.x=r,e.x2=i):(e.y=r,e.y2=i)}(i,n,e):function(t,e,o){for(let r of Object.keys(V)){let i=t[$(t,o,r)];if(i){let{min:t,max:n,start:a,end:s,startProp:l,endProp:d}=V[r],h=B(i,{min:o[t],max:o[n],start:i[a],end:i[s]});e[l]=h.start,e[d]=h.end}}}(o,n,e),n}(t,e),{x:r,y:n,x2:a,y2:s}=o,d=function({x:t,y:e,x2:o,y2:r},{top:i,right:n,bottom:a,left:s}){return!(t<s&&o<s||t>n&&o>n||e<i&&r<i||e>a&&r>a)}(o,t.chartArea),h=d?function(t,e,o){let{x:r,y:i}=tm(t,e,o),{x:n,y:a}=tm(e,t,o);return{x:r,y:i,x2:n,y2:a,width:Math.abs(n-r),height:Math.abs(a-i)}}({x:r,y:n},{x:a,y:s},t.chartArea):{x:r,y:n,x2:a,y2:s,width:Math.abs(a-r),height:Math.abs(s-n)};if(h.centerX=(a+r)/2,h.centerY=(s+n)/2,h.initProperties=T(t,h,e),e.curve){let t={x:h.x,y:h.y},o={x:h.x2,y:h.y2};h.cp=function(t,e,o){let{x:r,y:i,x2:n,y2:a,centerX:s,centerY:d}=t,h=Math.atan2(a-i,n-r),c=S(e.controlPoint,0);return l({x:s+w(o,c.x,!1),y:d+w(o,c.y,!1)},{x:s,y:d},h)}(h,e,(0,i.aF)(t,o))}let c=function(t,e,o){let r=o.borderWidth,n=(0,i.E)(o.padding),a=_(t.ctx,o);return function(t,e,o,r){let{width:n,height:a,padding:s}=o,{xAdjust:l,yAdjust:d}=e,h={x:t.x,y:t.y},c={x:t.x2,y:t.y2},u="auto"===e.rotation?function(t){let{x:e,y:o,x2:r,y2:n}=t,a=Math.atan2(n-o,r-e);return a>i.P/2?a-i.P:a<-(i.P/2)?a+i.P:a}(t):(0,i.t)(e.rotation),f=function(t,e,o){let r=Math.cos(o),i=Math.sin(o);return{w:Math.abs(t*r)+Math.abs(e*i),h:Math.abs(t*i)+Math.abs(e*r)}}(n,a,u),p=function(t,e,o,r){let i,n=function(t,e){let{x:o,x2:r,y:i,y2:n}=t,a=Math.min(i,n)-e.top,s=Math.min(o,r)-e.left,l=e.bottom-Math.max(i,n),d=e.right-Math.max(o,r);return{x:Math.min(s,d),y:Math.min(a,l),dx:s<=d?1:-1,dy:a<=l?1:-1}}(t,r);return"start"===e.position?tM({w:t.x2-t.x,h:t.y2-t.y},o,e,n):"end"===e.position?1-tM({w:t.x-t.x2,h:t.y-t.y2},o,e,n):m(1,e.position)}(t,e,{labelSize:f,padding:s},r),x=t.cp?tx(h,t.cp,c,p):td(h,c,p),b={size:f.w,min:r.left,max:r.right,padding:s.left},y={size:f.h,min:r.top,max:r.bottom,padding:s.top},g=tS(x.x,b)+l,v=tS(x.y,y)+d;return{x:g-n/2,y:v-a/2,x2:g+n/2,y2:v+a/2,centerX:g,centerY:v,pointX:x.x,pointY:x.y,width:n,height:a,rotation:(0,i.U)(u)}}(e,o,{width:a.width+n.width+r,height:a.height+n.height+r,padding:n},t.chartArea)}(t,h,e.label);return c._visible=d,h.elements=[{type:"label",optionScope:"label",properties:c,initProperties:h.initProperties}],h}}tg.id="lineAnnotation";let tv={backgroundColor:void 0,backgroundShadowColor:void 0,borderColor:void 0,borderDash:void 0,borderDashOffset:void 0,borderShadowColor:void 0,borderWidth:void 0,display:void 0,fill:void 0,length:void 0,shadowBlur:void 0,shadowOffsetX:void 0,shadowOffsetY:void 0,width:void 0};function tm({x:t,y:e},o,{top:r,right:i,bottom:n,left:a}){return t<a&&(e=tc(a,{x:t,y:e},o),t=a),t>i&&(e=tc(i,{x:t,y:e},o),t=i),e<r&&(t=th(r,{x:t,y:e},o),e=r),e>n&&(t=th(n,{x:t,y:e},o),e=n),{x:t,y:e}}function tw(t,{mouseX:e,mouseY:o},r,i){let n=t.label;return n.options.display&&n.inRange(e,o,i,r)}function tM(t,e,o,r){let{labelSize:i,padding:n}=e,a=t.w*r.dx,s=t.h*r.dy;return h(Math.max(a>0&&(i.w/2+n.left-r.x)/a,s>0&&(i.h/2+n.top-r.y)/s),0,.25)}function tS(t,e){let{size:o,min:r,max:i,padding:n}=e,a=o/2;return o>i-r?(i+r)/2:(r>=t-n-a&&(t=r+n+a),i<=t+n+a&&(t=i-n-a),t)}function tk(t){let e=t.options,o=e.arrowHeads&&e.arrowHeads.start,r=e.arrowHeads&&e.arrowHeads.end;return{startOpts:o,endOpts:r,startAdjust:tC(t,o),endAdjust:tC(t,r)}}function tC(t,e){if(!e||!e.display)return 0;let{length:o,width:r}=e,i=t.options.borderWidth/2;return Math.abs(th(0,{x:o,y:r+i},{x:0,y:i}))}function tP(t,e,o,r){if(!r||!r.display)return;let{length:i,width:n,fill:a,backgroundColor:s,borderColor:l}=r,d=Math.abs(e-i)+o;t.beginPath(),E(t,r),X(t,r),t.moveTo(d,-n),t.lineTo(e+o,0),t.lineTo(d,n),!0===a?(t.fillStyle=s||l,t.closePath(),t.fill(),t.shadowColor="transparent"):t.shadowColor=r.borderShadowColor,t.stroke()}function tD(t,{x:e,y:o},{angle:r,adjust:i},n){n&&n.display&&(t.save(),t.translate(e,o),t.rotate(r),tP(t,0,-i,n),t.restore())}tg.defaults={adjustScaleRange:!0,arrowHeads:{display:!1,end:Object.assign({},tv),fill:!1,length:12,start:Object.assign({},tv),width:6},borderDash:[],borderDashOffset:0,borderShadowColor:"transparent",borderWidth:2,curve:!1,controlPoint:{y:"-50%"},display:!0,endValue:void 0,init:void 0,hitTolerance:0,label:{backgroundColor:"rgba(0,0,0,0.8)",backgroundShadowColor:"transparent",borderCapStyle:"butt",borderColor:"black",borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",borderRadius:6,borderShadowColor:"transparent",borderWidth:0,callout:Object.assign({},tl.defaults.callout),color:"#fff",content:null,display:!1,drawTime:void 0,font:{family:void 0,lineHeight:void 0,size:void 0,style:void 0,weight:"bold"},height:void 0,hitTolerance:void 0,opacity:void 0,padding:6,position:"center",rotation:0,shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,textAlign:"center",textStrokeColor:void 0,textStrokeWidth:0,width:void 0,xAdjust:0,yAdjust:0,z:void 0},scaleID:void 0,shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,value:void 0,xMax:void 0,xMin:void 0,xScaleID:void 0,yMax:void 0,yMin:void 0,yScaleID:void 0,z:0},tg.descriptors={arrowHeads:{start:{_fallback:!0},end:{_fallback:!0},_fallback:!0}},tg.defaultRoutes={borderColor:"color"};class tT extends r.Hg{inRange(t,e,o,r){let n=this.options.rotation,a=(this.options.borderWidth+this.options.hitTolerance)/2;if("x"!==o&&"y"!==o)return function(t,e,o,r){let{width:n,height:a,centerX:s,centerY:l}=e,d=n/2,h=a/2;if(d<=0||h<=0)return!1;let c=(0,i.t)(o||0),u=Math.cos(c),f=Math.sin(c);return Math.pow(u*(t.x-s)+f*(t.y-l),2)/Math.pow(d+r,2)+Math.pow(f*(t.x-s)-u*(t.y-l),2)/Math.pow(h+r,2)<=1.0001}({x:t,y:e},this.getProps(["width","height","centerX","centerY"],r),n,a);let{x:s,y:d,x2:h,y2:c}=this.getProps(["x","y","x2","y2"],r),u="y"===o?{start:d,end:c}:{start:s,end:h},f=l({x:t,y:e},this.getCenterPoint(r),(0,i.t)(-n));return f[o]>=u.start-a-.001&&f[o]<=u.end+a+.001}getCenterPoint(t){return p(this,t)}draw(t){let{width:e,height:o,centerX:r,centerY:n,options:a}=this;t.save(),R(t,this.getCenterPoint(),a.rotation),E(t,this.options),t.beginPath(),t.fillStyle=a.backgroundColor;let s=X(t,a);t.ellipse(r,n,o/2,e/2,i.P/2,0,2*i.P),t.fill(),s&&(t.shadowColor=a.borderShadowColor,t.stroke()),t.restore()}get label(){return this.elements&&this.elements[0]}resolveElementProperties(t,e){return Q(t,e)}}tT.id="ellipseAnnotation",tT.defaults={adjustScaleRange:!0,backgroundShadowColor:"transparent",borderDash:[],borderDashOffset:0,borderShadowColor:"transparent",borderWidth:1,display:!0,hitTolerance:0,init:void 0,label:Object.assign({},ta.defaults.label),rotation:0,shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,xMax:void 0,xMin:void 0,xScaleID:void 0,yMax:void 0,yMin:void 0,yScaleID:void 0,z:0},tT.defaultRoutes={borderColor:"color",backgroundColor:"color"},tT.descriptors={label:{_fallback:!0}};class tO extends r.Hg{inRange(t,e,o,r){let{x:i,y:n,x2:a,y2:s,width:l}=this.getProps(["x","y","x2","y2","width"],r),d=(this.options.borderWidth+this.options.hitTolerance)/2;if("x"!==o&&"y"!==o){var h,u,f;return h={x:t,y:e},u=this.getCenterPoint(r),f=l/2,!!h&&!!u&&!(f<=0)&&Math.pow(h.x-u.x,2)+Math.pow(h.y-u.y,2)<=Math.pow(f+d,2)}return c("y"===o?{start:n,end:s,value:e}:{start:i,end:a,value:t},d)}getCenterPoint(t){return p(this,t)}draw(t){let e=this.options,o=e.borderWidth;if(e.radius<.1)return;t.save(),t.fillStyle=e.backgroundColor,E(t,e);let r=X(t,e);!function(t,e,o,r){let{radius:n,options:a}=e,s=a.pointStyle,l=a.rotation,d=(l||0)*i.b4;if(W(s)){t.save(),t.translate(o,r),t.rotate(d),t.drawImage(s,-s.width/2,-s.height/2,s.width,s.height),t.restore();return}I(n)||function(t,{x:e,y:o,radius:r,rotation:n,style:a,rad:s}){let l,d,h,c;switch(t.beginPath(),a){default:t.arc(e,o,r,0,i.T),t.closePath();break;case"triangle":t.moveTo(e+Math.sin(s)*r,o-Math.cos(s)*r),s+=i.b6,t.lineTo(e+Math.sin(s)*r,o-Math.cos(s)*r),s+=i.b6,t.lineTo(e+Math.sin(s)*r,o-Math.cos(s)*r),t.closePath();break;case"rectRounded":c=.516*r,h=r-c,l=Math.cos(s+i.b5)*h,d=Math.sin(s+i.b5)*h,t.arc(e-l,o-d,c,s-i.P,s-i.H),t.arc(e+d,o-l,c,s-i.H,s),t.arc(e+l,o+d,c,s,s+i.H),t.arc(e-d,o+l,c,s+i.H,s+i.P),t.closePath();break;case"rect":if(!n){h=Math.SQRT1_2*r,t.rect(e-h,o-h,2*h,2*h);break}s+=i.b5;case"rectRot":l=Math.cos(s)*r,d=Math.sin(s)*r,t.moveTo(e-l,o-d),t.lineTo(e+d,o-l),t.lineTo(e+l,o+d),t.lineTo(e-d,o+l),t.closePath();break;case"crossRot":s+=i.b5;case"cross":l=Math.cos(s)*r,d=Math.sin(s)*r,t.moveTo(e-l,o-d),t.lineTo(e+l,o+d),t.moveTo(e+d,o-l),t.lineTo(e-d,o+l);break;case"star":l=Math.cos(s)*r,d=Math.sin(s)*r,t.moveTo(e-l,o-d),t.lineTo(e+l,o+d),t.moveTo(e+d,o-l),t.lineTo(e-d,o+l),s+=i.b5,l=Math.cos(s)*r,d=Math.sin(s)*r,t.moveTo(e-l,o-d),t.lineTo(e+l,o+d),t.moveTo(e+d,o-l),t.lineTo(e-d,o+l);break;case"line":l=Math.cos(s)*r,d=Math.sin(s)*r,t.moveTo(e-l,o-d),t.lineTo(e+l,o+d);break;case"dash":t.moveTo(e,o),t.lineTo(e+Math.cos(s)*r,o+Math.sin(s)*r)}t.fill()}(t,{x:o,y:r,radius:n,rotation:l,style:s,rad:d})}(t,this,this.centerX,this.centerY),r&&!W(e.pointStyle)&&(t.shadowColor=e.borderShadowColor,t.stroke()),t.restore(),e.borderWidth=o}resolveElementProperties(t,e){let o=q(t,e);return o.initProperties=T(t,o,e),o}}tO.id="pointAnnotation",tO.defaults={adjustScaleRange:!0,backgroundShadowColor:"transparent",borderDash:[],borderDashOffset:0,borderShadowColor:"transparent",borderWidth:1,display:!0,hitTolerance:0,init:void 0,pointStyle:"circle",radius:10,rotation:0,shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,xAdjust:0,xMax:void 0,xMin:void 0,xScaleID:void 0,xValue:void 0,yAdjust:0,yMax:void 0,yMin:void 0,yScaleID:void 0,yValue:void 0,z:0},tO.defaultRoutes={borderColor:"color",backgroundColor:"color"};class tj extends r.Hg{inRange(t,e,o,r){if("x"!==o&&"y"!==o)return this.options.radius>=.1&&this.elements.length>1&&function(t,e,o,r){let i=!1,n=t[t.length-1].getProps(["bX","bY"],r);for(let a of t){let t=a.getProps(["bX","bY"],r);t.bY>o!=n.bY>o&&e<(n.bX-t.bX)*(o-t.bY)/(n.bY-t.bY)+t.bX&&(i=!i),n=t}return i}(this.elements,t,e,r);let n=l({x:t,y:e},this.getCenterPoint(r),(0,i.t)(-this.options.rotation)),a=this.elements.map(t=>"y"===o?t.bY:t.bX),s=Math.min(...a),d=Math.max(...a);return n[o]>=s&&n[o]<=d}getCenterPoint(t){return p(this,t)}draw(t){let{elements:e,options:o}=this;t.save(),t.beginPath(),t.fillStyle=o.backgroundColor,E(t,o);let r=X(t,o),i=!0;for(let o of e)i?(t.moveTo(o.x,o.y),i=!1):t.lineTo(o.x,o.y);t.closePath(),t.fill(),r&&(t.shadowColor=o.borderShadowColor,t.stroke()),t.restore()}resolveElementProperties(t,e){let o=q(t,e),{sides:r,rotation:n}=e,a=[],s=2*i.P/r,l=n*i.b4;for(let i=0;i<r;i++,l+=s){let r=function({centerX:t,centerY:e},{radius:o,borderWidth:r,hitTolerance:i},n){let a=(r+i)/2,s=Math.sin(n),l=Math.cos(n),d={x:t+s*o,y:e-l*o};return{type:"point",optionScope:"point",properties:{x:d.x,y:d.y,centerX:d.x,centerY:d.y,bX:t+s*(o+a),bY:e-l*(o+a)}}}(o,e,l);r.initProperties=T(t,o,e),a.push(r)}return o.elements=a,o}}tj.id="polygonAnnotation",tj.defaults={adjustScaleRange:!0,backgroundShadowColor:"transparent",borderCapStyle:"butt",borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",borderShadowColor:"transparent",borderWidth:1,display:!0,hitTolerance:0,init:void 0,point:{radius:0},radius:10,rotation:0,shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,sides:3,xAdjust:0,xMax:void 0,xMin:void 0,xScaleID:void 0,xValue:void 0,yAdjust:0,yMax:void 0,yMin:void 0,yScaleID:void 0,yValue:void 0,z:0},tj.defaultRoutes={borderColor:"color",backgroundColor:"color"};let tA={box:ta,doughnutLabel:ts,ellipse:tT,label:tl,line:tg,point:tO,polygon:tj};Object.keys(tA).forEach(t=>{i.d.describe(`elements.${tA[t].id}`,{_fallback:"plugins.annotation.common"})});let tI={update:Object.assign},tY=Z.concat(to),tW=(t,e)=>(0,i.i)(e)?tH(t,e):t,tR=t=>"color"===t||"font"===t;function tX(t="line"){return tA[t]?t:(console.warn(`Unknown annotation type: '${t}', defaulting to 'line'`),"line")}function tE(t,e,o,r){let i=tA[tX(o)],n=t[e];return n&&n instanceof i||Object.assign(n=t[e]=new i,r),n}function t_(t){let e=tA[tX(t.type)],o={};for(let r of(o.id=t.id,o.type=t.type,o.drawTime=t.drawTime,Object.assign(o,tH(t,e.defaults),tH(t,e.defaultRoutes)),tY))o[r]=t[r];return o}function tH(t,e){let o={};for(let r of Object.keys(e)){let n=e[r],a=t[r];tR(r)&&(0,i.b)(a)?o[r]=a.map(t=>tW(t,n)):o[r]=tW(a,n)}return o}let tz=new Map,tN=t=>"doughnutLabel"!==t.type,tV=Z.concat(to);var tL={id:"annotation",version:"3.1.0",beforeRegister(){!function(t,e,o,r=!0){let i=o.split("."),n=0;for(let e of"4.0".split(".")){let a=i[n++];if(parseInt(e,10)<parseInt(a,10))break;if(d(a,e))if(!r)return!1;else throw Error(`${t} v${o} is not supported. v4.0 or newer is required.`)}}("chart.js","4.0",r.t1.version)},afterRegister(){r.t1.register(tA)},afterUnregister(){r.t1.unregister(tA)},beforeInit(t){tz.set(t,{annotations:[],elements:[],visibleElements:[],listeners:{},listened:!1,moveListened:!1,hooks:{},hooked:!1,hovered:[]})},beforeUpdate(t,e,o){let r=tz.get(t).annotations=[],n=o.annotations;(0,i.i)(n)?Object.keys(n).forEach(t=>{let e=n[t];(0,i.i)(e)&&(e.id=t,r.push(e))}):(0,i.b)(n)&&r.push(...n);var a=r.filter(tN),s=t.scales;for(let t of a){var l=t,d=s;for(let t of["scaleID","xScaleID","yScaleID"]){let e=$(d,l,t);e&&!d[e]&&function(t,e){if("scaleID"===e)return!0;let o=e.charAt(0);for(let e of["Min","Max","Value"])if((0,i.h)(t[o+e]))return!0;return!1}(l,t)&&console.warn(`No scale found with id '${e}' for annotation '${l.id}'`)}}},afterDataLimits(t,e){let o=tz.get(t);!function(t,e,o){let r=function(t,e,o){let r=e.axis,n=e.id,a=r+"ScaleID",s={min:(0,i.v)(e.min,Number.NEGATIVE_INFINITY),max:(0,i.v)(e.max,Number.POSITIVE_INFINITY)};for(let i of o)i.scaleID===n?tn(i,e,["value","endValue"],s):$(t,i,a)===n&&tn(i,e,[r+"Min",r+"Max",r+"Value"],s);return s}(t.scales,e,o),n=ti(e,r,"min","suggestedMin");(n=ti(e,r,"max","suggestedMax")||n)&&(0,i.a7)(e.handleTickRangeOptions)&&e.handleTickRangeOptions()}(t,e.scale,o.annotations.filter(tN).filter(t=>t.display&&t.adjustScaleRange))},afterUpdate(t,e,o){let n=tz.get(t);n.listened=O(o,Z,n.listeners),n.moveListened=!1,K.forEach(t=>{(0,i.a7)(o[t])&&(n.moveListened=!0)}),n.listened&&n.moveListened||n.annotations.forEach(t=>{!n.listened&&(0,i.a7)(t.click)&&(n.listened=!0),n.moveListened||K.forEach(e=>{(0,i.a7)(t[e])&&(n.listened=!0,n.moveListened=!0)})}),function(t,e,o,n){var a,s,l,d,h,c,u,f;let p=(a=t,s=o.animations,"reset"===(l=n)||"none"===l||"resize"===l?tI:new r.Qw(a,s)),x=e.annotations,b=function(t,e){let o=e.length,r=t.length;return r<o?t.splice(r,0,...Array(o-r)):r>o&&t.splice(o,r-o),t}(e.elements,x);for(let e=0;e<x.length;e++){let o=x[e],r=tE(b,e,o.type),n=o.setContext((d=t,h=r,c=b,u=o,h.$context||(h.$context=Object.assign(Object.create(d.getContext()),{element:h,get elements(){return c.filter(t=>t&&t.options)},id:u.id,type:"annotation"})))),a=r.resolveElementProperties(t,n);a.skip=isNaN((f=a).x)||isNaN(f.y),"elements"in a&&(function(t,e,o,r){let i=t.elements||(t.elements=[]);i.length=e.length;for(let t=0;t<e.length;t++){let n=e[t],a=n.properties,s=tE(i,t,n.type,n.initProperties);a.options=t_(o[n.optionScope].override(n)),r.update(s,a)}}(r,a.elements,n,p),delete a.elements),(0,i.h)(r.x)||Object.assign(r,a),Object.assign(r,a.initProperties),a.options=t_(n),p.update(r,a)}}(t,n,o,e.mode),n.visibleElements=n.elements.filter(t=>!t.skip&&t.options.display);let a=n.visibleElements;n.hooked=O(o,to,n.hooks),n.hooked||a.forEach(t=>{n.hooked||to.forEach(e=>{(0,i.a7)(t.options[e])&&(n.hooked=!0)})})},beforeDatasetsDraw(t,e,o){t$(t,"beforeDatasetsDraw",o.clip)},afterDatasetsDraw(t,e,o){t$(t,"afterDatasetsDraw",o.clip)},beforeDatasetDraw(t,e,o){t$(t,e.index,o.clip)},beforeDraw(t,e,o){t$(t,"beforeDraw",o.clip)},afterDraw(t,e,o){t$(t,"afterDraw",o.clip)},beforeEvent(t,e,o){(function(t,e,o){if(t.listened)switch(e.type){case"mousemove":case"mouseout":let r;var i=t,n=e,s=o;if(!i.moveListened)return;r="mousemove"===n.type?a(i.visibleElements,n,s.interaction):[];let l=i.hovered;i.hovered=r;let d={state:i,event:n},h=tt(d,"leave",l,r);return tt(d,"enter",r,l)||h;case"click":let c;var u=t,f=e,p=o;let x=u.listeners;for(let t of a(u.visibleElements,f,p.interaction))c=te(t.options.click||x.click,t,f)||c;return c}})(tz.get(t),e.event,o)&&(e.changed=!0)},afterDestroy(t){tz.delete(t)},getAnnotations(t){let e=tz.get(t);return e?e.elements:[]},_getAnnotationElementsAtEventForMode:(t,e,o)=>a(t,e,o),defaults:{animations:{numbers:{properties:["x","y","x2","y2","width","height","centerX","centerY","pointX","pointY","radius"],type:"number"},colors:{properties:["backgroundColor","borderColor"],type:"color"}},clip:!0,interaction:{mode:void 0,axis:void 0,intersect:void 0},common:{drawTime:"afterDatasetsDraw",init:!1,label:{}}},descriptors:{_indexable:!1,_scriptable:t=>!tV.includes(t)&&"init"!==t,annotations:{_allKeys:!1,_fallback:(t,e)=>`elements.${tA[tX(e.type)].id}`},interaction:{_fallback:!0},common:{label:{_indexable:tR,_fallback:!0},_indexable:tR}},additionalOptionScopes:[""]};function t$(t,e,o){let{ctx:r,chartArea:n}=t,a=tz.get(t);for(let t of(o&&(0,i.Y)(r,n),(function(t,e){let o=[];for(let r of t)if(r.options.drawTime===e&&o.push({element:r,main:!0}),r.elements&&r.elements.length)for(let t of r.elements)t.options.display&&t.options.drawTime===e&&o.push({element:t});return o})(a.visibleElements,e).sort((t,e)=>t.element.options.z-e.element.options.z))){var s=r,l=n,d=a,h=t;let e=h.element;h.main?(tr(d,e,"beforeDraw"),e.draw(s,l),tr(d,e,"afterDraw")):e.draw(s,l)}o&&(0,i.$)(r)}}}]);