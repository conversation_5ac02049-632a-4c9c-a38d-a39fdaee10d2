{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/app/globals.css"], "sourcesContent": ["/* Bootstrap import doit être en premier */\r\n@import \"bootstrap/dist/css/bootstrap.min.css\";\r\n\r\n/* P<PERSON>s <PERSON> */\r\n*, ::before, ::after{\n  --tw-border-spacing-x: 0;\n  --tw-border-spacing-y: 0;\n  --tw-translate-x: 0;\n  --tw-translate-y: 0;\n  --tw-rotate: 0;\n  --tw-skew-x: 0;\n  --tw-skew-y: 0;\n  --tw-scale-x: 1;\n  --tw-scale-y: 1;\n  --tw-pan-x:  ;\n  --tw-pan-y:  ;\n  --tw-pinch-zoom:  ;\n  --tw-scroll-snap-strictness: proximity;\n  --tw-gradient-from-position:  ;\n  --tw-gradient-via-position:  ;\n  --tw-gradient-to-position:  ;\n  --tw-ordinal:  ;\n  --tw-slashed-zero:  ;\n  --tw-numeric-figure:  ;\n  --tw-numeric-spacing:  ;\n  --tw-numeric-fraction:  ;\n  --tw-ring-inset:  ;\n  --tw-ring-offset-width: 0px;\n  --tw-ring-offset-color: #fff;\n  --tw-ring-color: rgb(59 130 246 / 0.5);\n  --tw-ring-offset-shadow: 0 0 #0000;\n  --tw-ring-shadow: 0 0 #0000;\n  --tw-shadow: 0 0 #0000;\n  --tw-shadow-colored: 0 0 #0000;\n  --tw-blur:  ;\n  --tw-brightness:  ;\n  --tw-contrast:  ;\n  --tw-grayscale:  ;\n  --tw-hue-rotate:  ;\n  --tw-invert:  ;\n  --tw-saturate:  ;\n  --tw-sepia:  ;\n  --tw-drop-shadow:  ;\n  --tw-backdrop-blur:  ;\n  --tw-backdrop-brightness:  ;\n  --tw-backdrop-contrast:  ;\n  --tw-backdrop-grayscale:  ;\n  --tw-backdrop-hue-rotate:  ;\n  --tw-backdrop-invert:  ;\n  --tw-backdrop-opacity:  ;\n  --tw-backdrop-saturate:  ;\n  --tw-backdrop-sepia:  ;\n  --tw-contain-size:  ;\n  --tw-contain-layout:  ;\n  --tw-contain-paint:  ;\n  --tw-contain-style:  ;\n}\r\n::backdrop{\n  --tw-border-spacing-x: 0;\n  --tw-border-spacing-y: 0;\n  --tw-translate-x: 0;\n  --tw-translate-y: 0;\n  --tw-rotate: 0;\n  --tw-skew-x: 0;\n  --tw-skew-y: 0;\n  --tw-scale-x: 1;\n  --tw-scale-y: 1;\n  --tw-pan-x:  ;\n  --tw-pan-y:  ;\n  --tw-pinch-zoom:  ;\n  --tw-scroll-snap-strictness: proximity;\n  --tw-gradient-from-position:  ;\n  --tw-gradient-via-position:  ;\n  --tw-gradient-to-position:  ;\n  --tw-ordinal:  ;\n  --tw-slashed-zero:  ;\n  --tw-numeric-figure:  ;\n  --tw-numeric-spacing:  ;\n  --tw-numeric-fraction:  ;\n  --tw-ring-inset:  ;\n  --tw-ring-offset-width: 0px;\n  --tw-ring-offset-color: #fff;\n  --tw-ring-color: rgb(59 130 246 / 0.5);\n  --tw-ring-offset-shadow: 0 0 #0000;\n  --tw-ring-shadow: 0 0 #0000;\n  --tw-shadow: 0 0 #0000;\n  --tw-shadow-colored: 0 0 #0000;\n  --tw-blur:  ;\n  --tw-brightness:  ;\n  --tw-contrast:  ;\n  --tw-grayscale:  ;\n  --tw-hue-rotate:  ;\n  --tw-invert:  ;\n  --tw-saturate:  ;\n  --tw-sepia:  ;\n  --tw-drop-shadow:  ;\n  --tw-backdrop-blur:  ;\n  --tw-backdrop-brightness:  ;\n  --tw-backdrop-contrast:  ;\n  --tw-backdrop-grayscale:  ;\n  --tw-backdrop-hue-rotate:  ;\n  --tw-backdrop-invert:  ;\n  --tw-backdrop-opacity:  ;\n  --tw-backdrop-saturate:  ;\n  --tw-backdrop-sepia:  ;\n  --tw-contain-size:  ;\n  --tw-contain-layout:  ;\n  --tw-contain-paint:  ;\n  --tw-contain-style:  ;\n}\r\n/* ! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com */\r\n/*\n1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)\n2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)\n*/\r\n*,\n::before,\n::after {\n  box-sizing: border-box; /* 1 */\n  border-width: 0; /* 2 */\n  border-style: solid; /* 2 */\n  border-color: #e5e7eb; /* 2 */\n}\r\n::before,\n::after {\n  --tw-content: '';\n}\r\n/*\n1. Use a consistent sensible line-height in all browsers.\n2. Prevent adjustments of font size after orientation changes in iOS.\n3. Use a more readable tab size.\n4. Use the user's configured `sans` font-family by default.\n5. Use the user's configured `sans` font-feature-settings by default.\n6. Use the user's configured `sans` font-variation-settings by default.\n7. Disable tap highlights on iOS\n*/\r\nhtml,\n:host {\n  line-height: 1.5; /* 1 */\n  -webkit-text-size-adjust: 100%; /* 2 */\n  -moz-tab-size: 4; /* 3 */\n  -o-tab-size: 4;\n     tab-size: 4; /* 3 */\n  font-family: ui-sans-serif, system-ui, sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\"; /* 4 */\n  font-feature-settings: normal; /* 5 */\n  font-variation-settings: normal; /* 6 */\n  -webkit-tap-highlight-color: transparent; /* 7 */\n}\r\n/*\n1. Remove the margin in all browsers.\n2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.\n*/\r\nbody {\n  margin: 0; /* 1 */\n  line-height: inherit; /* 2 */\n}\r\n/*\n1. Add the correct height in Firefox.\n2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)\n3. Ensure horizontal rules are visible by default.\n*/\r\nhr {\n  height: 0; /* 1 */\n  color: inherit; /* 2 */\n  border-top-width: 1px; /* 3 */\n}\r\n/*\nAdd the correct text decoration in Chrome, Edge, and Safari.\n*/\r\nabbr:where([title]) {\n  -webkit-text-decoration: underline dotted;\n          text-decoration: underline dotted;\n}\r\n/*\nRemove the default font size and weight for headings.\n*/\r\nh1,\nh2,\nh3,\nh4,\nh5,\nh6 {\n  font-size: inherit;\n  font-weight: inherit;\n}\r\n/*\nReset links to optimize for opt-in styling instead of opt-out.\n*/\r\na {\n  color: inherit;\n  text-decoration: inherit;\n}\r\n/*\nAdd the correct font weight in Edge and Safari.\n*/\r\nb,\nstrong {\n  font-weight: bolder;\n}\r\n/*\n1. Use the user's configured `mono` font-family by default.\n2. Use the user's configured `mono` font-feature-settings by default.\n3. Use the user's configured `mono` font-variation-settings by default.\n4. Correct the odd `em` font sizing in all browsers.\n*/\r\ncode,\nkbd,\nsamp,\npre {\n  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace; /* 1 */\n  font-feature-settings: normal; /* 2 */\n  font-variation-settings: normal; /* 3 */\n  font-size: 1em; /* 4 */\n}\r\n/*\nAdd the correct font size in all browsers.\n*/\r\nsmall {\n  font-size: 80%;\n}\r\n/*\nPrevent `sub` and `sup` elements from affecting the line height in all browsers.\n*/\r\nsub,\nsup {\n  font-size: 75%;\n  line-height: 0;\n  position: relative;\n  vertical-align: baseline;\n}\r\nsub {\n  bottom: -0.25em;\n}\r\nsup {\n  top: -0.5em;\n}\r\n/*\n1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)\n2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)\n3. Remove gaps between table borders by default.\n*/\r\ntable {\n  text-indent: 0; /* 1 */\n  border-color: inherit; /* 2 */\n  border-collapse: collapse; /* 3 */\n}\r\n/*\n1. Change the font styles in all browsers.\n2. Remove the margin in Firefox and Safari.\n3. Remove default padding in all browsers.\n*/\r\nbutton,\ninput,\noptgroup,\nselect,\ntextarea {\n  font-family: inherit; /* 1 */\n  font-feature-settings: inherit; /* 1 */\n  font-variation-settings: inherit; /* 1 */\n  font-size: 100%; /* 1 */\n  font-weight: inherit; /* 1 */\n  line-height: inherit; /* 1 */\n  letter-spacing: inherit; /* 1 */\n  color: inherit; /* 1 */\n  margin: 0; /* 2 */\n  padding: 0; /* 3 */\n}\r\n/*\nRemove the inheritance of text transform in Edge and Firefox.\n*/\r\nbutton,\nselect {\n  text-transform: none;\n}\r\n/*\n1. Correct the inability to style clickable types in iOS and Safari.\n2. Remove default button styles.\n*/\r\nbutton,\ninput:where([type='button']),\ninput:where([type='reset']),\ninput:where([type='submit']) {\n  -webkit-appearance: button; /* 1 */\n  background-color: transparent; /* 2 */\n  background-image: none; /* 2 */\n}\r\n/*\nUse the modern Firefox focus style for all focusable elements.\n*/\r\n:-moz-focusring {\n  outline: auto;\n}\r\n/*\nRemove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)\n*/\r\n:-moz-ui-invalid {\n  box-shadow: none;\n}\r\n/*\nAdd the correct vertical alignment in Chrome and Firefox.\n*/\r\nprogress {\n  vertical-align: baseline;\n}\r\n/*\nCorrect the cursor style of increment and decrement buttons in Safari.\n*/\r\n::-webkit-inner-spin-button,\n::-webkit-outer-spin-button {\n  height: auto;\n}\r\n/*\n1. Correct the odd appearance in Chrome and Safari.\n2. Correct the outline style in Safari.\n*/\r\n[type='search'] {\n  -webkit-appearance: textfield; /* 1 */\n  outline-offset: -2px; /* 2 */\n}\r\n/*\nRemove the inner padding in Chrome and Safari on macOS.\n*/\r\n::-webkit-search-decoration {\n  -webkit-appearance: none;\n}\r\n/*\n1. Correct the inability to style clickable types in iOS and Safari.\n2. Change font properties to `inherit` in Safari.\n*/\r\n::-webkit-file-upload-button {\n  -webkit-appearance: button; /* 1 */\n  font: inherit; /* 2 */\n}\r\n/*\nAdd the correct display in Chrome and Safari.\n*/\r\nsummary {\n  display: list-item;\n}\r\n/*\nRemoves the default spacing and border for appropriate elements.\n*/\r\nblockquote,\ndl,\ndd,\nh1,\nh2,\nh3,\nh4,\nh5,\nh6,\nhr,\nfigure,\np,\npre {\n  margin: 0;\n}\r\nfieldset {\n  margin: 0;\n  padding: 0;\n}\r\nlegend {\n  padding: 0;\n}\r\nol,\nul,\nmenu {\n  list-style: none;\n  margin: 0;\n  padding: 0;\n}\r\n/*\nReset default styling for dialogs.\n*/\r\ndialog {\n  padding: 0;\n}\r\n/*\nPrevent resizing textareas horizontally by default.\n*/\r\ntextarea {\n  resize: vertical;\n}\r\n/*\n1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)\n2. Set the default placeholder color to the user's configured gray 400 color.\n*/\r\ninput::-moz-placeholder, textarea::-moz-placeholder {\n  opacity: 1; /* 1 */\n  color: #9ca3af; /* 2 */\n}\r\ninput::placeholder,\ntextarea::placeholder {\n  opacity: 1; /* 1 */\n  color: #9ca3af; /* 2 */\n}\r\n/*\nSet the default cursor for buttons.\n*/\r\nbutton,\n[role=\"button\"] {\n  cursor: pointer;\n}\r\n/*\nMake sure disabled buttons don't get the pointer cursor.\n*/\r\n:disabled {\n  cursor: default;\n}\r\n/*\n1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)\n2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)\n   This can trigger a poorly considered lint error in some tools but is included by design.\n*/\r\nimg,\nsvg,\nvideo,\ncanvas,\naudio,\niframe,\nembed,\nobject {\n  display: block; /* 1 */\n  vertical-align: middle; /* 2 */\n}\r\n/*\nConstrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)\n*/\r\nimg,\nvideo {\n  max-width: 100%;\n  height: auto;\n}\r\n/* Make elements with the HTML hidden attribute stay hidden by default */\r\n[hidden]:where(:not([hidden=\"until-found\"])) {\n  display: none;\n}\r\n.container{\n  width: 100%;\n}\r\n@media (min-width: 640px){\r\n  .container{\n    max-width: 640px;\n  }\n}\r\n@media (min-width: 768px){\r\n  .container{\n    max-width: 768px;\n  }\n}\r\n@media (min-width: 1024px){\r\n  .container{\n    max-width: 1024px;\n  }\n}\r\n@media (min-width: 1280px){\r\n  .container{\n    max-width: 1280px;\n  }\n}\r\n@media (min-width: 1536px){\r\n  .container{\n    max-width: 1536px;\n  }\n}\r\n.collapse{\n  visibility: collapse;\n}\r\n.m-2{\n  margin: 0.5rem;\n}\r\n.my-2{\n  margin-top: 0.5rem;\n  margin-bottom: 0.5rem;\n}\r\n.mb-0{\n  margin-bottom: 0px;\n}\r\n.mb-1{\n  margin-bottom: 0.25rem;\n}\r\n.mb-2{\n  margin-bottom: 0.5rem;\n}\r\n.mb-3{\n  margin-bottom: 0.75rem;\n}\r\n.mb-4{\n  margin-bottom: 1rem;\n}\r\n.me-1{\n  margin-inline-end: 0.25rem;\n}\r\n.me-2{\n  margin-inline-end: 0.5rem;\n}\r\n.me-auto{\n  margin-inline-end: auto;\n}\r\n.ms-1{\n  margin-inline-start: 0.25rem;\n}\r\n.ms-2{\n  margin-inline-start: 0.5rem;\n}\r\n.mt-2{\n  margin-top: 0.5rem;\n}\r\n.mt-3{\n  margin-top: 0.75rem;\n}\r\n.mt-4{\n  margin-top: 1rem;\n}\r\n.mt-5{\n  margin-top: 1.25rem;\n}\r\n.inline-block{\n  display: inline-block;\n}\r\n.table{\n  display: table;\n}\r\n.hidden{\n  display: none;\n}\r\n.gap-2{\n  gap: 0.5rem;\n}\r\n.break-all{\n  word-break: break-all;\n}\r\n.rounded{\n  border-radius: 0.25rem;\n}\r\n.border{\n  border-width: 1px;\n}\r\n.p-3{\n  padding: 0.75rem;\n}\r\n.p-4{\n  padding: 1rem;\n}\r\n.text-center{\n  text-align: center;\n}\r\n.text-end{\n  text-align: end;\n}\r\n.text-white{\n  --tw-text-opacity: 1;\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\n}\r\n.filter{\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\n}\r\n.transition{\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\r\n\r\n/* Styles from frontend/src/index.css */\r\nbody {\r\n  margin: 0;\r\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',\r\n    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\r\n    sans-serif;\r\n  -webkit-font-smoothing: antialiased;\r\n  -moz-osx-font-smoothing: grayscale;\r\n  /* The background and color from the default Next.js theme will be applied below if not overridden */\r\n}\r\n\r\ncode {\r\n  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',\r\n    monospace;\r\n}\r\n\r\n/* Styles from frontend/src/App.css */\r\n.App {\r\n  text-align: center;\r\n}\r\n\r\n/* Force light theme - override dark mode */\r\n:root {\r\n  --background: #ffffff;\r\n  --foreground: #212529;\r\n}\r\n\r\n/* Disable dark mode completely */\r\n@media (prefers-color-scheme: dark) {\r\n  :root {\r\n    --background: #ffffff;\r\n    --foreground: #212529;\r\n  }\r\n}\r\n\r\n/* Apply light theme to body */\r\nbody {\r\n  background: var(--background) !important;\r\n  color: var(--foreground) !important;\r\n}\r\n\r\n/* Ensure navbar is properly styled */\r\n.navbar {\r\n  z-index: 1030;\r\n}\r\n\r\n/* Force navbar links to display on large screens */\r\n@media (min-width: 992px) {\r\n  .navbar-collapse {\r\n    display: block !important;\r\n  }\r\n}\r\n\r\n/* Ensure main content has proper spacing */\r\nmain.container {\r\n  background: var(--background);\r\n  min-height: calc(100vh - 56px); /* Account for navbar height */\r\n}\r\n"], "names": [], "mappings": "AAIA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+GA;;;;;AAQA;;;;AAaA;;;;;;;;;;;;AAgBA;;;;;AASA;;;;;;AAQA;;;;AAOA;;;;;AAYA;;;;;AAOA;;;;AAUA;;;;;;;AAYA;;;;AAMA;;;;;;;AAOA;;;;AAGA;;;;AAQA;;;;;;AAUA;;;;;;;;;;;;;AAmBA;;;;AAQA;;;;;;AAWA;;;;AAMA;;;;AAMA;;;;AAMA;;;;AAQA;;;;;AAOA;;;;AAOA;;;;;AAOA;;;;AAMA;;;;AAeA;;;;;AAIA;;;;AAGA;;;;;;AAUA;;;;AAMA;;;;AAOA;;;;;AAAA;;;;;AAYA;;;;AAOA;;;;AAQA;;;;;AAcA;;;;;AAMA;;;;AAGA;;;;AAGA;EACE;;;;;AAIF;EACE;;;;;AAIF;EACE;;;;;AAIF;EACE;;;;;AAIF;EACE;;;;;AAIF;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;;;AASA;;;;;;;AAUA;;;;AAMA;;;;AAKA;;;;;AAMA;EACE;;;;;;AAOF;;;;;AAMA;;;;AAKA;EACE;;;;;AAMF"}}]}