'use client';

import React, { useState } from 'react';
import Link from 'next/link';

const Navbar: React.FC = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const closeMenu = () => {
    setIsMenuOpen(false);
  };

  return (
    <nav className="navbar navbar-expand-lg navbar-dark bg-dark">
      <div className="container-fluid">
        <Link className="navbar-brand me-3" href="/" onClick={closeMenu}>
          FIRE Dashboard
        </Link>
        <div className="d-lg-flex">
          <ul className="navbar-nav me-auto mb-2 mb-lg-0 flex-row">
            <li className="nav-item">
              <Link className="nav-link" href="/" onClick={closeMenu}>
                Dashboard
              </Link>
            </li>
            <li className="nav-item">
              <Link className="nav-link" href="/assets" onClick={closeMenu}>
                Patrimoine
              </Link>
            </li>
            <li className="nav-item">
              <Link className="nav-link" href="/liabilities" onClick={closeMenu}>
                Emprunts
              </Link>
            </li>
            <li className="nav-item">
              <Link className="nav-link" href="/scpi" onClick={closeMenu}>
                SCPI
              </Link>
            </li>
            <li className="nav-item">
              <Link className="nav-link" href="/evolution" onClick={closeMenu}>
                Évolution
              </Link>
            </li>
            <li className="nav-item">
              <Link className="nav-link" href="/budget" onClick={closeMenu}>
                Budget FIRE
              </Link>
            </li>
            <li className="nav-item">
              <Link className="nav-link" href="/scenarios" onClick={closeMenu}>
                Scénarios
              </Link>
            </li>
            <li className="nav-item">
              <Link className="nav-link" href="/fire" onClick={closeMenu}>
                Objectif FIRE
              </Link>
            </li>
            <li className="nav-item">
              <Link className="nav-link" href="/test" onClick={closeMenu}>
                Test API
              </Link>
            </li>
          </ul>
        </div>
        <button
          className="navbar-toggler d-lg-none"
          type="button"
          onClick={toggleMenu}
          aria-controls="navbarNavContent"
          aria-expanded={isMenuOpen}
          aria-label="Toggle navigation"
        >
          <span className="navbar-toggler-icon"></span>
        </button>
        {/* Mobile menu */}
        {isMenuOpen && (
          <div className="navbar-collapse show d-lg-none position-absolute top-100 start-0 w-100 bg-dark">
            <ul className="navbar-nav">
              <li className="nav-item">
                <Link className="nav-link" href="/" onClick={closeMenu}>
                  Dashboard
                </Link>
              </li>
              <li className="nav-item">
                <Link className="nav-link" href="/assets" onClick={closeMenu}>
                  Patrimoine
                </Link>
              </li>
              <li className="nav-item">
                <Link className="nav-link" href="/liabilities" onClick={closeMenu}>
                  Emprunts
                </Link>
              </li>
              <li className="nav-item">
                <Link className="nav-link" href="/scpi" onClick={closeMenu}>
                  SCPI
                </Link>
              </li>
              <li className="nav-item">
                <Link className="nav-link" href="/evolution" onClick={closeMenu}>
                  Évolution
                </Link>
              </li>
              <li className="nav-item">
                <Link className="nav-link" href="/budget" onClick={closeMenu}>
                  Budget FIRE
                </Link>
              </li>
              <li className="nav-item">
                <Link className="nav-link" href="/scenarios" onClick={closeMenu}>
                  Scénarios
                </Link>
              </li>
              <li className="nav-item">
                <Link className="nav-link" href="/fire" onClick={closeMenu}>
                  Objectif FIRE
                </Link>
              </li>
              <li className="nav-item">
                <Link className="nav-link" href="/test" onClick={closeMenu}>
                  Test API
                </Link>
              </li>
            </ul>
          </div>
        )}
      </div>
    </nav>
  );
};

export default Navbar;
