{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/AI-Code/fire_UI/frontnextjs/src/components/ApiClient.ts"], "sourcesContent": ["import axios from 'axios';\r\n\r\nconst apiClient = axios.create({\r\n  baseURL: 'http://localhost:8000/api',\r\n  headers: {\r\n    'Content-Type': 'application/json',\r\n  },\r\n  timeout: 10000, // 10 second timeout\r\n});\r\n\r\n// Add response interceptor for error handling\r\napiClient.interceptors.response.use(\r\n  (response) => response,\r\n  (error) => {\r\n    console.error('API Error:', error.response?.status, error.config?.url, error.message);\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\nexport default apiClient;\r\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,YAAY,wIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAC7B,SAAS;IACT,SAAS;QACP,gBAAgB;IAClB;IACA,SAAS;AACX;AAEA,8CAA8C;AAC9C,UAAU,YAAY,CAAC,QAAQ,CAAC,GAAG,CACjC,CAAC,WAAa,UACd,CAAC;IACC,QAAQ,KAAK,CAAC,cAAc,MAAM,QAAQ,EAAE,QAAQ,MAAM,MAAM,EAAE,KAAK,MAAM,OAAO;IACpF,OAAO,QAAQ,MAAM,CAAC;AACxB;uCAGa", "debugId": null}}, {"offset": {"line": 34, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/AI-Code/fire_UI/frontnextjs/src/app/budget/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useEffect, useState, FormEvent } from 'react';\r\nimport apiClient from '../../components/ApiClient'; // Ajusté\r\nimport { Modal, Button, Form, Alert } from 'react-bootstrap'; // ProgressBar non utilisé ici\r\n\r\ninterface BudgetCategory {\r\n  id: number;\r\n  nom: string;\r\n  budget_annuel: number;\r\n  description: string;\r\n  ordre_affichage: number;\r\n}\r\n\r\ninterface BudgetSummary {\r\n  total_budget_annuel: number;\r\n  retrait_brut_necessaire: number;\r\n  impact_date_fire: string; // Peut-être pas utilisé dans l'affichage direct mais bon à avoir\r\n}\r\n\r\nconst BudgetCategoryForm: React.FC<{\r\n  category: Partial<BudgetCategory> | null,\r\n  onSave: () => void,\r\n  onCancel: () => void\r\n}> = ({ category, onSave, onCancel }) => {\r\n  const [formData, setFormData] = useState({\r\n    nom: category?.nom || '',\r\n    budget_annuel: category?.budget_annuel || 0,\r\n    description: category?.description || '',\r\n    ordre_affichage: category?.ordre_affichage || 0,\r\n  });\r\n\r\n  useEffect(() => {\r\n    setFormData({\r\n        nom: category?.nom || '',\r\n        budget_annuel: category?.budget_annuel || 0,\r\n        description: category?.description || '',\r\n        ordre_affichage: category?.ordre_affichage || 0,\r\n    });\r\n  }, [category]);\r\n\r\n  const handleSubmit = async (e: FormEvent) => {\r\n    e.preventDefault();\r\n    const method = category?.id ? 'put' : 'post';\r\n    const url = category?.id ? `/budget/categories/${category.id}` : '/budget/categories';\r\n    try {\r\n      await apiClient[method](url, formData);\r\n      onSave();\r\n    } catch (error: unknown) {\r\n      const errorMessage = error instanceof Error ? error.message : 'Unknown error';\r\n      console.error(\"Erreur lors de la sauvegarde de la catégorie\", errorMessage);\r\n      alert(`Erreur: ${errorMessage}`);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Form onSubmit={handleSubmit}>\r\n      <Form.Group className=\"mb-3\"><Form.Label>Nom de la catégorie</Form.Label><Form.Control type=\"text\" value={formData.nom} onChange={(e) => setFormData({ ...formData, nom: e.target.value })} required placeholder=\"Ex: Alimentation, Assurance Maison...\" /></Form.Group>\r\n      <Form.Group className=\"mb-3\"><Form.Label>Budget annuel (€)</Form.Label><Form.Control type=\"number\" step=\"0.01\" value={formData.budget_annuel} onChange={(e) => setFormData({ ...formData, budget_annuel: parseFloat(e.target.value) || 0 })} required /></Form.Group>\r\n      <Form.Group className=\"mb-3\"><Form.Label>Description</Form.Label><Form.Control type=\"text\" value={formData.description} onChange={(e) => setFormData({ ...formData, description: e.target.value })} placeholder=\"Description de la catégorie...\" /></Form.Group>\r\n      <Form.Group className=\"mb-3\"><Form.Label>Ordre d&apos;affichage</Form.Label><Form.Control type=\"number\" value={formData.ordre_affichage} onChange={(e) => setFormData({ ...formData, ordre_affichage: parseInt(e.target.value) || 0 })} placeholder=\"Ordre d'affichage (1, 2, 3...)\" /><Form.Text className=\"text-muted\">Plus le nombre est petit, plus la catégorie apparaîtra en haut de la liste</Form.Text></Form.Group>\r\n      <div className=\"d-flex justify-content-end gap-2\"><Button variant=\"secondary\" onClick={onCancel}>Annuler</Button><Button variant=\"primary\" type=\"submit\">Sauvegarder</Button></div>\r\n    </Form>\r\n  );\r\n};\r\n\r\nconst BudgetFirePage: React.FC = () => {\r\n  const [categories, setCategories] = useState<BudgetCategory[]>([]);\r\n  const [summary, setSummary] = useState<BudgetSummary | null>(null);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const [showCategoryModal, setShowCategoryModal] = useState(false);\r\n  const [selectedCategory, setSelectedCategory] = useState<Partial<BudgetCategory> | null>(null);\r\n  const [retryCount, setRetryCount] = useState(0);\r\n  const [showCategoriesTable, setShowCategoriesTable] = useState(false);\r\n\r\n  const fetchData = async () => {\r\n    setLoading(true);\r\n    setError(null);\r\n    try {\r\n      const [categoriesRes, summaryRes] = await Promise.all([\r\n        apiClient.get('/budget/categories'),\r\n        apiClient.get('/budget/summary')\r\n      ]);\r\n      setCategories(categoriesRes.data.sort((a: BudgetCategory, b: BudgetCategory) => a.ordre_affichage - b.ordre_affichage));\r\n      setSummary(summaryRes.data);\r\n      setLoading(false);\r\n      setRetryCount(0);\r\n    } catch (error: unknown) {\r\n      console.error('Budget API error:', error);\r\n      if (retryCount < 2) {\r\n        setRetryCount(prev => prev + 1);\r\n        setTimeout(() => fetchData(), 1000);\r\n      } else {\r\n        setError('Erreur lors de la récupération des données budgétaires.');\r\n        setLoading(false);\r\n      }\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    fetchData();\r\n  // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, []);\r\n\r\n  const handleCategorySave = () => {\r\n    setShowCategoryModal(false);\r\n    setSelectedCategory(null);\r\n    fetchData();\r\n  };\r\n\r\n  const handleDeleteCategory = async (categoryId: number, categoryName: string) => {\r\n    if (window.confirm(`Êtes-vous sûr de vouloir supprimer la catégorie \"${categoryName}\" ? Cette action est irréversible.`)) {\r\n      try {\r\n        await apiClient.delete(`/budget/categories/${categoryId}`);\r\n        fetchData();\r\n      } catch (error: unknown) {\r\n        const errorMessage = error instanceof Error ? error.message : 'Unknown error';\r\n        console.error(\"Erreur lors de la suppression de la catégorie\", errorMessage);\r\n        alert(`Erreur lors de la suppression: ${errorMessage}`);\r\n      }\r\n    }\r\n  };\r\n\r\n  const formatCurrency = (value: number) => {\r\n    return new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR' }).format(value);\r\n  };\r\n\r\n  if (loading && !showCategoryModal && retryCount === 0) return <p>Chargement du budget...</p>;\r\n  if (error && retryCount >=2) return (\r\n    <div><p className=\"text-danger\">{error}</p><button className=\"btn btn-primary\" onClick={() => {setRetryCount(0); fetchData();}}>Réessayer</button></div>\r\n  );\r\n  if (!loading && !summary && !error) return <p>Aucune donnée budgétaire trouvée.</p>;\r\n  if (loading && !showCategoryModal) return <p>Chargement du budget, tentative {retryCount + 1}...</p>;\r\n\r\n  return (\r\n    <div>\r\n      <div className=\"d-flex justify-content-between align-items-center mb-4\">\r\n        <h1>Calculateur Budget FIRE</h1>\r\n        <div className=\"d-flex gap-2\">\r\n          <Button variant=\"outline-secondary\" onClick={() => setShowCategoriesTable(!showCategoriesTable)}>{showCategoriesTable ? 'Masquer' : 'Gérer'} Catégories</Button>\r\n          <Button variant=\"success\" onClick={() => { setSelectedCategory({}); setShowCategoryModal(true); }}>Nouvelle Catégorie</Button>\r\n        </div>\r\n      </div>\r\n\r\n      {summary && (\r\n        <>\r\n          <div className=\"row mb-4\">\r\n            <div className=\"col-md-4\"><div className=\"card text-white bg-primary\"><div className=\"card-body text-center\"><h5>Budget Annuel FIRE</h5><h4>{formatCurrency(summary.total_budget_annuel)}</h4><small>Dépenses nettes cibles</small></div></div></div>\r\n            <div className=\"col-md-4\"><div className=\"card text-white bg-warning\"><div className=\"card-body text-center\"><h5>Retrait Brut Nécessaire</h5><h4>{formatCurrency(summary.retrait_brut_necessaire)}</h4><small>+30% impôts/PS</small></div></div></div>\r\n            <div className=\"col-md-4\"><div className=\"card text-white bg-success\"><div className=\"card-body text-center\"><h5>Capital FIRE Requis</h5><h4>{formatCurrency(910150)}</h4><small>Objectif documenté</small></div></div></div>\r\n          </div>\r\n          <Alert variant=\"info\" className=\"mb-4\">\r\n            <Alert.Heading>Objectif FIRE 2038</Alert.Heading>\r\n            <p className=\"mb-0\">Avec ce budget de <strong>{formatCurrency(summary.total_budget_annuel)}</strong> par an, votre objectif FIRE documenté est de <strong>{formatCurrency(910150)}</strong> (retrait brut de <strong>{formatCurrency(summary.retrait_brut_necessaire)}</strong>/an).</p>\r\n          </Alert>\r\n        </>\r\n      )}\r\n\r\n      {showCategoriesTable && (\r\n        <div className=\"mb-4\">\r\n          <h3>Gestion des Catégories Budgétaires</h3>\r\n          <div className=\"table-responsive\">\r\n            <table className=\"table table-striped table-hover\">\r\n              <thead className=\"table-secondary\"><tr><th>Ordre</th><th>Nom</th><th className=\"text-end\">Budget Annuel</th><th>Description</th><th className=\"text-center\">Actions</th></tr></thead>\r\n              <tbody>\r\n                {categories.map(category => (\r\n                  <tr key={category.id}><td>{category.ordre_affichage}</td><td><strong>{category.nom}</strong></td><td className=\"text-end\">{formatCurrency(category.budget_annuel)}</td><td>{category.description}</td>\r\n                    <td className=\"text-center\">\r\n                      <Button variant=\"outline-primary\" size=\"sm\" className=\"me-2\" onClick={() => { setSelectedCategory(category); setShowCategoryModal(true); }}>Modifier</Button>\r\n                      <Button variant=\"outline-danger\" size=\"sm\" onClick={() => handleDeleteCategory(category.id, category.nom)}>Supprimer</Button>\r\n                    </td>\r\n                  </tr>\r\n                ))}\r\n              </tbody>\r\n              <tfoot><tr className=\"table-info\"><th colSpan={2}>TOTAL</th><th className=\"text-end\">{formatCurrency(categories.reduce((sum, cat) => sum + cat.budget_annuel, 0))}</th><th colSpan={2}></th></tr></tfoot>\r\n            </table>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      <div className=\"table-responsive\">\r\n        <table className=\"table table-striped table-hover\">\r\n          <thead className=\"table-dark\"><tr><th>Catégorie</th><th className=\"text-end\">Budget Annuel</th><th className=\"text-end\">Budget Mensuel</th></tr></thead>\r\n          <tbody>\r\n            {categories.map(category => (\r\n              <tr key={category.id}>\r\n                <td><strong>{category.nom}</strong>{category.description && (<><br /><small className=\"text-muted\">{category.description}</small></>)}</td>\r\n                <td className=\"text-end\">{formatCurrency(category.budget_annuel)}</td>\r\n                <td className=\"text-end\">{formatCurrency(category.budget_annuel / 12)}</td>\r\n              </tr>\r\n            ))}\r\n          </tbody>\r\n          <tfoot><tr className=\"table-info\"><th>TOTAL</th><th className=\"text-end\">{formatCurrency(categories.reduce((sum, cat) => sum + cat.budget_annuel, 0))}</th><th className=\"text-end\">{formatCurrency(categories.reduce((sum, cat) => sum + cat.budget_annuel, 0) / 12)}</th></tr></tfoot>\r\n        </table>\r\n      </div>\r\n\r\n      <Modal show={showCategoryModal} onHide={() => setShowCategoryModal(false)} size=\"lg\"><Modal.Header closeButton><Modal.Title>{selectedCategory?.id ? `Modifier \"${selectedCategory.nom}\"` : 'Nouvelle catégorie budgétaire'}</Modal.Title></Modal.Header><Modal.Body><BudgetCategoryForm category={selectedCategory} onSave={handleCategorySave} onCancel={() => setShowCategoryModal(false)} /></Modal.Body></Modal>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default BudgetFirePage;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA,oOAAoD,SAAS;AAC7D,oUAA8D,8BAA8B;AAA5F;AAAA;AAAA;;;AAJA;;;;AAoBA,MAAM,qBAID,CAAC,EAAE,QAAQ,EAAE,MAAM,EAAE,QAAQ,EAAE;;IAClC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,KAAK,UAAU,OAAO;QACtB,eAAe,UAAU,iBAAiB;QAC1C,aAAa,UAAU,eAAe;QACtC,iBAAiB,UAAU,mBAAmB;IAChD;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,YAAY;gBACR,KAAK,UAAU,OAAO;gBACtB,eAAe,UAAU,iBAAiB;gBAC1C,aAAa,UAAU,eAAe;gBACtC,iBAAiB,UAAU,mBAAmB;YAClD;QACF;uCAAG;QAAC;KAAS;IAEb,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,MAAM,SAAS,UAAU,KAAK,QAAQ;QACtC,MAAM,MAAM,UAAU,KAAK,CAAC,mBAAmB,EAAE,SAAS,EAAE,EAAE,GAAG;QACjE,IAAI;YACF,MAAM,iIAAA,CAAA,UAAS,CAAC,OAAO,CAAC,KAAK;YAC7B;QACF,EAAE,OAAO,OAAgB;YACvB,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC9D,QAAQ,KAAK,CAAC,gDAAgD;YAC9D,MAAM,CAAC,QAAQ,EAAE,cAAc;QACjC;IACF;IAEA,qBACE,6LAAC,uLAAA,CAAA,OAAI;QAAC,UAAU;;0BACd,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;gBAAC,WAAU;;kCAAO,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;kCAAC;;;;;;kCAAgC,6LAAC,uLAAA,CAAA,OAAI,CAAC,OAAO;wBAAC,MAAK;wBAAO,OAAO,SAAS,GAAG;wBAAE,UAAU,CAAC,IAAM,YAAY;gCAAE,GAAG,QAAQ;gCAAE,KAAK,EAAE,MAAM,CAAC,KAAK;4BAAC;wBAAI,QAAQ;wBAAC,aAAY;;;;;;;;;;;;0BACjN,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;gBAAC,WAAU;;kCAAO,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;kCAAC;;;;;;kCAA8B,6LAAC,uLAAA,CAAA,OAAI,CAAC,OAAO;wBAAC,MAAK;wBAAS,MAAK;wBAAO,OAAO,SAAS,aAAa;wBAAE,UAAU,CAAC,IAAM,YAAY;gCAAE,GAAG,QAAQ;gCAAE,eAAe,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;4BAAE;wBAAI,QAAQ;;;;;;;;;;;;0BACrP,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;gBAAC,WAAU;;kCAAO,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;kCAAC;;;;;;kCAAwB,6LAAC,uLAAA,CAAA,OAAI,CAAC,OAAO;wBAAC,MAAK;wBAAO,OAAO,SAAS,WAAW;wBAAE,UAAU,CAAC,IAAM,YAAY;gCAAE,GAAG,QAAQ;gCAAE,aAAa,EAAE,MAAM,CAAC,KAAK;4BAAC;wBAAI,aAAY;;;;;;;;;;;;0BAChN,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;gBAAC,WAAU;;kCAAO,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;kCAAC;;;;;;kCAAmC,6LAAC,uLAAA,CAAA,OAAI,CAAC,OAAO;wBAAC,MAAK;wBAAS,OAAO,SAAS,eAAe;wBAAE,UAAU,CAAC,IAAM,YAAY;gCAAE,GAAG,QAAQ;gCAAE,iBAAiB,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;4BAAE;wBAAI,aAAY;;;;;;kCAAmC,6LAAC,uLAAA,CAAA,OAAI,CAAC,IAAI;wBAAC,WAAU;kCAAa;;;;;;;;;;;;0BACzT,6LAAC;gBAAI,WAAU;;kCAAmC,6LAAC,2LAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAY,SAAS;kCAAU;;;;;;kCAAgB,6LAAC,2LAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAU,MAAK;kCAAS;;;;;;;;;;;;;;;;;;AAG/J;GA5CM;KAAA;AA8CN,MAAM,iBAA2B;;IAC/B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB,EAAE;IACjE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAwB;IAC7D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkC;IACzF,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/D,MAAM,YAAY;QAChB,WAAW;QACX,SAAS;QACT,IAAI;YACF,MAAM,CAAC,eAAe,WAAW,GAAG,MAAM,QAAQ,GAAG,CAAC;gBACpD,iIAAA,CAAA,UAAS,CAAC,GAAG,CAAC;gBACd,iIAAA,CAAA,UAAS,CAAC,GAAG,CAAC;aACf;YACD,cAAc,cAAc,IAAI,CAAC,IAAI,CAAC,CAAC,GAAmB,IAAsB,EAAE,eAAe,GAAG,EAAE,eAAe;YACrH,WAAW,WAAW,IAAI;YAC1B,WAAW;YACX,cAAc;QAChB,EAAE,OAAO,OAAgB;YACvB,QAAQ,KAAK,CAAC,qBAAqB;YACnC,IAAI,aAAa,GAAG;gBAClB,cAAc,CAAA,OAAQ,OAAO;gBAC7B,WAAW,IAAM,aAAa;YAChC,OAAO;gBACL,SAAS;gBACT,WAAW;YACb;QACF;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR;QACF,uDAAuD;QACvD;mCAAG,EAAE;IAEL,MAAM,qBAAqB;QACzB,qBAAqB;QACrB,oBAAoB;QACpB;IACF;IAEA,MAAM,uBAAuB,OAAO,YAAoB;QACtD,IAAI,OAAO,OAAO,CAAC,CAAC,iDAAiD,EAAE,aAAa,kCAAkC,CAAC,GAAG;YACxH,IAAI;gBACF,MAAM,iIAAA,CAAA,UAAS,CAAC,MAAM,CAAC,CAAC,mBAAmB,EAAE,YAAY;gBACzD;YACF,EAAE,OAAO,OAAgB;gBACvB,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,QAAQ,KAAK,CAAC,iDAAiD;gBAC/D,MAAM,CAAC,+BAA+B,EAAE,cAAc;YACxD;QACF;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YAAE,OAAO;YAAY,UAAU;QAAM,GAAG,MAAM,CAAC;IACvF;IAEA,IAAI,WAAW,CAAC,qBAAqB,eAAe,GAAG,qBAAO,6LAAC;kBAAE;;;;;;IACjE,IAAI,SAAS,cAAa,GAAG,qBAC3B,6LAAC;;0BAAI,6LAAC;gBAAE,WAAU;0BAAe;;;;;;0BAAU,6LAAC;gBAAO,WAAU;gBAAkB,SAAS;oBAAO,cAAc;oBAAI;gBAAY;0BAAG;;;;;;;;;;;;IAElI,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,OAAO,qBAAO,6LAAC;kBAAE;;;;;;IAC9C,IAAI,WAAW,CAAC,mBAAmB,qBAAO,6LAAC;;YAAE;YAAiC,aAAa;YAAE;;;;;;;IAE7F,qBACE,6LAAC;;0BACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;kCAAG;;;;;;kCACJ,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,2LAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAoB,SAAS,IAAM,uBAAuB,CAAC;;oCAAuB,sBAAsB,YAAY;oCAAQ;;;;;;;0CAC5I,6LAAC,2LAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,SAAS;oCAAQ,oBAAoB,CAAC;oCAAI,qBAAqB;gCAAO;0CAAG;;;;;;;;;;;;;;;;;;YAItG,yBACC;;kCACE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CAAW,cAAA,6LAAC;oCAAI,WAAU;8CAA6B,cAAA,6LAAC;wCAAI,WAAU;;0DAAwB,6LAAC;0DAAG;;;;;;0DAAuB,6LAAC;0DAAI,eAAe,QAAQ,mBAAmB;;;;;;0DAAO,6LAAC;0DAAM;;;;;;;;;;;;;;;;;;;;;;0CACrM,6LAAC;gCAAI,WAAU;0CAAW,cAAA,6LAAC;oCAAI,WAAU;8CAA6B,cAAA,6LAAC;wCAAI,WAAU;;0DAAwB,6LAAC;0DAAG;;;;;;0DAA4B,6LAAC;0DAAI,eAAe,QAAQ,uBAAuB;;;;;;0DAAO,6LAAC;0DAAM;;;;;;;;;;;;;;;;;;;;;;0CAC9M,6LAAC;gCAAI,WAAU;0CAAW,cAAA,6LAAC;oCAAI,WAAU;8CAA6B,cAAA,6LAAC;wCAAI,WAAU;;0DAAwB,6LAAC;0DAAG;;;;;;0DAAwB,6LAAC;0DAAI,eAAe;;;;;;0DAAa,6LAAC;0DAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAEnL,6LAAC,yLAAA,CAAA,QAAK;wBAAC,SAAQ;wBAAO,WAAU;;0CAC9B,6LAAC,yLAAA,CAAA,QAAK,CAAC,OAAO;0CAAC;;;;;;0CACf,6LAAC;gCAAE,WAAU;;oCAAO;kDAAkB,6LAAC;kDAAQ,eAAe,QAAQ,mBAAmB;;;;;;oCAAW;kDAA8C,6LAAC;kDAAQ,eAAe;;;;;;oCAAiB;kDAAkB,6LAAC;kDAAQ,eAAe,QAAQ,uBAAuB;;;;;;oCAAW;;;;;;;;;;;;;;;YAKpR,qCACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;kCAAG;;;;;;kCACJ,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAM,WAAU;;8CACf,6LAAC;oCAAM,WAAU;8CAAkB,cAAA,6LAAC;;0DAAG,6LAAC;0DAAG;;;;;;0DAAU,6LAAC;0DAAG;;;;;;0DAAQ,6LAAC;gDAAG,WAAU;0DAAW;;;;;;0DAAkB,6LAAC;0DAAG;;;;;;0DAAgB,6LAAC;gDAAG,WAAU;0DAAc;;;;;;;;;;;;;;;;;8CAC5J,6LAAC;8CACE,WAAW,GAAG,CAAC,CAAA,yBACd,6LAAC;;8DAAqB,6LAAC;8DAAI,SAAS,eAAe;;;;;;8DAAM,6LAAC;8DAAG,cAAA,6LAAC;kEAAQ,SAAS,GAAG;;;;;;;;;;;8DAAe,6LAAC;oDAAG,WAAU;8DAAY,eAAe,SAAS,aAAa;;;;;;8DAAO,6LAAC;8DAAI,SAAS,WAAW;;;;;;8DAC9L,6LAAC;oDAAG,WAAU;;sEACZ,6LAAC,2LAAA,CAAA,SAAM;4DAAC,SAAQ;4DAAkB,MAAK;4DAAK,WAAU;4DAAO,SAAS;gEAAQ,oBAAoB;gEAAW,qBAAqB;4DAAO;sEAAG;;;;;;sEAC5I,6LAAC,2LAAA,CAAA,SAAM;4DAAC,SAAQ;4DAAiB,MAAK;4DAAK,SAAS,IAAM,qBAAqB,SAAS,EAAE,EAAE,SAAS,GAAG;sEAAG;;;;;;;;;;;;;2CAHtG,SAAS,EAAE;;;;;;;;;;8CAQxB,6LAAC;8CAAM,cAAA,6LAAC;wCAAG,WAAU;;0DAAa,6LAAC;gDAAG,SAAS;0DAAG;;;;;;0DAAU,6LAAC;gDAAG,WAAU;0DAAY,eAAe,WAAW,MAAM,CAAC,CAAC,KAAK,MAAQ,MAAM,IAAI,aAAa,EAAE;;;;;;0DAAS,6LAAC;gDAAG,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAM5L,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAM,WAAU;;sCACf,6LAAC;4BAAM,WAAU;sCAAa,cAAA,6LAAC;;kDAAG,6LAAC;kDAAG;;;;;;kDAAc,6LAAC;wCAAG,WAAU;kDAAW;;;;;;kDAAkB,6LAAC;wCAAG,WAAU;kDAAW;;;;;;;;;;;;;;;;;sCACxH,6LAAC;sCACE,WAAW,GAAG,CAAC,CAAA,yBACd,6LAAC;;sDACC,6LAAC;;8DAAG,6LAAC;8DAAQ,SAAS,GAAG;;;;;;gDAAW,SAAS,WAAW,kBAAK;;sEAAE,6LAAC;;;;;sEAAK,6LAAC;4DAAM,WAAU;sEAAc,SAAS,WAAW;;;;;;;;;;;;;;sDACxH,6LAAC;4CAAG,WAAU;sDAAY,eAAe,SAAS,aAAa;;;;;;sDAC/D,6LAAC;4CAAG,WAAU;sDAAY,eAAe,SAAS,aAAa,GAAG;;;;;;;mCAH3D,SAAS,EAAE;;;;;;;;;;sCAOxB,6LAAC;sCAAM,cAAA,6LAAC;gCAAG,WAAU;;kDAAa,6LAAC;kDAAG;;;;;;kDAAU,6LAAC;wCAAG,WAAU;kDAAY,eAAe,WAAW,MAAM,CAAC,CAAC,KAAK,MAAQ,MAAM,IAAI,aAAa,EAAE;;;;;;kDAAS,6LAAC;wCAAG,WAAU;kDAAY,eAAe,WAAW,MAAM,CAAC,CAAC,KAAK,MAAQ,MAAM,IAAI,aAAa,EAAE,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAItQ,6LAAC,yLAAA,CAAA,QAAK;gBAAC,MAAM;gBAAmB,QAAQ,IAAM,qBAAqB;gBAAQ,MAAK;;kCAAK,6LAAC,yLAAA,CAAA,QAAK,CAAC,MAAM;wBAAC,WAAW;kCAAC,cAAA,6LAAC,yLAAA,CAAA,QAAK,CAAC,KAAK;sCAAE,kBAAkB,KAAK,CAAC,UAAU,EAAE,iBAAiB,GAAG,CAAC,CAAC,CAAC,GAAG;;;;;;;;;;;kCAA6D,6LAAC,yLAAA,CAAA,QAAK,CAAC,IAAI;kCAAC,cAAA,6LAAC;4BAAmB,UAAU;4BAAkB,QAAQ;4BAAoB,UAAU,IAAM,qBAAqB;;;;;;;;;;;;;;;;;;;;;;;AAG3X;IAtIM;MAAA;uCAwIS", "debugId": null}}]}