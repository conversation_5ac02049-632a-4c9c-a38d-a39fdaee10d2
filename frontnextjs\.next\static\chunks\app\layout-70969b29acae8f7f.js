(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{347:()=>{},749:(e,t,n)=>{Promise.resolve().then(n.t.bind(n,347,23)),Promise.resolve().then(n.t.bind(n,5685,23)),Promise.resolve().then(n.bind(n,5494))},2664:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isLocalURL",{enumerable:!0,get:function(){return a}});let r=n(9991),o=n(7102);function a(e){if(!(0,r.isAbsoluteUrl)(e))return!0;try{let t=(0,r.getLocationOrigin)(),n=new URL(e,t);return n.origin===t&&(0,o.hasBasePath)(n.pathname)}catch(e){return!1}}},2757:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{formatUrl:function(){return a},formatWithValidation:function(){return l},urlObjectKeys:function(){return i}});let r=n(6966)._(n(8859)),o=/https?|ftp|gopher|file/;function a(e){let{auth:t,hostname:n}=e,a=e.protocol||"",i=e.pathname||"",l=e.hash||"",s=e.query||"",u=!1;t=t?encodeURIComponent(t).replace(/%3A/i,":")+"@":"",e.host?u=t+e.host:n&&(u=t+(~n.indexOf(":")?"["+n+"]":n),e.port&&(u+=":"+e.port)),s&&"object"==typeof s&&(s=String(r.urlQueryToSearchParams(s)));let c=e.search||s&&"?"+s||"";return a&&!a.endsWith(":")&&(a+=":"),e.slashes||(!a||o.test(a))&&!1!==u?(u="//"+(u||""),i&&"/"!==i[0]&&(i="/"+i)):u||(u=""),l&&"#"!==l[0]&&(l="#"+l),c&&"?"!==c[0]&&(c="?"+c),""+a+u+(i=i.replace(/[?#]/g,encodeURIComponent))+(c=c.replace("#","%23"))+l}let i=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function l(e){return a(e)}},3180:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"errorOnce",{enumerable:!0,get:function(){return n}});let n=e=>{}},5494:(e,t,n)=>{"use strict";n.d(t,{default:()=>l});var r=n(5155),o=n(2115),a=n(6874),i=n.n(a);let l=()=>{let[e,t]=(0,o.useState)(!1),n=()=>{t(!1)};return(0,r.jsx)("nav",{className:"navbar navbar-expand-lg navbar-dark bg-dark",children:(0,r.jsxs)("div",{className:"container-fluid",children:[(0,r.jsx)(i(),{className:"navbar-brand",href:"/",onClick:n,children:"FIRE Dashboard"}),(0,r.jsx)("button",{className:"navbar-toggler",type:"button",onClick:()=>{t(!e)},"aria-controls":"navbarNavContent","aria-expanded":e,"aria-label":"Toggle navigation",children:(0,r.jsx)("span",{className:"navbar-toggler-icon"})}),(0,r.jsx)("div",{className:"collapse navbar-collapse ".concat(e?"show":""),id:"navbarNavContent",children:(0,r.jsxs)("ul",{className:"navbar-nav me-auto mb-2 mb-lg-0",children:[(0,r.jsx)("li",{className:"nav-item",children:(0,r.jsx)(i(),{className:"nav-link",href:"/",onClick:n,children:"Dashboard"})}),(0,r.jsx)("li",{className:"nav-item",children:(0,r.jsx)(i(),{className:"nav-link",href:"/assets",onClick:n,children:"Patrimoine"})}),(0,r.jsx)("li",{className:"nav-item",children:(0,r.jsx)(i(),{className:"nav-link",href:"/liabilities",onClick:n,children:"Emprunts"})}),(0,r.jsx)("li",{className:"nav-item",children:(0,r.jsx)(i(),{className:"nav-link",href:"/scpi",onClick:n,children:"SCPI"})}),(0,r.jsx)("li",{className:"nav-item",children:(0,r.jsx)(i(),{className:"nav-link",href:"/evolution",onClick:n,children:"\xc9volution"})}),(0,r.jsx)("li",{className:"nav-item",children:(0,r.jsx)(i(),{className:"nav-link",href:"/budget",onClick:n,children:"Budget FIRE"})}),(0,r.jsx)("li",{className:"nav-item",children:(0,r.jsx)(i(),{className:"nav-link",href:"/scenarios",onClick:n,children:"Sc\xe9narios"})}),(0,r.jsx)("li",{className:"nav-item",children:(0,r.jsx)(i(),{className:"nav-link",href:"/fire",onClick:n,children:"Objectif FIRE"})}),(0,r.jsx)("li",{className:"nav-item",children:(0,r.jsx)(i(),{className:"nav-link",href:"/test",onClick:n,children:"Test API"})})]})})]})})}},5685:()=>{},6654:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useMergedRef",{enumerable:!0,get:function(){return o}});let r=n(2115);function o(e,t){let n=(0,r.useRef)(null),o=(0,r.useRef)(null);return(0,r.useCallback)(r=>{if(null===r){let e=n.current;e&&(n.current=null,e());let t=o.current;t&&(o.current=null,t())}else e&&(n.current=a(e,r)),t&&(o.current=a(t,r))},[e,t])}function a(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let n=e(t);return"function"==typeof n?n:()=>e(null)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6874:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{default:function(){return v},useLinkStatus:function(){return b}});let r=n(6966),o=n(5155),a=r._(n(2115)),i=n(2757),l=n(5227),s=n(9818),u=n(6654),c=n(9991),f=n(5929);n(3230);let d=n(4930),p=n(2664),h=n(6634);function m(e){return"string"==typeof e?e:(0,i.formatUrl)(e)}function v(e){let t,n,r,[i,v]=(0,a.useOptimistic)(d.IDLE_LINK_STATUS),b=(0,a.useRef)(null),{href:y,as:j,children:N,prefetch:x=null,passHref:P,replace:_,shallow:k,scroll:C,onClick:E,onMouseEnter:O,onTouchStart:T,legacyBehavior:S=!1,onNavigate:A,ref:I,unstable_dynamicOnHover:L,...R}=e;t=N,S&&("string"==typeof t||"number"==typeof t)&&(t=(0,o.jsx)("a",{children:t}));let M=a.default.useContext(l.AppRouterContext),U=!1!==x,w=null===x?s.PrefetchKind.AUTO:s.PrefetchKind.FULL,{href:F,as:D}=a.default.useMemo(()=>{let e=m(y);return{href:e,as:j?m(j):e}},[y,j]);S&&(n=a.default.Children.only(t));let K=S?n&&"object"==typeof n&&n.ref:I,B=a.default.useCallback(e=>(null!==M&&(b.current=(0,d.mountLinkInstance)(e,F,M,w,U,v)),()=>{b.current&&((0,d.unmountLinkForCurrentNavigation)(b.current),b.current=null),(0,d.unmountPrefetchableInstance)(e)}),[U,F,M,w,v]),z={ref:(0,u.useMergedRef)(B,K),onClick(e){S||"function"!=typeof E||E(e),S&&n.props&&"function"==typeof n.props.onClick&&n.props.onClick(e),M&&(e.defaultPrevented||function(e,t,n,r,o,i,l){let{nodeName:s}=e.currentTarget;if(!("A"===s.toUpperCase()&&function(e){let t=e.currentTarget.getAttribute("target");return t&&"_self"!==t||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e)||e.currentTarget.hasAttribute("download"))){if(!(0,p.isLocalURL)(t)){o&&(e.preventDefault(),location.replace(t));return}e.preventDefault(),a.default.startTransition(()=>{if(l){let e=!1;if(l({preventDefault:()=>{e=!0}}),e)return}(0,h.dispatchNavigateAction)(n||t,o?"replace":"push",null==i||i,r.current)})}}(e,F,D,b,_,C,A))},onMouseEnter(e){S||"function"!=typeof O||O(e),S&&n.props&&"function"==typeof n.props.onMouseEnter&&n.props.onMouseEnter(e),M&&U&&(0,d.onNavigationIntent)(e.currentTarget,!0===L)},onTouchStart:function(e){S||"function"!=typeof T||T(e),S&&n.props&&"function"==typeof n.props.onTouchStart&&n.props.onTouchStart(e),M&&U&&(0,d.onNavigationIntent)(e.currentTarget,!0===L)}};return(0,c.isAbsoluteUrl)(D)?z.href=D:S&&!P&&("a"!==n.type||"href"in n.props)||(z.href=(0,f.addBasePath)(D)),r=S?a.default.cloneElement(n,z):(0,o.jsx)("a",{...R,...z,children:t}),(0,o.jsx)(g.Provider,{value:i,children:r})}n(3180);let g=(0,a.createContext)(d.IDLE_LINK_STATUS),b=()=>(0,a.useContext)(g);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8859:(e,t)=>{"use strict";function n(e){let t={};for(let[n,r]of e.entries()){let e=t[n];void 0===e?t[n]=r:Array.isArray(e)?e.push(r):t[n]=[e,r]}return t}function r(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function o(e){let t=new URLSearchParams;for(let[n,o]of Object.entries(e))if(Array.isArray(o))for(let e of o)t.append(n,r(e));else t.set(n,r(o));return t}function a(e){for(var t=arguments.length,n=Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];for(let t of n){for(let n of t.keys())e.delete(n);for(let[n,r]of t.entries())e.append(n,r)}return e}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{assign:function(){return a},searchParamsToUrlQuery:function(){return n},urlQueryToSearchParams:function(){return o}})},9991:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{DecodeError:function(){return h},MiddlewareNotFoundError:function(){return b},MissingStaticPage:function(){return g},NormalizeError:function(){return m},PageNotFoundError:function(){return v},SP:function(){return d},ST:function(){return p},WEB_VITALS:function(){return n},execOnce:function(){return r},getDisplayName:function(){return s},getLocationOrigin:function(){return i},getURL:function(){return l},isAbsoluteUrl:function(){return a},isResSent:function(){return u},loadGetInitialProps:function(){return f},normalizeRepeatedSlashes:function(){return c},stringifyError:function(){return y}});let n=["CLS","FCP","FID","INP","LCP","TTFB"];function r(e){let t,n=!1;return function(){for(var r=arguments.length,o=Array(r),a=0;a<r;a++)o[a]=arguments[a];return n||(n=!0,t=e(...o)),t}}let o=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,a=e=>o.test(e);function i(){let{protocol:e,hostname:t,port:n}=window.location;return e+"//"+t+(n?":"+n:"")}function l(){let{href:e}=window.location,t=i();return e.substring(t.length)}function s(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function u(e){return e.finished||e.headersSent}function c(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function f(e,t){let n=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await f(t.Component,t.ctx)}:{};let r=await e.getInitialProps(t);if(n&&u(n))return r;if(!r)throw Object.defineProperty(Error('"'+s(e)+'.getInitialProps()" should resolve to an object. But found "'+r+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return r}let d="undefined"!=typeof performance,p=d&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class h extends Error{}class m extends Error{}class v extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class g extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class b extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function y(e){return JSON.stringify({message:e.message,stack:e.stack})}}},e=>{var t=t=>e(e.s=t);e.O(0,[690,740,441,684,358],()=>t(749)),_N_E=e.O()}]);