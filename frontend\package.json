{"name": "frontend", "version": "0.1.0", "private": true, "dependencies": {"@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^30.0.0", "@types/node": "^24.0.10", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "axios": "^1.10.0", "bootstrap": "^5.3.7", "chart.js": "^4.5.0", "chartjs-plugin-annotation": "^3.1.0", "react": "^19.1.0", "react-bootstrap": "^2.10.10", "react-chartjs-2": "^5.3.0", "react-dom": "^19.1.0", "react-scripts": "5.0.1", "typescript": "^4.9.5", "web-vitals": "^5.0.3"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}