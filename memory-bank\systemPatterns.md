# Architecture Système - Fire UI

## Architecture du projet
Le projet Fire UI est structuré en deux parties principales :
- **Frontend** : Développé avec **React.js**, situé dans le dossier `frontend`.
- **Backend** : Développé avec **Python** et **FastAPI**, situé dans le dossier `backend`.

## Relations entre composants
- Le frontend communique avec le backend via des API RESTful.
- Les données financières saisies par l'utilisateur sont envoyées au backend pour traitement et stockage.
- Les résultats des simulations sont renvoyés au frontend pour affichage.

## Choix technologiques
- **Frontend** : React.js pour sa flexibilité et sa popularité dans le développement d'applications web modernes.
- **Backend** : **FastAPI** pour sa simplicité, ses performances élevées et son intégration native avec Pydantic pour la validation des données.

## Organisation des fichiers
- `frontend/src/components/` : Contient tous les composants React de l'application.
- `backend/`: Contient les modules Python pour le backend, y compris les routes API et le modèle de données.

## Points critiques d'implémentation
- Connexion entre frontend et backend via des appels API sécurisés.
- Stockage des données financières dans une base de données relationnelle ou NoSQL selon les besoins.
