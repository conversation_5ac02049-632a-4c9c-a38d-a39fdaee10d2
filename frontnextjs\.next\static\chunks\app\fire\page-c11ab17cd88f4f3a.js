(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[919],{1973:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>x});var r=t(5155),a=t(2115),n=t(2365),i=t(8827),c=t(902),l=t(8700),d=t(5717),o=t(3622),m=t(9986);let u=e=>{let{settings:s,onSave:t,onCancel:l}=e,[d,o]=(0,a.useState)({fire_target_amount:(null==s?void 0:s.fire_target_amount)||910150,secure_withdrawal_rate:100*((null==s?void 0:s.secure_withdrawal_rate)||.04)});(0,a.useEffect)(()=>{o({fire_target_amount:(null==s?void 0:s.fire_target_amount)||910150,secure_withdrawal_rate:100*((null==s?void 0:s.secure_withdrawal_rate)||.04)})},[s]);let m=async e=>{e.preventDefault();let s={fire_target_amount:d.fire_target_amount,secure_withdrawal_rate:d.secure_withdrawal_rate/100};try{await n.A.put("/fire-settings",s),t()}catch(e){console.error("Erreur lors de la sauvegarde des param\xe8tres FIRE",e instanceof Error?e.message:"Unknown error")}};return(0,r.jsxs)(i.A,{onSubmit:m,children:[(0,r.jsxs)(i.A.Group,{className:"mb-3",children:[(0,r.jsx)(i.A.Label,{children:"Objectif FIRE (€)"}),(0,r.jsx)(i.A.Control,{type:"number",step:"1000",value:d.fire_target_amount,onChange:e=>o({...d,fire_target_amount:parseFloat(e.target.value)||0}),required:!0}),(0,r.jsx)(i.A.Text,{className:"text-muted",children:"Montant total que vous souhaitez atteindre pour votre ind\xe9pendance financi\xe8re"})]}),(0,r.jsxs)(i.A.Group,{className:"mb-3",children:[(0,r.jsx)(i.A.Label,{children:"Taux de retrait s\xe9curis\xe9 (%)"}),(0,r.jsx)(i.A.Control,{type:"number",step:"0.1",min:"1",max:"10",value:d.secure_withdrawal_rate,onChange:e=>o({...d,secure_withdrawal_rate:parseFloat(e.target.value)||4}),required:!0}),(0,r.jsx)(i.A.Text,{className:"text-muted",children:"Pourcentage de votre patrimoine que vous pouvez retirer annuellement (r\xe8gle des 4% = 4.0)"})]}),(0,r.jsxs)("div",{className:"d-flex justify-content-end gap-2",children:[(0,r.jsx)(c.A,{variant:"secondary",onClick:l,children:"Annuler"}),(0,r.jsx)(c.A,{variant:"primary",type:"submit",children:"Sauvegarder"})]})]})},h=e=>{var s;let{currentNetPatrimoine:t}=e,a=[{id:0,name:"Phase 0: Bilan Patrimonial & Actions Imm\xe9diates",description:"R\xe9organisation et pr\xe9paration",startDate:"Juin 2025",endDate:"Juillet 2025",startAge:47,endAge:47,keyMilestones:["Apport SCPI Com\xe8te (27 000€)","Optimisation PEA (72 750€ DCA Nasdaq-100)","Consolidation crypto (75% BTC / 25% ETH)","Sortie progressive crowdlending"],recommendedActions:["N\xe9gocier taux pr\xeat SCPI Com\xe8te (<3,95%)","D\xe9ployer DCA adaptatif sur 6 mois","Simplifier portefeuille crypto","Liquider positions Trading 212"],status:"completed"},{id:1,name:"Phase 1: Accumulation Intensive",description:"Investissement mensuel de 3 415€",startDate:"Ao\xfbt 2025",endDate:"Fin 2037",startAge:47,endAge:59,keyMilestones:["Saturation PEA (mi-2033, \xe2ge 55 ans)","Fin cr\xe9dit SCPI existant (Nov 2035)","Atteinte objectif 910k€ (courant 2035, \xe2ge 57 ans)"],recommendedActions:["SCPI: 812€/mois + remboursements 1 303€/mois","ETF Actions: 800€/mois (PUST puis SXR8)","Fonds Euros: 200€/mois","Crypto: 200€/mois (BTC/ETH)"],status:"current"},{id:2,name:"Phase 2: Transition Pr\xe9-FIRE",description:"R\xe9duction des risques et pr\xe9paration",startDate:"Fin 2037",endDate:"D\xe9but 2038",startAge:59,endAge:60,targetAmount:910150,keyMilestones:["Capital FIRE atteint (910 150€)","R\xe9duction risque crypto (<7% patrimoine)","Constitution poche ETF dividendes (30-40%)"],recommendedActions:["Arbitrer PUST/SXR8 vers ETF dividendes","S\xe9curiser gains crypto si >7-10%","V\xe9rifier fonds d'urgence (50 000€)","Optimiser fiscalit\xe9 retraits"],status:"upcoming"},{id:3,name:"Phase 3: Vie en Mode FIRE",description:"Ind\xe9pendance financi\xe8re confortable",startDate:"2038",endDate:"2040",startAge:60,endAge:62,targetAmount:12e5,keyMilestones:["FIRE Confortable (revenus > d\xe9penses + dette)","G\xe9n\xe9ration 37 400€/an revenus passifs","Allocation: 40% SCPI, 40% ETF, 15% Fonds Euros, 5% Crypto"],recommendedActions:["Optimiser retraits fiscaux (PFU/bar\xe8me)","G\xe9rer allocation mix croissance/dividendes","Suivi d\xe9penses cibles (25 484€/an)","Gestion budg\xe9taire et CSM"],status:"upcoming"},{id:4,name:"Phase 4: \xc9volution Post-FIRE & Retraite L\xe9gale",description:"FIRE pleine puissance et retraite",startDate:"2040",endDate:"2042+",startAge:62,endAge:64,keyMilestones:["Fin pr\xeat SCPI Com\xe8te (fin 2040, +10k€/an)","Retraite l\xe9gale (2042, \xe2ge 64 ans)","Pension \xc9tat (~1 800€/mois) + PER (45-55k€)"],recommendedActions:["Augmentation revenu net (+10k€/an)","Int\xe9gration pension \xc9tat","D\xe9blocage PER Linxea Spirit","R\xe9duction besoin puisage capital FIRE"],status:"upcoming"}],n=(()=>{let e=new Date().getFullYear()-1978,s=1;t>=910150?s=e>=62?4:e>=60?3:2:e>=59&&t>=8e5&&(s=2);let r=0,n=0,i=a[s];if(1===s){r=i.targetAmount?t/i.targetAmount*100:t/910150*100;let e=(i.targetAmount||910150)-t;n=Math.max(0,e>0?e/40980:0)}else if(i){let s=i.endAge-i.startAge,t=Math.max(0,e-i.startAge);r=s>0?t/s*100:100*(e>=i.startAge),n=Math.max(0,i.endAge-e),i.targetAmount&&i.targetAmount}return{currentPhase:s,progressInCurrentPhase:Math.min(100,Math.max(0,r)),yearsToNextPhase:n,currentAge:e}})(),i=a.map((e,s)=>({...e,status:s<n.currentPhase?"completed":s===n.currentPhase?"current":"upcoming"})),c=e=>({completed:"success",current:"primary",upcoming:"secondary"})[e]||"secondary",m=e=>({completed:"✅",current:"\uD83D\uDD25",upcoming:"⏳"})[e]||"⏳";return(0,r.jsxs)("div",{className:"mt-4",children:[(0,r.jsx)("h3",{className:"mb-4",children:"\uD83C\uDFAF Tracker des Phases FIRE"}),(0,r.jsxs)("div",{className:"row mb-4",children:[(0,r.jsx)("div",{className:"col-md-3",children:(0,r.jsx)("div",{className:"card text-white bg-info",children:(0,r.jsxs)("div",{className:"card-body text-center",children:[(0,r.jsx)("h5",{children:"Phase Actuelle"}),(0,r.jsxs)("h4",{children:["Phase ",n.currentPhase]}),(0,r.jsx)("small",{children:(null==(s=i[n.currentPhase])?void 0:s.name.split(":")[1])||"N/A"})]})})}),(0,r.jsx)("div",{className:"col-md-3",children:(0,r.jsx)("div",{className:"card text-white bg-warning",children:(0,r.jsxs)("div",{className:"card-body text-center",children:[(0,r.jsx)("h5",{children:"\xc2ge Actuel"}),(0,r.jsxs)("h4",{children:[n.currentAge," ans"]}),(0,r.jsx)("small",{children:"N\xe9 en septembre 1978"})]})})}),(0,r.jsx)("div",{className:"col-md-3",children:(0,r.jsx)("div",{className:"card text-white bg-success",children:(0,r.jsxs)("div",{className:"card-body text-center",children:[(0,r.jsx)("h5",{children:"Progression Phase"}),(0,r.jsxs)("h4",{children:[n.progressInCurrentPhase.toFixed(1),"%"]}),(0,r.jsx)("small",{children:"Dans la phase actuelle"})]})})}),(0,r.jsx)("div",{className:"col-md-3",children:(0,r.jsx)("div",{className:"card text-white bg-danger",children:(0,r.jsxs)("div",{className:"card-body text-center",children:[(0,r.jsx)("h5",{children:"Prochaine Phase"}),(0,r.jsxs)("h4",{children:[n.yearsToNextPhase.toFixed(1)," ans"]}),(0,r.jsx)("small",{children:"Estimation restante"})]})})})]}),(0,r.jsxs)("div",{className:"card",children:[(0,r.jsx)("div",{className:"card-header",children:(0,r.jsx)("h5",{className:"mb-0",children:"Timeline des Phases FIRE (2025-2042+)"})}),(0,r.jsx)("div",{className:"card-body",children:(0,r.jsx)(l.A,{defaultActiveKey:n.currentPhase.toString(),children:i.map((e,s)=>(0,r.jsxs)(l.A.Item,{eventKey:s.toString(),children:[(0,r.jsx)(l.A.Header,{children:(0,r.jsxs)("div",{className:"d-flex align-items-center w-100",children:[(0,r.jsx)("span",{className:"me-2",children:m(e.status)}),(0,r.jsxs)("div",{className:"flex-grow-1",children:[(0,r.jsx)("strong",{children:e.name}),(0,r.jsx)(d.A,{bg:c(e.status),className:"ms-2",children:"completed"===e.status?"Termin\xe9e":"current"===e.status?"En cours":"\xc0 venir"})]}),(0,r.jsxs)("small",{className:"text-muted",children:[e.startDate," - ",e.endDate," (\xe2ge ",e.startAge,"-",e.endAge,")"]})]})}),(0,r.jsxs)(l.A.Body,{children:[(0,r.jsxs)("div",{className:"row",children:[(0,r.jsxs)("div",{className:"col-md-6",children:[(0,r.jsx)("h6",{children:"\uD83C\uDFAF Jalons Cl\xe9s :"}),(0,r.jsx)("ul",{children:e.keyMilestones.map((e,s)=>(0,r.jsx)("li",{children:e},s))}),e.targetAmount&&(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"Objectif Patrimoine :"})," ",new Intl.NumberFormat("fr-FR",{style:"currency",currency:"EUR"}).format(e.targetAmount)]})]}),(0,r.jsxs)("div",{className:"col-md-6",children:[(0,r.jsx)("h6",{children:"\uD83D\uDCCB Actions Recommand\xe9es :"}),(0,r.jsx)("ul",{children:e.recommendedActions.map((e,s)=>(0,r.jsx)("li",{children:e},s))})]})]}),"current"===e.status&&(0,r.jsxs)("div",{className:"mt-3",children:[(0,r.jsx)("h6",{children:"Progression dans cette phase :"}),(0,r.jsx)(o.A,{now:n.progressInCurrentPhase,label:"".concat(n.progressInCurrentPhase.toFixed(1),"%"),variant:c(e.status),style:{height:"25px"}}),(0,r.jsxs)("small",{className:"text-muted",children:["Estimation : ",n.yearsToNextPhase.toFixed(1)," ann\xe9es restantes"]})]})]})]},e.id))})})]})]})},x=()=>{let[e,s]=(0,a.useState)(null),[t,i]=(0,a.useState)(!0),[l,d]=(0,a.useState)(null),[x,j]=(0,a.useState)(0),[p,g]=(0,a.useState)(!1),v=()=>{i(!0),d(null),n.A.get("/fire-target").then(e=>{s(e.data),i(!1),j(0)}).catch(e=>{console.error("FireTarget API error:",e),x<2?(j(e=>e+1),setTimeout(()=>v(),1e3)):(d("Erreur lors de la r\xe9cup\xe9ration des donn\xe9es FIRE."),i(!1))})};if((0,a.useEffect)(()=>{v()},[]),t&&0===x)return(0,r.jsx)("p",{children:"Chargement de l'objectif FIRE..."});if(l&&x>=2)return(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-danger",children:l}),(0,r.jsx)("button",{className:"btn btn-primary",onClick:()=>{j(0),v()},children:"R\xe9essayer"})]});if(!e&&!t)return(0,r.jsx)("p",{children:"Aucune donn\xe9e d'objectif FIRE disponible."});if(t)return(0,r.jsxs)("p",{children:["Chargement de l'objectif FIRE, tentative ",x+1,"..."]});if(!e)return(0,r.jsx)("p",{children:"Donn\xe9es FIRE non disponibles."});let N=e=>new Intl.NumberFormat("fr-FR",{style:"currency",currency:"EUR"}).format(e),A=e=>new Intl.NumberFormat("fr-FR",{style:"percent",minimumFractionDigits:1}).format(e/100);return(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"d-flex justify-content-between align-items-center mb-4",children:[(0,r.jsx)("h1",{children:"Objectif FIRE"}),(0,r.jsx)(c.A,{variant:"primary",onClick:()=>g(!0),children:"Modifier l'objectif"})]}),(0,r.jsx)("div",{className:"row mb-4",children:(0,r.jsx)("div",{className:"col-12",children:(0,r.jsx)("div",{className:"card",children:(0,r.jsxs)("div",{className:"card-body",children:[(0,r.jsx)("h5",{className:"card-title",children:"Progression vers votre Objectif FIRE"}),(0,r.jsxs)("div",{className:"row mb-3",children:[(0,r.jsx)("div",{className:"col-md-4",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("h3",{className:"text-primary",children:N(e.current_net_patrimoine)}),(0,r.jsx)("small",{className:"text-muted",children:"Patrimoine Net Actuel"})]})}),(0,r.jsx)("div",{className:"col-md-4",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("h3",{className:"text-success",children:N(e.fire_target_amount)}),(0,r.jsx)("small",{className:"text-muted",children:"Objectif FIRE"})]})}),(0,r.jsx)("div",{className:"col-md-4",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("h3",{className:"text-info",children:A(e.progress_percentage)}),(0,r.jsx)("small",{className:"text-muted",children:"Progression"})]})})]}),(0,r.jsx)(o.A,{now:e.progress_percentage,label:"".concat(e.progress_percentage.toFixed(1),"%"),style:{height:"30px"},className:"mb-3"}),(0,r.jsxs)("div",{className:"row",children:[(0,r.jsxs)("div",{className:"col-md-6",children:[(0,r.jsx)("p",{className:"mb-1",children:(0,r.jsx)("strong",{children:"Montant restant \xe0 investir :"})}),(0,r.jsx)("p",{className:"text-danger fs-5",children:N(Math.max(0,e.remaining_to_invest))})]}),(0,r.jsxs)("div",{className:"col-md-6",children:[(0,r.jsx)("p",{className:"mb-1",children:(0,r.jsx)("strong",{children:"Revenu passif annuel potentiel :"})}),(0,r.jsx)("p",{className:"text-success fs-5",children:N(e.potential_passive_income)}),(0,r.jsxs)("small",{className:"text-muted",children:["Montant que vous pourriez retirer annuellement de votre patrimoine actuel selon la r\xe8gle des ",A(100*e.secure_withdrawal_rate)]})]})]})]})})})}),(0,r.jsxs)("div",{className:"row",children:[(0,r.jsx)("div",{className:"col-md-4",children:(0,r.jsxs)("div",{className:"card text-white bg-success mb-3",children:[(0,r.jsx)("div",{className:"card-header",children:"Total Actifs"}),(0,r.jsxs)("div",{className:"card-body",children:[(0,r.jsx)("h5",{className:"card-title",children:N(e.total_assets)}),(0,r.jsx)("p",{className:"card-text",children:"Incluant les SCPI et tous vos investissements"})]})]})}),(0,r.jsx)("div",{className:"col-md-4",children:(0,r.jsxs)("div",{className:"card text-white bg-danger mb-3",children:[(0,r.jsx)("div",{className:"card-header",children:"Total Passifs"}),(0,r.jsxs)("div",{className:"card-body",children:[(0,r.jsx)("h5",{className:"card-title",children:N(e.total_liabilities)}),(0,r.jsx)("p",{className:"card-text",children:"Emprunts et dettes en cours"})]})]})}),(0,r.jsx)("div",{className:"col-md-4",children:(0,r.jsxs)("div",{className:"card text-white bg-info mb-3",children:[(0,r.jsx)("div",{className:"card-header",children:"Taux de Retrait S\xe9curis\xe9"}),(0,r.jsxs)("div",{className:"card-body",children:[(0,r.jsx)("h5",{className:"card-title",children:A(100*e.secure_withdrawal_rate)}),(0,r.jsx)("p",{className:"card-text",children:"Pourcentage de retrait annuel s\xe9curis\xe9"})]})]})})]}),(0,r.jsx)(h,{currentNetPatrimoine:e.current_net_patrimoine}),(0,r.jsxs)(m.A,{show:p,onHide:()=>g(!1),size:"lg",children:[(0,r.jsx)(m.A.Header,{closeButton:!0,children:(0,r.jsx)(m.A.Title,{children:"Modifier les Param\xe8tres FIRE"})}),(0,r.jsx)(m.A.Body,{children:(0,r.jsx)(u,{settings:e?{fire_target_amount:e.fire_target_amount,secure_withdrawal_rate:e.secure_withdrawal_rate}:null,onSave:()=>{g(!1),v()},onCancel:()=>g(!1)})})]})]})}},2365:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let r=t(3464).A.create({baseURL:"http://localhost:8000/api",headers:{"Content-Type":"application/json"},timeout:1e4});r.interceptors.response.use(e=>e,e=>{var s,t;return console.error("API Error:",null==(s=e.response)?void 0:s.status,null==(t=e.config)?void 0:t.url,e.message),Promise.reject(e)});let a=r},2548:(e,s,t)=>{Promise.resolve().then(t.bind(t,1973))}},e=>{var s=s=>e(e.s=s);e.O(0,[464,160,405,423,441,684,358],()=>s(2548)),_N_E=e.O()}]);