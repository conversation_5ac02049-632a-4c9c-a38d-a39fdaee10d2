import React, { useState } from 'react';
import './App.css';
import Dashboard from './components/Dashboard';
import Assets from './components/Assets';
import Liabilities from './components/Liabilities';
import FireTarget from './components/FireTarget';
import SCPI from './components/SCPI';
import EvolutionAnnuelle from './components/EvolutionAnnuelle';
import BudgetFire from './components/BudgetFire';
import ScenariosFire from './components/ScenariosFire';
import ApiTest from './components/ApiTest';

type View = 'dashboard' | 'assets' | 'liabilities' | 'scpi' | 'evolution' | 'budget' | 'scenarios' | 'fire' | 'test';

function App() {
  const [view, setView] = useState<View>('dashboard');

  const renderView = () => {
    switch (view) {
      case 'assets':
        return <Assets />;
      case 'liabilities':
        return <Liabilities />;
      case 'scpi':
        return <SCPI />;
      case 'evolution':
        return <EvolutionAnnuelle />;
      case 'budget':
        return <BudgetFire />;
      case 'scenarios':
        return <ScenariosFire />;
      case 'fire':
        return <FireTarget />;
      case 'test':
        return <ApiTest />;
      default:
        return <Dashboard />;
    }
  };

  return (
    <div className="App">
      <nav className="navbar navbar-expand-lg navbar-dark bg-dark">
        <div className="container-fluid">
          <a className="navbar-brand" href="#" onClick={() => setView('dashboard')}>FIRE Dashboard</a>
          <div className="collapse navbar-collapse">
            <ul className="navbar-nav me-auto mb-2 mb-lg-0">
              <li className="nav-item">
                <a className="nav-link" href="#" onClick={() => setView('dashboard')}>Dashboard</a>
              </li>
              <li className="nav-item">
                <a className="nav-link" href="#" onClick={() => setView('assets')}>Patrimoine</a>
              </li>
              <li className="nav-item">
                <a className="nav-link" href="#" onClick={() => setView('liabilities')}>Emprunts</a>
              </li>
              <li className="nav-item">
                <a className="nav-link" href="#" onClick={() => setView('scpi')}>SCPI</a>
              </li>
              <li className="nav-item">
                <a className="nav-link" href="#" onClick={() => setView('evolution')}>Évolution</a>
              </li>
              <li className="nav-item">
                <a className="nav-link" href="#" onClick={() => setView('budget')}>Budget FIRE</a>
              </li>
              <li className="nav-item">
                <a className="nav-link" href="#" onClick={() => setView('scenarios')}>Scénarios</a>
              </li>
              <li className="nav-item">
                <a className="nav-link" href="#" onClick={() => setView('fire')}>Objectif FIRE</a>
              </li>
              <li className="nav-item">
                <a className="nav-link" href="#" onClick={() => setView('test')}>Test API</a>
              </li>
            </ul>
          </div>
        </div>
      </nav>
      <main className="container mt-4">
        {renderView()}
      </main>
    </div>
  );
}

export default App;