# Progrès du projet - Fire UI

## État actuel
- **Migration du Frontend vers Next.js en cours.**
  - Le projet frontend original basé sur Create React App (`frontend/`) est en cours de migration vers Next.js (`frontnextjs/`).
  - **Phase 1 de la migration terminée** : L'application React originale fonctionne maintenant en mode Single Page Application (SPA) à l'intérieur de l'environnement Next.js.
- Le backend FastAPI (`backend/`) est fonctionnel et sert de base pour l'application.
- Les scripts d'installation (`install.bat`) et de lancement (`run.bat`) ainsi que le `README.md` principal ont été mis à jour pour refléter la nouvelle structure du frontend.

## Fonctionnalités Réalisées (depuis la dernière mise à jour majeure)
- Initialisation d'un nouveau projet Next.js (`frontnextjs/`) avec TypeScript, Tailwind CSS, ESLint et App Router.
- Copie et adaptation des dépendances du projet React original vers le projet Next.js.
- Création d'un layout racine (`app/layout.tsx`) dans Next.js, incluant la gestion des métadonnées et des styles globaux (Bootstrap).
- Mise en place d'une route catch-all (`app/[[...slug]]/page.tsx`) et d'un point d'entrée client (`client.tsx`) pour charger l'application React existante (`App.tsx` et ses composants) en mode SPA.
- Gestion des assets statiques (favicon, manifest, images de base) selon les conventions Next.js.
- Le serveur de développement Next.js démarre et sert l'application React en mode SPA.
- Mise à jour de `install.bat`, `run.bat`, et `README.md` pour prendre en compte `frontnextjs`.

## Prochaines étapes (Phase 2 de la migration Next.js)
1.  **Suppression du mode SPA** : Configurer Next.js pour utiliser pleinement ses fonctionnalités (SSR/SSG).
2.  **Migration du Routage** :
    *   Remplacer la navigation basée sur l'état de `App.tsx` par le système de routage basé sur les fichiers de Next.js (App Router).
    *   Créer des pages Next.js distinctes pour chaque vue de l'application (Dashboard, Patrimoine, SCPI, etc.).
3.  **Refactorisation des Composants** :
    *   Adapter les composants existants pour tirer parti des Server Components et Client Components. (Exemple fait pour Dashboard).
    *   Utiliser `next/image` pour l'optimisation des images et `next/font` pour les polices (Reporté).
4.  **Intégration API** : Optimiser les appels API en utilisant les capacités de Next.js (Route Handlers, Server Actions, ou fetching dans les Server Components). (Exemple fait pour Dashboard).
5.  **Tests** :
    *   Configuration de Jest avec Next.js : **Terminée**.
    *   Premiers tests unitaires pour `DashboardClientPart` : **Passent**.
    *   Migrer/écrire plus de tests pour les autres pages/composants.
6.  **Optimisations** : Améliorer les performances, le SEO, et l'expérience utilisateur grâce aux fonctionnalités de Next.js (Reporté pour optimisations avancées).
7.  Nettoyage du code et de la structure du projet `frontnextjs` (Nettoyage de App.tsx et route catch-all : **Terminé**).

## Problèmes connus et solutions potentielles
- **Cohabitation Bootstrap / Tailwind CSS** : Actuellement, Bootstrap est importé globalement et Tailwind CSS est configuré. Il faudra s'assurer qu'il n'y a pas de conflits de style majeurs ou choisir une stratégie d'utilisation (par exemple, utiliser Tailwind pour la structure et Bootstrap pour certains composants, ou migrer progressivement les composants Bootstrap vers Tailwind).
- **Chemins d'importation** : Vérifier tous les chemins d'importation relatifs dans les composants copiés de `frontend/` vers `frontnextjs/`.

## Évolutions des décisions du projet
- **Migration du frontend de React CRA vers Next.js** pour bénéficier d'un meilleur outillage, de performances optimisées (SSR/SSG), d'une meilleure expérience de développement et de fonctionnalités avancées pour le SEO et l'optimisation des images/polices.
- Le backend reste sur FastAPI.

## Statut des livrables
- Phase 1 de la migration frontend vers Next.js (mode SPA) : **Terminée**.
- Documentation principale (`README.md`, scripts `.bat`) : **Mise à jour**.
- Memory Bank : En cours de mise à jour pour refléter ces changements.
- Prochaine étape majeure : Phase 2 de la migration Next.js.
