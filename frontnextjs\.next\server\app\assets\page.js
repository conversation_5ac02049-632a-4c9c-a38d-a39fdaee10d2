(()=>{var e={};e.id=830,e.ids=[830],e.modules={1040:(e,t,s)=>{Promise.resolve().then(s.bind(s,27542))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},24255:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,86346,23)),Promise.resolve().then(s.t.bind(s,27924,23)),Promise.resolve().then(s.t.bind(s,35656,23)),Promise.resolve().then(s.t.bind(s,40099,23)),Promise.resolve().then(s.t.bind(s,38243,23)),Promise.resolve().then(s.t.bind(s,28827,23)),Promise.resolve().then(s.t.bind(s,62763,23)),Promise.resolve().then(s.t.bind(s,97173,23))},27542:(e,t,s)=>{"use strict";s.d(t,{AssetsClientPart:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call AssetsClientPart() from the server but AssetsClientPart is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\AI-Code\\fire_UI\\frontnextjs\\src\\app\\assets\\AssetsClientPart.tsx","AssetsClientPart")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29190:(e,t,s)=>{"use strict";s.d(t,{default:()=>l});var r=s(60687),n=s(43210),a=s(85814),i=s.n(a);let l=()=>{let[e,t]=(0,n.useState)(!1),s=()=>{t(!1)};return(0,r.jsx)("nav",{className:"navbar navbar-expand-lg navbar-dark bg-dark",children:(0,r.jsxs)("div",{className:"container-fluid",children:[(0,r.jsx)(i(),{className:"navbar-brand",href:"/",onClick:s,children:"FIRE Dashboard"}),(0,r.jsx)("button",{className:"navbar-toggler",type:"button",onClick:()=>{t(!e)},"aria-controls":"navbarNavContent","aria-expanded":e,"aria-label":"Toggle navigation",children:(0,r.jsx)("span",{className:"navbar-toggler-icon"})}),(0,r.jsx)("div",{className:`collapse navbar-collapse ${e?"show":""}`,id:"navbarNavContent",children:(0,r.jsxs)("ul",{className:"navbar-nav me-auto mb-2 mb-lg-0",children:[(0,r.jsx)("li",{className:"nav-item",children:(0,r.jsx)(i(),{className:"nav-link",href:"/",onClick:s,children:"Dashboard"})}),(0,r.jsx)("li",{className:"nav-item",children:(0,r.jsx)(i(),{className:"nav-link",href:"/assets",onClick:s,children:"Patrimoine"})}),(0,r.jsx)("li",{className:"nav-item",children:(0,r.jsx)(i(),{className:"nav-link",href:"/liabilities",onClick:s,children:"Emprunts"})}),(0,r.jsx)("li",{className:"nav-item",children:(0,r.jsx)(i(),{className:"nav-link",href:"/scpi",onClick:s,children:"SCPI"})}),(0,r.jsx)("li",{className:"nav-item",children:(0,r.jsx)(i(),{className:"nav-link",href:"/evolution",onClick:s,children:"\xc9volution"})}),(0,r.jsx)("li",{className:"nav-item",children:(0,r.jsx)(i(),{className:"nav-link",href:"/budget",onClick:s,children:"Budget FIRE"})}),(0,r.jsx)("li",{className:"nav-item",children:(0,r.jsx)(i(),{className:"nav-link",href:"/scenarios",onClick:s,children:"Sc\xe9narios"})}),(0,r.jsx)("li",{className:"nav-item",children:(0,r.jsx)(i(),{className:"nav-link",href:"/fire",onClick:s,children:"Objectif FIRE"})}),(0,r.jsx)("li",{className:"nav-item",children:(0,r.jsx)(i(),{className:"nav-link",href:"/test",onClick:s,children:"Test API"})})]})})]})})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30004:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\AI-Code\\\\fire_UI\\\\frontnextjs\\\\src\\\\components\\\\Navbar.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\AI-Code\\fire_UI\\frontnextjs\\src\\components\\Navbar.tsx","default")},33873:e=>{"use strict";e.exports=require("path")},37268:(e,t,s)=>{"use strict";s.d(t,{AssetsClientPart:()=>g});var r=s(60687),n=s(43210),a=s.n(n),i=s(90317),l=s(48710),o=s(69662),c=s.n(o),d=s(98466),u=s(84986);let m=n.createContext(null);m.displayName="InputGroupContext";let p=n.forwardRef(({className:e,bsPrefix:t,as:s="span",...n},a)=>(t=(0,d.oU)(t,"input-group-text"),(0,r.jsx)(s,{ref:a,className:c()(e,t),...n})));p.displayName="InputGroupText";let h=n.forwardRef(({bsPrefix:e,size:t,hasValidation:s,className:a,as:i="div",...l},o)=>{e=(0,d.oU)(e,"input-group");let u=(0,n.useMemo)(()=>({}),[]);return(0,r.jsx)(m.Provider,{value:u,children:(0,r.jsx)(i,{ref:o,...l,className:c()(a,e,t&&`${e}-${t}`,s&&"has-validation")})})});h.displayName="InputGroup";let x=Object.assign(h,{Text:p,Radio:e=>(0,r.jsx)(p,{children:(0,r.jsx)(u.A,{type:"radio",...e})}),Checkbox:e=>(0,r.jsx)(p,{children:(0,r.jsx)(u.A,{type:"checkbox",...e})})});var v=s(12785),j=s(92388),f=s(25865);let b=({asset:e,onSave:t,onCancel:s})=>{let a=e=>e&&0!==e.length?e.map(e=>({name:e.category_name,value:e.value})):[{name:"Liquidit\xe9",value:0}],o=(0,n.useCallback)(()=>{if(e&&e.id){let t=a(e.categories);return{name:e.name||"",value:e.value||0,annual_interest:e.annual_interest||null,notes:e.notes||"",categories:t,update_date:e.update_date?new Date(e.update_date).toISOString().split("T")[0]:new Date().toISOString().split("T")[0]}}return{name:"",value:0,annual_interest:null,notes:"",categories:[{name:"Liquidit\xe9",value:0}],update_date:new Date().toISOString().split("T")[0]}},[e]),[c,d]=(0,n.useState)(o);(0,n.useEffect)(()=>{d(o())},[e,o]),(0,n.useEffect)(()=>{let e=c.categories.reduce((e,t)=>e+(Number(t.value)||0),0);e!==c.value&&d(t=>({...t,value:e}))},[c.categories,c.value]);let u=(e,t,s)=>{let r=[...c.categories];r[e]={...r[e],[t]:"value"===t?parseFloat(s)||0:s},d({...c,categories:r})},m=e=>{let t=[...c.categories];t.splice(e,1),d({...c,categories:t})},p=async s=>{s.preventDefault();let r=e?.id?"put":"post",n=e?.id?`/assets/${e.id}`:"/assets";try{await i.A[r](n,c),t()}catch(e){console.error("Erreur sauvegarde actif",e instanceof Error?e.message:"Unknown error")}};return(0,r.jsxs)(l.A,{onSubmit:p,children:[(0,r.jsxs)(l.A.Group,{className:"mb-3",children:[(0,r.jsx)(l.A.Label,{children:"Nom"}),(0,r.jsx)(l.A.Control,{type:"text",value:c.name,onChange:e=>d({...c,name:e.target.value}),required:!0})]}),(0,r.jsxs)(l.A.Group,{className:"mb-3",children:[(0,r.jsx)(l.A.Label,{children:"Valeur"}),(0,r.jsx)(l.A.Control,{type:"number",value:c.value,readOnly:!0,disabled:!0})]}),(0,r.jsxs)(l.A.Group,{className:"mb-3",children:[(0,r.jsx)(l.A.Label,{children:"Int\xe9r\xeat Annuel"}),(0,r.jsx)(l.A.Control,{type:"number",step:"0.01",value:c.annual_interest||"",onChange:e=>d({...c,annual_interest:parseFloat(e.target.value)||null})})]}),(0,r.jsxs)(l.A.Group,{className:"mb-3",children:[(0,r.jsx)(l.A.Label,{children:"Notes"}),(0,r.jsx)(l.A.Control,{type:"text",value:c.notes||"",onChange:e=>d({...c,notes:e.target.value})})]}),(0,r.jsx)("h5",{children:"Cat\xe9gories"}),c.categories.map((e,t)=>(0,r.jsxs)(x,{className:"mb-3",children:[(0,r.jsxs)(l.A.Select,{value:e.name,onChange:e=>u(t,"name",e.target.value),children:[(0,r.jsx)("option",{children:"Liquidit\xe9"}),(0,r.jsx)("option",{children:"Bourse"}),(0,r.jsx)("option",{children:"Crypto-Actifs"}),(0,r.jsx)("option",{children:"Fonds s\xe9curis\xe9s"}),(0,r.jsx)("option",{children:"Immobilier"}),(0,r.jsx)("option",{children:"Pr\xeats participatifs"})]}),(0,r.jsx)(v.A,{type:"number",value:e.value,onChange:e=>u(t,"value",e.target.value)}),(0,r.jsx)(j.A,{variant:"outline-danger",onClick:()=>m(t),children:"X"})]},t)),(0,r.jsx)(j.A,{variant:"outline-primary",onClick:()=>d({...c,categories:[...c.categories,{name:"Bourse",value:0}]}),className:"mb-3",children:"Ajouter une cat\xe9gorie"}),(0,r.jsxs)(l.A.Group,{className:"mb-3 mt-3",children:[(0,r.jsx)(l.A.Label,{children:"Date de mise \xe0 jour"}),(0,r.jsx)(l.A.Control,{type:"date",value:c.update_date,onChange:e=>d({...c,update_date:e.target.value}),required:!0})]}),(0,r.jsx)(j.A,{variant:"primary",type:"submit",children:"Sauvegarder"}),(0,r.jsx)(j.A,{variant:"secondary",onClick:s,className:"ms-2",children:"Annuler"})]})},g=({initialAssets:e,fetchError:t})=>{let[s,l]=(0,n.useState)(e||[]),[o,c]=(0,n.useState)(!1),[d,u]=(0,n.useState)(t||null),[m,p]=(0,n.useState)(!1),[h,x]=(0,n.useState)(null),[v,g]=(0,n.useState)({key:"value",direction:"descending"});(0,n.useEffect)(()=>{l(e||[]),u(t||null)},[e,t]);let y=()=>{c(!0),u(null),i.A.get("/assets").then(e=>{l(e.data)}).catch(e=>{console.error("Assets API error (client fetch):",e),u("Erreur lors du rechargement des actifs.")}).finally(()=>c(!1))},A=e=>{let t="ascending";v.key===e&&"ascending"===v.direction&&(t="descending"),g({key:e,direction:t})},C=a().useMemo(()=>{let e=[...s];return v.key&&e.sort((e,t)=>{let s=e[v.key],r=t[v.key];return null==s?1:null==r?-1:s<r?"ascending"===v.direction?-1:1:s>r?"ascending"===v.direction?1:-1:0}),e},[s,v]),N=async e=>{if(window.confirm("\xcates-vous s\xfbr de vouloir supprimer cet actif ?"))try{await i.A.delete(`/assets/${e}`),y()}catch(e){console.error("Erreur suppression actif",e)}};return d&&0===s.length?(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-danger",children:d}),(0,r.jsx)("button",{className:"btn btn-primary",onClick:y,children:"R\xe9essayer"})]}):o?(0,r.jsx)("p",{children:"Rechargement des actifs..."}):(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"d-flex justify-content-between align-items-center mb-4",children:[(0,r.jsx)("h1",{children:"Mon Patrimoine (Actifs)"}),(0,r.jsx)(j.A,{variant:"primary",onClick:()=>{x({}),p(!0)},children:"Ajouter un Actif"})]}),0===s.length&&!d&&(0,r.jsx)("p",{children:"Aucun actif trouv\xe9."}),s.length>0&&(0,r.jsxs)("table",{className:"table table-striped table-hover",children:[(0,r.jsx)("thead",{className:"table-dark",children:(0,r.jsxs)("tr",{children:[(0,r.jsxs)("th",{onClick:()=>A("name"),style:{cursor:"pointer"},children:["Nom ","name"===v.key?"ascending"===v.direction?"▲":"▼":""]}),(0,r.jsx)("th",{children:"Cat\xe9gories"}),(0,r.jsx)("th",{className:"text-end",children:"Int\xe9r\xeat Annuel"}),(0,r.jsxs)("th",{className:"text-end",onClick:()=>A("value"),style:{cursor:"pointer"},children:["Valeur ","value"===v.key?"ascending"===v.direction?"▲":"▼":""]}),(0,r.jsxs)("th",{className:"text-center",onClick:()=>A("update_date"),style:{cursor:"pointer"},children:["Derni\xe8re M\xe0J ","update_date"===v.key?"ascending"===v.direction?"▲":"▼":""]}),(0,r.jsx)("th",{className:"text-center",children:"Actions"})]})}),(0,r.jsx)("tbody",{children:C.map(e=>(0,r.jsxs)("tr",{children:[(0,r.jsx)("td",{children:e.name}),(0,r.jsx)("td",{children:e.categories.map(e=>`${e.category_name} (${new Intl.NumberFormat("fr-FR",{style:"currency",currency:"EUR"}).format(e.value)})`).join(", ")}),(0,r.jsx)("td",{className:"text-end",children:e.annual_interest?new Intl.NumberFormat("fr-FR",{style:"currency",currency:"EUR"}).format(e.annual_interest):"N/A"}),(0,r.jsx)("td",{className:"text-end",children:new Intl.NumberFormat("fr-FR",{style:"currency",currency:"EUR"}).format(e.value)}),(0,r.jsx)("td",{className:"text-center",children:new Date(e.update_date).toLocaleDateString("fr-FR")}),(0,r.jsxs)("td",{className:"text-center",children:[(0,r.jsx)(j.A,{variant:"outline-primary",size:"sm",onClick:()=>{x(e),p(!0)},children:"Modifier"})," ",(0,r.jsx)(j.A,{variant:"outline-danger",size:"sm",onClick:()=>N(e.id),children:"Supprimer"})]})]},e.id))})]}),(0,r.jsxs)(f.A,{show:m,onHide:()=>p(!1),children:[(0,r.jsx)(f.A.Header,{closeButton:!0,children:(0,r.jsxs)(f.A.Title,{children:[h?.id?"Modifier":"Ajouter"," un Actif"]})}),(0,r.jsx)(f.A.Body,{children:(0,r.jsx)(b,{asset:h,onSave:()=>{p(!1),x(null),y()},onCancel:()=>p(!1)})})]})]})}},52945:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>c});var r=s(65239),n=s(48088),a=s(88170),i=s.n(a),l=s(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(t,o);let c={children:["",{children:["assets",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,81334)),"D:\\AI-Code\\fire_UI\\frontnextjs\\src\\app\\assets\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e),async e=>(await Promise.resolve().then(s.bind(s,78162))).default(e)],apple:[async e=>(await Promise.resolve().then(s.bind(s,94650))).default(e)],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"D:\\AI-Code\\fire_UI\\frontnextjs\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e),async e=>(await Promise.resolve().then(s.bind(s,78162))).default(e)],apple:[async e=>(await Promise.resolve().then(s.bind(s,94650))).default(e)],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["D:\\AI-Code\\fire_UI\\frontnextjs\\src\\app\\assets\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/assets/page",pathname:"/assets",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},59184:(e,t,s)=>{Promise.resolve().then(s.bind(s,37268))},61135:()=>{},62907:(e,t,s)=>{Promise.resolve().then(s.bind(s,30004))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n});var r=s(31658);let n=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},73523:(e,t,s)=>{Promise.resolve().then(s.bind(s,29190))},74075:e=>{"use strict";e.exports=require("zlib")},78162:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n});var r=s(31658);let n=async e=>[{type:"image/png",sizes:"192x192",url:(0,r.fillMetadataSegment)(".",await e.params,"icon.png")+"?d84b06edc1baa420"}]},79551:e=>{"use strict";e.exports=require("url")},81334:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i});var r=s(37413);s(61120);var n=s(27542);async function a(){try{let e=await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL||"http://localhost:8000/api"}/assets`,{cache:"no-store"});if(!e.ok){console.error("SSR Fetch Error (Assets):",e.status,e.statusText);let t=await e.text();return console.error("SSR Fetch Error Body (Assets):",t),null}return await e.json()}catch(e){return console.error("SSR Fetch Exception (Assets):",e),null}}async function i(){let e,t=await a();return null===t&&(e="Erreur lors du chargement des actifs c\xf4t\xe9 serveur."),(0,r.jsx)(n.AssetsClientPart,{initialAssets:t||[],fetchError:e})}},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},90317:(e,t,s)=>{"use strict";s.d(t,{A:()=>n});let r=s(51060).A.create({baseURL:"http://localhost:8000/api",headers:{"Content-Type":"application/json"},timeout:1e4});r.interceptors.response.use(e=>e,e=>(console.error("API Error:",e.response?.status,e.config?.url,e.message),Promise.reject(e)));let n=r},93991:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,16444,23)),Promise.resolve().then(s.t.bind(s,16042,23)),Promise.resolve().then(s.t.bind(s,88170,23)),Promise.resolve().then(s.t.bind(s,49477,23)),Promise.resolve().then(s.t.bind(s,29345,23)),Promise.resolve().then(s.t.bind(s,12089,23)),Promise.resolve().then(s.t.bind(s,46577,23)),Promise.resolve().then(s.t.bind(s,31307,23))},94431:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>l,metadata:()=>a,viewport:()=>i});var r=s(37413);s(61135),s(27209);var n=s(30004);let a={title:"FIRE Dashboard",description:"Application pour le suivi de l'objectif FIRE (Financial Independence, Retire Early)",manifest:"/manifest.json",icons:{icon:"/icon.png",apple:"/apple-icon.png"}},i={themeColor:"#000000"};function l({children:e}){return(0,r.jsx)("html",{lang:"en",children:(0,r.jsx)("body",{children:(0,r.jsxs)("div",{className:"App",children:[(0,r.jsx)(n.default,{}),(0,r.jsx)("main",{className:"container mt-4",children:e})]})})})}},94650:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n});var r=s(31658);let n=async e=>[{type:"image/png",sizes:"192x192",url:(0,r.fillMetadataSegment)(".",await e.params,"apple-icon.png")+"?d84b06edc1baa420"}]},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[447,989,517,315,36],()=>s(52945));module.exports=r})();