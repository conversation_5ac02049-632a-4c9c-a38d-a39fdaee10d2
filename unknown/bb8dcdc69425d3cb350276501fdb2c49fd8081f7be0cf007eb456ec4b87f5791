(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/bootstrap/dist/js/bootstrap.bundle.min.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_bootstrap_dist_js_bootstrap_bundle_min_35ba9846.js",
  "static/chunks/node_modules_bootstrap_dist_js_bootstrap_bundle_min_d962a1e5.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/bootstrap/dist/js/bootstrap.bundle.min.js [app-client] (ecmascript)");
    });
});
}}),
}]);