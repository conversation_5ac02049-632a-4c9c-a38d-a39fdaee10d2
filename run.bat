@echo OFF
ECHO "Starting backend server..."
REM Assurez-vous que l'environnement virtuel est activé.
REM Le script install.bat devrait déjà l'avoir créé et activé pour l'installation.
REM Pour run.bat, il est préférable de s'assurer qu'il est activé à chaque fois si nécessaire,
REM ou que les commandes Python/uvicorn sont accessibles globalement ou via le .venv.
start "Backend" cmd /k "cd backend && .\.venv\Scripts\activate && uvicorn main:app --reload --port 8000"

ECHO "Starting Next.js frontend server (frontnextjs)..."
start "Frontend Next.js" cmd /k "cd frontnextjs && npm run dev -- --port 3000"

REM Pour lancer l'ancien frontend React (frontend) sur un autre port si nécessaire:
REM ECHO "Starting original React frontend server (frontend) on port 3001..."
REM start "Original Frontend" cmd /k "cd frontend && npm start -- --port 3001"

ECHO.
ECHO ==================================================================
ECHO Serveurs en cours de démarrage...
ECHO.
ECHO    - Backend API: http://localhost:8000
ECHO    - Documentation API: http://localhost:8000/docs
ECHO    - Frontend (Next.js): http://localhost:3000
REM ECHO    - Frontend (React original): http://localhost:3001 (si décommenté et lancé)
ECHO ==================================================================
ECHO.

