(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[830],{2365:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let a=n(3464).A.create({baseURL:"http://localhost:8000/api",headers:{"Content-Type":"application/json"},timeout:1e4});a.interceptors.response.use(e=>e,e=>{var t,n;return console.error("API Error:",null==(t=e.response)?void 0:t.status,null==(n=e.config)?void 0:n.url,e.message),Promise.reject(e)});let r=a},6553:(e,t,n)=>{"use strict";n.d(t,{AssetsClientPart:()=>A});var a=n(5155),r=n(2115),s=n(2365),l=n(8827),i=n(9300),c=n.n(i),o=n(7390),u=n(5560);let d=r.createContext(null);d.displayName="InputGroupContext";let h=r.forwardRef((e,t)=>{let{className:n,bsPrefix:r,as:s="span",...l}=e;return r=(0,o.oU)(r,"input-group-text"),(0,a.jsx)(s,{ref:t,className:c()(n,r),...l})});h.displayName="InputGroupText";let m=r.forwardRef((e,t)=>{let{bsPrefix:n,size:s,hasValidation:l,className:i,as:u="div",...h}=e;n=(0,o.oU)(n,"input-group");let m=(0,r.useMemo)(()=>({}),[]);return(0,a.jsx)(d.Provider,{value:m,children:(0,a.jsx)(u,{ref:t,...h,className:c()(i,n,s&&"".concat(n,"-").concat(s),l&&"has-validation")})})});m.displayName="InputGroup";let p=Object.assign(m,{Text:h,Radio:e=>(0,a.jsx)(h,{children:(0,a.jsx)(u.A,{type:"radio",...e})}),Checkbox:e=>(0,a.jsx)(h,{children:(0,a.jsx)(u.A,{type:"checkbox",...e})})});var x=n(3005),j=n(902),v=n(9986);let g=e=>{let{asset:t,onSave:n,onCancel:i}=e,c=e=>e&&0!==e.length?e.map(e=>({name:e.category_name,value:e.value})):[{name:"Liquidit\xe9",value:0}],o=(0,r.useCallback)(()=>{if(t&&t.id){let e=c(t.categories);return{name:t.name||"",value:t.value||0,annual_interest:t.annual_interest||null,notes:t.notes||"",categories:e,update_date:t.update_date?new Date(t.update_date).toISOString().split("T")[0]:new Date().toISOString().split("T")[0]}}return{name:"",value:0,annual_interest:null,notes:"",categories:[{name:"Liquidit\xe9",value:0}],update_date:new Date().toISOString().split("T")[0]}},[t]),[u,d]=(0,r.useState)(o);(0,r.useEffect)(()=>{d(o())},[t,o]),(0,r.useEffect)(()=>{let e=u.categories.reduce((e,t)=>e+(Number(t.value)||0),0);e!==u.value&&d(t=>({...t,value:e}))},[u.categories,u.value]);let h=(e,t,n)=>{let a=[...u.categories];a[e]={...a[e],[t]:"value"===t?parseFloat(n)||0:n},d({...u,categories:a})},m=e=>{let t=[...u.categories];t.splice(e,1),d({...u,categories:t})},v=async e=>{e.preventDefault();let a=(null==t?void 0:t.id)?"put":"post",r=(null==t?void 0:t.id)?"/assets/".concat(t.id):"/assets";try{await s.A[a](r,u),n()}catch(e){console.error("Erreur sauvegarde actif",e instanceof Error?e.message:"Unknown error")}};return(0,a.jsxs)(l.A,{onSubmit:v,children:[(0,a.jsxs)(l.A.Group,{className:"mb-3",children:[(0,a.jsx)(l.A.Label,{children:"Nom"}),(0,a.jsx)(l.A.Control,{type:"text",value:u.name,onChange:e=>d({...u,name:e.target.value}),required:!0})]}),(0,a.jsxs)(l.A.Group,{className:"mb-3",children:[(0,a.jsx)(l.A.Label,{children:"Valeur"}),(0,a.jsx)(l.A.Control,{type:"number",value:u.value,readOnly:!0,disabled:!0})]}),(0,a.jsxs)(l.A.Group,{className:"mb-3",children:[(0,a.jsx)(l.A.Label,{children:"Int\xe9r\xeat Annuel"}),(0,a.jsx)(l.A.Control,{type:"number",step:"0.01",value:u.annual_interest||"",onChange:e=>d({...u,annual_interest:parseFloat(e.target.value)||null})})]}),(0,a.jsxs)(l.A.Group,{className:"mb-3",children:[(0,a.jsx)(l.A.Label,{children:"Notes"}),(0,a.jsx)(l.A.Control,{type:"text",value:u.notes||"",onChange:e=>d({...u,notes:e.target.value})})]}),(0,a.jsx)("h5",{children:"Cat\xe9gories"}),u.categories.map((e,t)=>(0,a.jsxs)(p,{className:"mb-3",children:[(0,a.jsxs)(l.A.Select,{value:e.name,onChange:e=>h(t,"name",e.target.value),children:[(0,a.jsx)("option",{children:"Liquidit\xe9"}),(0,a.jsx)("option",{children:"Bourse"}),(0,a.jsx)("option",{children:"Crypto-Actifs"}),(0,a.jsx)("option",{children:"Fonds s\xe9curis\xe9s"}),(0,a.jsx)("option",{children:"Immobilier"}),(0,a.jsx)("option",{children:"Pr\xeats participatifs"})]}),(0,a.jsx)(x.A,{type:"number",value:e.value,onChange:e=>h(t,"value",e.target.value)}),(0,a.jsx)(j.A,{variant:"outline-danger",onClick:()=>m(t),children:"X"})]},t)),(0,a.jsx)(j.A,{variant:"outline-primary",onClick:()=>d({...u,categories:[...u.categories,{name:"Bourse",value:0}]}),className:"mb-3",children:"Ajouter une cat\xe9gorie"}),(0,a.jsxs)(l.A.Group,{className:"mb-3 mt-3",children:[(0,a.jsx)(l.A.Label,{children:"Date de mise \xe0 jour"}),(0,a.jsx)(l.A.Control,{type:"date",value:u.update_date,onChange:e=>d({...u,update_date:e.target.value}),required:!0})]}),(0,a.jsx)(j.A,{variant:"primary",type:"submit",children:"Sauvegarder"}),(0,a.jsx)(j.A,{variant:"secondary",onClick:i,className:"ms-2",children:"Annuler"})]})},A=e=>{let{initialAssets:t,fetchError:n}=e,[l,i]=(0,r.useState)(t||[]),[c,o]=(0,r.useState)(!1),[u,d]=(0,r.useState)(n||null),[h,m]=(0,r.useState)(!1),[p,x]=(0,r.useState)(null),[A,y]=(0,r.useState)({key:"value",direction:"descending"});(0,r.useEffect)(()=>{i(t||[]),d(n||null)},[t,n]);let f=()=>{o(!0),d(null),s.A.get("/assets").then(e=>{i(e.data)}).catch(e=>{console.error("Assets API error (client fetch):",e),d("Erreur lors du rechargement des actifs.")}).finally(()=>o(!1))},b=e=>{let t="ascending";A.key===e&&"ascending"===A.direction&&(t="descending"),y({key:e,direction:t})},N=r.useMemo(()=>{let e=[...l];return A.key&&e.sort((e,t)=>{let n=e[A.key],a=t[A.key];return null==n?1:null==a?-1:n<a?"ascending"===A.direction?-1:1:n>a?"ascending"===A.direction?1:-1:0}),e},[l,A]),C=async e=>{if(window.confirm("\xcates-vous s\xfbr de vouloir supprimer cet actif ?"))try{await s.A.delete("/assets/".concat(e)),f()}catch(e){console.error("Erreur suppression actif",e)}};return u&&0===l.length?(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-danger",children:u}),(0,a.jsx)("button",{className:"btn btn-primary",onClick:f,children:"R\xe9essayer"})]}):c?(0,a.jsx)("p",{children:"Rechargement des actifs..."}):(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"d-flex justify-content-between align-items-center mb-4",children:[(0,a.jsx)("h1",{children:"Mon Patrimoine (Actifs)"}),(0,a.jsx)(j.A,{variant:"primary",onClick:()=>{x({}),m(!0)},children:"Ajouter un Actif"})]}),0===l.length&&!u&&(0,a.jsx)("p",{children:"Aucun actif trouv\xe9."}),l.length>0&&(0,a.jsxs)("table",{className:"table table-striped table-hover",children:[(0,a.jsx)("thead",{className:"table-dark",children:(0,a.jsxs)("tr",{children:[(0,a.jsxs)("th",{onClick:()=>b("name"),style:{cursor:"pointer"},children:["Nom ","name"===A.key?"ascending"===A.direction?"▲":"▼":""]}),(0,a.jsx)("th",{children:"Cat\xe9gories"}),(0,a.jsx)("th",{className:"text-end",children:"Int\xe9r\xeat Annuel"}),(0,a.jsxs)("th",{className:"text-end",onClick:()=>b("value"),style:{cursor:"pointer"},children:["Valeur ","value"===A.key?"ascending"===A.direction?"▲":"▼":""]}),(0,a.jsxs)("th",{className:"text-center",onClick:()=>b("update_date"),style:{cursor:"pointer"},children:["Derni\xe8re M\xe0J ","update_date"===A.key?"ascending"===A.direction?"▲":"▼":""]}),(0,a.jsx)("th",{className:"text-center",children:"Actions"})]})}),(0,a.jsx)("tbody",{children:N.map(e=>(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{children:e.name}),(0,a.jsx)("td",{children:e.categories.map(e=>"".concat(e.category_name," (").concat(new Intl.NumberFormat("fr-FR",{style:"currency",currency:"EUR"}).format(e.value),")")).join(", ")}),(0,a.jsx)("td",{className:"text-end",children:e.annual_interest?new Intl.NumberFormat("fr-FR",{style:"currency",currency:"EUR"}).format(e.annual_interest):"N/A"}),(0,a.jsx)("td",{className:"text-end",children:new Intl.NumberFormat("fr-FR",{style:"currency",currency:"EUR"}).format(e.value)}),(0,a.jsx)("td",{className:"text-center",children:new Date(e.update_date).toLocaleDateString("fr-FR")}),(0,a.jsxs)("td",{className:"text-center",children:[(0,a.jsx)(j.A,{variant:"outline-primary",size:"sm",onClick:()=>{x(e),m(!0)},children:"Modifier"})," ",(0,a.jsx)(j.A,{variant:"outline-danger",size:"sm",onClick:()=>C(e.id),children:"Supprimer"})]})]},e.id))})]}),(0,a.jsxs)(v.A,{show:h,onHide:()=>m(!1),children:[(0,a.jsx)(v.A.Header,{closeButton:!0,children:(0,a.jsxs)(v.A.Title,{children:[(null==p?void 0:p.id)?"Modifier":"Ajouter"," un Actif"]})}),(0,a.jsx)(v.A.Body,{children:(0,a.jsx)(g,{asset:p,onSave:()=>{m(!1),x(null),f()},onCancel:()=>m(!1)})})]})]})}},9384:(e,t,n)=>{Promise.resolve().then(n.bind(n,6553))}},e=>{var t=t=>e(e.s=t);e.O(0,[464,160,405,441,684,358],()=>t(9384)),_N_E=e.O()}]);