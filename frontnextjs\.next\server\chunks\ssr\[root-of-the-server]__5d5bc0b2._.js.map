{"version": 3, "sources": [], "sections": [{"offset": {"line": 111, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/AI-Code/fire_UI/frontnextjs/src/components/ApiClient.ts"], "sourcesContent": ["import axios from 'axios';\r\n\r\nconst apiClient = axios.create({\r\n  baseURL: 'http://localhost:8000/api',\r\n  headers: {\r\n    'Content-Type': 'application/json',\r\n  },\r\n  timeout: 10000, // 10 second timeout\r\n});\r\n\r\n// Add response interceptor for error handling\r\napiClient.interceptors.response.use(\r\n  (response) => response,\r\n  (error) => {\r\n    console.error('API Error:', error.response?.status, error.config?.url, error.message);\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\nexport default apiClient;\r\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,YAAY,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAC7B,SAAS;IACT,SAAS;QACP,gBAAgB;IAClB;IACA,SAAS;AACX;AAEA,8CAA8C;AAC9C,UAAU,YAAY,CAAC,QAAQ,CAAC,GAAG,CACjC,CAAC,WAAa,UACd,CAAC;IACC,QAAQ,KAAK,CAAC,cAAc,MAAM,QAAQ,EAAE,QAAQ,MAAM,MAAM,EAAE,KAAK,MAAM,OAAO;IACpF,OAAO,QAAQ,MAAM,CAAC;AACxB;uCAGa", "debugId": null}}, {"offset": {"line": 135, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/AI-Code/fire_UI/frontnextjs/src/app/scenarios/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport { Card, Row, Col, Form, Table, Badge, Alert } from 'react-bootstrap';\r\nimport apiClient from '../../components/ApiClient'; // Ajusté\r\n\r\ninterface ScenarioResult {\r\n  swr: number;\r\n  returnRate: number;\r\n  requiredCapital: number;\r\n  yearsToFire: number;\r\n  fireDate: number;\r\n  ageAtFire: number;\r\n  monthlyInvestmentNeeded: number;\r\n  status: 'early' | 'ontime' | 'delayed';\r\n}\r\n\r\ninterface FireData {\r\n  current_net_patrimoine: number;\r\n  fire_target_amount: number; // Bien que non utilisé directement dans les calculs ici, il est bon de l'avoir\r\n}\r\n\r\nconst ScenariosFirePage: React.FC = () => {\r\n  const [fireData, setFireData] = useState<FireData | null>(null);\r\n  const [customSwr, setCustomSwr] = useState(4.0);\r\n  const [customReturn, setCustomReturn] = useState(5.0);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState<string | null>(null);\r\n\r\n\r\n  const CURRENT_YEAR = 2025;\r\n  const BIRTH_YEAR = 1978;\r\n  const TARGET_YEAR = 2038;\r\n  const ANNUAL_GROSS_WITHDRAWAL = 36406;\r\n  const MONTHLY_INVESTMENT_CAPACITY = 3415;\r\n\r\n  const swrScenarios = [\r\n    { name: 'Conservateur', rate: 3.8, description: 'Sécurité maximale' },\r\n    { name: 'Standard', rate: 4.0, description: 'Règle classique' },\r\n    { name: 'Agressif', rate: 4.5, description: 'Optimiste' }\r\n  ];\r\n\r\n  const returnScenarios = [\r\n    { name: 'Conservateur', rate: 4.0, description: 'Marché difficile' },\r\n    { name: 'Modéré', rate: 5.0, description: 'Hypothèse actuelle' },\r\n    { name: 'Optimiste', rate: 6.0, description: 'Marché favorable' }\r\n  ];\r\n\r\n  useEffect(() => {\r\n    const fetchFireData = async () => {\r\n      setLoading(true);\r\n      setError(null);\r\n      try {\r\n        const response = await apiClient.get('/fire-target');\r\n        setFireData(response.data);\r\n      } catch (err) {\r\n        console.error('Error fetching FIRE data:', err);\r\n        setError(\"Erreur lors du chargement des données de base FIRE.\");\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n    fetchFireData();\r\n  }, []);\r\n\r\n  const calculateScenario = (swr: number, returnRate: number): ScenarioResult => {\r\n    if (!fireData) {\r\n      return { swr, returnRate, requiredCapital: 0, yearsToFire: 0, fireDate: 0, ageAtFire: 0, monthlyInvestmentNeeded: 0, status: 'delayed' };\r\n    }\r\n\r\n    const requiredCapital = ANNUAL_GROSS_WITHDRAWAL / (swr / 100);\r\n    const remainingToInvest = Math.max(0, requiredCapital - fireData.current_net_patrimoine);\r\n    let yearsToFire = 0;\r\n\r\n    if (remainingToInvest > 0) {\r\n      const R = returnRate / 100; // Taux de rendement annuel\r\n      const P = MONTHLY_INVESTMENT_CAPACITY * 12; // Paiement annuel\r\n      const PV = fireData.current_net_patrimoine; // Valeur actuelle\r\n      const FV = requiredCapital; // Valeur future (capital requis)\r\n\r\n      if (R > 0) {\r\n        // Formule complexe pour NPER avec investissements réguliers et capital initial\r\n        // N = log((FV*R + P) / (PV*R + P)) / log(1+R)\r\n        const numerator = Math.log((FV * R + P) / (PV * R + P));\r\n        const denominator = Math.log(1 + R);\r\n        if (denominator === 0 || (PV * R + P) <= 0 || (FV * R + P) <=0 ) { // Eviter division par zéro ou log de non-positif\r\n            yearsToFire = P > 0 ? remainingToInvest / P : Infinity; // Si pas de rendement ou problème de calcul\r\n        } else {\r\n            yearsToFire = numerator / denominator;\r\n        }\r\n\r\n      } else if (P > 0) { // Si pas de rendement, calcul linéaire\r\n        yearsToFire = remainingToInvest / P;\r\n      } else { // Si pas de rendement et pas d'investissement, impossible d'atteindre\r\n        yearsToFire = Infinity;\r\n      }\r\n    }\r\n\r\n    yearsToFire = Math.max(0, yearsToFire); // S'assurer que ce n'est pas négatif\r\n\r\n    const fireDate = CURRENT_YEAR + yearsToFire;\r\n    const ageAtFire = (CURRENT_YEAR - BIRTH_YEAR) + yearsToFire;\r\n\r\n    let monthlyInvestmentNeeded = 0;\r\n    const yearsToTarget = TARGET_YEAR - CURRENT_YEAR;\r\n    if (yearsToTarget > 0) {\r\n        const R_monthly = returnRate / 100 / 12;\r\n        const N_months = yearsToTarget * 12;\r\n        const PV = fireData.current_net_patrimoine;\r\n        const FV_target = requiredCapital;\r\n\r\n        if (R_monthly > 0) {\r\n            const futureValueOfPV = PV * Math.pow(1 + R_monthly, N_months);\r\n            const neededFromInvestments = FV_target - futureValueOfPV;\r\n            if (neededFromInvestments > 0) {\r\n                monthlyInvestmentNeeded = (neededFromInvestments * R_monthly) / (Math.pow(1 + R_monthly, N_months) - 1);\r\n            } else {\r\n                monthlyInvestmentNeeded = 0; // L'objectif est atteint sans investissements supplémentaires\r\n            }\r\n        } else if (N_months > 0) { // Pas de rendement, investissement linéaire\r\n            const neededFromInvestments = FV_target - PV;\r\n             if (neededFromInvestments > 0) {\r\n                monthlyInvestmentNeeded = neededFromInvestments / N_months;\r\n            } else {\r\n                 monthlyInvestmentNeeded = 0;\r\n            }\r\n        } else {\r\n            monthlyInvestmentNeeded = (FV_target > PV) ? Infinity : 0; // Cible déjà passée ou impossible sans temps/rendement\r\n        }\r\n    }\r\n    monthlyInvestmentNeeded = Math.max(0, monthlyInvestmentNeeded);\r\n\r\n\r\n    let status: 'early' | 'ontime' | 'delayed' = 'ontime';\r\n    if (fireDate < TARGET_YEAR - 0.5) status = 'early';\r\n    else if (fireDate > TARGET_YEAR + 0.5) status = 'delayed';\r\n\r\n    return { swr, returnRate, requiredCapital, yearsToFire, fireDate, ageAtFire, monthlyInvestmentNeeded, status };\r\n  };\r\n\r\n  const calculateCustomScenario = (): ScenarioResult => calculateScenario(customSwr, customReturn);\r\n\r\n  const formatCurrency = (value: number) => new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR', maximumFractionDigits: 0 }).format(value);\r\n  const formatYear = (year: number) => (isFinite(year) && year > 0) ? year.toFixed(1) : \"N/A\";\r\n  const formatAge = (age: number) => (isFinite(age) && age > 0) ? age.toFixed(1) : \"N/A\";\r\n\r\n\r\n  const getStatusColor = (status: string) => ({ early: 'success', ontime: 'primary', delayed: 'danger' }[status] || 'secondary');\r\n  const getStatusText = (status: string) => ({ early: 'En avance', ontime: 'Dans les temps', delayed: 'En retard' }[status] || 'Inconnu');\r\n\r\n  if (loading) return <p>Chargement des données de base...</p>;\r\n  if (error || !fireData) return <Alert variant=\"danger\">{error || \"Données FIRE non disponibles.\"}</Alert>;\r\n\r\n  const allScenarios: ScenarioResult[] = swrScenarios.flatMap(swr => returnScenarios.map(ret => calculateScenario(swr.rate, ret.rate)));\r\n  const customScenario = calculateCustomScenario();\r\n\r\n  const validScenarios = allScenarios.filter(s => isFinite(s.fireDate));\r\n  const bestScenario = validScenarios.length > 0 ? validScenarios.reduce((best, current) => current.fireDate < best.fireDate ? current : best) : customScenario;\r\n  const worstScenario = validScenarios.length > 0 ? validScenarios.reduce((worst, current) => current.fireDate > worst.fireDate ? current : worst) : customScenario;\r\n\r\n  return (\r\n    <div>\r\n      <div className=\"d-flex justify-content-between align-items-center mb-4\"><h1>🎯 Calculateur de Scénarios FIRE</h1></div>\r\n      <Row className=\"mb-4\">\r\n        <Col md={4}><Card className=\"text-white bg-success\"><Card.Body className=\"text-center\"><h5>Meilleur Scénario</h5><h4>{formatYear(bestScenario.fireDate)}</h4><small>SWR {bestScenario.swr}% • Rendement {bestScenario.returnRate}%</small></Card.Body></Card></Col>\r\n        <Col md={4}><Card className=\"text-white bg-primary\"><Card.Body className=\"text-center\"><h5>Objectif Documenté</h5><h4>{TARGET_YEAR}</h4><small>SWR 4% • Rendement 5% • Âge 60 ans</small></Card.Body></Card></Col>\r\n        <Col md={4}><Card className=\"text-white bg-danger\"><Card.Body className=\"text-center\"><h5>Pire Scénario</h5><h4>{formatYear(worstScenario.fireDate)}</h4><small>SWR {worstScenario.swr}% • Rendement {worstScenario.returnRate}%</small></Card.Body></Card></Col>\r\n      </Row>\r\n      <Alert variant=\"info\" className=\"mb-4\"><Alert.Heading>Paramètres de Base</Alert.Heading><Row>\r\n          <Col md={3}><strong>Patrimoine actuel :</strong><br />{formatCurrency(fireData.current_net_patrimoine)}</Col>\r\n          <Col md={3}><strong>Retrait annuel brut :</strong><br />{formatCurrency(ANNUAL_GROSS_WITHDRAWAL)}</Col>\r\n          <Col md={3}><strong>Investissement mensuel :</strong><br />{formatCurrency(MONTHLY_INVESTMENT_CAPACITY)}</Col>\r\n          <Col md={3}><strong>Objectif documenté :</strong><br />{TARGET_YEAR} (âge {(TARGET_YEAR - CURRENT_YEAR) + (CURRENT_YEAR - BIRTH_YEAR)} ans)</Col>\r\n      </Row></Alert>\r\n      <Card className=\"mb-4\"><Card.Header><h5>🔧 Calculateur Personnalisé</h5></Card.Header><Card.Body>\r\n          <Row>\r\n            <Col md={6}><Form.Group className=\"mb-3\"><Form.Label>Taux de Retrait Sécurisé (SWR) : {customSwr}%</Form.Label><Form.Range min={3} max={6} step={0.1} value={customSwr} onChange={(e) => setCustomSwr(parseFloat(e.target.value))} /><div className=\"d-flex justify-content-between\"><small>3% (Très conservateur)</small><small>6% (Très agressif)</small></div></Form.Group></Col>\r\n            <Col md={6}><Form.Group className=\"mb-3\"><Form.Label>Rendement Annuel Attendu : {customReturn}%</Form.Label><Form.Range min={3} max={8} step={0.1} value={customReturn} onChange={(e) => setCustomReturn(parseFloat(e.target.value))} /><div className=\"d-flex justify-content-between\"><small>3% (Très conservateur)</small><small>8% (Très optimiste)</small></div></Form.Group></Col>\r\n          </Row>\r\n          <Row>\r\n            <Col md={3}><div className=\"text-center\"><h6>Capital Requis</h6><h4 className=\"text-primary\">{formatCurrency(customScenario.requiredCapital)}</h4></div></Col>\r\n            <Col md={3}><div className=\"text-center\"><h6>Date FIRE</h6><h4 className=\"text-success\">{formatYear(customScenario.fireDate)}</h4></div></Col>\r\n            <Col md={3}><div className=\"text-center\"><h6>Âge FIRE</h6><h4 className=\"text-info\">{formatAge(customScenario.ageAtFire)} ans</h4></div></Col>\r\n            <Col md={3}><div className=\"text-center\"><h6>Statut vs {TARGET_YEAR}</h6><Badge bg={getStatusColor(customScenario.status)} className=\"fs-6\">{getStatusText(customScenario.status)}</Badge></div></Col>\r\n          </Row>\r\n      </Card.Body></Card>\r\n      <Card className=\"mb-4\"><Card.Header><h5>📊 Matrice des Scénarios (Date FIRE)</h5></Card.Header><Card.Body>\r\n          <Table responsive bordered className=\"text-center\">\r\n            <thead><tr><th>SWR \\ Rendement</th>{returnScenarios.map(ret => (<th key={ret.rate} className=\"bg-light\">{ret.name}<br /><small>{ret.rate}%</small></th>))}</tr></thead>\r\n            <tbody>{swrScenarios.map(swr => (<tr key={swr.rate}><td className=\"bg-light\"><strong>{swr.name}</strong><br /><small>{swr.rate}%</small></td>{returnScenarios.map(ret => { const scenario = allScenarios.find(s => s.swr === swr.rate && s.returnRate === ret.rate); return (<td key={`${swr.rate}-${ret.rate}`} className={`table-${getStatusColor(scenario?.status || 'secondary')}`}><strong>{formatYear(scenario?.fireDate || 0)}</strong><br /><small>{formatAge(scenario?.ageAtFire || 0)} ans</small></td>);})}</tr>))}</tbody>\r\n          </Table>\r\n          <div className=\"mt-2\"><Badge bg=\"success\" className=\"me-2\">En avance (avant {TARGET_YEAR - 0.5})</Badge><Badge bg=\"primary\" className=\"me-2\">Dans les temps ({TARGET_YEAR - 0.5}-{TARGET_YEAR + 0.5})</Badge><Badge bg=\"danger\">En retard (après {TARGET_YEAR + 0.5})</Badge></div>\r\n      </Card.Body></Card>\r\n      <Card><Card.Header><h5>📋 Analyse Détaillée des Scénarios</h5></Card.Header><Card.Body>\r\n          <Table responsive striped hover>\r\n            <thead><tr><th>SWR</th><th>Rendement</th><th>Capital Requis</th><th>Date FIRE</th><th>Âge FIRE</th><th>Années Restantes</th><th>Investissement Mensuel pour {TARGET_YEAR}</th><th>Statut</th></tr></thead>\r\n            <tbody>{allScenarios.sort((a, b) => a.fireDate - b.fireDate).map((scenario, index) => (\r\n                <tr key={index}><td>{scenario.swr}%</td><td>{scenario.returnRate}%</td><td>{formatCurrency(scenario.requiredCapital)}</td><td><strong>{formatYear(scenario.fireDate)}</strong></td><td>{formatAge(scenario.ageAtFire)} ans</td><td>{formatYear(scenario.yearsToFire)} ans</td>\r\n                  <td>{scenario.monthlyInvestmentNeeded > 0 ? formatCurrency(scenario.monthlyInvestmentNeeded) : <span className=\"text-success\">Objectif déjà atteignable</span>}</td>\r\n                  <td><Badge bg={getStatusColor(scenario.status)}>{getStatusText(scenario.status)}</Badge></td>\r\n                </tr>))}\r\n            </tbody>\r\n          </Table>\r\n      </Card.Body></Card>\r\n      <Alert variant=\"warning\" className=\"mt-4\"><Alert.Heading>💡 Analyse et Recommandations</Alert.Heading><Row>\r\n          <Col md={6}><h6>Observations clés :</h6><ul><li><strong>Scénario actuel (4% SWR, 5% rendement) :</strong> FIRE en {formatYear(allScenarios.find(s => s.swr === 4.0 && s.returnRate === 5.0)?.fireDate || TARGET_YEAR)}</li><li><strong>Meilleur cas :</strong> FIRE dès {formatYear(bestScenario.fireDate)} avec SWR {bestScenario.swr}% et rendement {bestScenario.returnRate}%</li><li><strong>Pire cas :</strong> FIRE retardé à {formatYear(worstScenario.fireDate)} avec SWR {worstScenario.swr}% et rendement {worstScenario.returnRate}%</li></ul></Col>\r\n          <Col md={6}><h6>Stratégies d&apos;optimisation :</h6><ul><li><strong>Pour accélérer :</strong> Viser un rendement de 6% ou accepter un SWR de 4.5%</li><li><strong>Pour sécuriser :</strong> Prévoir un SWR conservateur de 3.8% (capital requis : {formatCurrency(ANNUAL_GROSS_WITHDRAWAL / 0.038)})</li><li><strong>Flexibilité :</strong> Ajuster l&apos;allocation d&apos;actifs selon les conditions de marché</li></ul></Col>\r\n      </Row></Alert>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ScenariosFirePage;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,8NAAoD,SAAS;AAJ7D;;;;;AAsBA,MAAM,oBAA8B;IAClC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;IAC1D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAGlD,MAAM,eAAe;IACrB,MAAM,aAAa;IACnB,MAAM,cAAc;IACpB,MAAM,0BAA0B;IAChC,MAAM,8BAA8B;IAEpC,MAAM,eAAe;QACnB;YAAE,MAAM;YAAgB,MAAM;YAAK,aAAa;QAAoB;QACpE;YAAE,MAAM;YAAY,MAAM;YAAK,aAAa;QAAkB;QAC9D;YAAE,MAAM;YAAY,MAAM;YAAK,aAAa;QAAY;KACzD;IAED,MAAM,kBAAkB;QACtB;YAAE,MAAM;YAAgB,MAAM;YAAK,aAAa;QAAmB;QACnE;YAAE,MAAM;YAAU,MAAM;YAAK,aAAa;QAAqB;QAC/D;YAAE,MAAM;YAAa,MAAM;YAAK,aAAa;QAAmB;KACjE;IAED,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,gBAAgB;YACpB,WAAW;YACX,SAAS;YACT,IAAI;gBACF,MAAM,WAAW,MAAM,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAC;gBACrC,YAAY,SAAS,IAAI;YAC3B,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,6BAA6B;gBAC3C,SAAS;YACX,SAAU;gBACR,WAAW;YACb;QACF;QACA;IACF,GAAG,EAAE;IAEL,MAAM,oBAAoB,CAAC,KAAa;QACtC,IAAI,CAAC,UAAU;YACb,OAAO;gBAAE;gBAAK;gBAAY,iBAAiB;gBAAG,aAAa;gBAAG,UAAU;gBAAG,WAAW;gBAAG,yBAAyB;gBAAG,QAAQ;YAAU;QACzI;QAEA,MAAM,kBAAkB,0BAA0B,CAAC,MAAM,GAAG;QAC5D,MAAM,oBAAoB,KAAK,GAAG,CAAC,GAAG,kBAAkB,SAAS,sBAAsB;QACvF,IAAI,cAAc;QAElB,IAAI,oBAAoB,GAAG;YACzB,MAAM,IAAI,aAAa,KAAK,2BAA2B;YACvD,MAAM,IAAI,8BAA8B,IAAI,kBAAkB;YAC9D,MAAM,KAAK,SAAS,sBAAsB,EAAE,kBAAkB;YAC9D,MAAM,KAAK,iBAAiB,iCAAiC;YAE7D,IAAI,IAAI,GAAG;gBACT,+EAA+E;gBAC/E,8CAA8C;gBAC9C,MAAM,YAAY,KAAK,GAAG,CAAC,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC;gBACrD,MAAM,cAAc,KAAK,GAAG,CAAC,IAAI;gBACjC,IAAI,gBAAgB,KAAK,AAAC,KAAK,IAAI,KAAM,KAAK,AAAC,KAAK,IAAI,KAAK,GAAI;oBAC7D,cAAc,IAAI,IAAI,oBAAoB,IAAI,UAAU,4CAA4C;gBACxG,OAAO;oBACH,cAAc,YAAY;gBAC9B;YAEF,OAAO,IAAI,IAAI,GAAG;gBAChB,cAAc,oBAAoB;YACpC,OAAO;gBACL,cAAc;YAChB;QACF;QAEA,cAAc,KAAK,GAAG,CAAC,GAAG,cAAc,qCAAqC;QAE7E,MAAM,WAAW,eAAe;QAChC,MAAM,YAAY,AAAC,eAAe,aAAc;QAEhD,IAAI,0BAA0B;QAC9B,MAAM,gBAAgB,cAAc;QACpC,IAAI,gBAAgB,GAAG;YACnB,MAAM,YAAY,aAAa,MAAM;YACrC,MAAM,WAAW,gBAAgB;YACjC,MAAM,KAAK,SAAS,sBAAsB;YAC1C,MAAM,YAAY;YAElB,IAAI,YAAY,GAAG;gBACf,MAAM,kBAAkB,KAAK,KAAK,GAAG,CAAC,IAAI,WAAW;gBACrD,MAAM,wBAAwB,YAAY;gBAC1C,IAAI,wBAAwB,GAAG;oBAC3B,0BAA0B,AAAC,wBAAwB,YAAa,CAAC,KAAK,GAAG,CAAC,IAAI,WAAW,YAAY,CAAC;gBAC1G,OAAO;oBACH,0BAA0B,GAAG,8DAA8D;gBAC/F;YACJ,OAAO,IAAI,WAAW,GAAG;gBACrB,MAAM,wBAAwB,YAAY;gBACzC,IAAI,wBAAwB,GAAG;oBAC5B,0BAA0B,wBAAwB;gBACtD,OAAO;oBACF,0BAA0B;gBAC/B;YACJ,OAAO;gBACH,0BAA0B,AAAC,YAAY,KAAM,WAAW,GAAG,uDAAuD;YACtH;QACJ;QACA,0BAA0B,KAAK,GAAG,CAAC,GAAG;QAGtC,IAAI,SAAyC;QAC7C,IAAI,WAAW,cAAc,KAAK,SAAS;aACtC,IAAI,WAAW,cAAc,KAAK,SAAS;QAEhD,OAAO;YAAE;YAAK;YAAY;YAAiB;YAAa;YAAU;YAAW;YAAyB;QAAO;IAC/G;IAEA,MAAM,0BAA0B,IAAsB,kBAAkB,WAAW;IAEnF,MAAM,iBAAiB,CAAC,QAAkB,IAAI,KAAK,YAAY,CAAC,SAAS;YAAE,OAAO;YAAY,UAAU;YAAO,uBAAuB;QAAE,GAAG,MAAM,CAAC;IAClJ,MAAM,aAAa,CAAC,OAAiB,AAAC,SAAS,SAAS,OAAO,IAAK,KAAK,OAAO,CAAC,KAAK;IACtF,MAAM,YAAY,CAAC,MAAgB,AAAC,SAAS,QAAQ,MAAM,IAAK,IAAI,OAAO,CAAC,KAAK;IAGjF,MAAM,iBAAiB,CAAC,SAAoB,CAAA;YAAE,OAAO;YAAW,QAAQ;YAAW,SAAS;QAAS,CAAA,CAAC,CAAC,OAAO,IAAI;IAClH,MAAM,gBAAgB,CAAC,SAAoB,CAAA;YAAE,OAAO;YAAa,QAAQ;YAAkB,SAAS;QAAY,CAAA,CAAC,CAAC,OAAO,IAAI;IAE7H,IAAI,SAAS,qBAAO,8OAAC;kBAAE;;;;;;IACvB,IAAI,SAAS,CAAC,UAAU,qBAAO,8OAAC,sLAAA,CAAA,QAAK;QAAC,SAAQ;kBAAU,SAAS;;;;;;IAEjE,MAAM,eAAiC,aAAa,OAAO,CAAC,CAAA,MAAO,gBAAgB,GAAG,CAAC,CAAA,MAAO,kBAAkB,IAAI,IAAI,EAAE,IAAI,IAAI;IAClI,MAAM,iBAAiB;IAEvB,MAAM,iBAAiB,aAAa,MAAM,CAAC,CAAA,IAAK,SAAS,EAAE,QAAQ;IACnE,MAAM,eAAe,eAAe,MAAM,GAAG,IAAI,eAAe,MAAM,CAAC,CAAC,MAAM,UAAY,QAAQ,QAAQ,GAAG,KAAK,QAAQ,GAAG,UAAU,QAAQ;IAC/I,MAAM,gBAAgB,eAAe,MAAM,GAAG,IAAI,eAAe,MAAM,CAAC,CAAC,OAAO,UAAY,QAAQ,QAAQ,GAAG,MAAM,QAAQ,GAAG,UAAU,SAAS;IAEnJ,qBACE,8OAAC;;0BACC,8OAAC;gBAAI,WAAU;0BAAyD,cAAA,8OAAC;8BAAG;;;;;;;;;;;0BAC5E,8OAAC,kLAAA,CAAA,MAAG;gBAAC,WAAU;;kCACb,8OAAC,kLAAA,CAAA,MAAG;wBAAC,IAAI;kCAAG,cAAA,8OAAC,oLAAA,CAAA,OAAI;4BAAC,WAAU;sCAAwB,cAAA,8OAAC,oLAAA,CAAA,OAAI,CAAC,IAAI;gCAAC,WAAU;;kDAAc,8OAAC;kDAAG;;;;;;kDAAsB,8OAAC;kDAAI,WAAW,aAAa,QAAQ;;;;;;kDAAO,8OAAC;;4CAAM;4CAAK,aAAa,GAAG;4CAAC;4CAAe,aAAa,UAAU;4CAAC;;;;;;;;;;;;;;;;;;;;;;;kCACjO,8OAAC,kLAAA,CAAA,MAAG;wBAAC,IAAI;kCAAG,cAAA,8OAAC,oLAAA,CAAA,OAAI;4BAAC,WAAU;sCAAwB,cAAA,8OAAC,oLAAA,CAAA,OAAI,CAAC,IAAI;gCAAC,WAAU;;kDAAc,8OAAC;kDAAG;;;;;;kDAAuB,8OAAC;kDAAI;;;;;;kDAAiB,8OAAC;kDAAM;;;;;;;;;;;;;;;;;;;;;;kCAC/I,8OAAC,kLAAA,CAAA,MAAG;wBAAC,IAAI;kCAAG,cAAA,8OAAC,oLAAA,CAAA,OAAI;4BAAC,WAAU;sCAAuB,cAAA,8OAAC,oLAAA,CAAA,OAAI,CAAC,IAAI;gCAAC,WAAU;;kDAAc,8OAAC;kDAAG;;;;;;kDAAkB,8OAAC;kDAAI,WAAW,cAAc,QAAQ;;;;;;kDAAO,8OAAC;;4CAAM;4CAAK,cAAc,GAAG;4CAAC;4CAAe,cAAc,UAAU;4CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAEjO,8OAAC,sLAAA,CAAA,QAAK;gBAAC,SAAQ;gBAAO,WAAU;;kCAAO,8OAAC,sLAAA,CAAA,QAAK,CAAC,OAAO;kCAAC;;;;;;kCAAkC,8OAAC,kLAAA,CAAA,MAAG;;0CACxF,8OAAC,kLAAA,CAAA,MAAG;gCAAC,IAAI;;kDAAG,8OAAC;kDAAO;;;;;;kDAA4B,8OAAC;;;;;oCAAM,eAAe,SAAS,sBAAsB;;;;;;;0CACrG,8OAAC,kLAAA,CAAA,MAAG;gCAAC,IAAI;;kDAAG,8OAAC;kDAAO;;;;;;kDAA8B,8OAAC;;;;;oCAAM,eAAe;;;;;;;0CACxE,8OAAC,kLAAA,CAAA,MAAG;gCAAC,IAAI;;kDAAG,8OAAC;kDAAO;;;;;;kDAAiC,8OAAC;;;;;oCAAM,eAAe;;;;;;;0CAC3E,8OAAC,kLAAA,CAAA,MAAG;gCAAC,IAAI;;kDAAG,8OAAC;kDAAO;;;;;;kDAA6B,8OAAC;;;;;oCAAM;oCAAY;oCAAQ,cAAc,eAAgB,CAAC,eAAe,UAAU;oCAAE;;;;;;;;;;;;;;;;;;;0BAE1I,8OAAC,oLAAA,CAAA,OAAI;gBAAC,WAAU;;kCAAO,8OAAC,oLAAA,CAAA,OAAI,CAAC,MAAM;kCAAC,cAAA,8OAAC;sCAAG;;;;;;;;;;;kCAA8C,8OAAC,oLAAA,CAAA,OAAI,CAAC,IAAI;;0CAC5F,8OAAC,kLAAA,CAAA,MAAG;;kDACF,8OAAC,kLAAA,CAAA,MAAG;wCAAC,IAAI;kDAAG,cAAA,8OAAC,oLAAA,CAAA,OAAI,CAAC,KAAK;4CAAC,WAAU;;8DAAO,8OAAC,oLAAA,CAAA,OAAI,CAAC,KAAK;;wDAAC;wDAAkC;wDAAU;;;;;;;8DAAc,8OAAC,oLAAA,CAAA,OAAI,CAAC,KAAK;oDAAC,KAAK;oDAAG,KAAK;oDAAG,MAAM;oDAAK,OAAO;oDAAW,UAAU,CAAC,IAAM,aAAa,WAAW,EAAE,MAAM,CAAC,KAAK;;;;;;8DAAM,8OAAC;oDAAI,WAAU;;sEAAiC,8OAAC;sEAAM;;;;;;sEAA8B,8OAAC;sEAAM;;;;;;;;;;;;;;;;;;;;;;;kDACjU,8OAAC,kLAAA,CAAA,MAAG;wCAAC,IAAI;kDAAG,cAAA,8OAAC,oLAAA,CAAA,OAAI,CAAC,KAAK;4CAAC,WAAU;;8DAAO,8OAAC,oLAAA,CAAA,OAAI,CAAC,KAAK;;wDAAC;wDAA4B;wDAAa;;;;;;;8DAAc,8OAAC,oLAAA,CAAA,OAAI,CAAC,KAAK;oDAAC,KAAK;oDAAG,KAAK;oDAAG,MAAM;oDAAK,OAAO;oDAAc,UAAU,CAAC,IAAM,gBAAgB,WAAW,EAAE,MAAM,CAAC,KAAK;;;;;;8DAAM,8OAAC;oDAAI,WAAU;;sEAAiC,8OAAC;sEAAM;;;;;;sEAA8B,8OAAC;sEAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAEtU,8OAAC,kLAAA,CAAA,MAAG;;kDACF,8OAAC,kLAAA,CAAA,MAAG;wCAAC,IAAI;kDAAG,cAAA,8OAAC;4CAAI,WAAU;;8DAAc,8OAAC;8DAAG;;;;;;8DAAmB,8OAAC;oDAAG,WAAU;8DAAgB,eAAe,eAAe,eAAe;;;;;;;;;;;;;;;;;kDAC3I,8OAAC,kLAAA,CAAA,MAAG;wCAAC,IAAI;kDAAG,cAAA,8OAAC;4CAAI,WAAU;;8DAAc,8OAAC;8DAAG;;;;;;8DAAc,8OAAC;oDAAG,WAAU;8DAAgB,WAAW,eAAe,QAAQ;;;;;;;;;;;;;;;;;kDAC3H,8OAAC,kLAAA,CAAA,MAAG;wCAAC,IAAI;kDAAG,cAAA,8OAAC;4CAAI,WAAU;;8DAAc,8OAAC;8DAAG;;;;;;8DAAa,8OAAC;oDAAG,WAAU;;wDAAa,UAAU,eAAe,SAAS;wDAAE;;;;;;;;;;;;;;;;;;kDACzH,8OAAC,kLAAA,CAAA,MAAG;wCAAC,IAAI;kDAAG,cAAA,8OAAC;4CAAI,WAAU;;8DAAc,8OAAC;;wDAAG;wDAAW;;;;;;;8DAAiB,8OAAC,sLAAA,CAAA,QAAK;oDAAC,IAAI,eAAe,eAAe,MAAM;oDAAG,WAAU;8DAAQ,cAAc,eAAe,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAGtL,8OAAC,oLAAA,CAAA,OAAI;gBAAC,WAAU;;kCAAO,8OAAC,oLAAA,CAAA,OAAI,CAAC,MAAM;kCAAC,cAAA,8OAAC;sCAAG;;;;;;;;;;;kCAAuD,8OAAC,oLAAA,CAAA,OAAI,CAAC,IAAI;;0CACrG,8OAAC,sLAAA,CAAA,QAAK;gCAAC,UAAU;gCAAC,QAAQ;gCAAC,WAAU;;kDACnC,8OAAC;kDAAM,cAAA,8OAAC;;8DAAG,8OAAC;8DAAG;;;;;;gDAAqB,gBAAgB,GAAG,CAAC,CAAA,oBAAQ,8OAAC;wDAAkB,WAAU;;4DAAY,IAAI,IAAI;0EAAC,8OAAC;;;;;0EAAK,8OAAC;;oEAAO,IAAI,IAAI;oEAAC;;;;;;;;uDAAhE,IAAI,IAAI;;;;;;;;;;;;;;;;kDACjF,8OAAC;kDAAO,aAAa,GAAG,CAAC,CAAA,oBAAQ,8OAAC;;kEAAkB,8OAAC;wDAAG,WAAU;;0EAAW,8OAAC;0EAAQ,IAAI,IAAI;;;;;;0EAAU,8OAAC;;;;;0EAAK,8OAAC;;oEAAO,IAAI,IAAI;oEAAC;;;;;;;;;;;;;oDAAe,gBAAgB,GAAG,CAAC,CAAA;wDAAS,MAAM,WAAW,aAAa,IAAI,CAAC,CAAA,IAAK,EAAE,GAAG,KAAK,IAAI,IAAI,IAAI,EAAE,UAAU,KAAK,IAAI,IAAI;wDAAG,qBAAQ,8OAAC;4DAAmC,WAAW,CAAC,MAAM,EAAE,eAAe,UAAU,UAAU,cAAc;;8EAAE,8OAAC;8EAAQ,WAAW,UAAU,YAAY;;;;;;8EAAY,8OAAC;;;;;8EAAK,8OAAC;;wEAAO,UAAU,UAAU,aAAa;wEAAG;;;;;;;;2DAA1M,GAAG,IAAI,IAAI,CAAC,CAAC,EAAE,IAAI,IAAI,EAAE;;;;;oDAAoM;;+CAAzc,IAAI,IAAI;;;;;;;;;;;;;;;;0CAEpD,8OAAC;gCAAI,WAAU;;kDAAO,8OAAC,sLAAA,CAAA,QAAK;wCAAC,IAAG;wCAAU,WAAU;;4CAAO;4CAAkB,cAAc;4CAAI;;;;;;;kDAAS,8OAAC,sLAAA,CAAA,QAAK;wCAAC,IAAG;wCAAU,WAAU;;4CAAO;4CAAiB,cAAc;4CAAI;4CAAE,cAAc;4CAAI;;;;;;;kDAAS,8OAAC,sLAAA,CAAA,QAAK;wCAAC,IAAG;;4CAAS;4CAAkB,cAAc;4CAAI;;;;;;;;;;;;;;;;;;;;;;;;;0BAExQ,8OAAC,oLAAA,CAAA,OAAI;;kCAAC,8OAAC,oLAAA,CAAA,OAAI,CAAC,MAAM;kCAAC,cAAA,8OAAC;sCAAG;;;;;;;;;;;kCAAqD,8OAAC,oLAAA,CAAA,OAAI,CAAC,IAAI;kCAClF,cAAA,8OAAC,sLAAA,CAAA,QAAK;4BAAC,UAAU;4BAAC,OAAO;4BAAC,KAAK;;8CAC7B,8OAAC;8CAAM,cAAA,8OAAC;;0DAAG,8OAAC;0DAAG;;;;;;0DAAQ,8OAAC;0DAAG;;;;;;0DAAc,8OAAC;0DAAG;;;;;;0DAAmB,8OAAC;0DAAG;;;;;;0DAAc,8OAAC;0DAAG;;;;;;0DAAa,8OAAC;0DAAG;;;;;;0DAAqB,8OAAC;;oDAAG;oDAA6B;;;;;;;0DAAiB,8OAAC;0DAAG;;;;;;;;;;;;;;;;;8CAClL,8OAAC;8CAAO,aAAa,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,QAAQ,GAAG,EAAE,QAAQ,EAAE,GAAG,CAAC,CAAC,UAAU,sBACxE,8OAAC;;8DAAe,8OAAC;;wDAAI,SAAS,GAAG;wDAAC;;;;;;;8DAAM,8OAAC;;wDAAI,SAAS,UAAU;wDAAC;;;;;;;8DAAM,8OAAC;8DAAI,eAAe,SAAS,eAAe;;;;;;8DAAO,8OAAC;8DAAG,cAAA,8OAAC;kEAAQ,WAAW,SAAS,QAAQ;;;;;;;;;;;8DAAgB,8OAAC;;wDAAI,UAAU,SAAS,SAAS;wDAAE;;;;;;;8DAAS,8OAAC;;wDAAI,WAAW,SAAS,WAAW;wDAAE;;;;;;;8DACnQ,8OAAC;8DAAI,SAAS,uBAAuB,GAAG,IAAI,eAAe,SAAS,uBAAuB,kBAAI,8OAAC;wDAAK,WAAU;kEAAe;;;;;;;;;;;8DAC9H,8OAAC;8DAAG,cAAA,8OAAC,sLAAA,CAAA,QAAK;wDAAC,IAAI,eAAe,SAAS,MAAM;kEAAI,cAAc,SAAS,MAAM;;;;;;;;;;;;2CAFvE;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOnB,8OAAC,sLAAA,CAAA,QAAK;gBAAC,SAAQ;gBAAU,WAAU;;kCAAO,8OAAC,sLAAA,CAAA,QAAK,CAAC,OAAO;kCAAC;;;;;;kCAA6C,8OAAC,kLAAA,CAAA,MAAG;;0CACtG,8OAAC,kLAAA,CAAA,MAAG;gCAAC,IAAI;;kDAAG,8OAAC;kDAAG;;;;;;kDAAwB,8OAAC;;0DAAG,8OAAC;;kEAAG,8OAAC;kEAAO;;;;;;oDAAiD;oDAAU,WAAW,aAAa,IAAI,CAAC,CAAA,IAAK,EAAE,GAAG,KAAK,OAAO,EAAE,UAAU,KAAK,MAAM,YAAY;;;;;;;0DAAkB,8OAAC;;kEAAG,8OAAC;kEAAO;;;;;;oDAAuB;oDAAW,WAAW,aAAa,QAAQ;oDAAE;oDAAW,aAAa,GAAG;oDAAC;oDAAgB,aAAa,UAAU;oDAAC;;;;;;;0DAAM,8OAAC;;kEAAG,8OAAC;kEAAO;;;;;;oDAAmB;oDAAiB,WAAW,cAAc,QAAQ;oDAAE;oDAAW,cAAc,GAAG;oDAAC;oDAAgB,cAAc,UAAU;oDAAC;;;;;;;;;;;;;;;;;;;0CAC9gB,8OAAC,kLAAA,CAAA,MAAG;gCAAC,IAAI;;kDAAG,8OAAC;kDAAG;;;;;;kDAAqC,8OAAC;;0DAAG,8OAAC;;kEAAG,8OAAC;kEAAO;;;;;;oDAAyB;;;;;;;0DAAyD,8OAAC;;kEAAG,8OAAC;kEAAO;;;;;;oDAAyB;oDAAwD,eAAe,0BAA0B;oDAAO;;;;;;;0DAAM,8OAAC;;kEAAG,8OAAC;kEAAO;;;;;;oDAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAItV;uCAEe", "debugId": null}}]}