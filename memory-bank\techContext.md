# Contexte Technique - Fire UI

## Technologies utilisées
- **Frontend (Original - `frontend/`)** :
  - React.js (v19)
  - TypeScript (via Create React App)
  - CSS, Bootstrap 5
- **Frontend (Nouveau - `frontnextjs/`)** :
  - Next.js (v15) utilisant React (v19)
  - TypeScript
  - App Router
  - Tailwind CSS (configuration de base)
  - Bootstrap 5 (intégration en cours, cohabitation avec Tailwind à gérer)
  - Styling : CSS Modules, Global CSS, Tailwind classes.
- **Backend (`backend/`)** :
  - Python avec **FastAPI**
  - SQLAlchemy pour l'ORM
  - SQLite comme base de données

## Configuration du développement
- **Frontend (Original - `frontend/`)** :
  - Basé sur Create React App.
  - `package.json` pour la gestion des dépendances.
- **Frontend (Nouveau - `frontnextjs/`)** :
  - Initialisé avec `create-next-app`.
  - `package.json` pour la gestion des dépendances.
  - `next.config.ts` pour la configuration spécifique à Next.js.
  - ESLint et Prettier (via la configuration Next.js) pour le linting et formatage.
- **Backend (`backend/`)** :
  - Virtualenv pour gérer les dépendances Python.
  - `requirements.txt` pour lister les bibliothèques nécessaires.

## Contraintes techniques
- Compatibilité entre le frontend Next.js et le backend FastAPI.
- Assurer la sécurité des données financières.
- Maintenir une bonne expérience utilisateur pendant et après la migration.

## Dépendances Clés
- **Frontend (Original - `frontend/`)** :
  - `react`, `react-dom`, `react-scripts`
  - `axios`, `bootstrap`, `chart.js`, `react-bootstrap`, `react-chartjs-2`
  - `@testing-library/react`
- **Frontend (Nouveau - `frontnextjs/`)** :
  - `next`, `react`, `react-dom`
  - `axios`, `bootstrap`, `chart.js`, `react-bootstrap`, `react-chartjs-2` (migrées depuis l'original)
  - `tailwindcss`
  - `@testing-library/react`, `@testing-library/jest-dom` (configurés avec Jest pour Next.js)
  - `jest`, `jest-environment-jsdom`
- **Backend (`backend/`)** :
  - `fastapi`, `uvicorn`, `sqlalchemy`, `pydantic`
  - `python-jose[cryptography]` pour la sécurité (si authentification JWT est ajoutée)
  - `requests` et `BeautifulSoup4` pour le web scraping des données SCPI.

## Configuration des outils
- **Frontend (Nouveau - `frontnextjs/`)**:
  - ESLint configuré par Next.js.
  - Tests unitaires et d'intégration avec Jest (configuration de base effectuée, premiers tests passent).
- **Backend (`backend/`)**:
  - Pas de linter ou formateur spécifié, mais des outils comme Black ou Flake8 pourraient être utilisés.
