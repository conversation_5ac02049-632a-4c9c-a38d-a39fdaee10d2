{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [], "dynamicRoutes": [], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/apple-icon.png", "regex": "^/apple\\-icon\\.png(?:/)?$", "routeKeys": {}, "namedRegex": "^/apple\\-icon\\.png(?:/)?$"}, {"page": "/assets", "regex": "^/assets(?:/)?$", "routeKeys": {}, "namedRegex": "^/assets(?:/)?$"}, {"page": "/budget", "regex": "^/budget(?:/)?$", "routeKeys": {}, "namedRegex": "^/budget(?:/)?$"}, {"page": "/evolution", "regex": "^/evolution(?:/)?$", "routeKeys": {}, "namedRegex": "^/evolution(?:/)?$"}, {"page": "/favicon.ico", "regex": "^/favicon\\.ico(?:/)?$", "routeKeys": {}, "namedRegex": "^/favicon\\.ico(?:/)?$"}, {"page": "/fire", "regex": "^/fire(?:/)?$", "routeKeys": {}, "namedRegex": "^/fire(?:/)?$"}, {"page": "/icon.png", "regex": "^/icon\\.png(?:/)?$", "routeKeys": {}, "namedRegex": "^/icon\\.png(?:/)?$"}, {"page": "/liabilities", "regex": "^/liabilities(?:/)?$", "routeKeys": {}, "namedRegex": "^/liabilities(?:/)?$"}, {"page": "/scenarios", "regex": "^/scenarios(?:/)?$", "routeKeys": {}, "namedRegex": "^/scenarios(?:/)?$"}, {"page": "/scpi", "regex": "^/scpi(?:/)?$", "routeKeys": {}, "namedRegex": "^/scpi(?:/)?$"}, {"page": "/test", "regex": "^/test(?:/)?$", "routeKeys": {}, "namedRegex": "^/test(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc", "prefetchSegmentHeader": "Next-Router-Segment-Prefetch", "prefetchSegmentSuffix": ".segment.rsc", "prefetchSegmentDirSuffix": ".segments"}, "rewriteHeaders": {"pathHeader": "x-nextjs-rewritten-path", "queryHeader": "x-nextjs-rewritten-query"}, "rewrites": []}