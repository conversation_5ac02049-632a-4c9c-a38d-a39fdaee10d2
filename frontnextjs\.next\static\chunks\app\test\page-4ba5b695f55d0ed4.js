(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[11],{2365:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let a=s(3464).A.create({baseURL:"http://localhost:8000/api",headers:{"Content-Type":"application/json"},timeout:1e4});a.interceptors.response.use(e=>e,e=>{var t,s;return console.error("API Error:",null==(t=e.response)?void 0:t.status,null==(s=e.config)?void 0:s.url,e.message),Promise.reject(e)});let r=a},3529:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n});var a=s(5155),r=s(2115),o=s(2365);let n=()=>{let[e,t]=(0,r.useState)(""),[s,n]=(0,r.useState)(!1),c=async()=>{n(!0),t("Testing with Axios apiClient...");try{let e=await o.A.get("/dashboard");t("Axios Success: ".concat(JSON.stringify(e.data,null,2)))}catch(o){var e,s,a,r,c,l;t("Axios Error: ".concat(o.message,"\nDetails: ").concat(JSON.stringify({status:null==(e=o.response)?void 0:e.status,statusText:null==(s=o.response)?void 0:s.statusText,data:null==(a=o.response)?void 0:a.data,config:{url:null==(r=o.config)?void 0:r.url,baseURL:null==(c=o.config)?void 0:c.baseURL,method:null==(l=o.config)?void 0:l.method}},null,2)))}finally{n(!1)}},l=async()=>{n(!0),t("Testing with fetch (http://localhost:8000/api/dashboard)...");try{let e=await fetch("http://localhost:8000/api/dashboard");if(!e.ok)throw Error("HTTP error! status: ".concat(e.status));let s=await e.json();t("Fetch (localhost) Success: ".concat(JSON.stringify(s,null,2)))}catch(e){t("Fetch (localhost) Error: ".concat(e.message))}finally{n(!1)}},i=async()=>{n(!0),t("Testing with fetch (http://127.0.0.1:8000/api/dashboard)...");try{let e=await fetch("http://127.0.0.1:8000/api/dashboard");if(!e.ok)throw Error("HTTP error! status: ".concat(e.status));let s=await e.json();t("Fetch (127.0.0.1) Success: ".concat(JSON.stringify(s,null,2)))}catch(e){t("Fetch (127.0.0.1) Error: ".concat(e.message))}finally{n(!1)}};return(0,a.jsxs)("div",{className:"container mt-4",children:[(0,a.jsx)("h2",{children:"Test des Appels API vers le Backend"}),(0,a.jsxs)("p",{children:["Cet outil permet de v\xe9rifier la connectivit\xe9 et les r\xe9ponses de base du backend FastAPI. Assurez-vous que le serveur backend (",(0,a.jsx)("code",{children:"cd backend && uvicorn main:app --reload"}),") est en cours d'ex\xe9cution sur ",(0,a.jsx)("code",{children:"http://localhost:8000"}),"."]}),(0,a.jsxs)("div",{className:"mb-3",children:[(0,a.jsx)("button",{className:"btn btn-primary me-2 mb-2",onClick:c,disabled:s,children:"Test /dashboard (via apiClient Axios)"}),(0,a.jsx)("button",{className:"btn btn-secondary me-2 mb-2",onClick:l,disabled:s,children:"Test /dashboard (via fetch direct sur localhost:8000)"}),(0,a.jsx)("button",{className:"btn btn-info mb-2",onClick:i,disabled:s,children:"Test /dashboard (via fetch direct sur 127.0.0.1:8000)"})]}),s&&(0,a.jsx)("p",{children:"Chargement du test..."}),(0,a.jsx)("pre",{className:"bg-light p-3 border rounded",style:{whiteSpace:"pre-wrap",wordBreak:"break-all"},children:e||"Cliquez sur un bouton pour tester l'API."})]})}},5544:(e,t,s)=>{Promise.resolve().then(s.bind(s,3529))}},e=>{var t=t=>e(e.s=t);e.O(0,[464,441,684,358],()=>t(5544)),_N_E=e.O()}]);