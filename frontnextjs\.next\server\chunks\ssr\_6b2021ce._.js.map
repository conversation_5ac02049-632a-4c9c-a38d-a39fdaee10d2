{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 36, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/AI-Code/fire_UI/frontnextjs/src/app/assets/AssetsClientPart.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const AssetsClientPart = registerClientReference(\n    function() { throw new Error(\"Attempted to call AssetsClientPart() from the server but AssetsClientPart is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/assets/AssetsClientPart.tsx <module evaluation>\",\n    \"AssetsClientPart\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,mBAAmB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,qEACA", "debugId": null}}, {"offset": {"line": 50, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/AI-Code/fire_UI/frontnextjs/src/app/assets/AssetsClientPart.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const AssetsClientPart = registerClientReference(\n    function() { throw new Error(\"Attempted to call AssetsClientPart() from the server but AssetsClientPart is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/assets/AssetsClientPart.tsx\",\n    \"AssetsClientPart\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,mBAAmB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,iDACA", "debugId": null}}, {"offset": {"line": 64, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 74, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/AI-Code/fire_UI/frontnextjs/src/app/assets/page.tsx"], "sourcesContent": ["import React from 'react'; // React pour JSX dans le Server Component\r\nimport { AssetsClientPart, Asset } from './AssetsClientPart'; // Importer le Client Component et le type Asset\r\n\r\n// --- Server Component (Page principale) ---\r\nasync function getAssetsSsr(): Promise<Asset[] | null> {\r\n  try {\r\n    const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000/api'}/assets`, {\r\n      cache: 'no-store' // Pour s'assurer que les données sont fraîches à chaque chargement\r\n    });\r\n    if (!response.ok) {\r\n      console.error(\"SSR Fetch Error (Assets):\", response.status, response.statusText);\r\n      const errorText = await response.text();\r\n      console.error(\"SSR Fetch Error Body (Assets):\", errorText);\r\n      return null;\r\n    }\r\n    const data = await response.json();\r\n    return data;\r\n  } catch (error) {\r\n    console.error(\"SSR Fetch Exception (Assets):\", error);\r\n    return null;\r\n  }\r\n}\r\n\r\nexport default async function AssetsPage() {\r\n  const initialAssets = await getAssetsSsr();\r\n  let fetchErrorOccurred;\r\n\r\n  if (initialAssets === null) {\r\n    fetchErrorOccurred = \"Erreur lors du chargement des actifs côté serveur.\";\r\n  }\r\n\r\n  return (\r\n    <AssetsClientPart\r\n      initialAssets={initialAssets || []}\r\n      fetchError={fetchErrorOccurred}\r\n    />\r\n  );\r\n}\r\n// --- Fin du Server Component ---\r\n"], "names": [], "mappings": ";;;;AACA,iPAA8D,gDAAgD;;;AAE9G,6CAA6C;AAC7C,eAAe;IACb,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,GAAG,CAAC,wBAAwB,IAAI,4BAA4B,OAAO,CAAC,EAAE;YAC5G,OAAO,WAAW,mEAAmE;QACvF;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,QAAQ,KAAK,CAAC,6BAA6B,SAAS,MAAM,EAAE,SAAS,UAAU;YAC/E,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,QAAQ,KAAK,CAAC,kCAAkC;YAChD,OAAO;QACT;QACA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,OAAO;IACT;AACF;AAEe,eAAe;IAC5B,MAAM,gBAAgB,MAAM;IAC5B,IAAI;IAEJ,IAAI,kBAAkB,MAAM;QAC1B,qBAAqB;IACvB;IAEA,qBACE,8OAAC,yIAAA,CAAA,mBAAgB;QACf,eAAe,iBAAiB,EAAE;QAClC,YAAY;;;;;;AAGlB,EACA,kCAAkC", "debugId": null}}]}