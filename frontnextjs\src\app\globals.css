@import "tailwindcss/base";
@import "tailwindcss/components";
@import "tailwindcss/utilities";

/* Bootstrap DOIT venir après Tailwind */
@import "bootstrap/dist/css/bootstrap.min.css";

/* Styles from frontend/src/index.css */
body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  /* The background and color from the default Next.js theme will be applied below if not overridden */
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* Styles from frontend/src/App.css */
.App {
  text-align: center;
}

/* Default theme variables and styles from create-next-app */
:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  /* These variables might be used by Tail<PERSON> if configured, or can be used directly */
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans); /* Assuming Geist font variables are still desired/available */
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

/* Applying default background and color after custom body styles from index.css,
   allowing them to take precedence if not specified in the index.css body rules.
   The font-family from index.css will take precedence over the Arial default here. */
body {
  background: var(--background);
  color: var(--foreground);
  /* font-family: Arial, Helvetica, sans-serif; */ /* This is commented out to prefer font stack from index.css */
}
