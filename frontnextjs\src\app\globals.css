/* Bootstrap import doit être en premier */
@import "bootstrap/dist/css/bootstrap.min.css";

/* <PERSON><PERSON>s <PERSON> */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Styles from frontend/src/index.css */
body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  /* The background and color from the default Next.js theme will be applied below if not overridden */
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* Styles from frontend/src/App.css */
.App {
  text-align: center;
}

/* Force light theme - override dark mode */
:root {
  --background: #ffffff;
  --foreground: #212529;
}

/* Disable dark mode completely */
@media (prefers-color-scheme: dark) {
  :root {
    --background: #ffffff;
    --foreground: #212529;
  }
}

/* Apply light theme to body */
body {
  background: var(--background) !important;
  color: var(--foreground) !important;
}

/* Ensure navbar is properly styled */
.navbar {
  z-index: 1030;
}

/* Force navbar links to display on large screens */
@media (min-width: 992px) {
  .navbar-collapse {
    display: block !important;
  }

  /* Force navbar links to align left and display horizontally */
  .navbar-nav.flex-row {
    flex-direction: row !important;
  }

  .navbar-nav.flex-row .nav-item {
    margin-right: 1rem;
  }

  .navbar-nav.flex-row .nav-link {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }
}

/* Ensure main content has proper spacing */
main.container {
  background: var(--background);
  min-height: calc(100vh - 56px); /* Account for navbar height */
}
