[{"D:\\AI-Code\\fire_UI\\frontnextjs\\src\\app\\assets\\AssetsClientPart.tsx": "1", "D:\\AI-Code\\fire_UI\\frontnextjs\\src\\app\\assets\\page.tsx": "2", "D:\\AI-Code\\fire_UI\\frontnextjs\\src\\app\\budget\\page.tsx": "3", "D:\\AI-Code\\fire_UI\\frontnextjs\\src\\app\\evolution\\page.tsx": "4", "D:\\AI-Code\\fire_UI\\frontnextjs\\src\\app\\fire\\page.tsx": "5", "D:\\AI-Code\\fire_UI\\frontnextjs\\src\\app\\layout.tsx": "6", "D:\\AI-Code\\fire_UI\\frontnextjs\\src\\app\\liabilities\\page.tsx": "7", "D:\\AI-Code\\fire_UI\\frontnextjs\\src\\app\\page.tsx": "8", "D:\\AI-Code\\fire_UI\\frontnextjs\\src\\app\\scenarios\\page.tsx": "9", "D:\\AI-Code\\fire_UI\\frontnextjs\\src\\app\\scpi\\page.tsx": "10", "D:\\AI-Code\\fire_UI\\frontnextjs\\src\\app\\test\\page.tsx": "11", "D:\\AI-Code\\fire_UI\\frontnextjs\\src\\app\\__tests__\\HomePage.test.tsx": "12", "D:\\AI-Code\\fire_UI\\frontnextjs\\src\\components\\ApiClient.ts": "13", "D:\\AI-Code\\fire_UI\\frontnextjs\\src\\components\\ApiTest.tsx": "14", "D:\\AI-Code\\fire_UI\\frontnextjs\\src\\components\\Assets.tsx": "15", "D:\\AI-Code\\fire_UI\\frontnextjs\\src\\components\\BudgetFire.tsx": "16", "D:\\AI-Code\\fire_UI\\frontnextjs\\src\\components\\Dashboard.tsx": "17", "D:\\AI-Code\\fire_UI\\frontnextjs\\src\\components\\EvolutionAnnuelle.tsx": "18", "D:\\AI-Code\\fire_UI\\frontnextjs\\src\\components\\EvolutionChart.tsx": "19", "D:\\AI-Code\\fire_UI\\frontnextjs\\src\\components\\FireTarget.tsx": "20", "D:\\AI-Code\\fire_UI\\frontnextjs\\src\\components\\Liabilities.tsx": "21", "D:\\AI-Code\\fire_UI\\frontnextjs\\src\\components\\Navbar.tsx": "22", "D:\\AI-Code\\fire_UI\\frontnextjs\\src\\components\\ScenariosFire.tsx": "23", "D:\\AI-Code\\fire_UI\\frontnextjs\\src\\components\\SCPI.tsx": "24", "D:\\AI-Code\\fire_UI\\frontnextjs\\src\\setupTests.ts": "25"}, {"size": 11048, "mtime": 1752150087399, "results": "26", "hashOfConfig": "27"}, {"size": 1359, "mtime": 1752129467063, "results": "28", "hashOfConfig": "27"}, {"size": 11099, "mtime": 1752150027692, "results": "29", "hashOfConfig": "27"}, {"size": 13732, "mtime": 1752150194510, "results": "30", "hashOfConfig": "27"}, {"size": 17666, "mtime": 1752150267850, "results": "31", "hashOfConfig": "27"}, {"size": 933, "mtime": 1752149134423, "results": "32", "hashOfConfig": "27"}, {"size": 9104, "mtime": 1752150424621, "results": "33", "hashOfConfig": "27"}, {"size": 19380, "mtime": 1752150506895, "results": "34", "hashOfConfig": "27"}, {"size": 14015, "mtime": 1752150541891, "results": "35", "hashOfConfig": "27"}, {"size": 13277, "mtime": 1752150583242, "results": "36", "hashOfConfig": "27"}, {"size": 3565, "mtime": 1752150646755, "results": "37", "hashOfConfig": "27"}, {"size": 5709, "mtime": 1752129467062, "results": "38", "hashOfConfig": "27"}, {"size": 507, "mtime": 1752061409413, "results": "39", "hashOfConfig": "27"}, {"size": 2714, "mtime": 1752061409413, "results": "40", "hashOfConfig": "27"}, {"size": 13831, "mtime": 1752061409413, "results": "41", "hashOfConfig": "27"}, {"size": 12789, "mtime": 1752061409415, "results": "42", "hashOfConfig": "27"}, {"size": 25475, "mtime": 1752061409415, "results": "43", "hashOfConfig": "27"}, {"size": 15836, "mtime": 1752061409415, "results": "44", "hashOfConfig": "27"}, {"size": 3939, "mtime": 1752070119360, "results": "45", "hashOfConfig": "27"}, {"size": 22118, "mtime": 1752061409415, "results": "46", "hashOfConfig": "27"}, {"size": 7245, "mtime": 1752061409415, "results": "47", "hashOfConfig": "27"}, {"size": 2841, "mtime": 1752149402120, "results": "48", "hashOfConfig": "27"}, {"size": 16441, "mtime": 1752061409415, "results": "49", "hashOfConfig": "27"}, {"size": 13338, "mtime": 1752061409415, "results": "50", "hashOfConfig": "27"}, {"size": 246, "mtime": 1752061409415, "results": "51", "hashOfConfig": "27"}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "15fn66z", {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\AI-Code\\fire_UI\\frontnextjs\\src\\app\\assets\\AssetsClientPart.tsx", [], [], "D:\\AI-Code\\fire_UI\\frontnextjs\\src\\app\\assets\\page.tsx", [], [], "D:\\AI-Code\\fire_UI\\frontnextjs\\src\\app\\budget\\page.tsx", [], ["127"], "D:\\AI-Code\\fire_UI\\frontnextjs\\src\\app\\evolution\\page.tsx", [], ["128"], "D:\\AI-Code\\fire_UI\\frontnextjs\\src\\app\\fire\\page.tsx", [], ["129"], "D:\\AI-Code\\fire_UI\\frontnextjs\\src\\app\\layout.tsx", [], [], "D:\\AI-Code\\fire_UI\\frontnextjs\\src\\app\\liabilities\\page.tsx", [], ["130"], "D:\\AI-Code\\fire_UI\\frontnextjs\\src\\app\\page.tsx", [], ["131", "132"], "D:\\AI-Code\\fire_UI\\frontnextjs\\src\\app\\scenarios\\page.tsx", [], [], "D:\\AI-Code\\fire_UI\\frontnextjs\\src\\app\\scpi\\page.tsx", [], ["133"], "D:\\AI-Code\\fire_UI\\frontnextjs\\src\\app\\test\\page.tsx", [], [], "D:\\AI-Code\\fire_UI\\frontnextjs\\src\\app\\__tests__\\HomePage.test.tsx", [], [], "D:\\AI-Code\\fire_UI\\frontnextjs\\src\\components\\ApiClient.ts", [], [], "D:\\AI-Code\\fire_UI\\frontnextjs\\src\\components\\ApiTest.tsx", ["134", "135", "136"], [], "D:\\AI-Code\\fire_UI\\frontnextjs\\src\\components\\Assets.tsx", ["137", "138", "139", "140", "141"], [], "D:\\AI-Code\\fire_UI\\frontnextjs\\src\\components\\BudgetFire.tsx", ["142", "143", "144", "145", "146", "147"], [], "D:\\AI-Code\\fire_UI\\frontnextjs\\src\\components\\Dashboard.tsx", ["148", "149", "150", "151", "152"], ["153"], "D:\\AI-Code\\fire_UI\\frontnextjs\\src\\components\\EvolutionAnnuelle.tsx", ["154", "155", "156", "157", "158", "159", "160", "161", "162", "163", "164"], [], "D:\\AI-Code\\fire_UI\\frontnextjs\\src\\components\\EvolutionChart.tsx", ["165", "166"], [], "D:\\AI-Code\\fire_UI\\frontnextjs\\src\\components\\FireTarget.tsx", ["167", "168", "169", "170"], [], "D:\\AI-Code\\fire_UI\\frontnextjs\\src\\components\\Liabilities.tsx", ["171", "172"], [], "D:\\AI-Code\\fire_UI\\frontnextjs\\src\\components\\Navbar.tsx", [], [], "D:\\AI-Code\\fire_UI\\frontnextjs\\src\\components\\ScenariosFire.tsx", ["173", "174", "175", "176"], [], "D:\\AI-Code\\fire_UI\\frontnextjs\\src\\components\\SCPI.tsx", ["177", "178", "179", "180"], [], "D:\\AI-Code\\fire_UI\\frontnextjs\\src\\setupTests.ts", [], [], {"ruleId": "181", "severity": 1, "message": "182", "line": 104, "column": 6, "nodeType": "183", "endLine": 104, "endColumn": 8, "suggestions": "184", "suppressions": "185"}, {"ruleId": "181", "severity": 1, "message": "186", "line": 126, "column": 6, "nodeType": "183", "endLine": 126, "endColumn": 8, "suggestions": "187", "suppressions": "188"}, {"ruleId": "181", "severity": 1, "message": "189", "line": 181, "column": 6, "nodeType": "183", "endLine": 181, "endColumn": 8, "suggestions": "190", "suppressions": "191"}, {"ruleId": "181", "severity": 1, "message": "192", "line": 122, "column": 6, "nodeType": "183", "endLine": 122, "endColumn": 8, "suggestions": "193", "suppressions": "194"}, {"ruleId": "181", "severity": 1, "message": "195", "line": 88, "column": 6, "nodeType": "183", "endLine": 88, "endColumn": 8, "suggestions": "196", "suppressions": "197"}, {"ruleId": "181", "severity": 1, "message": "198", "line": 114, "column": 6, "nodeType": "183", "endLine": 114, "endColumn": 38, "suggestions": "199", "suppressions": "200"}, {"ruleId": "181", "severity": 1, "message": "201", "line": 152, "column": 6, "nodeType": "183", "endLine": 152, "endColumn": 8, "suggestions": "202", "suppressions": "203"}, {"ruleId": "204", "severity": 1, "message": "205", "line": 15, "column": 21, "nodeType": "206", "messageId": "207", "endLine": 15, "endColumn": 24, "suggestions": "208"}, {"ruleId": "204", "severity": 1, "message": "205", "line": 39, "column": 21, "nodeType": "206", "messageId": "207", "endLine": 39, "endColumn": 24, "suggestions": "209"}, {"ruleId": "204", "severity": 1, "message": "205", "line": 54, "column": 21, "nodeType": "206", "messageId": "207", "endLine": 54, "endColumn": 24, "suggestions": "210"}, {"ruleId": "204", "severity": 1, "message": "205", "line": 23, "column": 16, "nodeType": "206", "messageId": "207", "endLine": 23, "endColumn": 19, "suggestions": "211"}, {"ruleId": "204", "severity": 1, "message": "205", "line": 100, "column": 72, "nodeType": "206", "messageId": "207", "endLine": 100, "endColumn": 75, "suggestions": "212"}, {"ruleId": "204", "severity": 1, "message": "205", "line": 127, "column": 25, "nodeType": "206", "messageId": "207", "endLine": 127, "endColumn": 28, "suggestions": "213"}, {"ruleId": "181", "severity": 1, "message": "214", "line": 214, "column": 6, "nodeType": "183", "endLine": 214, "endColumn": 8, "suggestions": "215"}, {"ruleId": "216", "severity": 1, "message": "217", "line": 225, "column": 9, "nodeType": "218", "messageId": "219", "endLine": 225, "endColumn": 22, "fix": "220"}, {"ruleId": "221", "severity": 1, "message": "222", "line": 3, "column": 38, "nodeType": null, "messageId": "223", "endLine": 3, "endColumn": 49}, {"ruleId": "204", "severity": 1, "message": "205", "line": 43, "column": 21, "nodeType": "206", "messageId": "207", "endLine": 43, "endColumn": 24, "suggestions": "224"}, {"ruleId": "225", "severity": 1, "message": "226", "line": 85, "column": 28, "nodeType": "227", "messageId": "228", "suggestions": "229"}, {"ruleId": "204", "severity": 1, "message": "205", "line": 131, "column": 21, "nodeType": "206", "messageId": "207", "endLine": 131, "endColumn": 24, "suggestions": "230"}, {"ruleId": "181", "severity": 1, "message": "182", "line": 146, "column": 6, "nodeType": "183", "endLine": 146, "endColumn": 8, "suggestions": "231"}, {"ruleId": "204", "severity": 1, "message": "205", "line": 159, "column": 23, "nodeType": "206", "messageId": "207", "endLine": 159, "endColumn": 26, "suggestions": "232"}, {"ruleId": "181", "severity": 1, "message": "195", "line": 96, "column": 6, "nodeType": "183", "endLine": 96, "endColumn": 8, "suggestions": "233"}, {"ruleId": "225", "severity": 1, "message": "226", "line": 434, "column": 35, "nodeType": "227", "messageId": "228", "suggestions": "234"}, {"ruleId": "225", "severity": 1, "message": "226", "line": 443, "column": 38, "nodeType": "227", "messageId": "228", "suggestions": "235"}, {"ruleId": "221", "severity": 1, "message": "236", "line": 453, "column": 55, "nodeType": null, "messageId": "223", "endLine": 453, "endColumn": 60}, {"ruleId": "225", "severity": 1, "message": "226", "line": 556, "column": 36, "nodeType": "227", "messageId": "228", "suggestions": "237"}, {"ruleId": "181", "severity": 1, "message": "238", "line": 159, "column": 6, "nodeType": "183", "endLine": 159, "endColumn": 12, "suggestions": "239", "suppressions": "240"}, {"ruleId": "204", "severity": 1, "message": "205", "line": 46, "column": 21, "nodeType": "206", "messageId": "207", "endLine": 46, "endColumn": 24, "suggestions": "241"}, {"ruleId": "225", "severity": 1, "message": "226", "line": 124, "column": 26, "nodeType": "227", "messageId": "228", "suggestions": "242"}, {"ruleId": "181", "severity": 1, "message": "186", "line": 196, "column": 6, "nodeType": "183", "endLine": 196, "endColumn": 8, "suggestions": "243"}, {"ruleId": "225", "severity": 1, "message": "226", "line": 360, "column": 39, "nodeType": "227", "messageId": "228", "suggestions": "244"}, {"ruleId": "225", "severity": 1, "message": "226", "line": 366, "column": 89, "nodeType": "227", "messageId": "228", "suggestions": "245"}, {"ruleId": "225", "severity": 1, "message": "226", "line": 372, "column": 99, "nodeType": "227", "messageId": "228", "suggestions": "246"}, {"ruleId": "225", "severity": 1, "message": "226", "line": 374, "column": 29, "nodeType": "227", "messageId": "228", "suggestions": "247"}, {"ruleId": "225", "severity": 1, "message": "226", "line": 379, "column": 25, "nodeType": "227", "messageId": "228", "suggestions": "248"}, {"ruleId": "225", "severity": 1, "message": "226", "line": 379, "column": 110, "nodeType": "227", "messageId": "228", "suggestions": "249"}, {"ruleId": "225", "severity": 1, "message": "226", "line": 386, "column": 49, "nodeType": "227", "messageId": "228", "suggestions": "250"}, {"ruleId": "225", "severity": 1, "message": "226", "line": 386, "column": 109, "nodeType": "227", "messageId": "228", "suggestions": "251"}, {"ruleId": "204", "severity": 1, "message": "205", "line": 113, "column": 36, "nodeType": "206", "messageId": "207", "endLine": 113, "endColumn": 39, "suggestions": "252"}, {"ruleId": "204", "severity": 1, "message": "205", "line": 137, "column": 45, "nodeType": "206", "messageId": "207", "endLine": 137, "endColumn": 48, "suggestions": "253"}, {"ruleId": "221", "severity": 1, "message": "254", "line": 16, "column": 11, "nodeType": null, "messageId": "223", "endLine": 16, "endColumn": 23}, {"ruleId": "204", "severity": 1, "message": "205", "line": 68, "column": 21, "nodeType": "206", "messageId": "207", "endLine": 68, "endColumn": 24, "suggestions": "255"}, {"ruleId": "181", "severity": 1, "message": "189", "line": 455, "column": 6, "nodeType": "183", "endLine": 455, "endColumn": 8, "suggestions": "256"}, {"ruleId": "225", "severity": 1, "message": "226", "line": 484, "column": 21, "nodeType": "227", "messageId": "228", "suggestions": "257"}, {"ruleId": "225", "severity": 1, "message": "226", "line": 53, "column": 35, "nodeType": "227", "messageId": "228", "suggestions": "258"}, {"ruleId": "181", "severity": 1, "message": "192", "line": 100, "column": 6, "nodeType": "183", "endLine": 100, "endColumn": 8, "suggestions": "259"}, {"ruleId": "221", "severity": 1, "message": "260", "line": 31, "column": 9, "nodeType": null, "messageId": "223", "endLine": 31, "endColumn": 28}, {"ruleId": "225", "severity": 1, "message": "226", "line": 429, "column": 29, "nodeType": "227", "messageId": "228", "suggestions": "261"}, {"ruleId": "225", "severity": 1, "message": "226", "line": 438, "column": 57, "nodeType": "227", "messageId": "228", "suggestions": "262"}, {"ruleId": "225", "severity": 1, "message": "226", "line": 438, "column": 70, "nodeType": "227", "messageId": "228", "suggestions": "263"}, {"ruleId": "181", "severity": 1, "message": "264", "line": 51, "column": 8, "nodeType": "183", "endLine": 51, "endColumn": 61, "suggestions": "265"}, {"ruleId": "204", "severity": 1, "message": "205", "line": 64, "column": 25, "nodeType": "206", "messageId": "207", "endLine": 64, "endColumn": 28, "suggestions": "266"}, {"ruleId": "181", "severity": 1, "message": "201", "line": 187, "column": 6, "nodeType": "183", "endLine": 187, "endColumn": 8, "suggestions": "267"}, {"ruleId": "204", "severity": 1, "message": "205", "line": 222, "column": 19, "nodeType": "206", "messageId": "207", "endLine": 222, "endColumn": 22, "suggestions": "268"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchData'. Either include it or remove the dependency array.", "ArrayExpression", ["269"], ["270"], "React Hook useEffect has a missing dependency: 'fetchEvolutions'. Either include it or remove the dependency array.", ["271"], ["272"], "React Hook useEffect has a missing dependency: 'fetchFireTargetData'. Either include it or remove the dependency array.", ["273"], ["274"], "React Hook useEffect has a missing dependency: 'fetchLiabilities'. Either include it or remove the dependency array.", ["275"], ["276"], "React Hook useEffect has a missing dependency: 'fetchAllData'. Either include it or remove the dependency array.", ["277"], ["278"], "React Hook useEffect has missing dependencies: 'editableAllocations' and 'processAllocations'. Either include them or remove the dependency array.", ["279"], ["280"], "React Hook useEffect has a missing dependency: 'fetchSCPIs'. Either include it or remove the dependency array.", ["281"], ["282"], "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["283", "284"], ["285", "286"], ["287", "288"], ["289", "290"], ["291", "292"], ["293", "294"], "React Hook useEffect has a missing dependency: 'fetchAssets'. Either include it or remove the dependency array.", ["295"], "prefer-const", "'sortableItems' is never reassigned. Use 'const' instead.", "Identifier", "useConst", {"range": "296", "text": "297"}, "@typescript-eslint/no-unused-vars", "'ProgressBar' is defined but never used.", "unusedVar", ["298", "299"], "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["300", "301", "302", "303"], ["304", "305"], ["306"], ["307", "308"], ["309"], ["310", "311", "312", "313"], ["314", "315", "316", "317"], "'index' is defined but never used.", ["318", "319", "320", "321"], "React Hook useEffect has missing dependencies: 'editableAllocations', 'initialTargetPercentages', and 'processAllocations'. Either include them or remove the dependency array.", ["322"], ["323"], ["324", "325"], ["326", "327", "328", "329"], ["330"], ["331", "332", "333", "334"], ["335", "336", "337", "338"], ["339", "340", "341", "342"], ["343", "344", "345", "346"], ["347", "348", "349", "350"], ["351", "352", "353", "354"], ["355", "356", "357", "358"], ["359", "360", "361", "362"], ["363", "364"], ["365", "366"], "'FireSettings' is defined but never used.", ["367", "368"], ["369"], ["370", "371", "372", "373"], ["374", "375", "376", "377"], ["378"], "'ANNUAL_NET_EXPENSES' is assigned a value but never used.", ["379", "380", "381", "382"], ["383", "384", "385", "386"], ["387", "388", "389", "390"], "React Hook useEffect has a missing dependency: 'formData.total_value'. Either include it or remove the dependency array.", ["391"], ["392", "393"], ["394"], ["395", "396"], {"desc": "397", "fix": "398"}, {"kind": "399", "justification": "400"}, {"desc": "401", "fix": "402"}, {"kind": "399", "justification": "400"}, {"desc": "403", "fix": "404"}, {"kind": "399", "justification": "400"}, {"desc": "405", "fix": "406"}, {"kind": "399", "justification": "400"}, {"desc": "407", "fix": "408"}, {"kind": "399", "justification": "400"}, {"desc": "409", "fix": "410"}, {"kind": "399", "justification": "400"}, {"desc": "411", "fix": "412"}, {"kind": "399", "justification": "400"}, {"messageId": "413", "fix": "414", "desc": "415"}, {"messageId": "416", "fix": "417", "desc": "418"}, {"messageId": "413", "fix": "419", "desc": "415"}, {"messageId": "416", "fix": "420", "desc": "418"}, {"messageId": "413", "fix": "421", "desc": "415"}, {"messageId": "416", "fix": "422", "desc": "418"}, {"messageId": "413", "fix": "423", "desc": "415"}, {"messageId": "416", "fix": "424", "desc": "418"}, {"messageId": "413", "fix": "425", "desc": "415"}, {"messageId": "416", "fix": "426", "desc": "418"}, {"messageId": "413", "fix": "427", "desc": "415"}, {"messageId": "416", "fix": "428", "desc": "418"}, {"desc": "429", "fix": "430"}, [9587, 9619], "const sortableItems = [...assets];", {"messageId": "413", "fix": "431", "desc": "415"}, {"messageId": "416", "fix": "432", "desc": "418"}, {"messageId": "433", "data": "434", "fix": "435", "desc": "436"}, {"messageId": "433", "data": "437", "fix": "438", "desc": "439"}, {"messageId": "433", "data": "440", "fix": "441", "desc": "442"}, {"messageId": "433", "data": "443", "fix": "444", "desc": "445"}, {"messageId": "413", "fix": "446", "desc": "415"}, {"messageId": "416", "fix": "447", "desc": "418"}, {"desc": "397", "fix": "448"}, {"messageId": "413", "fix": "449", "desc": "415"}, {"messageId": "416", "fix": "450", "desc": "418"}, {"desc": "407", "fix": "451"}, {"messageId": "433", "data": "452", "fix": "453", "desc": "436"}, {"messageId": "433", "data": "454", "fix": "455", "desc": "439"}, {"messageId": "433", "data": "456", "fix": "457", "desc": "442"}, {"messageId": "433", "data": "458", "fix": "459", "desc": "445"}, {"messageId": "433", "data": "460", "fix": "461", "desc": "436"}, {"messageId": "433", "data": "462", "fix": "463", "desc": "439"}, {"messageId": "433", "data": "464", "fix": "465", "desc": "442"}, {"messageId": "433", "data": "466", "fix": "467", "desc": "445"}, {"messageId": "433", "data": "468", "fix": "469", "desc": "436"}, {"messageId": "433", "data": "470", "fix": "471", "desc": "439"}, {"messageId": "433", "data": "472", "fix": "473", "desc": "442"}, {"messageId": "433", "data": "474", "fix": "475", "desc": "445"}, {"desc": "409", "fix": "476"}, {"kind": "399", "justification": "400"}, {"messageId": "413", "fix": "477", "desc": "415"}, {"messageId": "416", "fix": "478", "desc": "418"}, {"messageId": "433", "data": "479", "fix": "480", "desc": "436"}, {"messageId": "433", "data": "481", "fix": "482", "desc": "439"}, {"messageId": "433", "data": "483", "fix": "484", "desc": "442"}, {"messageId": "433", "data": "485", "fix": "486", "desc": "445"}, {"desc": "401", "fix": "487"}, {"messageId": "433", "data": "488", "fix": "489", "desc": "436"}, {"messageId": "433", "data": "490", "fix": "491", "desc": "439"}, {"messageId": "433", "data": "492", "fix": "493", "desc": "442"}, {"messageId": "433", "data": "494", "fix": "495", "desc": "445"}, {"messageId": "433", "data": "496", "fix": "497", "desc": "436"}, {"messageId": "433", "data": "498", "fix": "499", "desc": "439"}, {"messageId": "433", "data": "500", "fix": "501", "desc": "442"}, {"messageId": "433", "data": "502", "fix": "503", "desc": "445"}, {"messageId": "433", "data": "504", "fix": "505", "desc": "436"}, {"messageId": "433", "data": "506", "fix": "507", "desc": "439"}, {"messageId": "433", "data": "508", "fix": "509", "desc": "442"}, {"messageId": "433", "data": "510", "fix": "511", "desc": "445"}, {"messageId": "433", "data": "512", "fix": "513", "desc": "436"}, {"messageId": "433", "data": "514", "fix": "515", "desc": "439"}, {"messageId": "433", "data": "516", "fix": "517", "desc": "442"}, {"messageId": "433", "data": "518", "fix": "519", "desc": "445"}, {"messageId": "433", "data": "520", "fix": "521", "desc": "436"}, {"messageId": "433", "data": "522", "fix": "523", "desc": "439"}, {"messageId": "433", "data": "524", "fix": "525", "desc": "442"}, {"messageId": "433", "data": "526", "fix": "527", "desc": "445"}, {"messageId": "433", "data": "528", "fix": "529", "desc": "436"}, {"messageId": "433", "data": "530", "fix": "531", "desc": "439"}, {"messageId": "433", "data": "532", "fix": "533", "desc": "442"}, {"messageId": "433", "data": "534", "fix": "535", "desc": "445"}, {"messageId": "433", "data": "536", "fix": "537", "desc": "436"}, {"messageId": "433", "data": "538", "fix": "539", "desc": "439"}, {"messageId": "433", "data": "540", "fix": "541", "desc": "442"}, {"messageId": "433", "data": "542", "fix": "543", "desc": "445"}, {"messageId": "433", "data": "544", "fix": "545", "desc": "436"}, {"messageId": "433", "data": "546", "fix": "547", "desc": "439"}, {"messageId": "433", "data": "548", "fix": "549", "desc": "442"}, {"messageId": "433", "data": "550", "fix": "551", "desc": "445"}, {"messageId": "413", "fix": "552", "desc": "415"}, {"messageId": "416", "fix": "553", "desc": "418"}, {"messageId": "413", "fix": "554", "desc": "415"}, {"messageId": "416", "fix": "555", "desc": "418"}, {"messageId": "413", "fix": "556", "desc": "415"}, {"messageId": "416", "fix": "557", "desc": "418"}, {"desc": "403", "fix": "558"}, {"messageId": "433", "data": "559", "fix": "560", "desc": "436"}, {"messageId": "433", "data": "561", "fix": "562", "desc": "439"}, {"messageId": "433", "data": "563", "fix": "564", "desc": "442"}, {"messageId": "433", "data": "565", "fix": "566", "desc": "445"}, {"messageId": "433", "data": "567", "fix": "568", "desc": "436"}, {"messageId": "433", "data": "569", "fix": "570", "desc": "439"}, {"messageId": "433", "data": "571", "fix": "572", "desc": "442"}, {"messageId": "433", "data": "573", "fix": "574", "desc": "445"}, {"desc": "405", "fix": "575"}, {"messageId": "433", "data": "576", "fix": "577", "desc": "436"}, {"messageId": "433", "data": "578", "fix": "579", "desc": "439"}, {"messageId": "433", "data": "580", "fix": "581", "desc": "442"}, {"messageId": "433", "data": "582", "fix": "583", "desc": "445"}, {"messageId": "433", "data": "584", "fix": "585", "desc": "436"}, {"messageId": "433", "data": "586", "fix": "587", "desc": "439"}, {"messageId": "433", "data": "588", "fix": "589", "desc": "442"}, {"messageId": "433", "data": "590", "fix": "591", "desc": "445"}, {"messageId": "433", "data": "592", "fix": "593", "desc": "436"}, {"messageId": "433", "data": "594", "fix": "595", "desc": "439"}, {"messageId": "433", "data": "596", "fix": "597", "desc": "442"}, {"messageId": "433", "data": "598", "fix": "599", "desc": "445"}, {"desc": "600", "fix": "601"}, {"messageId": "413", "fix": "602", "desc": "415"}, {"messageId": "416", "fix": "603", "desc": "418"}, {"desc": "411", "fix": "604"}, {"messageId": "413", "fix": "605", "desc": "415"}, {"messageId": "416", "fix": "606", "desc": "418"}, "Update the dependencies array to be: [fetchData]", {"range": "607", "text": "608"}, "directive", "", "Update the dependencies array to be: [fetchEvolutions]", {"range": "609", "text": "610"}, "Update the dependencies array to be: [fetchFireTargetData]", {"range": "611", "text": "612"}, "Update the dependencies array to be: [fetchLiabilities]", {"range": "613", "text": "614"}, "Update the dependencies array to be: [fetchAllData]", {"range": "615", "text": "616"}, "Update the dependencies array to be: [data, editableAllocations, initialTargetPercentages, processAllocations]", {"range": "617", "text": "618"}, "Update the dependencies array to be: [fetchSCPIs]", {"range": "619", "text": "620"}, "suggestUnknown", {"range": "621", "text": "622"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "623", "text": "624"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "625", "text": "622"}, {"range": "626", "text": "624"}, {"range": "627", "text": "622"}, {"range": "628", "text": "624"}, {"range": "629", "text": "622"}, {"range": "630", "text": "624"}, {"range": "631", "text": "622"}, {"range": "632", "text": "624"}, {"range": "633", "text": "622"}, {"range": "634", "text": "624"}, "Update the dependencies array to be: [fetchAssets]", {"range": "635", "text": "636"}, {"range": "637", "text": "622"}, {"range": "638", "text": "624"}, "replaceWithAlt", {"alt": "639"}, {"range": "640", "text": "641"}, "Replace with `&apos;`.", {"alt": "642"}, {"range": "643", "text": "644"}, "Replace with `&lsquo;`.", {"alt": "645"}, {"range": "646", "text": "647"}, "Replace with `&#39;`.", {"alt": "648"}, {"range": "649", "text": "650"}, "Replace with `&rsquo;`.", {"range": "651", "text": "622"}, {"range": "652", "text": "624"}, {"range": "653", "text": "608"}, {"range": "654", "text": "622"}, {"range": "655", "text": "624"}, {"range": "656", "text": "616"}, {"alt": "639"}, {"range": "657", "text": "658"}, {"alt": "642"}, {"range": "659", "text": "660"}, {"alt": "645"}, {"range": "661", "text": "662"}, {"alt": "648"}, {"range": "663", "text": "664"}, {"alt": "639"}, {"range": "665", "text": "666"}, {"alt": "642"}, {"range": "667", "text": "668"}, {"alt": "645"}, {"range": "669", "text": "670"}, {"alt": "648"}, {"range": "671", "text": "672"}, {"alt": "639"}, {"range": "673", "text": "674"}, {"alt": "642"}, {"range": "675", "text": "676"}, {"alt": "645"}, {"range": "677", "text": "678"}, {"alt": "648"}, {"range": "679", "text": "680"}, {"range": "681", "text": "618"}, {"range": "682", "text": "622"}, {"range": "683", "text": "624"}, {"alt": "639"}, {"range": "684", "text": "685"}, {"alt": "642"}, {"range": "686", "text": "687"}, {"alt": "645"}, {"range": "688", "text": "689"}, {"alt": "648"}, {"range": "690", "text": "691"}, {"range": "692", "text": "610"}, {"alt": "639"}, {"range": "693", "text": "694"}, {"alt": "642"}, {"range": "695", "text": "696"}, {"alt": "645"}, {"range": "697", "text": "698"}, {"alt": "648"}, {"range": "699", "text": "700"}, {"alt": "639"}, {"range": "701", "text": "702"}, {"alt": "642"}, {"range": "703", "text": "704"}, {"alt": "645"}, {"range": "705", "text": "706"}, {"alt": "648"}, {"range": "707", "text": "708"}, {"alt": "639"}, {"range": "709", "text": "710"}, {"alt": "642"}, {"range": "711", "text": "712"}, {"alt": "645"}, {"range": "713", "text": "714"}, {"alt": "648"}, {"range": "715", "text": "716"}, {"alt": "639"}, {"range": "717", "text": "718"}, {"alt": "642"}, {"range": "719", "text": "720"}, {"alt": "645"}, {"range": "721", "text": "722"}, {"alt": "648"}, {"range": "723", "text": "724"}, {"alt": "639"}, {"range": "725", "text": "726"}, {"alt": "642"}, {"range": "727", "text": "728"}, {"alt": "645"}, {"range": "729", "text": "730"}, {"alt": "648"}, {"range": "731", "text": "732"}, {"alt": "639"}, {"range": "733", "text": "734"}, {"alt": "642"}, {"range": "735", "text": "736"}, {"alt": "645"}, {"range": "737", "text": "738"}, {"alt": "648"}, {"range": "739", "text": "740"}, {"alt": "639"}, {"range": "741", "text": "742"}, {"alt": "642"}, {"range": "743", "text": "744"}, {"alt": "645"}, {"range": "745", "text": "746"}, {"alt": "648"}, {"range": "747", "text": "748"}, {"alt": "639"}, {"range": "749", "text": "750"}, {"alt": "642"}, {"range": "751", "text": "752"}, {"alt": "645"}, {"range": "753", "text": "754"}, {"alt": "648"}, {"range": "755", "text": "756"}, {"range": "757", "text": "622"}, {"range": "758", "text": "624"}, {"range": "759", "text": "622"}, {"range": "760", "text": "624"}, {"range": "761", "text": "622"}, {"range": "762", "text": "624"}, {"range": "763", "text": "612"}, {"alt": "639"}, {"range": "764", "text": "765"}, {"alt": "642"}, {"range": "766", "text": "767"}, {"alt": "645"}, {"range": "768", "text": "769"}, {"alt": "648"}, {"range": "770", "text": "771"}, {"alt": "639"}, {"range": "772", "text": "773"}, {"alt": "642"}, {"range": "774", "text": "775"}, {"alt": "645"}, {"range": "776", "text": "777"}, {"alt": "648"}, {"range": "778", "text": "779"}, {"range": "780", "text": "614"}, {"alt": "639"}, {"range": "781", "text": "782"}, {"alt": "642"}, {"range": "783", "text": "784"}, {"alt": "645"}, {"range": "785", "text": "786"}, {"alt": "648"}, {"range": "787", "text": "788"}, {"alt": "639"}, {"range": "789", "text": "790"}, {"alt": "642"}, {"range": "791", "text": "792"}, {"alt": "645"}, {"range": "793", "text": "794"}, {"alt": "648"}, {"range": "795", "text": "796"}, {"alt": "639"}, {"range": "797", "text": "798"}, {"alt": "642"}, {"range": "799", "text": "800"}, {"alt": "645"}, {"range": "801", "text": "802"}, {"alt": "648"}, {"range": "803", "text": "804"}, "Update the dependencies array to be: [formData.price_per_share, formData.number_of_shares, formData.total_value]", {"range": "805", "text": "806"}, {"range": "807", "text": "622"}, {"range": "808", "text": "624"}, {"range": "809", "text": "620"}, {"range": "810", "text": "622"}, {"range": "811", "text": "624"}, [4797, 4799], "[fetchData]", [6266, 6268], "[fetchEvolutions]", [13137, 13139], "[fetchFireTargetData]", [5676, 5678], "[fetchLiabilities]", [4439, 4441], "[fetchAllData]", [6206, 6238], "[data, editableAllocations, initialTargetPercentages, processAllocations]", [6968, 6970], "[fetchSCPIs]", [478, 481], "unknown", [478, 481], "never", [1239, 1242], [1239, 1242], [1709, 1712], [1709, 1712], [534, 537], [534, 537], [4188, 4191], [4188, 4191], [5259, 5262], [5259, 5262], [9257, 9259], "[fetchAssets]", [1348, 1351], [1348, 1351], "&apos;", [2765, 2782], "Ordre d&apos;affichage", "&lsquo;", [2765, 2782], "Ordre d&lsquo;affichage", "&#39;", [2765, 2782], "Ordre d&#39;affichage", "&rsquo;", [2765, 2782], "Ordre d&rsquo;affichage", [4473, 4476], [4473, 4476], [4840, 4842], [5319, 5322], [5319, 5322], [3629, 3631], [18091, 18194], "\r\n                Progression vers l&apos;objectif de 910 150€ selon la stratégie documentée\r\n              ", [18091, 18194], "\r\n                Progression vers l&lsquo;objectif de 910 150€ selon la stratégie documentée\r\n              ", [18091, 18194], "\r\n                Progression vers l&#39;objectif de 910 150€ selon la stratégie documentée\r\n              ", [18091, 18194], "\r\n                Progression vers l&rsquo;objectif de 910 150€ selon la stratégie documentée\r\n              ", [18562, 18579], "Catégorie d&apos;Actif", [18562, 18579], "Catégorie d&lsquo;Actif", [18562, 18579], "Catégorie d&#39;Actif", [18562, 18579], "Catégorie d&rsquo;Actif", [24200, 24220], "vers l&apos;objectif FIRE", [24200, 24220], "vers l&lsquo;objectif FIRE", [24200, 24220], "vers l&#39;objectif FIRE", [24200, 24220], "vers l&rsquo;objectif FIRE", [6710, 6716], [1644, 1647], [1644, 1647], [4227, 4341], "\r\n          Les métriques d&apos;évolution (%, €, TCAM) seront calculées automatiquement après la sauvegarde.\r\n        ", [4227, 4341], "\r\n          Les métriques d&lsquo;évolution (%, €, TCAM) seront calculées automatiquement après la sauvegarde.\r\n        ", [4227, 4341], "\r\n          Les métriques d&#39;évolution (%, €, TCAM) seront calculées automatiquement après la sauvegarde.\r\n        ", [4227, 4341], "\r\n          Les métriques d&rsquo;évolution (%, €, TCAM) seront calculées automatiquement après la sauvegarde.\r\n        ", [7178, 7180], [13428, 13464], "Comprendre les métriques d&apos;évolution", [13428, 13464], "Comprendre les métriques d&lsquo;évolution", [13428, 13464], "Comprendre les métriques d&#39;évolution", [13428, 13464], "Comprendre les métriques d&rsquo;évolution", [13927, 13988], "((Valeur Finale / Valeur Initiale)^(1 / Nombre d&apos;Années)) - 1", [13927, 13988], "((Valeur Finale / Valeur Initiale)^(1 / Nombre d&lsquo;Années)) - 1", [13927, 13988], "((Valeur Finale / Valeur Initiale)^(1 / Nombre d&#39;Années)) - 1", [13927, 13988], "((Valeur Finale / Valeur Initiale)^(1 / Nombre d&rsquo;Années)) - 1", [14253, 14354], "Cette valeur montre la variation en pourcentage du patrimoine total par rapport à l&apos;ann<PERSON> p<PERSON>.", [14253, 14354], "Cette valeur montre la variation en pourcentage du patrimoine total par rapport à l&lsquo;ann<PERSON> p<PERSON>.", [14253, 14354], "Cette valeur montre la variation en pourcentage du patrimoine total par rapport à l&#39;ann<PERSON>.", [14253, 14354], "Cette valeur montre la variation en pourcentage du patrimoine total par rapport à l&rsquo;ann<PERSON> p<PERSON>.", [14520, 14604], "Elle permet d&apos;identifier rapidement les années de forte croissance ou de stagnation.", [14520, 14604], "Elle permet d&lsquo;identifier rapidement les années de forte croissance ou de stagnation.", [14520, 14604], "Elle permet d&#39;identifier rapidement les années de forte croissance ou de stagnation.", [14520, 14604], "Elle permet d&rsquo;identifier rapidement les années de forte croissance ou de stagnation.", [14747, 14859], "Indique l&apos;augmentation (ou la diminution) absolue du patrimoine total en euros par rapport à l'année précédente.", [14747, 14859], "Indique l&lsquo;augmentation (ou la diminution) absolue du patrimoine total en euros par rapport à l'année précédente.", [14747, 14859], "Indique l&#39;augmentation (ou la diminution) absolue du patrimoine total en euros par rapport à l'année précédente.", [14747, 14859], "Indique l&rsquo;augmentation (ou la diminution) absolue du patrimoine total en euros par rapport à l'année précédente.", [14747, 14859], "Indique l'augmentation (ou la diminution) absolue du patrimoine total en euros par rapport à l&apos;ann<PERSON>.", [14747, 14859], "Indique l'augmentation (ou la diminution) absolue du patrimoine total en euros par rapport à l&lsquo;ann<PERSON>.", [14747, 14859], "Indique l'augmentation (ou la diminution) absolue du patrimoine total en euros par rapport à l&#39;ann<PERSON>.", [14747, 14859], "Indique l'augmentation (ou la diminution) absolue du patrimoine total en euros par rapport à l&rsquo;ann<PERSON>.", [15229, 15344], "Représente la moyenne simple de l&apos;augmentation annuelle de votre patrimoine en euros depuis l'année de base (2015).", [15229, 15344], "Représente la moyenne simple de l&lsquo;augmentation annuelle de votre patrimoine en euros depuis l'année de base (2015).", [15229, 15344], "Représente la moyenne simple de l&#39;augmentation annuelle de votre patrimoine en euros depuis l'année de base (2015).", [15229, 15344], "Représente la moyenne simple de l&rsquo;augmentation annuelle de votre patrimoine en euros depuis l'année de base (2015).", [15229, 15344], "Représente la moyenne simple de l'augmentation annuelle de votre patrimoine en euros depuis l&apos;ann<PERSON> de base (2015).", [15229, 15344], "Représente la moyenne simple de l'augmentation annuelle de votre patrimoine en euros depuis l&lsquo;ann<PERSON> de base (2015).", [15229, 15344], "Représente la moyenne simple de l'augmentation annuelle de votre patrimoine en euros depuis l&#39;ann<PERSON> de base (2015).", [15229, 15344], "Représente la moyenne simple de l'augmentation annuelle de votre patrimoine en euros depuis l&rsquo;ann<PERSON> de base (2015).", [3119, 3122], [3119, 3122], [3801, 3804], [3801, 3804], [2032, 2035], [2032, 2035], [16210, 16212], [17182, 17223], "\r\n          Modifier l&apos;objectif\r\n        ", [17182, 17223], "\r\n          Modifier l&lsquo;objectif\r\n        ", [17182, 17223], "\r\n          Modifier l&#39;objectif\r\n        ", [17182, 17223], "\r\n          Modifier l&rsquo;objectif\r\n        ", [2426, 2444], "Taux d&apos;<PERSON><PERSON><PERSON><PERSON><PERSON> (%)", [2426, 2444], "Taux d&lsquo;<PERSON><PERSON><PERSON><PERSON><PERSON> (%)", [2426, 2444], "Taux d&#39;<PERSON><PERSON><PERSON><PERSON><PERSON> (%)", [2426, 2444], "Taux d&rsquo;<PERSON><PERSON><PERSON><PERSON><PERSON> (%)", [4232, 4234], [15687, 15714], "Stratégies d&apos;optimisation :", [15687, 15714], "Stratégies d&lsquo;optimisation :", [15687, 15714], "Stratégies d&#39;optimisation :", [15687, 15714], "Stratégies d&rsquo;optimisation :", [16146, 16223], " Ajuster l&apos;allocation d'actifs selon les conditions de marché\r\n              ", [16146, 16223], " Ajuster l&lsquo;allocation d'actifs selon les conditions de marché\r\n              ", [16146, 16223], " Ajuster l&#39;allocation d'actifs selon les conditions de marché\r\n              ", [16146, 16223], " Ajuster l&rsquo;allocation d'actifs selon les conditions de marché\r\n              ", [16146, 16223], " Ajuster l'allocation d&apos;actifs selon les conditions de marché\r\n              ", [16146, 16223], " Ajuster l'allocation d&lsquo;actifs selon les conditions de marché\r\n              ", [16146, 16223], " Ajuster l'allocation d&#39;actifs selon les conditions de marché\r\n              ", [16146, 16223], " Ajuster l'allocation d&rsquo;actifs selon les conditions de marché\r\n              ", [1886, 1939], "[formData.price_per_share, formData.number_of_shares, formData.total_value]", [2413, 2416], [2413, 2416], [7163, 7165], [8217, 8220], [8217, 8220]]