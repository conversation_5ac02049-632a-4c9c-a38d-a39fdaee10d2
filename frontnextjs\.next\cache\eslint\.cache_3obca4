[{"D:\\AI-Code\\fire_UI\\frontnextjs\\src\\app\\assets\\AssetsClientPart.tsx": "1", "D:\\AI-Code\\fire_UI\\frontnextjs\\src\\app\\assets\\page.tsx": "2", "D:\\AI-Code\\fire_UI\\frontnextjs\\src\\app\\budget\\page.tsx": "3", "D:\\AI-Code\\fire_UI\\frontnextjs\\src\\app\\evolution\\page.tsx": "4", "D:\\AI-Code\\fire_UI\\frontnextjs\\src\\app\\fire\\page.tsx": "5", "D:\\AI-Code\\fire_UI\\frontnextjs\\src\\app\\layout.tsx": "6", "D:\\AI-Code\\fire_UI\\frontnextjs\\src\\app\\liabilities\\page.tsx": "7", "D:\\AI-Code\\fire_UI\\frontnextjs\\src\\app\\page.tsx": "8", "D:\\AI-Code\\fire_UI\\frontnextjs\\src\\app\\scenarios\\page.tsx": "9", "D:\\AI-Code\\fire_UI\\frontnextjs\\src\\app\\scpi\\page.tsx": "10", "D:\\AI-Code\\fire_UI\\frontnextjs\\src\\app\\test\\page.tsx": "11", "D:\\AI-Code\\fire_UI\\frontnextjs\\src\\app\\__tests__\\HomePage.test.tsx": "12", "D:\\AI-Code\\fire_UI\\frontnextjs\\src\\components\\ApiClient.ts": "13", "D:\\AI-Code\\fire_UI\\frontnextjs\\src\\components\\ApiTest.tsx": "14", "D:\\AI-Code\\fire_UI\\frontnextjs\\src\\components\\Assets.tsx": "15", "D:\\AI-Code\\fire_UI\\frontnextjs\\src\\components\\BudgetFire.tsx": "16", "D:\\AI-Code\\fire_UI\\frontnextjs\\src\\components\\Dashboard.tsx": "17", "D:\\AI-Code\\fire_UI\\frontnextjs\\src\\components\\EvolutionAnnuelle.tsx": "18", "D:\\AI-Code\\fire_UI\\frontnextjs\\src\\components\\EvolutionChart.tsx": "19", "D:\\AI-Code\\fire_UI\\frontnextjs\\src\\components\\FireTarget.tsx": "20", "D:\\AI-Code\\fire_UI\\frontnextjs\\src\\components\\Liabilities.tsx": "21", "D:\\AI-Code\\fire_UI\\frontnextjs\\src\\components\\Navbar.tsx": "22", "D:\\AI-Code\\fire_UI\\frontnextjs\\src\\components\\ScenariosFire.tsx": "23", "D:\\AI-Code\\fire_UI\\frontnextjs\\src\\components\\SCPI.tsx": "24", "D:\\AI-Code\\fire_UI\\frontnextjs\\src\\setupTests.ts": "25"}, {"size": 11048, "mtime": 1752150087399, "results": "26", "hashOfConfig": "27"}, {"size": 1359, "mtime": 1752129467063, "results": "28", "hashOfConfig": "27"}, {"size": 11099, "mtime": 1752150027692, "results": "29", "hashOfConfig": "27"}, {"size": 13732, "mtime": 1752150194510, "results": "30", "hashOfConfig": "27"}, {"size": 17666, "mtime": 1752150267850, "results": "31", "hashOfConfig": "27"}, {"size": 933, "mtime": 1752149134423, "results": "32", "hashOfConfig": "27"}, {"size": 9099, "mtime": 1752070119355, "results": "33", "hashOfConfig": "27"}, {"size": 19349, "mtime": 1752129467066, "results": "34", "hashOfConfig": "27"}, {"size": 14000, "mtime": 1752070119356, "results": "35", "hashOfConfig": "27"}, {"size": 13141, "mtime": 1752070119358, "results": "36", "hashOfConfig": "27"}, {"size": 3617, "mtime": 1752070119359, "results": "37", "hashOfConfig": "27"}, {"size": 5709, "mtime": 1752129467062, "results": "38", "hashOfConfig": "27"}, {"size": 507, "mtime": 1752061409413, "results": "39", "hashOfConfig": "27"}, {"size": 2714, "mtime": 1752061409413, "results": "40", "hashOfConfig": "27"}, {"size": 13831, "mtime": 1752061409413, "results": "41", "hashOfConfig": "27"}, {"size": 12789, "mtime": 1752061409415, "results": "42", "hashOfConfig": "27"}, {"size": 25475, "mtime": 1752061409415, "results": "43", "hashOfConfig": "27"}, {"size": 15836, "mtime": 1752061409415, "results": "44", "hashOfConfig": "27"}, {"size": 3939, "mtime": 1752070119360, "results": "45", "hashOfConfig": "27"}, {"size": 22118, "mtime": 1752061409415, "results": "46", "hashOfConfig": "27"}, {"size": 7245, "mtime": 1752061409415, "results": "47", "hashOfConfig": "27"}, {"size": 2841, "mtime": 1752149402120, "results": "48", "hashOfConfig": "27"}, {"size": 16441, "mtime": 1752061409415, "results": "49", "hashOfConfig": "27"}, {"size": 13338, "mtime": 1752061409415, "results": "50", "hashOfConfig": "27"}, {"size": 246, "mtime": 1752061409415, "results": "51", "hashOfConfig": "27"}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "7ov1jb", {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 7, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 1, "fixableWarningCount": 0, "source": null}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 10, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\AI-Code\\fire_UI\\frontnextjs\\src\\app\\assets\\AssetsClientPart.tsx", [], [], "D:\\AI-Code\\fire_UI\\frontnextjs\\src\\app\\assets\\page.tsx", [], [], "D:\\AI-Code\\fire_UI\\frontnextjs\\src\\app\\budget\\page.tsx", [], ["127"], "D:\\AI-Code\\fire_UI\\frontnextjs\\src\\app\\evolution\\page.tsx", [], ["128"], "D:\\AI-Code\\fire_UI\\frontnextjs\\src\\app\\fire\\page.tsx", [], ["129"], "D:\\AI-Code\\fire_UI\\frontnextjs\\src\\app\\layout.tsx", [], [], "D:\\AI-Code\\fire_UI\\frontnextjs\\src\\app\\liabilities\\page.tsx", ["130"], ["131"], "D:\\AI-Code\\fire_UI\\frontnextjs\\src\\app\\page.tsx", ["132", "133", "134", "135", "136", "137", "138"], ["139", "140"], "D:\\AI-Code\\fire_UI\\frontnextjs\\src\\app\\scenarios\\page.tsx", ["141", "142", "143"], [], "D:\\AI-Code\\fire_UI\\frontnextjs\\src\\app\\scpi\\page.tsx", ["144", "145"], ["146"], "D:\\AI-Code\\fire_UI\\frontnextjs\\src\\app\\test\\page.tsx", ["147", "148", "149", "150"], [], "D:\\AI-Code\\fire_UI\\frontnextjs\\src\\app\\__tests__\\HomePage.test.tsx", [], [], "D:\\AI-Code\\fire_UI\\frontnextjs\\src\\components\\ApiClient.ts", [], [], "D:\\AI-Code\\fire_UI\\frontnextjs\\src\\components\\ApiTest.tsx", ["151", "152", "153"], [], "D:\\AI-Code\\fire_UI\\frontnextjs\\src\\components\\Assets.tsx", ["154", "155", "156", "157", "158"], [], "D:\\AI-Code\\fire_UI\\frontnextjs\\src\\components\\BudgetFire.tsx", ["159", "160", "161", "162", "163", "164"], [], "D:\\AI-Code\\fire_UI\\frontnextjs\\src\\components\\Dashboard.tsx", ["165", "166", "167", "168", "169"], ["170"], "D:\\AI-Code\\fire_UI\\frontnextjs\\src\\components\\EvolutionAnnuelle.tsx", ["171", "172", "173", "174", "175", "176", "177", "178", "179", "180", "181"], [], "D:\\AI-Code\\fire_UI\\frontnextjs\\src\\components\\EvolutionChart.tsx", ["182", "183"], [], "D:\\AI-Code\\fire_UI\\frontnextjs\\src\\components\\FireTarget.tsx", ["184", "185", "186", "187"], [], "D:\\AI-Code\\fire_UI\\frontnextjs\\src\\components\\Liabilities.tsx", ["188", "189"], [], "D:\\AI-Code\\fire_UI\\frontnextjs\\src\\components\\Navbar.tsx", [], [], "D:\\AI-Code\\fire_UI\\frontnextjs\\src\\components\\ScenariosFire.tsx", ["190", "191", "192", "193"], [], "D:\\AI-Code\\fire_UI\\frontnextjs\\src\\components\\SCPI.tsx", ["194", "195", "196", "197"], [], "D:\\AI-Code\\fire_UI\\frontnextjs\\src\\setupTests.ts", [], [], {"ruleId": "198", "severity": 1, "message": "199", "line": 104, "column": 6, "nodeType": "200", "endLine": 104, "endColumn": 8, "suggestions": "201", "suppressions": "202"}, {"ruleId": "198", "severity": 1, "message": "203", "line": 126, "column": 6, "nodeType": "200", "endLine": 126, "endColumn": 8, "suggestions": "204", "suppressions": "205"}, {"ruleId": "198", "severity": 1, "message": "206", "line": 181, "column": 6, "nodeType": "200", "endLine": 181, "endColumn": 8, "suggestions": "207", "suppressions": "208"}, {"ruleId": "209", "severity": 2, "message": "210", "line": 69, "column": 35, "nodeType": "211", "messageId": "212", "suggestions": "213"}, {"ruleId": "198", "severity": 1, "message": "214", "line": 122, "column": 6, "nodeType": "200", "endLine": 122, "endColumn": 8, "suggestions": "215", "suppressions": "216"}, {"ruleId": "217", "severity": 2, "message": "218", "line": 120, "column": 53, "nodeType": "219", "messageId": "220", "endLine": 120, "endColumn": 56, "suggestions": "221"}, {"ruleId": "217", "severity": 2, "message": "218", "line": 120, "column": 74, "nodeType": "219", "messageId": "220", "endLine": 120, "endColumn": 77, "suggestions": "222"}, {"ruleId": "217", "severity": 2, "message": "218", "line": 120, "column": 89, "nodeType": "219", "messageId": "220", "endLine": 120, "endColumn": 92, "suggestions": "223"}, {"ruleId": "217", "severity": 2, "message": "218", "line": 120, "column": 107, "nodeType": "219", "messageId": "220", "endLine": 120, "endColumn": 110, "suggestions": "224"}, {"ruleId": "209", "severity": 2, "message": "210", "line": 190, "column": 140, "nodeType": "211", "messageId": "212", "suggestions": "225"}, {"ruleId": "209", "severity": 2, "message": "210", "line": 195, "column": 68, "nodeType": "211", "messageId": "212", "suggestions": "226"}, {"ruleId": "209", "severity": 2, "message": "210", "line": 201, "column": 259, "nodeType": "211", "messageId": "212", "suggestions": "227"}, {"ruleId": "198", "severity": 1, "message": "228", "line": 88, "column": 6, "nodeType": "200", "endLine": 88, "endColumn": 8, "suggestions": "229", "suppressions": "230"}, {"ruleId": "198", "severity": 1, "message": "231", "line": 114, "column": 6, "nodeType": "200", "endLine": 114, "endColumn": 38, "suggestions": "232", "suppressions": "233"}, {"ruleId": "209", "severity": 2, "message": "210", "line": 207, "column": 39, "nodeType": "211", "messageId": "212", "suggestions": "234"}, {"ruleId": "209", "severity": 2, "message": "210", "line": 207, "column": 348, "nodeType": "211", "messageId": "212", "suggestions": "235"}, {"ruleId": "209", "severity": 2, "message": "210", "line": 207, "column": 361, "nodeType": "211", "messageId": "212", "suggestions": "236"}, {"ruleId": "217", "severity": 2, "message": "218", "line": 73, "column": 25, "nodeType": "219", "messageId": "220", "endLine": 73, "endColumn": 28, "suggestions": "237"}, {"ruleId": "217", "severity": 2, "message": "218", "line": 184, "column": 19, "nodeType": "219", "messageId": "220", "endLine": 184, "endColumn": 22, "suggestions": "238"}, {"ruleId": "198", "severity": 1, "message": "239", "line": 151, "column": 6, "nodeType": "200", "endLine": 151, "endColumn": 8, "suggestions": "240", "suppressions": "241"}, {"ruleId": "217", "severity": 2, "message": "218", "line": 16, "column": 21, "nodeType": "219", "messageId": "220", "endLine": 16, "endColumn": 24, "suggestions": "242"}, {"ruleId": "217", "severity": 2, "message": "218", "line": 42, "column": 21, "nodeType": "219", "messageId": "220", "endLine": 42, "endColumn": 24, "suggestions": "243"}, {"ruleId": "217", "severity": 2, "message": "218", "line": 59, "column": 21, "nodeType": "219", "messageId": "220", "endLine": 59, "endColumn": 24, "suggestions": "244"}, {"ruleId": "209", "severity": 2, "message": "210", "line": 71, "column": 114, "nodeType": "211", "messageId": "212", "suggestions": "245"}, {"ruleId": "217", "severity": 2, "message": "218", "line": 15, "column": 21, "nodeType": "219", "messageId": "220", "endLine": 15, "endColumn": 24, "suggestions": "246"}, {"ruleId": "217", "severity": 2, "message": "218", "line": 39, "column": 21, "nodeType": "219", "messageId": "220", "endLine": 39, "endColumn": 24, "suggestions": "247"}, {"ruleId": "217", "severity": 2, "message": "218", "line": 54, "column": 21, "nodeType": "219", "messageId": "220", "endLine": 54, "endColumn": 24, "suggestions": "248"}, {"ruleId": "217", "severity": 2, "message": "218", "line": 23, "column": 16, "nodeType": "219", "messageId": "220", "endLine": 23, "endColumn": 19, "suggestions": "249"}, {"ruleId": "217", "severity": 2, "message": "218", "line": 100, "column": 72, "nodeType": "219", "messageId": "220", "endLine": 100, "endColumn": 75, "suggestions": "250"}, {"ruleId": "217", "severity": 2, "message": "218", "line": 127, "column": 25, "nodeType": "219", "messageId": "220", "endLine": 127, "endColumn": 28, "suggestions": "251"}, {"ruleId": "198", "severity": 1, "message": "252", "line": 214, "column": 6, "nodeType": "200", "endLine": 214, "endColumn": 8, "suggestions": "253"}, {"ruleId": "254", "severity": 2, "message": "255", "line": 225, "column": 9, "nodeType": "256", "messageId": "257", "endLine": 225, "endColumn": 22, "fix": "258"}, {"ruleId": "259", "severity": 2, "message": "260", "line": 3, "column": 38, "nodeType": null, "messageId": "261", "endLine": 3, "endColumn": 49}, {"ruleId": "217", "severity": 2, "message": "218", "line": 43, "column": 21, "nodeType": "219", "messageId": "220", "endLine": 43, "endColumn": 24, "suggestions": "262"}, {"ruleId": "209", "severity": 2, "message": "210", "line": 85, "column": 28, "nodeType": "211", "messageId": "212", "suggestions": "263"}, {"ruleId": "217", "severity": 2, "message": "218", "line": 131, "column": 21, "nodeType": "219", "messageId": "220", "endLine": 131, "endColumn": 24, "suggestions": "264"}, {"ruleId": "198", "severity": 1, "message": "199", "line": 146, "column": 6, "nodeType": "200", "endLine": 146, "endColumn": 8, "suggestions": "265"}, {"ruleId": "217", "severity": 2, "message": "218", "line": 159, "column": 23, "nodeType": "219", "messageId": "220", "endLine": 159, "endColumn": 26, "suggestions": "266"}, {"ruleId": "198", "severity": 1, "message": "228", "line": 96, "column": 6, "nodeType": "200", "endLine": 96, "endColumn": 8, "suggestions": "267"}, {"ruleId": "209", "severity": 2, "message": "210", "line": 434, "column": 35, "nodeType": "211", "messageId": "212", "suggestions": "268"}, {"ruleId": "209", "severity": 2, "message": "210", "line": 443, "column": 38, "nodeType": "211", "messageId": "212", "suggestions": "269"}, {"ruleId": "259", "severity": 2, "message": "270", "line": 453, "column": 55, "nodeType": null, "messageId": "261", "endLine": 453, "endColumn": 60}, {"ruleId": "209", "severity": 2, "message": "210", "line": 556, "column": 36, "nodeType": "211", "messageId": "212", "suggestions": "271"}, {"ruleId": "198", "severity": 1, "message": "272", "line": 159, "column": 6, "nodeType": "200", "endLine": 159, "endColumn": 12, "suggestions": "273", "suppressions": "274"}, {"ruleId": "217", "severity": 2, "message": "218", "line": 46, "column": 21, "nodeType": "219", "messageId": "220", "endLine": 46, "endColumn": 24, "suggestions": "275"}, {"ruleId": "209", "severity": 2, "message": "210", "line": 124, "column": 26, "nodeType": "211", "messageId": "212", "suggestions": "276"}, {"ruleId": "198", "severity": 1, "message": "203", "line": 196, "column": 6, "nodeType": "200", "endLine": 196, "endColumn": 8, "suggestions": "277"}, {"ruleId": "209", "severity": 2, "message": "210", "line": 360, "column": 39, "nodeType": "211", "messageId": "212", "suggestions": "278"}, {"ruleId": "209", "severity": 2, "message": "210", "line": 366, "column": 89, "nodeType": "211", "messageId": "212", "suggestions": "279"}, {"ruleId": "209", "severity": 2, "message": "210", "line": 372, "column": 99, "nodeType": "211", "messageId": "212", "suggestions": "280"}, {"ruleId": "209", "severity": 2, "message": "210", "line": 374, "column": 29, "nodeType": "211", "messageId": "212", "suggestions": "281"}, {"ruleId": "209", "severity": 2, "message": "210", "line": 379, "column": 25, "nodeType": "211", "messageId": "212", "suggestions": "282"}, {"ruleId": "209", "severity": 2, "message": "210", "line": 379, "column": 110, "nodeType": "211", "messageId": "212", "suggestions": "283"}, {"ruleId": "209", "severity": 2, "message": "210", "line": 386, "column": 49, "nodeType": "211", "messageId": "212", "suggestions": "284"}, {"ruleId": "209", "severity": 2, "message": "210", "line": 386, "column": 109, "nodeType": "211", "messageId": "212", "suggestions": "285"}, {"ruleId": "217", "severity": 2, "message": "218", "line": 113, "column": 36, "nodeType": "219", "messageId": "220", "endLine": 113, "endColumn": 39, "suggestions": "286"}, {"ruleId": "217", "severity": 2, "message": "218", "line": 137, "column": 45, "nodeType": "219", "messageId": "220", "endLine": 137, "endColumn": 48, "suggestions": "287"}, {"ruleId": "259", "severity": 2, "message": "288", "line": 16, "column": 11, "nodeType": null, "messageId": "261", "endLine": 16, "endColumn": 23}, {"ruleId": "217", "severity": 2, "message": "218", "line": 68, "column": 21, "nodeType": "219", "messageId": "220", "endLine": 68, "endColumn": 24, "suggestions": "289"}, {"ruleId": "198", "severity": 1, "message": "206", "line": 455, "column": 6, "nodeType": "200", "endLine": 455, "endColumn": 8, "suggestions": "290"}, {"ruleId": "209", "severity": 2, "message": "210", "line": 484, "column": 21, "nodeType": "211", "messageId": "212", "suggestions": "291"}, {"ruleId": "209", "severity": 2, "message": "210", "line": 53, "column": 35, "nodeType": "211", "messageId": "212", "suggestions": "292"}, {"ruleId": "198", "severity": 1, "message": "214", "line": 100, "column": 6, "nodeType": "200", "endLine": 100, "endColumn": 8, "suggestions": "293"}, {"ruleId": "259", "severity": 2, "message": "294", "line": 31, "column": 9, "nodeType": null, "messageId": "261", "endLine": 31, "endColumn": 28}, {"ruleId": "209", "severity": 2, "message": "210", "line": 429, "column": 29, "nodeType": "211", "messageId": "212", "suggestions": "295"}, {"ruleId": "209", "severity": 2, "message": "210", "line": 438, "column": 57, "nodeType": "211", "messageId": "212", "suggestions": "296"}, {"ruleId": "209", "severity": 2, "message": "210", "line": 438, "column": 70, "nodeType": "211", "messageId": "212", "suggestions": "297"}, {"ruleId": "198", "severity": 1, "message": "298", "line": 51, "column": 8, "nodeType": "200", "endLine": 51, "endColumn": 61, "suggestions": "299"}, {"ruleId": "217", "severity": 2, "message": "218", "line": 64, "column": 25, "nodeType": "219", "messageId": "220", "endLine": 64, "endColumn": 28, "suggestions": "300"}, {"ruleId": "198", "severity": 1, "message": "239", "line": 187, "column": 6, "nodeType": "200", "endLine": 187, "endColumn": 8, "suggestions": "301"}, {"ruleId": "217", "severity": 2, "message": "218", "line": 222, "column": 19, "nodeType": "219", "messageId": "220", "endLine": 222, "endColumn": 22, "suggestions": "302"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchData'. Either include it or remove the dependency array.", "ArrayExpression", ["303"], ["304"], "React Hook useEffect has a missing dependency: 'fetchEvolutions'. Either include it or remove the dependency array.", ["305"], ["306"], "React Hook useEffect has a missing dependency: 'fetchFireTargetData'. Either include it or remove the dependency array.", ["307"], ["308"], "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["309", "310", "311", "312"], "React Hook useEffect has a missing dependency: 'fetchLiabilities'. Either include it or remove the dependency array.", ["313"], ["314"], "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["315", "316"], ["317", "318"], ["319", "320"], ["321", "322"], ["323", "324", "325", "326"], ["327", "328", "329", "330"], ["331", "332", "333", "334"], "React Hook useEffect has a missing dependency: 'fetchAllData'. Either include it or remove the dependency array.", ["335"], ["336"], "React Hook useEffect has missing dependencies: 'editableAllocations' and 'processAllocations'. Either include them or remove the dependency array.", ["337"], ["338"], ["339", "340", "341", "342"], ["343", "344", "345", "346"], ["347", "348", "349", "350"], ["351", "352"], ["353", "354"], "React Hook useEffect has a missing dependency: 'fetchSCPIs'. Either include it or remove the dependency array.", ["355"], ["356"], ["357", "358"], ["359", "360"], ["361", "362"], ["363", "364", "365", "366"], ["367", "368"], ["369", "370"], ["371", "372"], ["373", "374"], ["375", "376"], ["377", "378"], "React Hook useEffect has a missing dependency: 'fetchAssets'. Either include it or remove the dependency array.", ["379"], "prefer-const", "'sortableItems' is never reassigned. Use 'const' instead.", "Identifier", "useConst", {"range": "380", "text": "381"}, "@typescript-eslint/no-unused-vars", "'ProgressBar' is defined but never used.", "unusedVar", ["382", "383"], ["384", "385", "386", "387"], ["388", "389"], ["390"], ["391", "392"], ["393"], ["394", "395", "396", "397"], ["398", "399", "400", "401"], "'index' is defined but never used.", ["402", "403", "404", "405"], "React Hook useEffect has missing dependencies: 'editableAllocations', 'initialTargetPercentages', and 'processAllocations'. Either include them or remove the dependency array.", ["406"], ["407"], ["408", "409"], ["410", "411", "412", "413"], ["414"], ["415", "416", "417", "418"], ["419", "420", "421", "422"], ["423", "424", "425", "426"], ["427", "428", "429", "430"], ["431", "432", "433", "434"], ["435", "436", "437", "438"], ["439", "440", "441", "442"], ["443", "444", "445", "446"], ["447", "448"], ["449", "450"], "'FireSettings' is defined but never used.", ["451", "452"], ["453"], ["454", "455", "456", "457"], ["458", "459", "460", "461"], ["462"], "'ANNUAL_NET_EXPENSES' is assigned a value but never used.", ["463", "464", "465", "466"], ["467", "468", "469", "470"], ["471", "472", "473", "474"], "React Hook useEffect has a missing dependency: 'formData.total_value'. Either include it or remove the dependency array.", ["475"], ["476", "477"], ["478"], ["479", "480"], {"desc": "481", "fix": "482"}, {"kind": "483", "justification": "484"}, {"desc": "485", "fix": "486"}, {"kind": "483", "justification": "484"}, {"desc": "487", "fix": "488"}, {"kind": "483", "justification": "484"}, {"messageId": "489", "data": "490", "fix": "491", "desc": "492"}, {"messageId": "489", "data": "493", "fix": "494", "desc": "495"}, {"messageId": "489", "data": "496", "fix": "497", "desc": "498"}, {"messageId": "489", "data": "499", "fix": "500", "desc": "501"}, {"desc": "502", "fix": "503"}, {"kind": "483", "justification": "484"}, {"messageId": "504", "fix": "505", "desc": "506"}, {"messageId": "507", "fix": "508", "desc": "509"}, {"messageId": "504", "fix": "510", "desc": "506"}, {"messageId": "507", "fix": "511", "desc": "509"}, {"messageId": "504", "fix": "512", "desc": "506"}, {"messageId": "507", "fix": "513", "desc": "509"}, {"messageId": "504", "fix": "514", "desc": "506"}, {"messageId": "507", "fix": "515", "desc": "509"}, {"messageId": "489", "data": "516", "fix": "517", "desc": "492"}, {"messageId": "489", "data": "518", "fix": "519", "desc": "495"}, {"messageId": "489", "data": "520", "fix": "521", "desc": "498"}, {"messageId": "489", "data": "522", "fix": "523", "desc": "501"}, {"messageId": "489", "data": "524", "fix": "525", "desc": "492"}, {"messageId": "489", "data": "526", "fix": "527", "desc": "495"}, {"messageId": "489", "data": "528", "fix": "529", "desc": "498"}, {"messageId": "489", "data": "530", "fix": "531", "desc": "501"}, {"messageId": "489", "data": "532", "fix": "533", "desc": "492"}, {"messageId": "489", "data": "534", "fix": "535", "desc": "495"}, {"messageId": "489", "data": "536", "fix": "537", "desc": "498"}, {"messageId": "489", "data": "538", "fix": "539", "desc": "501"}, {"desc": "540", "fix": "541"}, {"kind": "483", "justification": "484"}, {"desc": "542", "fix": "543"}, {"kind": "483", "justification": "484"}, {"messageId": "489", "data": "544", "fix": "545", "desc": "492"}, {"messageId": "489", "data": "546", "fix": "547", "desc": "495"}, {"messageId": "489", "data": "548", "fix": "549", "desc": "498"}, {"messageId": "489", "data": "550", "fix": "551", "desc": "501"}, {"messageId": "489", "data": "552", "fix": "553", "desc": "492"}, {"messageId": "489", "data": "554", "fix": "555", "desc": "495"}, {"messageId": "489", "data": "556", "fix": "557", "desc": "498"}, {"messageId": "489", "data": "558", "fix": "559", "desc": "501"}, {"messageId": "489", "data": "560", "fix": "561", "desc": "492"}, {"messageId": "489", "data": "562", "fix": "563", "desc": "495"}, {"messageId": "489", "data": "564", "fix": "565", "desc": "498"}, {"messageId": "489", "data": "566", "fix": "567", "desc": "501"}, {"messageId": "504", "fix": "568", "desc": "506"}, {"messageId": "507", "fix": "569", "desc": "509"}, {"messageId": "504", "fix": "570", "desc": "506"}, {"messageId": "507", "fix": "571", "desc": "509"}, {"desc": "572", "fix": "573"}, {"kind": "483", "justification": "484"}, {"messageId": "504", "fix": "574", "desc": "506"}, {"messageId": "507", "fix": "575", "desc": "509"}, {"messageId": "504", "fix": "576", "desc": "506"}, {"messageId": "507", "fix": "577", "desc": "509"}, {"messageId": "504", "fix": "578", "desc": "506"}, {"messageId": "507", "fix": "579", "desc": "509"}, {"messageId": "489", "data": "580", "fix": "581", "desc": "492"}, {"messageId": "489", "data": "582", "fix": "583", "desc": "495"}, {"messageId": "489", "data": "584", "fix": "585", "desc": "498"}, {"messageId": "489", "data": "586", "fix": "587", "desc": "501"}, {"messageId": "504", "fix": "588", "desc": "506"}, {"messageId": "507", "fix": "589", "desc": "509"}, {"messageId": "504", "fix": "590", "desc": "506"}, {"messageId": "507", "fix": "591", "desc": "509"}, {"messageId": "504", "fix": "592", "desc": "506"}, {"messageId": "507", "fix": "593", "desc": "509"}, {"messageId": "504", "fix": "594", "desc": "506"}, {"messageId": "507", "fix": "595", "desc": "509"}, {"messageId": "504", "fix": "596", "desc": "506"}, {"messageId": "507", "fix": "597", "desc": "509"}, {"messageId": "504", "fix": "598", "desc": "506"}, {"messageId": "507", "fix": "599", "desc": "509"}, {"desc": "600", "fix": "601"}, [9587, 9619], "const sortableItems = [...assets];", {"messageId": "504", "fix": "602", "desc": "506"}, {"messageId": "507", "fix": "603", "desc": "509"}, {"messageId": "489", "data": "604", "fix": "605", "desc": "492"}, {"messageId": "489", "data": "606", "fix": "607", "desc": "495"}, {"messageId": "489", "data": "608", "fix": "609", "desc": "498"}, {"messageId": "489", "data": "610", "fix": "611", "desc": "501"}, {"messageId": "504", "fix": "612", "desc": "506"}, {"messageId": "507", "fix": "613", "desc": "509"}, {"desc": "481", "fix": "614"}, {"messageId": "504", "fix": "615", "desc": "506"}, {"messageId": "507", "fix": "616", "desc": "509"}, {"desc": "540", "fix": "617"}, {"messageId": "489", "data": "618", "fix": "619", "desc": "492"}, {"messageId": "489", "data": "620", "fix": "621", "desc": "495"}, {"messageId": "489", "data": "622", "fix": "623", "desc": "498"}, {"messageId": "489", "data": "624", "fix": "625", "desc": "501"}, {"messageId": "489", "data": "626", "fix": "627", "desc": "492"}, {"messageId": "489", "data": "628", "fix": "629", "desc": "495"}, {"messageId": "489", "data": "630", "fix": "631", "desc": "498"}, {"messageId": "489", "data": "632", "fix": "633", "desc": "501"}, {"messageId": "489", "data": "634", "fix": "635", "desc": "492"}, {"messageId": "489", "data": "636", "fix": "637", "desc": "495"}, {"messageId": "489", "data": "638", "fix": "639", "desc": "498"}, {"messageId": "489", "data": "640", "fix": "641", "desc": "501"}, {"desc": "542", "fix": "642"}, {"kind": "483", "justification": "484"}, {"messageId": "504", "fix": "643", "desc": "506"}, {"messageId": "507", "fix": "644", "desc": "509"}, {"messageId": "489", "data": "645", "fix": "646", "desc": "492"}, {"messageId": "489", "data": "647", "fix": "648", "desc": "495"}, {"messageId": "489", "data": "649", "fix": "650", "desc": "498"}, {"messageId": "489", "data": "651", "fix": "652", "desc": "501"}, {"desc": "485", "fix": "653"}, {"messageId": "489", "data": "654", "fix": "655", "desc": "492"}, {"messageId": "489", "data": "656", "fix": "657", "desc": "495"}, {"messageId": "489", "data": "658", "fix": "659", "desc": "498"}, {"messageId": "489", "data": "660", "fix": "661", "desc": "501"}, {"messageId": "489", "data": "662", "fix": "663", "desc": "492"}, {"messageId": "489", "data": "664", "fix": "665", "desc": "495"}, {"messageId": "489", "data": "666", "fix": "667", "desc": "498"}, {"messageId": "489", "data": "668", "fix": "669", "desc": "501"}, {"messageId": "489", "data": "670", "fix": "671", "desc": "492"}, {"messageId": "489", "data": "672", "fix": "673", "desc": "495"}, {"messageId": "489", "data": "674", "fix": "675", "desc": "498"}, {"messageId": "489", "data": "676", "fix": "677", "desc": "501"}, {"messageId": "489", "data": "678", "fix": "679", "desc": "492"}, {"messageId": "489", "data": "680", "fix": "681", "desc": "495"}, {"messageId": "489", "data": "682", "fix": "683", "desc": "498"}, {"messageId": "489", "data": "684", "fix": "685", "desc": "501"}, {"messageId": "489", "data": "686", "fix": "687", "desc": "492"}, {"messageId": "489", "data": "688", "fix": "689", "desc": "495"}, {"messageId": "489", "data": "690", "fix": "691", "desc": "498"}, {"messageId": "489", "data": "692", "fix": "693", "desc": "501"}, {"messageId": "489", "data": "694", "fix": "695", "desc": "492"}, {"messageId": "489", "data": "696", "fix": "697", "desc": "495"}, {"messageId": "489", "data": "698", "fix": "699", "desc": "498"}, {"messageId": "489", "data": "700", "fix": "701", "desc": "501"}, {"messageId": "489", "data": "702", "fix": "703", "desc": "492"}, {"messageId": "489", "data": "704", "fix": "705", "desc": "495"}, {"messageId": "489", "data": "706", "fix": "707", "desc": "498"}, {"messageId": "489", "data": "708", "fix": "709", "desc": "501"}, {"messageId": "489", "data": "710", "fix": "711", "desc": "492"}, {"messageId": "489", "data": "712", "fix": "713", "desc": "495"}, {"messageId": "489", "data": "714", "fix": "715", "desc": "498"}, {"messageId": "489", "data": "716", "fix": "717", "desc": "501"}, {"messageId": "504", "fix": "718", "desc": "506"}, {"messageId": "507", "fix": "719", "desc": "509"}, {"messageId": "504", "fix": "720", "desc": "506"}, {"messageId": "507", "fix": "721", "desc": "509"}, {"messageId": "504", "fix": "722", "desc": "506"}, {"messageId": "507", "fix": "723", "desc": "509"}, {"desc": "487", "fix": "724"}, {"messageId": "489", "data": "725", "fix": "726", "desc": "492"}, {"messageId": "489", "data": "727", "fix": "728", "desc": "495"}, {"messageId": "489", "data": "729", "fix": "730", "desc": "498"}, {"messageId": "489", "data": "731", "fix": "732", "desc": "501"}, {"messageId": "489", "data": "733", "fix": "734", "desc": "492"}, {"messageId": "489", "data": "735", "fix": "736", "desc": "495"}, {"messageId": "489", "data": "737", "fix": "738", "desc": "498"}, {"messageId": "489", "data": "739", "fix": "740", "desc": "501"}, {"desc": "502", "fix": "741"}, {"messageId": "489", "data": "742", "fix": "743", "desc": "492"}, {"messageId": "489", "data": "744", "fix": "745", "desc": "495"}, {"messageId": "489", "data": "746", "fix": "747", "desc": "498"}, {"messageId": "489", "data": "748", "fix": "749", "desc": "501"}, {"messageId": "489", "data": "750", "fix": "751", "desc": "492"}, {"messageId": "489", "data": "752", "fix": "753", "desc": "495"}, {"messageId": "489", "data": "754", "fix": "755", "desc": "498"}, {"messageId": "489", "data": "756", "fix": "757", "desc": "501"}, {"messageId": "489", "data": "758", "fix": "759", "desc": "492"}, {"messageId": "489", "data": "760", "fix": "761", "desc": "495"}, {"messageId": "489", "data": "762", "fix": "763", "desc": "498"}, {"messageId": "489", "data": "764", "fix": "765", "desc": "501"}, {"desc": "766", "fix": "767"}, {"messageId": "504", "fix": "768", "desc": "506"}, {"messageId": "507", "fix": "769", "desc": "509"}, {"desc": "572", "fix": "770"}, {"messageId": "504", "fix": "771", "desc": "506"}, {"messageId": "507", "fix": "772", "desc": "509"}, "Update the dependencies array to be: [fetchData]", {"range": "773", "text": "774"}, "directive", "", "Update the dependencies array to be: [fetchEvolutions]", {"range": "775", "text": "776"}, "Update the dependencies array to be: [fetchFireTargetData]", {"range": "777", "text": "778"}, "replaceWithAlt", {"alt": "779"}, {"range": "780", "text": "781"}, "Replace with `&apos;`.", {"alt": "782"}, {"range": "783", "text": "784"}, "Replace with `&lsquo;`.", {"alt": "785"}, {"range": "786", "text": "787"}, "Replace with `&#39;`.", {"alt": "788"}, {"range": "789", "text": "790"}, "Replace with `&rsquo;`.", "Update the dependencies array to be: [fetchLiabilities]", {"range": "791", "text": "792"}, "suggestUnknown", {"range": "793", "text": "794"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "795", "text": "796"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "797", "text": "794"}, {"range": "798", "text": "796"}, {"range": "799", "text": "794"}, {"range": "800", "text": "796"}, {"range": "801", "text": "794"}, {"range": "802", "text": "796"}, {"alt": "779"}, {"range": "803", "text": "804"}, {"alt": "782"}, {"range": "805", "text": "806"}, {"alt": "785"}, {"range": "807", "text": "808"}, {"alt": "788"}, {"range": "809", "text": "810"}, {"alt": "779"}, {"range": "811", "text": "812"}, {"alt": "782"}, {"range": "813", "text": "814"}, {"alt": "785"}, {"range": "815", "text": "816"}, {"alt": "788"}, {"range": "817", "text": "818"}, {"alt": "779"}, {"range": "819", "text": "820"}, {"alt": "782"}, {"range": "821", "text": "822"}, {"alt": "785"}, {"range": "823", "text": "824"}, {"alt": "788"}, {"range": "825", "text": "826"}, "Update the dependencies array to be: [fetchAllData]", {"range": "827", "text": "828"}, "Update the dependencies array to be: [data, editableAllocations, initialTargetPercentages, processAllocations]", {"range": "829", "text": "830"}, {"alt": "779"}, {"range": "831", "text": "832"}, {"alt": "782"}, {"range": "833", "text": "834"}, {"alt": "785"}, {"range": "835", "text": "836"}, {"alt": "788"}, {"range": "837", "text": "838"}, {"alt": "779"}, {"range": "839", "text": "840"}, {"alt": "782"}, {"range": "841", "text": "842"}, {"alt": "785"}, {"range": "843", "text": "844"}, {"alt": "788"}, {"range": "845", "text": "846"}, {"alt": "779"}, {"range": "847", "text": "848"}, {"alt": "782"}, {"range": "849", "text": "850"}, {"alt": "785"}, {"range": "851", "text": "852"}, {"alt": "788"}, {"range": "853", "text": "854"}, {"range": "855", "text": "794"}, {"range": "856", "text": "796"}, {"range": "857", "text": "794"}, {"range": "858", "text": "796"}, "Update the dependencies array to be: [fetchSCPIs]", {"range": "859", "text": "860"}, {"range": "861", "text": "794"}, {"range": "862", "text": "796"}, {"range": "863", "text": "794"}, {"range": "864", "text": "796"}, {"range": "865", "text": "794"}, {"range": "866", "text": "796"}, {"alt": "779"}, {"range": "867", "text": "868"}, {"alt": "782"}, {"range": "869", "text": "870"}, {"alt": "785"}, {"range": "871", "text": "872"}, {"alt": "788"}, {"range": "873", "text": "874"}, {"range": "875", "text": "794"}, {"range": "876", "text": "796"}, {"range": "877", "text": "794"}, {"range": "878", "text": "796"}, {"range": "879", "text": "794"}, {"range": "880", "text": "796"}, {"range": "881", "text": "794"}, {"range": "882", "text": "796"}, {"range": "883", "text": "794"}, {"range": "884", "text": "796"}, {"range": "885", "text": "794"}, {"range": "886", "text": "796"}, "Update the dependencies array to be: [fetchAssets]", {"range": "887", "text": "888"}, {"range": "889", "text": "794"}, {"range": "890", "text": "796"}, {"alt": "779"}, {"range": "891", "text": "892"}, {"alt": "782"}, {"range": "893", "text": "894"}, {"alt": "785"}, {"range": "895", "text": "896"}, {"alt": "788"}, {"range": "897", "text": "898"}, {"range": "899", "text": "794"}, {"range": "900", "text": "796"}, {"range": "901", "text": "774"}, {"range": "902", "text": "794"}, {"range": "903", "text": "796"}, {"range": "904", "text": "828"}, {"alt": "779"}, {"range": "905", "text": "906"}, {"alt": "782"}, {"range": "907", "text": "908"}, {"alt": "785"}, {"range": "909", "text": "910"}, {"alt": "788"}, {"range": "911", "text": "912"}, {"alt": "779"}, {"range": "913", "text": "812"}, {"alt": "782"}, {"range": "914", "text": "814"}, {"alt": "785"}, {"range": "915", "text": "816"}, {"alt": "788"}, {"range": "916", "text": "818"}, {"alt": "779"}, {"range": "917", "text": "820"}, {"alt": "782"}, {"range": "918", "text": "822"}, {"alt": "785"}, {"range": "919", "text": "824"}, {"alt": "788"}, {"range": "920", "text": "826"}, {"range": "921", "text": "830"}, {"range": "922", "text": "794"}, {"range": "923", "text": "796"}, {"alt": "779"}, {"range": "924", "text": "925"}, {"alt": "782"}, {"range": "926", "text": "927"}, {"alt": "785"}, {"range": "928", "text": "929"}, {"alt": "788"}, {"range": "930", "text": "931"}, {"range": "932", "text": "776"}, {"alt": "779"}, {"range": "933", "text": "934"}, {"alt": "782"}, {"range": "935", "text": "936"}, {"alt": "785"}, {"range": "937", "text": "938"}, {"alt": "788"}, {"range": "939", "text": "940"}, {"alt": "779"}, {"range": "941", "text": "942"}, {"alt": "782"}, {"range": "943", "text": "944"}, {"alt": "785"}, {"range": "945", "text": "946"}, {"alt": "788"}, {"range": "947", "text": "948"}, {"alt": "779"}, {"range": "949", "text": "950"}, {"alt": "782"}, {"range": "951", "text": "952"}, {"alt": "785"}, {"range": "953", "text": "954"}, {"alt": "788"}, {"range": "955", "text": "956"}, {"alt": "779"}, {"range": "957", "text": "958"}, {"alt": "782"}, {"range": "959", "text": "960"}, {"alt": "785"}, {"range": "961", "text": "962"}, {"alt": "788"}, {"range": "963", "text": "964"}, {"alt": "779"}, {"range": "965", "text": "966"}, {"alt": "782"}, {"range": "967", "text": "968"}, {"alt": "785"}, {"range": "969", "text": "970"}, {"alt": "788"}, {"range": "971", "text": "972"}, {"alt": "779"}, {"range": "973", "text": "974"}, {"alt": "782"}, {"range": "975", "text": "976"}, {"alt": "785"}, {"range": "977", "text": "978"}, {"alt": "788"}, {"range": "979", "text": "980"}, {"alt": "779"}, {"range": "981", "text": "982"}, {"alt": "782"}, {"range": "983", "text": "984"}, {"alt": "785"}, {"range": "985", "text": "986"}, {"alt": "788"}, {"range": "987", "text": "988"}, {"alt": "779"}, {"range": "989", "text": "990"}, {"alt": "782"}, {"range": "991", "text": "992"}, {"alt": "785"}, {"range": "993", "text": "994"}, {"alt": "788"}, {"range": "995", "text": "996"}, {"range": "997", "text": "794"}, {"range": "998", "text": "796"}, {"range": "999", "text": "794"}, {"range": "1000", "text": "796"}, {"range": "1001", "text": "794"}, {"range": "1002", "text": "796"}, {"range": "1003", "text": "778"}, {"alt": "779"}, {"range": "1004", "text": "1005"}, {"alt": "782"}, {"range": "1006", "text": "1007"}, {"alt": "785"}, {"range": "1008", "text": "1009"}, {"alt": "788"}, {"range": "1010", "text": "1011"}, {"alt": "779"}, {"range": "1012", "text": "781"}, {"alt": "782"}, {"range": "1013", "text": "784"}, {"alt": "785"}, {"range": "1014", "text": "787"}, {"alt": "788"}, {"range": "1015", "text": "790"}, {"range": "1016", "text": "792"}, {"alt": "779"}, {"range": "1017", "text": "832"}, {"alt": "782"}, {"range": "1018", "text": "834"}, {"alt": "785"}, {"range": "1019", "text": "836"}, {"alt": "788"}, {"range": "1020", "text": "838"}, {"alt": "779"}, {"range": "1021", "text": "1022"}, {"alt": "782"}, {"range": "1023", "text": "1024"}, {"alt": "785"}, {"range": "1025", "text": "1026"}, {"alt": "788"}, {"range": "1027", "text": "1028"}, {"alt": "779"}, {"range": "1029", "text": "1030"}, {"alt": "782"}, {"range": "1031", "text": "1032"}, {"alt": "785"}, {"range": "1033", "text": "1034"}, {"alt": "788"}, {"range": "1035", "text": "1036"}, "Update the dependencies array to be: [formData.price_per_share, formData.number_of_shares, formData.total_value]", {"range": "1037", "text": "1038"}, {"range": "1039", "text": "794"}, {"range": "1040", "text": "796"}, {"range": "1041", "text": "860"}, {"range": "1042", "text": "794"}, {"range": "1043", "text": "796"}, [4797, 4799], "[fetchData]", [6266, 6268], "[fetchEvolutions]", [13137, 13139], "[fetchFireTargetData]", "&apos;", [3271, 3289], "Taux d&apos;<PERSON><PERSON><PERSON><PERSON><PERSON> (%)", "&lsquo;", [3271, 3289], "Taux d&lsquo;<PERSON><PERSON><PERSON><PERSON><PERSON> (%)", "&#39;", [3271, 3289], "Taux d&#39;<PERSON><PERSON><PERSON><PERSON><PERSON> (%)", "&rsquo;", [3271, 3289], "Taux d&rsquo;<PERSON><PERSON><PERSON><PERSON><PERSON> (%)", [5671, 5673], "[fetchLiabilities]", [6555, 6558], "unknown", [6555, 6558], "never", [6576, 6579], [6576, 6579], [6591, 6594], [6591, 6594], [6609, 6612], [6609, 6612], [15040, 15071], "Progression vers l&apos;objectif de ", [15040, 15071], "Progression vers l&lsquo;objectif de ", [15040, 15071], "Progression vers l&#39;objectif de ", [15040, 15071], "Progression vers l&rsquo;objectif de ", [15604, 15621], "Catégorie d&apos;Actif", [15604, 15621], "Catégorie d&lsquo;Actif", [15604, 15621], "Catégorie d&#39;Actif", [15604, 15621], "Catégorie d&rsquo;Actif", [18616, 18636], "vers l&apos;objectif FIRE", [18616, 18636], "vers l&lsquo;objectif FIRE", [18616, 18636], "vers l&#39;objectif FIRE", [18616, 18636], "vers l&rsquo;objectif FIRE", [4439, 4441], "[fetchAllData]", [6206, 6238], "[data, editableAllocations, initialTargetPercentages, processAllocations]", [13451, 13478], "Stratégies d&apos;optimisation :", [13451, 13478], "Stratégies d&lsquo;optimisation :", [13451, 13478], "Stratégies d&#39;optimisation :", [13451, 13478], "Stratégies d&rsquo;optimisation :", [13762, 13823], " Ajuster l&apos;allocation d'actifs selon les conditions de marché", [13762, 13823], " Ajuster l&lsquo;allocation d'actifs selon les conditions de marché", [13762, 13823], " Ajuster l&#39;allocation d'actifs selon les conditions de marché", [13762, 13823], " Ajuster l&rsquo;allocation d'actifs selon les conditions de marché", [13762, 13823], " Ajuster l'allocation d&apos;actifs selon les conditions de marché", [13762, 13823], " Ajuster l'allocation d&lsquo;actifs selon les conditions de marché", [13762, 13823], " Ajuster l'allocation d&#39;actifs selon les conditions de marché", [13762, 13823], " Ajuster l'allocation d&rsquo;actifs selon les conditions de marché", [2871, 2874], [2871, 2874], [7937, 7940], [7937, 7940], [6897, 6899], "[fetchSCPIs]", [549, 552], [549, 552], [1477, 1480], [1477, 1480], [2087, 2090], [2087, 2090], [2524, 2555], ") est en cours d&apos;exécution sur ", [2524, 2555], ") est en cours d&lsquo;exécution sur ", [2524, 2555], ") est en cours d&#39;exécution sur ", [2524, 2555], ") est en cours d&rsquo;exécution sur ", [478, 481], [478, 481], [1239, 1242], [1239, 1242], [1709, 1712], [1709, 1712], [534, 537], [534, 537], [4188, 4191], [4188, 4191], [5259, 5262], [5259, 5262], [9257, 9259], "[fetchAssets]", [1348, 1351], [1348, 1351], [2765, 2782], "Ordre d&apos;affichage", [2765, 2782], "Ordre d&lsquo;affichage", [2765, 2782], "Ordre d&#39;affichage", [2765, 2782], "Ordre d&rsquo;affichage", [4473, 4476], [4473, 4476], [4840, 4842], [5319, 5322], [5319, 5322], [3629, 3631], [18091, 18194], "\r\n                Progression vers l&apos;objectif de 910 150€ selon la stratégie documentée\r\n              ", [18091, 18194], "\r\n                Progression vers l&lsquo;objectif de 910 150€ selon la stratégie documentée\r\n              ", [18091, 18194], "\r\n                Progression vers l&#39;objectif de 910 150€ selon la stratégie documentée\r\n              ", [18091, 18194], "\r\n                Progression vers l&rsquo;objectif de 910 150€ selon la stratégie documentée\r\n              ", [18562, 18579], [18562, 18579], [18562, 18579], [18562, 18579], [24200, 24220], [24200, 24220], [24200, 24220], [24200, 24220], [6710, 6716], [1644, 1647], [1644, 1647], [4227, 4341], "\r\n          Les métriques d&apos;évolution (%, €, TCAM) seront calculées automatiquement après la sauvegarde.\r\n        ", [4227, 4341], "\r\n          Les métriques d&lsquo;évolution (%, €, TCAM) seront calculées automatiquement après la sauvegarde.\r\n        ", [4227, 4341], "\r\n          Les métriques d&#39;évolution (%, €, TCAM) seront calculées automatiquement après la sauvegarde.\r\n        ", [4227, 4341], "\r\n          Les métriques d&rsquo;évolution (%, €, TCAM) seront calculées automatiquement après la sauvegarde.\r\n        ", [7178, 7180], [13428, 13464], "Comprendre les métriques d&apos;évolution", [13428, 13464], "Comprendre les métriques d&lsquo;évolution", [13428, 13464], "Comprendre les métriques d&#39;évolution", [13428, 13464], "Comprendre les métriques d&rsquo;évolution", [13927, 13988], "((Valeur Finale / Valeur Initiale)^(1 / Nombre d&apos;Années)) - 1", [13927, 13988], "((Valeur Finale / Valeur Initiale)^(1 / Nombre d&lsquo;Années)) - 1", [13927, 13988], "((Valeur Finale / Valeur Initiale)^(1 / Nombre d&#39;Années)) - 1", [13927, 13988], "((Valeur Finale / Valeur Initiale)^(1 / Nombre d&rsquo;Années)) - 1", [14253, 14354], "Cette valeur montre la variation en pourcentage du patrimoine total par rapport à l&apos;ann<PERSON> p<PERSON>.", [14253, 14354], "Cette valeur montre la variation en pourcentage du patrimoine total par rapport à l&lsquo;ann<PERSON> p<PERSON>.", [14253, 14354], "Cette valeur montre la variation en pourcentage du patrimoine total par rapport à l&#39;ann<PERSON>.", [14253, 14354], "Cette valeur montre la variation en pourcentage du patrimoine total par rapport à l&rsquo;ann<PERSON> p<PERSON>.", [14520, 14604], "Elle permet d&apos;identifier rapidement les années de forte croissance ou de stagnation.", [14520, 14604], "Elle permet d&lsquo;identifier rapidement les années de forte croissance ou de stagnation.", [14520, 14604], "Elle permet d&#39;identifier rapidement les années de forte croissance ou de stagnation.", [14520, 14604], "Elle permet d&rsquo;identifier rapidement les années de forte croissance ou de stagnation.", [14747, 14859], "Indique l&apos;augmentation (ou la diminution) absolue du patrimoine total en euros par rapport à l'année précédente.", [14747, 14859], "Indique l&lsquo;augmentation (ou la diminution) absolue du patrimoine total en euros par rapport à l'année précédente.", [14747, 14859], "Indique l&#39;augmentation (ou la diminution) absolue du patrimoine total en euros par rapport à l'année précédente.", [14747, 14859], "Indique l&rsquo;augmentation (ou la diminution) absolue du patrimoine total en euros par rapport à l'année précédente.", [14747, 14859], "Indique l'augmentation (ou la diminution) absolue du patrimoine total en euros par rapport à l&apos;ann<PERSON>.", [14747, 14859], "Indique l'augmentation (ou la diminution) absolue du patrimoine total en euros par rapport à l&lsquo;ann<PERSON>.", [14747, 14859], "Indique l'augmentation (ou la diminution) absolue du patrimoine total en euros par rapport à l&#39;ann<PERSON>.", [14747, 14859], "Indique l'augmentation (ou la diminution) absolue du patrimoine total en euros par rapport à l&rsquo;ann<PERSON>.", [15229, 15344], "Représente la moyenne simple de l&apos;augmentation annuelle de votre patrimoine en euros depuis l'année de base (2015).", [15229, 15344], "Représente la moyenne simple de l&lsquo;augmentation annuelle de votre patrimoine en euros depuis l'année de base (2015).", [15229, 15344], "Représente la moyenne simple de l&#39;augmentation annuelle de votre patrimoine en euros depuis l'année de base (2015).", [15229, 15344], "Représente la moyenne simple de l&rsquo;augmentation annuelle de votre patrimoine en euros depuis l'année de base (2015).", [15229, 15344], "Représente la moyenne simple de l'augmentation annuelle de votre patrimoine en euros depuis l&apos;ann<PERSON> de base (2015).", [15229, 15344], "Représente la moyenne simple de l'augmentation annuelle de votre patrimoine en euros depuis l&lsquo;ann<PERSON> de base (2015).", [15229, 15344], "Représente la moyenne simple de l'augmentation annuelle de votre patrimoine en euros depuis l&#39;ann<PERSON> de base (2015).", [15229, 15344], "Représente la moyenne simple de l'augmentation annuelle de votre patrimoine en euros depuis l&rsquo;ann<PERSON> de base (2015).", [3119, 3122], [3119, 3122], [3801, 3804], [3801, 3804], [2032, 2035], [2032, 2035], [16210, 16212], [17182, 17223], "\r\n          Modifier l&apos;objectif\r\n        ", [17182, 17223], "\r\n          Modifier l&lsquo;objectif\r\n        ", [17182, 17223], "\r\n          Modifier l&#39;objectif\r\n        ", [17182, 17223], "\r\n          Modifier l&rsquo;objectif\r\n        ", [2426, 2444], [2426, 2444], [2426, 2444], [2426, 2444], [4232, 4234], [15687, 15714], [15687, 15714], [15687, 15714], [15687, 15714], [16146, 16223], " Ajuster l&apos;allocation d'actifs selon les conditions de marché\r\n              ", [16146, 16223], " Ajuster l&lsquo;allocation d'actifs selon les conditions de marché\r\n              ", [16146, 16223], " Ajuster l&#39;allocation d'actifs selon les conditions de marché\r\n              ", [16146, 16223], " Ajuster l&rsquo;allocation d'actifs selon les conditions de marché\r\n              ", [16146, 16223], " Ajuster l'allocation d&apos;actifs selon les conditions de marché\r\n              ", [16146, 16223], " Ajuster l'allocation d&lsquo;actifs selon les conditions de marché\r\n              ", [16146, 16223], " Ajuster l'allocation d&#39;actifs selon les conditions de marché\r\n              ", [16146, 16223], " Ajuster l'allocation d&rsquo;actifs selon les conditions de marché\r\n              ", [1886, 1939], "[formData.price_per_share, formData.number_of_shares, formData.total_value]", [2413, 2416], [2413, 2416], [7163, 7165], [8217, 8220], [8217, 8220]]