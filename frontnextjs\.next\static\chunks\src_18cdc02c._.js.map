{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/AI-Code/fire_UI/frontnextjs/src/components/ApiClient.ts"], "sourcesContent": ["import axios from 'axios';\r\n\r\nconst apiClient = axios.create({\r\n  baseURL: 'http://localhost:8000/api',\r\n  headers: {\r\n    'Content-Type': 'application/json',\r\n  },\r\n  timeout: 10000, // 10 second timeout\r\n});\r\n\r\n// Add response interceptor for error handling\r\napiClient.interceptors.response.use(\r\n  (response) => response,\r\n  (error) => {\r\n    console.error('API Error:', error.response?.status, error.config?.url, error.message);\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\nexport default apiClient;\r\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,YAAY,wIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAC7B,SAAS;IACT,SAAS;QACP,gBAAgB;IAClB;IACA,SAAS;AACX;AAEA,8CAA8C;AAC9C,UAAU,YAAY,CAAC,QAAQ,CAAC,GAAG,CACjC,CAAC,WAAa,UACd,CAAC;IACC,QAAQ,KAAK,CAAC,cAAc,MAAM,QAAQ,EAAE,QAAQ,MAAM,MAAM,EAAE,KAAK,MAAM,OAAO;IACpF,OAAO,QAAQ,MAAM,CAAC;AACxB;uCAGa", "debugId": null}}, {"offset": {"line": 34, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/AI-Code/fire_UI/frontnextjs/src/app/scpi/page.tsx"], "sourcesContent": ["'use client'; // Ce composant utilise useState, useEffect, etc.\r\n\r\nimport React, { useEffect, useState, FormEvent } from 'react';\r\nimport apiClient from '../../components/ApiClient'; // Chemin ajusté\r\nimport { Modal, Button, Form } from 'react-bootstrap';\r\n\r\ninterface SCPI {\r\n  id: number;\r\n  name: string;\r\n  price_per_share: number;\r\n  number_of_shares: number;\r\n  total_value: number;\r\n  update_date: string;\r\n  primaliance_url?: string;\r\n}\r\n\r\ninterface ScrapedScpiData {\r\n  nom?: string;\r\n  type_de_capital?: string;\r\n  capitalisation?: string;\r\n  collecte_nette_2024?: string;\r\n  collecte_brute_2024?: string;\r\n  parts_en_attente?: string;\r\n  parts_echange?: string;\r\n  versement_des_dividendes?: string;\r\n  frais_reel_de_souscription?: string;\r\n  frais_de_gestion?: string;\r\n  delai_de_jouissance?: string;\r\n  minimum_1ere_souscription?: string;\r\n  prix_retrait_recent?: string;\r\n  prix_souscription_recent?: string;\r\n  date_prix_recent?: string;\r\n}\r\n\r\n// SCPIForm reste un sous-composant\r\nconst SCPIForm: React.FC<{ scpi: Partial<SCPI> | null, onSave: () => void, onCancel: () => void }> = ({ scpi, onSave, onCancel }) => {\r\n    const [formData, setFormData] = useState({\r\n        name: scpi?.name || '',\r\n        price_per_share: scpi?.price_per_share || 0,\r\n        number_of_shares: scpi?.number_of_shares || 0,\r\n        total_value: scpi?.total_value || 0,\r\n        update_date: scpi?.update_date ? new Date(scpi.update_date).toISOString().split('T')[0] : new Date().toISOString().split('T')[0],\r\n        primaliance_url: scpi?.primaliance_url || '',\r\n    });\r\n\r\n    useEffect(() => {\r\n        const calculatedTotal = formData.price_per_share * formData.number_of_shares;\r\n        if (calculatedTotal !== formData.total_value) {\r\n            setFormData(prev => ({ ...prev, total_value: calculatedTotal }));\r\n        }\r\n    }, [formData.price_per_share, formData.number_of_shares, formData.total_value]); // Ajout de formData.total_value pour éviter boucle si initialisé différemment\r\n\r\n    // Mettre à jour le formulaire si la prop scpi change (pour l'édition)\r\n    useEffect(() => {\r\n        setFormData({\r\n            name: scpi?.name || '',\r\n            price_per_share: scpi?.price_per_share || 0,\r\n            number_of_shares: scpi?.number_of_shares || 0,\r\n            total_value: scpi?.total_value || 0,\r\n            update_date: scpi?.update_date ? new Date(scpi.update_date).toISOString().split('T')[0] : new Date().toISOString().split('T')[0],\r\n            primaliance_url: scpi?.primaliance_url || '',\r\n        });\r\n    }, [scpi]);\r\n\r\n\r\n    const handleSubmit = async (e: FormEvent) => {\r\n        e.preventDefault();\r\n        const method = scpi?.id ? 'put' : 'post';\r\n        const url = scpi?.id ? `/scpi/${scpi.id}` : '/scpi';\r\n        try {\r\n            await apiClient[method](url, formData);\r\n            onSave();\r\n        } catch (error: unknown) {\r\n            const errorMessage = error instanceof Error ? error.message : 'Unknown error';\r\n            console.error(\"Erreur lors de la sauvegarde de la SCPI\", errorMessage);\r\n        }\r\n    };\r\n\r\n    return (\r\n        <Form onSubmit={handleSubmit}>\r\n            <Form.Group className=\"mb-3\">\r\n                <Form.Label>Nom de la SCPI</Form.Label>\r\n                <Form.Control type=\"text\" value={formData.name} onChange={(e) => setFormData({ ...formData, name: e.target.value })} required />\r\n            </Form.Group>\r\n            <Form.Group className=\"mb-3\">\r\n                <Form.Label>Prix par part (€)</Form.Label>\r\n                <Form.Control type=\"number\" step=\"0.01\" value={formData.price_per_share} onChange={(e) => setFormData({ ...formData, price_per_share: parseFloat(e.target.value) || 0 })} required />\r\n            </Form.Group>\r\n            <Form.Group className=\"mb-3\">\r\n                <Form.Label>Nombre de parts</Form.Label>\r\n                <Form.Control type=\"number\" value={formData.number_of_shares} onChange={(e) => setFormData({ ...formData, number_of_shares: parseInt(e.target.value) || 0 })} required />\r\n            </Form.Group>\r\n            <Form.Group className=\"mb-3\">\r\n                <Form.Label>Valeur totale (€)</Form.Label>\r\n                <Form.Control type=\"number\" step=\"0.01\" value={formData.total_value} onChange={(e) => setFormData({ ...formData, total_value: parseFloat(e.target.value) || 0 })} required />\r\n                <Form.Text className=\"text-muted\">Calculé automatiquement : {(formData.price_per_share * formData.number_of_shares).toFixed(2)} €</Form.Text>\r\n            </Form.Group>\r\n            <Form.Group className=\"mb-3\">\r\n                <Form.Label>Date de mise à jour</Form.Label>\r\n                <Form.Control type=\"date\" value={formData.update_date} onChange={(e) => setFormData({ ...formData, update_date: e.target.value })} required />\r\n            </Form.Group>\r\n            <Form.Group className=\"mb-3\">\r\n                <Form.Label>URL Primaliance (Optionnel)</Form.Label>\r\n                <Form.Control type=\"url\" value={formData.primaliance_url} onChange={(e) => setFormData({ ...formData, primaliance_url: e.target.value })} placeholder=\"https://www.primaliance.com/...\" />\r\n                <Form.Text className=\"text-muted\">Lien vers la page de la SCPI sur Primaliance pour récupérer des informations détaillées.</Form.Text>\r\n            </Form.Group>\r\n            <div className=\"d-flex justify-content-end gap-2\">\r\n                <Button variant=\"secondary\" onClick={onCancel}>Annuler</Button>\r\n                <Button variant=\"primary\" type=\"submit\">Sauvegarder</Button>\r\n            </div>\r\n        </Form>\r\n    );\r\n};\r\n\r\n// Renommé en SCPIPage\r\nconst SCPIPage: React.FC = () => {\r\n  const [scpis, setSCPIs] = useState<SCPI[]>([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const [showModal, setShowModal] = useState(false);\r\n  const [selectedSCPI, setSelectedSCPI] = useState<Partial<SCPI> | null>(null);\r\n  const [retryCount, setRetryCount] = useState(0);\r\n  const [scrapedData, setScrapedData] = useState<ScrapedScpiData | null>(null);\r\n  const [scrapingSCPIId, setScrapingSCPIId] = useState<number | null>(null);\r\n  const [isScraping, setIsScraping] = useState(false);\r\n  const [scrapingError, setScrapingError] = useState<string | null>(null);\r\n\r\n  const fetchSCPIs = () => {\r\n    setLoading(true);\r\n    setError(null);\r\n    apiClient.get('/scpi')\r\n      .then(response => {\r\n        setSCPIs(response.data);\r\n        setLoading(false);\r\n        setRetryCount(0);\r\n      })\r\n      .catch(error => {\r\n        console.error('SCPI API error:', error);\r\n        if (retryCount < 2) {\r\n          setRetryCount(prev => prev + 1);\r\n          setTimeout(() => fetchSCPIs(), 1000);\r\n        } else {\r\n          setError('Erreur lors de la récupération des SCPI.');\r\n          setLoading(false);\r\n        }\r\n      });\r\n  };\r\n\r\n  useEffect(() => {\r\n    fetchSCPIs();\r\n  // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, []);\r\n\r\n  const handleSave = () => {\r\n    setShowModal(false);\r\n    setSelectedSCPI(null);\r\n    fetchSCPIs();\r\n  };\r\n\r\n  const handleDelete = async (id: number) => {\r\n    if (window.confirm(\"Êtes-vous sûr de vouloir supprimer cette SCPI ?\")) {\r\n        try {\r\n            await apiClient.delete(`/scpi/${id}`);\r\n            fetchSCPIs();\r\n        } catch (error) {\r\n            console.error(\"Erreur lors de la suppression de la SCPI\", error);\r\n        }\r\n    }\r\n  };\r\n\r\n  const handleScrapeSCPI = async (scpiItem: SCPI) => {\r\n    if (!scpiItem.primaliance_url) {\r\n      setScrapingError(\"Aucune URL Primaliance n'est configurée pour cette SCPI.\");\r\n      setScrapedData(null);\r\n      setScrapingSCPIId(scpiItem.id);\r\n      return;\r\n    }\r\n    setIsScraping(true);\r\n    setScrapingError(null);\r\n    setScrapedData(null);\r\n    setScrapingSCPIId(scpiItem.id);\r\n    try {\r\n      const response = await apiClient.get(`/scpi/scrape/?url=${encodeURIComponent(scpiItem.primaliance_url)}`);\r\n      setScrapedData(response.data);\r\n    } catch (err: unknown) {\r\n      const errorMessage = err instanceof Error ? err.message : 'Unknown error';\r\n      console.error(\"Erreur lors du scraping SCPI\", errorMessage);\r\n      setScrapingError(\"Erreur lors de la récupération des données.\");\r\n      setScrapedData(null);\r\n    } finally {\r\n      setIsScraping(false);\r\n    }\r\n  };\r\n\r\n  if (loading && !showModal && retryCount === 0) return <p>Chargement des SCPI...</p>;\r\n  if (error && retryCount >=2) return (\r\n    <div>\r\n      <p className=\"text-danger\">{error}</p>\r\n      <button className=\"btn btn-primary\" onClick={() => {setRetryCount(0); fetchSCPIs();}}>\r\n        Réessayer\r\n      </button>\r\n    </div>\r\n  );\r\n  if (!loading && !scpis.length && !error) return <p>Aucune SCPI trouvée.</p>;\r\n  if (loading && !showModal) return <p>Chargement des SCPI, tentative {retryCount + 1}...</p>;\r\n\r\n  const totalSCPIValue = scpis.reduce((sum, scpiItem) => sum + scpiItem.total_value, 0);\r\n\r\n  return (\r\n    <div>\r\n      <div className=\"d-flex justify-content-between align-items-center mb-4\">\r\n        <h1>Mes SCPI</h1>\r\n        <Button variant=\"primary\" onClick={() => { setSelectedSCPI({}); setShowModal(true); }}>Ajouter une SCPI</Button>\r\n      </div>\r\n      { scpis.length > 0 &&\r\n        <table className=\"table table-striped table-hover\">\r\n          <thead className=\"table-dark\">\r\n            <tr>\r\n              <th>Nom</th>\r\n              <th className=\"text-end\">Prix par part</th>\r\n              <th className=\"text-end\">Nombre de parts</th>\r\n              <th className=\"text-end\">Valeur totale</th>\r\n              <th className=\"text-center\">Date MàJ</th>\r\n              <th className=\"text-center\" style={{ minWidth: '220px' }}>Actions</th>\r\n            </tr>\r\n          </thead>\r\n          <tbody>\r\n            {scpis.map(scpiItem => (\r\n              <React.Fragment key={scpiItem.id}>\r\n                <tr>\r\n                  <td>{scpiItem.name}</td>\r\n                  <td className=\"text-end\">{new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR' }).format(scpiItem.price_per_share)}</td>\r\n                  <td className=\"text-end\">{scpiItem.number_of_shares}</td>\r\n                  <td className=\"text-end\">{new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR' }).format(scpiItem.total_value)}</td>\r\n                  <td className=\"text-center\">{new Date(scpiItem.update_date).toLocaleDateString('fr-FR')}</td>\r\n                  <td className=\"text-center\">\r\n                    <Button variant=\"outline-info\" size=\"sm\" className=\"me-1\" onClick={() => handleScrapeSCPI(scpiItem)} disabled={isScraping && scrapingSCPIId === scpiItem.id}>\r\n                      {isScraping && scrapingSCPIId === scpiItem.id ? 'Chargt...' : 'Primaliance'}\r\n                    </Button>\r\n                    <Button variant=\"outline-primary\" size=\"sm\" className=\"me-1\" onClick={() => { setSelectedSCPI(scpiItem); setShowModal(true); }}>Modifier</Button>\r\n                    <Button variant=\"outline-danger\" size=\"sm\" onClick={() => handleDelete(scpiItem.id)}>Supprimer</Button>\r\n                  </td>\r\n                </tr>\r\n                {scrapingSCPIId === scpiItem.id && (\r\n                  <tr>\r\n                    <td colSpan={6}>\r\n                      {isScraping && <p className=\"text-info m-2\">Chargement des données depuis Primaliance...</p>}\r\n                      {scrapingError && <p className=\"text-danger m-2\">{scrapingError}</p>}\r\n                      {scrapedData && !isScraping && (\r\n                        <div className=\"p-3 my-2 bg-light border rounded\">\r\n                          <h5>Détails de {scrapedData.nom || scpiItem.name} (Primaliance)</h5>\r\n                          <ul className=\"list-unstyled\">\r\n                            {Object.entries(scrapedData).map(([key, value]) => {\r\n                              if (value && key !== 'nom') {\r\n                                const fieldName = key.replace(/_/g, ' ').replace(/\\b\\w/g, l => l.toUpperCase());\r\n                                return <li key={key}><strong>{fieldName}:</strong> {value}</li>;\r\n                              }\r\n                              return null;\r\n                            })}\r\n                          </ul>\r\n                        </div>\r\n                      )}\r\n                    </td>\r\n                  </tr>\r\n                )}\r\n              </React.Fragment>\r\n            ))}\r\n          </tbody>\r\n          <tfoot>\r\n            <tr className=\"table-info\">\r\n              <th colSpan={3}>TOTAL SCPI</th>\r\n              <th className=\"text-end\">{new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR' }).format(totalSCPIValue)}</th>\r\n              <th colSpan={2}></th>\r\n            </tr>\r\n          </tfoot>\r\n        </table>\r\n      }\r\n\r\n      <Modal show={showModal} onHide={() => setShowModal(false)} size=\"lg\">\r\n        <Modal.Header closeButton>\r\n          <Modal.Title>{selectedSCPI?.id ? 'Modifier' : 'Ajouter'} une SCPI</Modal.Title>\r\n        </Modal.Header>\r\n        <Modal.Body>\r\n          <SCPIForm scpi={selectedSCPI} onSave={handleSave} onCancel={() => setShowModal(false)} />\r\n        </Modal.Body>\r\n      </Modal>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default SCPIPage; // Exporter le composant de page\r\n"], "names": [], "mappings": ";;;;AAEA;AACA,oOAAoD,gBAAgB;AACpE;AAAA;AAAA;;;AAJA,cAAc,iDAAiD;;;;AAkC/D,mCAAmC;AACnC,MAAM,WAA+F,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE;;IAC5H,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACrC,MAAM,MAAM,QAAQ;QACpB,iBAAiB,MAAM,mBAAmB;QAC1C,kBAAkB,MAAM,oBAAoB;QAC5C,aAAa,MAAM,eAAe;QAClC,aAAa,MAAM,cAAc,IAAI,KAAK,KAAK,WAAW,EAAE,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,GAAG,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QAChI,iBAAiB,MAAM,mBAAmB;IAC9C;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACN,MAAM,kBAAkB,SAAS,eAAe,GAAG,SAAS,gBAAgB;YAC5E,IAAI,oBAAoB,SAAS,WAAW,EAAE;gBAC1C;0CAAY,CAAA,OAAQ,CAAC;4BAAE,GAAG,IAAI;4BAAE,aAAa;wBAAgB,CAAC;;YAClE;QACJ;6BAAG;QAAC,SAAS,eAAe;QAAE,SAAS,gBAAgB;QAAE,SAAS,WAAW;KAAC,GAAG,8EAA8E;IAE/J,sEAAsE;IACtE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACN,YAAY;gBACR,MAAM,MAAM,QAAQ;gBACpB,iBAAiB,MAAM,mBAAmB;gBAC1C,kBAAkB,MAAM,oBAAoB;gBAC5C,aAAa,MAAM,eAAe;gBAClC,aAAa,MAAM,cAAc,IAAI,KAAK,KAAK,WAAW,EAAE,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,GAAG,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;gBAChI,iBAAiB,MAAM,mBAAmB;YAC9C;QACJ;6BAAG;QAAC;KAAK;IAGT,MAAM,eAAe,OAAO;QACxB,EAAE,cAAc;QAChB,MAAM,SAAS,MAAM,KAAK,QAAQ;QAClC,MAAM,MAAM,MAAM,KAAK,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,GAAG;QAC5C,IAAI;YACA,MAAM,iIAAA,CAAA,UAAS,CAAC,OAAO,CAAC,KAAK;YAC7B;QACJ,EAAE,OAAO,OAAgB;YACrB,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC9D,QAAQ,KAAK,CAAC,2CAA2C;QAC7D;IACJ;IAEA,qBACI,6LAAC,uLAAA,CAAA,OAAI;QAAC,UAAU;;0BACZ,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;gBAAC,WAAU;;kCAClB,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;kCAAC;;;;;;kCACZ,6LAAC,uLAAA,CAAA,OAAI,CAAC,OAAO;wBAAC,MAAK;wBAAO,OAAO,SAAS,IAAI;wBAAE,UAAU,CAAC,IAAM,YAAY;gCAAE,GAAG,QAAQ;gCAAE,MAAM,EAAE,MAAM,CAAC,KAAK;4BAAC;wBAAI,QAAQ;;;;;;;;;;;;0BAEjI,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;gBAAC,WAAU;;kCAClB,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;kCAAC;;;;;;kCACZ,6LAAC,uLAAA,CAAA,OAAI,CAAC,OAAO;wBAAC,MAAK;wBAAS,MAAK;wBAAO,OAAO,SAAS,eAAe;wBAAE,UAAU,CAAC,IAAM,YAAY;gCAAE,GAAG,QAAQ;gCAAE,iBAAiB,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;4BAAE;wBAAI,QAAQ;;;;;;;;;;;;0BAEtL,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;gBAAC,WAAU;;kCAClB,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;kCAAC;;;;;;kCACZ,6LAAC,uLAAA,CAAA,OAAI,CAAC,OAAO;wBAAC,MAAK;wBAAS,OAAO,SAAS,gBAAgB;wBAAE,UAAU,CAAC,IAAM,YAAY;gCAAE,GAAG,QAAQ;gCAAE,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;4BAAE;wBAAI,QAAQ;;;;;;;;;;;;0BAE1K,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;gBAAC,WAAU;;kCAClB,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;kCAAC;;;;;;kCACZ,6LAAC,uLAAA,CAAA,OAAI,CAAC,OAAO;wBAAC,MAAK;wBAAS,MAAK;wBAAO,OAAO,SAAS,WAAW;wBAAE,UAAU,CAAC,IAAM,YAAY;gCAAE,GAAG,QAAQ;gCAAE,aAAa,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;4BAAE;wBAAI,QAAQ;;;;;;kCAC1K,6LAAC,uLAAA,CAAA,OAAI,CAAC,IAAI;wBAAC,WAAU;;4BAAa;4BAA2B,CAAC,SAAS,eAAe,GAAG,SAAS,gBAAgB,EAAE,OAAO,CAAC;4BAAG;;;;;;;;;;;;;0BAEnI,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;gBAAC,WAAU;;kCAClB,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;kCAAC;;;;;;kCACZ,6LAAC,uLAAA,CAAA,OAAI,CAAC,OAAO;wBAAC,MAAK;wBAAO,OAAO,SAAS,WAAW;wBAAE,UAAU,CAAC,IAAM,YAAY;gCAAE,GAAG,QAAQ;gCAAE,aAAa,EAAE,MAAM,CAAC,KAAK;4BAAC;wBAAI,QAAQ;;;;;;;;;;;;0BAE/I,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;gBAAC,WAAU;;kCAClB,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;kCAAC;;;;;;kCACZ,6LAAC,uLAAA,CAAA,OAAI,CAAC,OAAO;wBAAC,MAAK;wBAAM,OAAO,SAAS,eAAe;wBAAE,UAAU,CAAC,IAAM,YAAY;gCAAE,GAAG,QAAQ;gCAAE,iBAAiB,EAAE,MAAM,CAAC,KAAK;4BAAC;wBAAI,aAAY;;;;;;kCACtJ,6LAAC,uLAAA,CAAA,OAAI,CAAC,IAAI;wBAAC,WAAU;kCAAa;;;;;;;;;;;;0BAEtC,6LAAC;gBAAI,WAAU;;kCACX,6LAAC,2LAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAY,SAAS;kCAAU;;;;;;kCAC/C,6LAAC,2LAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAU,MAAK;kCAAS;;;;;;;;;;;;;;;;;;AAIxD;GA7EM;KAAA;AA+EN,sBAAsB;AACtB,MAAM,WAAqB;;IACzB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAwB;IACvE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B;IACvE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACpE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElE,MAAM,aAAa;QACjB,WAAW;QACX,SAAS;QACT,iIAAA,CAAA,UAAS,CAAC,GAAG,CAAC,SACX,IAAI,CAAC,CAAA;YACJ,SAAS,SAAS,IAAI;YACtB,WAAW;YACX,cAAc;QAChB,GACC,KAAK,CAAC,CAAA;YACL,QAAQ,KAAK,CAAC,mBAAmB;YACjC,IAAI,aAAa,GAAG;gBAClB,cAAc,CAAA,OAAQ,OAAO;gBAC7B,WAAW,IAAM,cAAc;YACjC,OAAO;gBACL,SAAS;gBACT,WAAW;YACb;QACF;IACJ;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR;QACF,uDAAuD;QACvD;6BAAG,EAAE;IAEL,MAAM,aAAa;QACjB,aAAa;QACb,gBAAgB;QAChB;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,IAAI,OAAO,OAAO,CAAC,oDAAoD;YACnE,IAAI;gBACA,MAAM,iIAAA,CAAA,UAAS,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,IAAI;gBACpC;YACJ,EAAE,OAAO,OAAO;gBACZ,QAAQ,KAAK,CAAC,4CAA4C;YAC9D;QACJ;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,IAAI,CAAC,SAAS,eAAe,EAAE;YAC7B,iBAAiB;YACjB,eAAe;YACf,kBAAkB,SAAS,EAAE;YAC7B;QACF;QACA,cAAc;QACd,iBAAiB;QACjB,eAAe;QACf,kBAAkB,SAAS,EAAE;QAC7B,IAAI;YACF,MAAM,WAAW,MAAM,iIAAA,CAAA,UAAS,CAAC,GAAG,CAAC,CAAC,kBAAkB,EAAE,mBAAmB,SAAS,eAAe,GAAG;YACxG,eAAe,SAAS,IAAI;QAC9B,EAAE,OAAO,KAAc;YACrB,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC1D,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,iBAAiB;YACjB,eAAe;QACjB,SAAU;YACR,cAAc;QAChB;IACF;IAEA,IAAI,WAAW,CAAC,aAAa,eAAe,GAAG,qBAAO,6LAAC;kBAAE;;;;;;IACzD,IAAI,SAAS,cAAa,GAAG,qBAC3B,6LAAC;;0BACC,6LAAC;gBAAE,WAAU;0BAAe;;;;;;0BAC5B,6LAAC;gBAAO,WAAU;gBAAkB,SAAS;oBAAO,cAAc;oBAAI;gBAAa;0BAAG;;;;;;;;;;;;IAK1F,IAAI,CAAC,WAAW,CAAC,MAAM,MAAM,IAAI,CAAC,OAAO,qBAAO,6LAAC;kBAAE;;;;;;IACnD,IAAI,WAAW,CAAC,WAAW,qBAAO,6LAAC;;YAAE;YAAgC,aAAa;YAAE;;;;;;;IAEpF,MAAM,iBAAiB,MAAM,MAAM,CAAC,CAAC,KAAK,WAAa,MAAM,SAAS,WAAW,EAAE;IAEnF,qBACE,6LAAC;;0BACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;kCAAG;;;;;;kCACJ,6LAAC,2LAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAU,SAAS;4BAAQ,gBAAgB,CAAC;4BAAI,aAAa;wBAAO;kCAAG;;;;;;;;;;;;YAEvF,MAAM,MAAM,GAAG,mBACf,6LAAC;gBAAM,WAAU;;kCACf,6LAAC;wBAAM,WAAU;kCACf,cAAA,6LAAC;;8CACC,6LAAC;8CAAG;;;;;;8CACJ,6LAAC;oCAAG,WAAU;8CAAW;;;;;;8CACzB,6LAAC;oCAAG,WAAU;8CAAW;;;;;;8CACzB,6LAAC;oCAAG,WAAU;8CAAW;;;;;;8CACzB,6LAAC;oCAAG,WAAU;8CAAc;;;;;;8CAC5B,6LAAC;oCAAG,WAAU;oCAAc,OAAO;wCAAE,UAAU;oCAAQ;8CAAG;;;;;;;;;;;;;;;;;kCAG9D,6LAAC;kCACE,MAAM,GAAG,CAAC,CAAA,yBACT,6LAAC,6JAAA,CAAA,UAAK,CAAC,QAAQ;;kDACb,6LAAC;;0DACC,6LAAC;0DAAI,SAAS,IAAI;;;;;;0DAClB,6LAAC;gDAAG,WAAU;0DAAY,IAAI,KAAK,YAAY,CAAC,SAAS;oDAAE,OAAO;oDAAY,UAAU;gDAAM,GAAG,MAAM,CAAC,SAAS,eAAe;;;;;;0DAChI,6LAAC;gDAAG,WAAU;0DAAY,SAAS,gBAAgB;;;;;;0DACnD,6LAAC;gDAAG,WAAU;0DAAY,IAAI,KAAK,YAAY,CAAC,SAAS;oDAAE,OAAO;oDAAY,UAAU;gDAAM,GAAG,MAAM,CAAC,SAAS,WAAW;;;;;;0DAC5H,6LAAC;gDAAG,WAAU;0DAAe,IAAI,KAAK,SAAS,WAAW,EAAE,kBAAkB,CAAC;;;;;;0DAC/E,6LAAC;gDAAG,WAAU;;kEACZ,6LAAC,2LAAA,CAAA,SAAM;wDAAC,SAAQ;wDAAe,MAAK;wDAAK,WAAU;wDAAO,SAAS,IAAM,iBAAiB;wDAAW,UAAU,cAAc,mBAAmB,SAAS,EAAE;kEACxJ,cAAc,mBAAmB,SAAS,EAAE,GAAG,cAAc;;;;;;kEAEhE,6LAAC,2LAAA,CAAA,SAAM;wDAAC,SAAQ;wDAAkB,MAAK;wDAAK,WAAU;wDAAO,SAAS;4DAAQ,gBAAgB;4DAAW,aAAa;wDAAO;kEAAG;;;;;;kEAChI,6LAAC,2LAAA,CAAA,SAAM;wDAAC,SAAQ;wDAAiB,MAAK;wDAAK,SAAS,IAAM,aAAa,SAAS,EAAE;kEAAG;;;;;;;;;;;;;;;;;;oCAGxF,mBAAmB,SAAS,EAAE,kBAC7B,6LAAC;kDACC,cAAA,6LAAC;4CAAG,SAAS;;gDACV,4BAAc,6LAAC;oDAAE,WAAU;8DAAgB;;;;;;gDAC3C,+BAAiB,6LAAC;oDAAE,WAAU;8DAAmB;;;;;;gDACjD,eAAe,CAAC,4BACf,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;gEAAG;gEAAY,YAAY,GAAG,IAAI,SAAS,IAAI;gEAAC;;;;;;;sEACjD,6LAAC;4DAAG,WAAU;sEACX,OAAO,OAAO,CAAC,aAAa,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM;gEAC5C,IAAI,SAAS,QAAQ,OAAO;oEAC1B,MAAM,YAAY,IAAI,OAAO,CAAC,MAAM,KAAK,OAAO,CAAC,SAAS,CAAA,IAAK,EAAE,WAAW;oEAC5E,qBAAO,6LAAC;;0FAAa,6LAAC;;oFAAQ;oFAAU;;;;;;;4EAAU;4EAAE;;uEAApC;;;;;gEAClB;gEACA,OAAO;4DACT;;;;;;;;;;;;;;;;;;;;;;;;+BA9BO,SAAS,EAAE;;;;;;;;;;kCAwCpC,6LAAC;kCACC,cAAA,6LAAC;4BAAG,WAAU;;8CACZ,6LAAC;oCAAG,SAAS;8CAAG;;;;;;8CAChB,6LAAC;oCAAG,WAAU;8CAAY,IAAI,KAAK,YAAY,CAAC,SAAS;wCAAE,OAAO;wCAAY,UAAU;oCAAM,GAAG,MAAM,CAAC;;;;;;8CACxG,6LAAC;oCAAG,SAAS;;;;;;;;;;;;;;;;;;;;;;;0BAMrB,6LAAC,yLAAA,CAAA,QAAK;gBAAC,MAAM;gBAAW,QAAQ,IAAM,aAAa;gBAAQ,MAAK;;kCAC9D,6LAAC,yLAAA,CAAA,QAAK,CAAC,MAAM;wBAAC,WAAW;kCACvB,cAAA,6LAAC,yLAAA,CAAA,QAAK,CAAC,KAAK;;gCAAE,cAAc,KAAK,aAAa;gCAAU;;;;;;;;;;;;kCAE1D,6LAAC,yLAAA,CAAA,QAAK,CAAC,IAAI;kCACT,cAAA,6LAAC;4BAAS,MAAM;4BAAc,QAAQ;4BAAY,UAAU,IAAM,aAAa;;;;;;;;;;;;;;;;;;;;;;;AAKzF;IA7KM;MAAA;uCA+KS;;;;;;;CAAU,gCAAgC", "debugId": null}}]}