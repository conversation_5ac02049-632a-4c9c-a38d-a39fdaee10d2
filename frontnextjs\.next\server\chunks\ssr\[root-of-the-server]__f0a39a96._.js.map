{"version": 3, "sources": [], "sections": [{"offset": {"line": 111, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/AI-Code/fire_UI/frontnextjs/src/components/ApiClient.ts"], "sourcesContent": ["import axios from 'axios';\r\n\r\nconst apiClient = axios.create({\r\n  baseURL: 'http://localhost:8000/api',\r\n  headers: {\r\n    'Content-Type': 'application/json',\r\n  },\r\n  timeout: 10000, // 10 second timeout\r\n});\r\n\r\n// Add response interceptor for error handling\r\napiClient.interceptors.response.use(\r\n  (response) => response,\r\n  (error) => {\r\n    console.error('API Error:', error.response?.status, error.config?.url, error.message);\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\nexport default apiClient;\r\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,YAAY,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAC7B,SAAS;IACT,SAAS;QACP,gBAAgB;IAClB;IACA,SAAS;AACX;AAEA,8CAA8C;AAC9C,UAAU,YAAY,CAAC,QAAQ,CAAC,GAAG,CACjC,CAAC,WAAa,UACd,CAAC;IACC,QAAQ,KAAK,CAAC,cAAc,MAAM,QAAQ,EAAE,QAAQ,MAAM,MAAM,EAAE,KAAK,MAAM,OAAO;IACpF,OAAO,QAAQ,MAAM,CAAC;AACxB;uCAGa", "debugId": null}}, {"offset": {"line": 135, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/AI-Code/fire_UI/frontnextjs/src/app/liabilities/page.tsx"], "sourcesContent": ["'use client'; // Ce composant utilise useState, useEffect, etc.\r\n\r\nimport React, { useEffect, useState, FormEvent } from 'react';\r\nimport apiClient from '../../components/ApiClient'; // Chemin ajusté\r\nimport { Modal, Button, Form } from 'react-bootstrap';\r\n\r\ninterface Liability {\r\n  id: number;\r\n  name: string;\r\n  initial_amount: number;\r\n  remaining_capital: number;\r\n  interest_rate: number;\r\n  end_date: string;\r\n  monthly_payment: number;\r\n}\r\n\r\n// LiabilityForm reste un sous-composant défini dans le même fichier\r\nconst LiabilityForm: React.FC<{ liability: Partial<Liability> | null, onSave: () => void, onCancel: () => void }> = ({ liability, onSave, onCancel }) => {\r\n    const [formData, setFormData] = useState({\r\n        name: liability?.name || '',\r\n        initial_amount: liability?.initial_amount || 0,\r\n        remaining_capital: liability?.remaining_capital || 0,\r\n        interest_rate: liability?.interest_rate || 0,\r\n        monthly_payment: liability?.monthly_payment || 0,\r\n        end_date: liability?.end_date ? new Date(liability.end_date).toISOString().split('T')[0] : new Date().toISOString().split('T')[0],\r\n    });\r\n\r\n    // Mettre à jour le formulaire si la prop liability change (pour l'édition)\r\n    useEffect(() => {\r\n        setFormData({\r\n            name: liability?.name || '',\r\n            initial_amount: liability?.initial_amount || 0,\r\n            remaining_capital: liability?.remaining_capital || 0,\r\n            interest_rate: liability?.interest_rate || 0,\r\n            monthly_payment: liability?.monthly_payment || 0,\r\n            end_date: liability?.end_date ? new Date(liability.end_date).toISOString().split('T')[0] : new Date().toISOString().split('T')[0],\r\n        });\r\n    }, [liability]);\r\n\r\n    const handleSubmit = async (e: FormEvent) => {\r\n        e.preventDefault();\r\n        const method = liability?.id ? 'put' : 'post';\r\n        const url = liability?.id ? `/liabilities/${liability.id}` : '/liabilities';\r\n\r\n        try {\r\n            await apiClient[method](url, formData);\r\n            onSave();\r\n        } catch (error) {\r\n            console.error(\"Erreur lors de la sauvegarde de l'emprunt\", error);\r\n            // Gérer l'affichage de l'erreur à l'utilisateur si nécessaire\r\n        }\r\n    };\r\n\r\n    return (\r\n        <Form onSubmit={handleSubmit}>\r\n            <Form.Group className=\"mb-3\">\r\n                <Form.Label>Nom</Form.Label>\r\n                <Form.Control type=\"text\" value={formData.name} onChange={e => setFormData({ ...formData, name: e.target.value })} required />\r\n            </Form.Group>\r\n            <Form.Group className=\"mb-3\">\r\n                <Form.Label>Capital Restant Dû</Form.Label>\r\n                <Form.Control type=\"number\" value={formData.remaining_capital} onChange={e => setFormData({ ...formData, remaining_capital: parseFloat(e.target.value) })} required />\r\n            </Form.Group>\r\n            <Form.Group className=\"mb-3\">\r\n                <Form.Label>Mensualité</Form.Label>\r\n                <Form.Control type=\"number\" value={formData.monthly_payment} onChange={e => setFormData({ ...formData, monthly_payment: parseFloat(e.target.value) })} required />\r\n            </Form.Group>\r\n            <Form.Group className=\"mb-3\">\r\n                <Form.Label>Taux d&apos;intérêt (%)</Form.Label>\r\n                <Form.Control type=\"number\" step=\"0.01\" value={formData.interest_rate} onChange={e => setFormData({ ...formData, interest_rate: parseFloat(e.target.value) })} required />\r\n            </Form.Group>\r\n            <Form.Group className=\"mb-3\">\r\n                <Form.Label>Date de fin</Form.Label>\r\n                <Form.Control type=\"date\" value={formData.end_date} onChange={e => setFormData({ ...formData, end_date: e.target.value })} required />\r\n            </Form.Group>\r\n            {/* Ajout du champ initial_amount pour la création, non modifiable pour l'update pour l'instant */}\r\n            {!liability?.id && (\r\n                 <Form.Group className=\"mb-3\">\r\n                    <Form.Label>Montant Initial (optionnel si non pertinent)</Form.Label>\r\n                    <Form.Control type=\"number\" value={formData.initial_amount} onChange={e => setFormData({ ...formData, initial_amount: parseFloat(e.target.value) })} />\r\n                </Form.Group>\r\n            )}\r\n            <Button variant=\"primary\" type=\"submit\">Sauvegarder</Button>\r\n            <Button variant=\"secondary\" onClick={onCancel} className=\"ms-2\">Annuler</Button>\r\n        </Form>\r\n    );\r\n};\r\n\r\n// Renommé en LiabilitiesPage\r\nconst LiabilitiesPage: React.FC = () => {\r\n  const [liabilities, setLiabilities] = useState<Liability[]>([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const [showModal, setShowModal] = useState(false);\r\n  const [selectedLiability, setSelectedLiability] = useState<Partial<Liability> | null>(null);\r\n  const [retryCount, setRetryCount] = useState(0);\r\n\r\n  const fetchLiabilities = () => {\r\n    setLoading(true);\r\n    setError(null);\r\n    apiClient.get('/liabilities')\r\n      .then(response => {\r\n        setLiabilities(response.data);\r\n        setLoading(false);\r\n        setRetryCount(0);\r\n      })\r\n      .catch(error => {\r\n        console.error('Liabilities API error:', error);\r\n        if (retryCount < 2) {\r\n          setRetryCount(prev => prev + 1);\r\n          setTimeout(() => fetchLiabilities(), 1000);\r\n        } else {\r\n          setError('Erreur lors de la récupération des emprunts.');\r\n          setLoading(false);\r\n        }\r\n      });\r\n  };\r\n\r\n  useEffect(() => {\r\n    fetchLiabilities();\r\n  // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, []);\r\n\r\n  const handleSave = () => {\r\n    setShowModal(false);\r\n    setSelectedLiability(null);\r\n    fetchLiabilities();\r\n  };\r\n\r\n  const handleDelete = async (id: number) => {\r\n    if (window.confirm(\"Êtes-vous sûr de vouloir supprimer cet emprunt ?\")) {\r\n        try {\r\n            await apiClient.delete(`/liabilities/${id}`);\r\n            fetchLiabilities();\r\n        } catch (error) {\r\n            console.error(\"Erreur lors de la suppression de l'emprunt\", error);\r\n        }\r\n    }\r\n  };\r\n\r\n  if (loading && !showModal && retryCount === 0) return <p>Chargement des emprunts...</p>;\r\n  if (error && retryCount >=2) return (\r\n    <div>\r\n      <p className=\"text-danger\">{error}</p>\r\n      <button className=\"btn btn-primary\" onClick={() => {setRetryCount(0); fetchLiabilities();}}>\r\n        Réessayer\r\n      </button>\r\n    </div>\r\n  );\r\n  if (!loading && !liabilities.length && !error) return <p>Aucun emprunt trouvé.</p>;\r\n  if (loading && !showModal) return <p>Chargement des emprunts, tentative {retryCount + 1}...</p>;\r\n\r\n\r\n  return (\r\n    <div>\r\n      <div className=\"d-flex justify-content-between align-items-center mb-4\">\r\n        <h1>Mes Emprunts (Passifs)</h1>\r\n        <Button variant=\"primary\" onClick={() => { setSelectedLiability({}); setShowModal(true); }}>Ajouter un Emprunt</Button>\r\n      </div>\r\n      { liabilities.length > 0 &&\r\n        <table className=\"table table-striped table-hover\">\r\n          <thead className=\"table-dark\">\r\n            <tr>\r\n              <th>Nom</th>\r\n              <th className=\"text-end\">Capital Restant Dû</th>\r\n              <th className=\"text-end\">Mensualité</th>\r\n              <th className=\"text-center\">Taux</th>\r\n              <th className=\"text-center\">Date de Fin</th>\r\n              <th className=\"text-center\">Actions</th>\r\n            </tr>\r\n          </thead>\r\n          <tbody>\r\n            {liabilities.map(liability => (\r\n              <tr key={liability.id}>\r\n                <td>{liability.name}</td>\r\n                <td className=\"text-end\">{new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR' }).format(liability.remaining_capital)}</td>\r\n                <td className=\"text-end\">{new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR' }).format(liability.monthly_payment)}</td>\r\n                <td className=\"text-center\">{liability.interest_rate}%</td>\r\n                <td className=\"text-center\">{new Date(liability.end_date).toLocaleDateString('fr-FR')}</td>\r\n                <td className=\"text-center\">\r\n                  <Button variant=\"outline-primary\" size=\"sm\" onClick={() => { setSelectedLiability(liability); setShowModal(true); }}>Modifier</Button>{' '}\r\n                  <Button variant=\"outline-danger\" size=\"sm\" onClick={() => handleDelete(liability.id)}>Supprimer</Button>\r\n                </td>\r\n              </tr>\r\n            ))}\r\n          </tbody>\r\n        </table>\r\n      }\r\n\r\n      <Modal show={showModal} onHide={() => setShowModal(false)}>\r\n        <Modal.Header closeButton>\r\n          <Modal.Title>{selectedLiability?.id ? 'Modifier' : 'Ajouter'} un Emprunt</Modal.Title>\r\n        </Modal.Header>\r\n        <Modal.Body>\r\n          <LiabilityForm liability={selectedLiability} onSave={handleSave} onCancel={() => setShowModal(false)} />\r\n        </Modal.Body>\r\n      </Modal>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default LiabilitiesPage; // Exporter le composant de page\r\n"], "names": [], "mappings": ";;;;AAEA;AACA,8NAAoD,gBAAgB;AACpE;AAAA;AAAA;AAJA,cAAc,iDAAiD;;;;;AAgB/D,oEAAoE;AACpE,MAAM,gBAA8G,CAAC,EAAE,SAAS,EAAE,MAAM,EAAE,QAAQ,EAAE;IAChJ,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACrC,MAAM,WAAW,QAAQ;QACzB,gBAAgB,WAAW,kBAAkB;QAC7C,mBAAmB,WAAW,qBAAqB;QACnD,eAAe,WAAW,iBAAiB;QAC3C,iBAAiB,WAAW,mBAAmB;QAC/C,UAAU,WAAW,WAAW,IAAI,KAAK,UAAU,QAAQ,EAAE,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,GAAG,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;IACrI;IAEA,2EAA2E;IAC3E,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,YAAY;YACR,MAAM,WAAW,QAAQ;YACzB,gBAAgB,WAAW,kBAAkB;YAC7C,mBAAmB,WAAW,qBAAqB;YACnD,eAAe,WAAW,iBAAiB;YAC3C,iBAAiB,WAAW,mBAAmB;YAC/C,UAAU,WAAW,WAAW,IAAI,KAAK,UAAU,QAAQ,EAAE,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,GAAG,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QACrI;IACJ,GAAG;QAAC;KAAU;IAEd,MAAM,eAAe,OAAO;QACxB,EAAE,cAAc;QAChB,MAAM,SAAS,WAAW,KAAK,QAAQ;QACvC,MAAM,MAAM,WAAW,KAAK,CAAC,aAAa,EAAE,UAAU,EAAE,EAAE,GAAG;QAE7D,IAAI;YACA,MAAM,8HAAA,CAAA,UAAS,CAAC,OAAO,CAAC,KAAK;YAC7B;QACJ,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,6CAA6C;QAC3D,8DAA8D;QAClE;IACJ;IAEA,qBACI,8OAAC,oLAAA,CAAA,OAAI;QAAC,UAAU;;0BACZ,8OAAC,oLAAA,CAAA,OAAI,CAAC,KAAK;gBAAC,WAAU;;kCAClB,8OAAC,oLAAA,CAAA,OAAI,CAAC,KAAK;kCAAC;;;;;;kCACZ,8OAAC,oLAAA,CAAA,OAAI,CAAC,OAAO;wBAAC,MAAK;wBAAO,OAAO,SAAS,IAAI;wBAAE,UAAU,CAAA,IAAK,YAAY;gCAAE,GAAG,QAAQ;gCAAE,MAAM,EAAE,MAAM,CAAC,KAAK;4BAAC;wBAAI,QAAQ;;;;;;;;;;;;0BAE/H,8OAAC,oLAAA,CAAA,OAAI,CAAC,KAAK;gBAAC,WAAU;;kCAClB,8OAAC,oLAAA,CAAA,OAAI,CAAC,KAAK;kCAAC;;;;;;kCACZ,8OAAC,oLAAA,CAAA,OAAI,CAAC,OAAO;wBAAC,MAAK;wBAAS,OAAO,SAAS,iBAAiB;wBAAE,UAAU,CAAA,IAAK,YAAY;gCAAE,GAAG,QAAQ;gCAAE,mBAAmB,WAAW,EAAE,MAAM,CAAC,KAAK;4BAAE;wBAAI,QAAQ;;;;;;;;;;;;0BAEvK,8OAAC,oLAAA,CAAA,OAAI,CAAC,KAAK;gBAAC,WAAU;;kCAClB,8OAAC,oLAAA,CAAA,OAAI,CAAC,KAAK;kCAAC;;;;;;kCACZ,8OAAC,oLAAA,CAAA,OAAI,CAAC,OAAO;wBAAC,MAAK;wBAAS,OAAO,SAAS,eAAe;wBAAE,UAAU,CAAA,IAAK,YAAY;gCAAE,GAAG,QAAQ;gCAAE,iBAAiB,WAAW,EAAE,MAAM,CAAC,KAAK;4BAAE;wBAAI,QAAQ;;;;;;;;;;;;0BAEnK,8OAAC,oLAAA,CAAA,OAAI,CAAC,KAAK;gBAAC,WAAU;;kCAClB,8OAAC,oLAAA,CAAA,OAAI,CAAC,KAAK;kCAAC;;;;;;kCACZ,8OAAC,oLAAA,CAAA,OAAI,CAAC,OAAO;wBAAC,MAAK;wBAAS,MAAK;wBAAO,OAAO,SAAS,aAAa;wBAAE,UAAU,CAAA,IAAK,YAAY;gCAAE,GAAG,QAAQ;gCAAE,eAAe,WAAW,EAAE,MAAM,CAAC,KAAK;4BAAE;wBAAI,QAAQ;;;;;;;;;;;;0BAE3K,8OAAC,oLAAA,CAAA,OAAI,CAAC,KAAK;gBAAC,WAAU;;kCAClB,8OAAC,oLAAA,CAAA,OAAI,CAAC,KAAK;kCAAC;;;;;;kCACZ,8OAAC,oLAAA,CAAA,OAAI,CAAC,OAAO;wBAAC,MAAK;wBAAO,OAAO,SAAS,QAAQ;wBAAE,UAAU,CAAA,IAAK,YAAY;gCAAE,GAAG,QAAQ;gCAAE,UAAU,EAAE,MAAM,CAAC,KAAK;4BAAC;wBAAI,QAAQ;;;;;;;;;;;;YAGtI,CAAC,WAAW,oBACR,8OAAC,oLAAA,CAAA,OAAI,CAAC,KAAK;gBAAC,WAAU;;kCACnB,8OAAC,oLAAA,CAAA,OAAI,CAAC,KAAK;kCAAC;;;;;;kCACZ,8OAAC,oLAAA,CAAA,OAAI,CAAC,OAAO;wBAAC,MAAK;wBAAS,OAAO,SAAS,cAAc;wBAAE,UAAU,CAAA,IAAK,YAAY;gCAAE,GAAG,QAAQ;gCAAE,gBAAgB,WAAW,EAAE,MAAM,CAAC,KAAK;4BAAE;;;;;;;;;;;;0BAGzJ,8OAAC,wLAAA,CAAA,SAAM;gBAAC,SAAQ;gBAAU,MAAK;0BAAS;;;;;;0BACxC,8OAAC,wLAAA,CAAA,SAAM;gBAAC,SAAQ;gBAAY,SAAS;gBAAU,WAAU;0BAAO;;;;;;;;;;;;AAG5E;AAEA,6BAA6B;AAC7B,MAAM,kBAA4B;IAChC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe,EAAE;IAC9D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA6B;IACtF,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,mBAAmB;QACvB,WAAW;QACX,SAAS;QACT,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAC,gBACX,IAAI,CAAC,CAAA;YACJ,eAAe,SAAS,IAAI;YAC5B,WAAW;YACX,cAAc;QAChB,GACC,KAAK,CAAC,CAAA;YACL,QAAQ,KAAK,CAAC,0BAA0B;YACxC,IAAI,aAAa,GAAG;gBAClB,cAAc,CAAA,OAAQ,OAAO;gBAC7B,WAAW,IAAM,oBAAoB;YACvC,OAAO;gBACL,SAAS;gBACT,WAAW;YACb;QACF;IACJ;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,uDAAuD;IACvD,GAAG,EAAE;IAEL,MAAM,aAAa;QACjB,aAAa;QACb,qBAAqB;QACrB;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,IAAI,OAAO,OAAO,CAAC,qDAAqD;YACpE,IAAI;gBACA,MAAM,8HAAA,CAAA,UAAS,CAAC,MAAM,CAAC,CAAC,aAAa,EAAE,IAAI;gBAC3C;YACJ,EAAE,OAAO,OAAO;gBACZ,QAAQ,KAAK,CAAC,8CAA8C;YAChE;QACJ;IACF;IAEA,IAAI,WAAW,CAAC,aAAa,eAAe,GAAG,qBAAO,8OAAC;kBAAE;;;;;;IACzD,IAAI,SAAS,cAAa,GAAG,qBAC3B,8OAAC;;0BACC,8OAAC;gBAAE,WAAU;0BAAe;;;;;;0BAC5B,8OAAC;gBAAO,WAAU;gBAAkB,SAAS;oBAAO,cAAc;oBAAI;gBAAmB;0BAAG;;;;;;;;;;;;IAKhG,IAAI,CAAC,WAAW,CAAC,YAAY,MAAM,IAAI,CAAC,OAAO,qBAAO,8OAAC;kBAAE;;;;;;IACzD,IAAI,WAAW,CAAC,WAAW,qBAAO,8OAAC;;YAAE;YAAoC,aAAa;YAAE;;;;;;;IAGxF,qBACE,8OAAC;;0BACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;kCAAG;;;;;;kCACJ,8OAAC,wLAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAU,SAAS;4BAAQ,qBAAqB,CAAC;4BAAI,aAAa;wBAAO;kCAAG;;;;;;;;;;;;YAE5F,YAAY,MAAM,GAAG,mBACrB,8OAAC;gBAAM,WAAU;;kCACf,8OAAC;wBAAM,WAAU;kCACf,cAAA,8OAAC;;8CACC,8OAAC;8CAAG;;;;;;8CACJ,8OAAC;oCAAG,WAAU;8CAAW;;;;;;8CACzB,8OAAC;oCAAG,WAAU;8CAAW;;;;;;8CACzB,8OAAC;oCAAG,WAAU;8CAAc;;;;;;8CAC5B,8OAAC;oCAAG,WAAU;8CAAc;;;;;;8CAC5B,8OAAC;oCAAG,WAAU;8CAAc;;;;;;;;;;;;;;;;;kCAGhC,8OAAC;kCACE,YAAY,GAAG,CAAC,CAAA,0BACf,8OAAC;;kDACC,8OAAC;kDAAI,UAAU,IAAI;;;;;;kDACnB,8OAAC;wCAAG,WAAU;kDAAY,IAAI,KAAK,YAAY,CAAC,SAAS;4CAAE,OAAO;4CAAY,UAAU;wCAAM,GAAG,MAAM,CAAC,UAAU,iBAAiB;;;;;;kDACnI,8OAAC;wCAAG,WAAU;kDAAY,IAAI,KAAK,YAAY,CAAC,SAAS;4CAAE,OAAO;4CAAY,UAAU;wCAAM,GAAG,MAAM,CAAC,UAAU,eAAe;;;;;;kDACjI,8OAAC;wCAAG,WAAU;;4CAAe,UAAU,aAAa;4CAAC;;;;;;;kDACrD,8OAAC;wCAAG,WAAU;kDAAe,IAAI,KAAK,UAAU,QAAQ,EAAE,kBAAkB,CAAC;;;;;;kDAC7E,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC,wLAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAkB,MAAK;gDAAK,SAAS;oDAAQ,qBAAqB;oDAAY,aAAa;gDAAO;0DAAG;;;;;;4CAAkB;0DACvI,8OAAC,wLAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAiB,MAAK;gDAAK,SAAS,IAAM,aAAa,UAAU,EAAE;0DAAG;;;;;;;;;;;;;+BARjF,UAAU,EAAE;;;;;;;;;;;;;;;;0BAgB7B,8OAAC,sLAAA,CAAA,QAAK;gBAAC,MAAM;gBAAW,QAAQ,IAAM,aAAa;;kCACjD,8OAAC,sLAAA,CAAA,QAAK,CAAC,MAAM;wBAAC,WAAW;kCACvB,cAAA,8OAAC,sLAAA,CAAA,QAAK,CAAC,KAAK;;gCAAE,mBAAmB,KAAK,aAAa;gCAAU;;;;;;;;;;;;kCAE/D,8OAAC,sLAAA,CAAA,QAAK,CAAC,IAAI;kCACT,cAAA,8OAAC;4BAAc,WAAW;4BAAmB,QAAQ;4BAAY,UAAU,IAAM,aAAa;;;;;;;;;;;;;;;;;;;;;;;AAKxG;uCAEe;CAAiB,gCAAgC", "debugId": null}}]}