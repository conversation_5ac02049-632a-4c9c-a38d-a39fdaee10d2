(()=>{var e={};e.id=919,e.ids=[919],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},17113:(e,t,r)=>{"use strict";r.d(t,{A:()=>c});var s=r(69662),a=r.n(s),n=r(43210),i=r(98466),l=r(60687);let o=n.forwardRef(({bsPrefix:e,bg:t="primary",pill:r=!1,text:s,className:n,as:o="span",...c},d)=>{let m=(0,i.oU)(e,"badge");return(0,l.jsx)(o,{ref:d,...c,className:a()(n,m,r&&"rounded-pill",s&&`text-${s}`,t&&`bg-${t}`)})});o.displayName="Badge";let c=o},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},24255:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29190:(e,t,r)=>{"use strict";r.d(t,{default:()=>l});var s=r(60687),a=r(43210),n=r(85814),i=r.n(n);let l=()=>{let[e,t]=(0,a.useState)(!1),r=()=>{t(!1)};return(0,s.jsx)("nav",{className:"navbar navbar-expand-lg navbar-dark bg-dark",children:(0,s.jsxs)("div",{className:"container-fluid",children:[(0,s.jsx)(i(),{className:"navbar-brand",href:"/",onClick:r,children:"FIRE Dashboard"}),(0,s.jsx)("button",{className:"navbar-toggler",type:"button",onClick:()=>{t(!e)},"aria-controls":"navbarNavContent","aria-expanded":e,"aria-label":"Toggle navigation",children:(0,s.jsx)("span",{className:"navbar-toggler-icon"})}),(0,s.jsx)("div",{className:`collapse navbar-collapse ${e?"show":""}`,id:"navbarNavContent",children:(0,s.jsxs)("ul",{className:"navbar-nav me-auto mb-2 mb-lg-0",children:[(0,s.jsx)("li",{className:"nav-item",children:(0,s.jsx)(i(),{className:"nav-link",href:"/",onClick:r,children:"Dashboard"})}),(0,s.jsx)("li",{className:"nav-item",children:(0,s.jsx)(i(),{className:"nav-link",href:"/assets",onClick:r,children:"Patrimoine"})}),(0,s.jsx)("li",{className:"nav-item",children:(0,s.jsx)(i(),{className:"nav-link",href:"/liabilities",onClick:r,children:"Emprunts"})}),(0,s.jsx)("li",{className:"nav-item",children:(0,s.jsx)(i(),{className:"nav-link",href:"/scpi",onClick:r,children:"SCPI"})}),(0,s.jsx)("li",{className:"nav-item",children:(0,s.jsx)(i(),{className:"nav-link",href:"/evolution",onClick:r,children:"\xc9volution"})}),(0,s.jsx)("li",{className:"nav-item",children:(0,s.jsx)(i(),{className:"nav-link",href:"/budget",onClick:r,children:"Budget FIRE"})}),(0,s.jsx)("li",{className:"nav-item",children:(0,s.jsx)(i(),{className:"nav-link",href:"/scenarios",onClick:r,children:"Sc\xe9narios"})}),(0,s.jsx)("li",{className:"nav-item",children:(0,s.jsx)(i(),{className:"nav-link",href:"/fire",onClick:r,children:"Objectif FIRE"})}),(0,s.jsx)("li",{className:"nav-item",children:(0,s.jsx)(i(),{className:"nav-link",href:"/test",onClick:r,children:"Test API"})})]})})]})})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30004:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\AI-Code\\\\fire_UI\\\\frontnextjs\\\\src\\\\components\\\\Navbar.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\AI-Code\\fire_UI\\frontnextjs\\src\\components\\Navbar.tsx","default")},33873:e=>{"use strict";e.exports=require("path")},34259:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\AI-Code\\\\fire_UI\\\\frontnextjs\\\\src\\\\app\\\\fire\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\AI-Code\\fire_UI\\frontnextjs\\src\\app\\fire\\page.tsx","default")},39522:(e,t,r)=>{"use strict";r.d(t,{A:()=>m});var s=r(69662),a=r.n(s),n=r(43210),i=r(98466),l=r(22057),o=r(60687);function c({min:e,now:t,max:r,label:s,visuallyHidden:n,striped:i,animated:l,className:c,style:d,variant:m,bsPrefix:u,...h},p){return(0,o.jsx)("div",{ref:p,...h,role:"progressbar",className:a()(c,`${u}-bar`,{[`bg-${m}`]:m,[`${u}-bar-animated`]:l,[`${u}-bar-striped`]:l||i}),style:{width:`${Math.round((t-e)/(r-e)*1e5)/1e3}%`,...d},"aria-valuenow":t,"aria-valuemin":e,"aria-valuemax":r,children:n?(0,o.jsx)("span",{className:"visually-hidden",children:s}):s})}let d=n.forwardRef(({isChild:e=!1,...t},r)=>{let s={min:0,max:100,animated:!1,visuallyHidden:!1,striped:!1,...t};if(s.bsPrefix=(0,i.oU)(s.bsPrefix,"progress"),e)return c(s,r);let{min:d,now:m,max:u,label:h,visuallyHidden:p,striped:x,animated:v,bsPrefix:f,variant:j,className:g,children:b,...N}=s;return(0,o.jsx)("div",{ref:r,...N,className:a()(g,f),children:b?(0,l.Tj)(b,e=>(0,n.cloneElement)(e,{isChild:!0})):c({min:d,now:m,max:u,label:h,visuallyHidden:p,striped:x,animated:v,bsPrefix:f,variant:j},r)})});d.displayName="ProgressBar";let m=d},40414:e=>{"use strict";e.exports=function(e,t,r,s,a,n,i,l){if(!e){var o;if(void 0===t)o=Error("Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.");else{var c=[r,s,a,n,i,l],d=0;(o=Error(t.replace(/%s/g,function(){return c[d++]}))).name="Invariant Violation"}throw o.framesToPop=1,o}}},41004:(e,t,r)=>{Promise.resolve().then(r.bind(r,77653))},54156:(e,t,r)=>{Promise.resolve().then(r.bind(r,34259))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},61135:()=>{},62907:(e,t,r)=>{Promise.resolve().then(r.bind(r,30004))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},73523:(e,t,r)=>{Promise.resolve().then(r.bind(r,29190))},74075:e=>{"use strict";e.exports=require("zlib")},77653:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>O});var s=r(60687),a=r(43210),n=r.n(a),i=r(90317),l=r(48710),o=r(92388),c=r(69662),d=r.n(c),m=r(78282),u=r(98466),h=r(7316),p=r(79013),x=r(20928),v=r(34222);let f=function(...e){return e.filter(e=>null!=e).reduce((e,t)=>{if("function"!=typeof t)throw Error("Invalid Argument Type, must only provide functions, undefined, or null.");return null===e?t:function(...r){e.apply(this,r),t.apply(this,r)}},null)};var j=r(7025),g=r(59727);let b={height:["marginTop","marginBottom"],width:["marginLeft","marginRight"]};function N(e,t){let r=t[`offset${e[0].toUpperCase()}${e.slice(1)}`],s=b[e];return r+parseInt((0,h.default)(t,s[0]),10)+parseInt((0,h.default)(t,s[1]),10)}let y={[p.kp]:"collapse",[p.ze]:"collapsing",[p.ns]:"collapsing",[p._K]:"collapse show"},A=n().forwardRef(({onEnter:e,onEntering:t,onEntered:r,onExit:i,onExiting:l,className:o,children:c,dimension:m="height",in:u=!1,timeout:h=300,mountOnEnter:p=!1,unmountOnExit:b=!1,appear:A=!1,getDimensionValue:P=N,...C},w)=>{let _="function"==typeof m?m():m,E=(0,a.useMemo)(()=>f(e=>{e.style[_]="0"},e),[_,e]),I=(0,a.useMemo)(()=>f(e=>{let t=`scroll${_[0].toUpperCase()}${_.slice(1)}`;e.style[_]=`${e[t]}px`},t),[_,t]),R=(0,a.useMemo)(()=>f(e=>{e.style[_]=null},r),[_,r]),F=(0,a.useMemo)(()=>f(e=>{e.style[_]=`${P(_,e)}px`,(0,j.A)(e)},i),[i,P,_]),k=(0,a.useMemo)(()=>f(e=>{e.style[_]=null},l),[_,l]);return(0,s.jsx)(g.A,{ref:w,addEndListener:v.A,...C,"aria-expanded":C.role?u:null,onEnter:E,onEntering:I,onEntered:R,onExit:F,onExiting:k,childRef:(0,x.getChildRef)(c),in:u,timeout:h,mountOnEnter:p,unmountOnExit:b,appear:A,children:(e,t)=>n().cloneElement(c,{...t,className:d()(o,c.props.className,y[e],"width"===_&&"collapse-horizontal")})})});function P(e,t){return Array.isArray(e)?e.includes(t):e===t}A.displayName="Collapse";let C=a.createContext({});C.displayName="AccordionContext";let w=a.forwardRef(({as:e="div",bsPrefix:t,className:r,children:n,eventKey:i,...l},o)=>{let{activeEventKey:c}=(0,a.useContext)(C);return t=(0,u.oU)(t,"accordion-collapse"),(0,s.jsx)(A,{ref:o,in:P(c,i),...l,className:d()(r,t),children:(0,s.jsx)(e,{children:a.Children.only(n)})})});w.displayName="AccordionCollapse";let _=a.createContext({eventKey:""});_.displayName="AccordionItemContext";let E=a.forwardRef(({as:e="div",bsPrefix:t,className:r,onEnter:n,onEntering:i,onEntered:l,onExit:o,onExiting:c,onExited:m,...h},p)=>{t=(0,u.oU)(t,"accordion-body");let{eventKey:x}=(0,a.useContext)(_);return(0,s.jsx)(w,{eventKey:x,onEnter:n,onEntering:i,onEntered:l,onExit:o,onExiting:c,onExited:m,children:(0,s.jsx)(e,{ref:p,...h,className:d()(r,t)})})});E.displayName="AccordionBody";let I=a.forwardRef(({as:e="button",bsPrefix:t,className:r,onClick:n,...i},l)=>{t=(0,u.oU)(t,"accordion-button");let{eventKey:o}=(0,a.useContext)(_),c=function(e,t){let{activeEventKey:r,onSelect:s,alwaysOpen:n}=(0,a.useContext)(C);return a=>{let i=e===r?null:e;n&&(i=Array.isArray(r)?r.includes(e)?r.filter(t=>t!==e):[...r,e]:[e]),null==s||s(i,a),null==t||t(a)}}(o,n),{activeEventKey:m}=(0,a.useContext)(C);return"button"===e&&(i.type="button"),(0,s.jsx)(e,{ref:l,onClick:c,...i,"aria-expanded":Array.isArray(m)?m.includes(o):o===m,className:d()(r,t,!P(m,o)&&"collapsed")})});I.displayName="AccordionButton";let R=a.forwardRef(({as:e="h2","aria-controls":t,bsPrefix:r,className:a,children:n,onClick:i,...l},o)=>(r=(0,u.oU)(r,"accordion-header"),(0,s.jsx)(e,{ref:o,...l,className:d()(a,r),children:(0,s.jsx)(I,{onClick:i,"aria-controls":t,children:n})})));R.displayName="AccordionHeader";let F=a.forwardRef(({as:e="div",bsPrefix:t,className:r,eventKey:n,...i},l)=>{t=(0,u.oU)(t,"accordion-item");let o=(0,a.useMemo)(()=>({eventKey:n}),[n]);return(0,s.jsx)(_.Provider,{value:o,children:(0,s.jsx)(e,{ref:l,...i,className:d()(r,t)})})});F.displayName="AccordionItem";let k=a.forwardRef((e,t)=>{let{as:r="div",activeKey:n,bsPrefix:i,className:l,onSelect:o,flush:c,alwaysOpen:h,...p}=(0,m.Zw)(e,{activeKey:"onSelect"}),x=(0,u.oU)(i,"accordion"),v=(0,a.useMemo)(()=>({activeEventKey:n,onSelect:o,alwaysOpen:h}),[n,o,h]);return(0,s.jsx)(C.Provider,{value:v,children:(0,s.jsx)(r,{ref:t,...p,className:d()(l,x,c&&`${x}-flush`)})})});k.displayName="Accordion";let S=Object.assign(k,{Button:I,Collapse:w,Item:F,Header:R,Body:E});var D=r(17113),T=r(39522),M=r(25865);let q=({settings:e,onSave:t,onCancel:r})=>{let[n,c]=(0,a.useState)({fire_target_amount:e?.fire_target_amount||910150,secure_withdrawal_rate:100*(e?.secure_withdrawal_rate||.04)});(0,a.useEffect)(()=>{c({fire_target_amount:e?.fire_target_amount||910150,secure_withdrawal_rate:100*(e?.secure_withdrawal_rate||.04)})},[e]);let d=async e=>{e.preventDefault();let r={fire_target_amount:n.fire_target_amount,secure_withdrawal_rate:n.secure_withdrawal_rate/100};try{await i.A.put("/fire-settings",r),t()}catch(e){console.error("Erreur lors de la sauvegarde des param\xe8tres FIRE",e instanceof Error?e.message:"Unknown error")}};return(0,s.jsxs)(l.A,{onSubmit:d,children:[(0,s.jsxs)(l.A.Group,{className:"mb-3",children:[(0,s.jsx)(l.A.Label,{children:"Objectif FIRE (€)"}),(0,s.jsx)(l.A.Control,{type:"number",step:"1000",value:n.fire_target_amount,onChange:e=>c({...n,fire_target_amount:parseFloat(e.target.value)||0}),required:!0}),(0,s.jsx)(l.A.Text,{className:"text-muted",children:"Montant total que vous souhaitez atteindre pour votre ind\xe9pendance financi\xe8re"})]}),(0,s.jsxs)(l.A.Group,{className:"mb-3",children:[(0,s.jsx)(l.A.Label,{children:"Taux de retrait s\xe9curis\xe9 (%)"}),(0,s.jsx)(l.A.Control,{type:"number",step:"0.1",min:"1",max:"10",value:n.secure_withdrawal_rate,onChange:e=>c({...n,secure_withdrawal_rate:parseFloat(e.target.value)||4}),required:!0}),(0,s.jsx)(l.A.Text,{className:"text-muted",children:"Pourcentage de votre patrimoine que vous pouvez retirer annuellement (r\xe8gle des 4% = 4.0)"})]}),(0,s.jsxs)("div",{className:"d-flex justify-content-end gap-2",children:[(0,s.jsx)(o.A,{variant:"secondary",onClick:r,children:"Annuler"}),(0,s.jsx)(o.A,{variant:"primary",type:"submit",children:"Sauvegarder"})]})]})},U=({currentNetPatrimoine:e})=>{let t=[{id:0,name:"Phase 0: Bilan Patrimonial & Actions Imm\xe9diates",description:"R\xe9organisation et pr\xe9paration",startDate:"Juin 2025",endDate:"Juillet 2025",startAge:47,endAge:47,keyMilestones:["Apport SCPI Com\xe8te (27 000€)","Optimisation PEA (72 750€ DCA Nasdaq-100)","Consolidation crypto (75% BTC / 25% ETH)","Sortie progressive crowdlending"],recommendedActions:["N\xe9gocier taux pr\xeat SCPI Com\xe8te (<3,95%)","D\xe9ployer DCA adaptatif sur 6 mois","Simplifier portefeuille crypto","Liquider positions Trading 212"],status:"completed"},{id:1,name:"Phase 1: Accumulation Intensive",description:"Investissement mensuel de 3 415€",startDate:"Ao\xfbt 2025",endDate:"Fin 2037",startAge:47,endAge:59,keyMilestones:["Saturation PEA (mi-2033, \xe2ge 55 ans)","Fin cr\xe9dit SCPI existant (Nov 2035)","Atteinte objectif 910k€ (courant 2035, \xe2ge 57 ans)"],recommendedActions:["SCPI: 812€/mois + remboursements 1 303€/mois","ETF Actions: 800€/mois (PUST puis SXR8)","Fonds Euros: 200€/mois","Crypto: 200€/mois (BTC/ETH)"],status:"current"},{id:2,name:"Phase 2: Transition Pr\xe9-FIRE",description:"R\xe9duction des risques et pr\xe9paration",startDate:"Fin 2037",endDate:"D\xe9but 2038",startAge:59,endAge:60,targetAmount:910150,keyMilestones:["Capital FIRE atteint (910 150€)","R\xe9duction risque crypto (<7% patrimoine)","Constitution poche ETF dividendes (30-40%)"],recommendedActions:["Arbitrer PUST/SXR8 vers ETF dividendes","S\xe9curiser gains crypto si >7-10%","V\xe9rifier fonds d'urgence (50 000€)","Optimiser fiscalit\xe9 retraits"],status:"upcoming"},{id:3,name:"Phase 3: Vie en Mode FIRE",description:"Ind\xe9pendance financi\xe8re confortable",startDate:"2038",endDate:"2040",startAge:60,endAge:62,targetAmount:12e5,keyMilestones:["FIRE Confortable (revenus > d\xe9penses + dette)","G\xe9n\xe9ration 37 400€/an revenus passifs","Allocation: 40% SCPI, 40% ETF, 15% Fonds Euros, 5% Crypto"],recommendedActions:["Optimiser retraits fiscaux (PFU/bar\xe8me)","G\xe9rer allocation mix croissance/dividendes","Suivi d\xe9penses cibles (25 484€/an)","Gestion budg\xe9taire et CSM"],status:"upcoming"},{id:4,name:"Phase 4: \xc9volution Post-FIRE & Retraite L\xe9gale",description:"FIRE pleine puissance et retraite",startDate:"2040",endDate:"2042+",startAge:62,endAge:64,keyMilestones:["Fin pr\xeat SCPI Com\xe8te (fin 2040, +10k€/an)","Retraite l\xe9gale (2042, \xe2ge 64 ans)","Pension \xc9tat (~1 800€/mois) + PER (45-55k€)"],recommendedActions:["Augmentation revenu net (+10k€/an)","Int\xe9gration pension \xc9tat","D\xe9blocage PER Linxea Spirit","R\xe9duction besoin puisage capital FIRE"],status:"upcoming"}],r=(()=>{let r=new Date().getFullYear()-1978,s=1;e>=910150?s=r>=62?4:r>=60?3:2:r>=59&&e>=8e5&&(s=2);let a=0,n=0,i=t[s];if(1===s){a=i.targetAmount?e/i.targetAmount*100:e/910150*100;let t=(i.targetAmount||910150)-e;n=Math.max(0,t>0?t/40980:0)}else if(i){let e=i.endAge-i.startAge,t=Math.max(0,r-i.startAge);a=e>0?t/e*100:100*(r>=i.startAge),n=Math.max(0,i.endAge-r),i.targetAmount&&i.targetAmount}return{currentPhase:s,progressInCurrentPhase:Math.min(100,Math.max(0,a)),yearsToNextPhase:n,currentAge:r}})(),a=t.map((e,t)=>({...e,status:t<r.currentPhase?"completed":t===r.currentPhase?"current":"upcoming"})),n=e=>({completed:"success",current:"primary",upcoming:"secondary"})[e]||"secondary",i=e=>({completed:"✅",current:"\uD83D\uDD25",upcoming:"⏳"})[e]||"⏳";return(0,s.jsxs)("div",{className:"mt-4",children:[(0,s.jsx)("h3",{className:"mb-4",children:"\uD83C\uDFAF Tracker des Phases FIRE"}),(0,s.jsxs)("div",{className:"row mb-4",children:[(0,s.jsx)("div",{className:"col-md-3",children:(0,s.jsx)("div",{className:"card text-white bg-info",children:(0,s.jsxs)("div",{className:"card-body text-center",children:[(0,s.jsx)("h5",{children:"Phase Actuelle"}),(0,s.jsxs)("h4",{children:["Phase ",r.currentPhase]}),(0,s.jsx)("small",{children:a[r.currentPhase]?.name.split(":")[1]||"N/A"})]})})}),(0,s.jsx)("div",{className:"col-md-3",children:(0,s.jsx)("div",{className:"card text-white bg-warning",children:(0,s.jsxs)("div",{className:"card-body text-center",children:[(0,s.jsx)("h5",{children:"\xc2ge Actuel"}),(0,s.jsxs)("h4",{children:[r.currentAge," ans"]}),(0,s.jsx)("small",{children:"N\xe9 en septembre 1978"})]})})}),(0,s.jsx)("div",{className:"col-md-3",children:(0,s.jsx)("div",{className:"card text-white bg-success",children:(0,s.jsxs)("div",{className:"card-body text-center",children:[(0,s.jsx)("h5",{children:"Progression Phase"}),(0,s.jsxs)("h4",{children:[r.progressInCurrentPhase.toFixed(1),"%"]}),(0,s.jsx)("small",{children:"Dans la phase actuelle"})]})})}),(0,s.jsx)("div",{className:"col-md-3",children:(0,s.jsx)("div",{className:"card text-white bg-danger",children:(0,s.jsxs)("div",{className:"card-body text-center",children:[(0,s.jsx)("h5",{children:"Prochaine Phase"}),(0,s.jsxs)("h4",{children:[r.yearsToNextPhase.toFixed(1)," ans"]}),(0,s.jsx)("small",{children:"Estimation restante"})]})})})]}),(0,s.jsxs)("div",{className:"card",children:[(0,s.jsx)("div",{className:"card-header",children:(0,s.jsx)("h5",{className:"mb-0",children:"Timeline des Phases FIRE (2025-2042+)"})}),(0,s.jsx)("div",{className:"card-body",children:(0,s.jsx)(S,{defaultActiveKey:r.currentPhase.toString(),children:a.map((e,t)=>(0,s.jsxs)(S.Item,{eventKey:t.toString(),children:[(0,s.jsx)(S.Header,{children:(0,s.jsxs)("div",{className:"d-flex align-items-center w-100",children:[(0,s.jsx)("span",{className:"me-2",children:i(e.status)}),(0,s.jsxs)("div",{className:"flex-grow-1",children:[(0,s.jsx)("strong",{children:e.name}),(0,s.jsx)(D.A,{bg:n(e.status),className:"ms-2",children:"completed"===e.status?"Termin\xe9e":"current"===e.status?"En cours":"\xc0 venir"})]}),(0,s.jsxs)("small",{className:"text-muted",children:[e.startDate," - ",e.endDate," (\xe2ge ",e.startAge,"-",e.endAge,")"]})]})}),(0,s.jsxs)(S.Body,{children:[(0,s.jsxs)("div",{className:"row",children:[(0,s.jsxs)("div",{className:"col-md-6",children:[(0,s.jsx)("h6",{children:"\uD83C\uDFAF Jalons Cl\xe9s :"}),(0,s.jsx)("ul",{children:e.keyMilestones.map((e,t)=>(0,s.jsx)("li",{children:e},t))}),e.targetAmount&&(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Objectif Patrimoine :"})," ",new Intl.NumberFormat("fr-FR",{style:"currency",currency:"EUR"}).format(e.targetAmount)]})]}),(0,s.jsxs)("div",{className:"col-md-6",children:[(0,s.jsx)("h6",{children:"\uD83D\uDCCB Actions Recommand\xe9es :"}),(0,s.jsx)("ul",{children:e.recommendedActions.map((e,t)=>(0,s.jsx)("li",{children:e},t))})]})]}),"current"===e.status&&(0,s.jsxs)("div",{className:"mt-3",children:[(0,s.jsx)("h6",{children:"Progression dans cette phase :"}),(0,s.jsx)(T.A,{now:r.progressInCurrentPhase,label:`${r.progressInCurrentPhase.toFixed(1)}%`,variant:n(e.status),style:{height:"25px"}}),(0,s.jsxs)("small",{className:"text-muted",children:["Estimation : ",r.yearsToNextPhase.toFixed(1)," ann\xe9es restantes"]})]})]})]},e.id))})})]})]})},O=()=>{let[e,t]=(0,a.useState)(null),[r,n]=(0,a.useState)(!0),[l,c]=(0,a.useState)(null),[d,m]=(0,a.useState)(0),[u,h]=(0,a.useState)(!1),p=()=>{n(!0),c(null),i.A.get("/fire-target").then(e=>{t(e.data),n(!1),m(0)}).catch(e=>{console.error("FireTarget API error:",e),d<2?(m(e=>e+1),setTimeout(()=>p(),1e3)):(c("Erreur lors de la r\xe9cup\xe9ration des donn\xe9es FIRE."),n(!1))})};if((0,a.useEffect)(()=>{p()},[]),r&&0===d)return(0,s.jsx)("p",{children:"Chargement de l'objectif FIRE..."});if(l&&d>=2)return(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-danger",children:l}),(0,s.jsx)("button",{className:"btn btn-primary",onClick:()=>{m(0),p()},children:"R\xe9essayer"})]});if(!e&&!r)return(0,s.jsx)("p",{children:"Aucune donn\xe9e d'objectif FIRE disponible."});if(r)return(0,s.jsxs)("p",{children:["Chargement de l'objectif FIRE, tentative ",d+1,"..."]});if(!e)return(0,s.jsx)("p",{children:"Donn\xe9es FIRE non disponibles."});let x=e=>new Intl.NumberFormat("fr-FR",{style:"currency",currency:"EUR"}).format(e),v=e=>new Intl.NumberFormat("fr-FR",{style:"percent",minimumFractionDigits:1}).format(e/100);return(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"d-flex justify-content-between align-items-center mb-4",children:[(0,s.jsx)("h1",{children:"Objectif FIRE"}),(0,s.jsx)(o.A,{variant:"primary",onClick:()=>h(!0),children:"Modifier l'objectif"})]}),(0,s.jsx)("div",{className:"row mb-4",children:(0,s.jsx)("div",{className:"col-12",children:(0,s.jsx)("div",{className:"card",children:(0,s.jsxs)("div",{className:"card-body",children:[(0,s.jsx)("h5",{className:"card-title",children:"Progression vers votre Objectif FIRE"}),(0,s.jsxs)("div",{className:"row mb-3",children:[(0,s.jsx)("div",{className:"col-md-4",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("h3",{className:"text-primary",children:x(e.current_net_patrimoine)}),(0,s.jsx)("small",{className:"text-muted",children:"Patrimoine Net Actuel"})]})}),(0,s.jsx)("div",{className:"col-md-4",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("h3",{className:"text-success",children:x(e.fire_target_amount)}),(0,s.jsx)("small",{className:"text-muted",children:"Objectif FIRE"})]})}),(0,s.jsx)("div",{className:"col-md-4",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("h3",{className:"text-info",children:v(e.progress_percentage)}),(0,s.jsx)("small",{className:"text-muted",children:"Progression"})]})})]}),(0,s.jsx)(T.A,{now:e.progress_percentage,label:`${e.progress_percentage.toFixed(1)}%`,style:{height:"30px"},className:"mb-3"}),(0,s.jsxs)("div",{className:"row",children:[(0,s.jsxs)("div",{className:"col-md-6",children:[(0,s.jsx)("p",{className:"mb-1",children:(0,s.jsx)("strong",{children:"Montant restant \xe0 investir :"})}),(0,s.jsx)("p",{className:"text-danger fs-5",children:x(Math.max(0,e.remaining_to_invest))})]}),(0,s.jsxs)("div",{className:"col-md-6",children:[(0,s.jsx)("p",{className:"mb-1",children:(0,s.jsx)("strong",{children:"Revenu passif annuel potentiel :"})}),(0,s.jsx)("p",{className:"text-success fs-5",children:x(e.potential_passive_income)}),(0,s.jsxs)("small",{className:"text-muted",children:["Montant que vous pourriez retirer annuellement de votre patrimoine actuel selon la r\xe8gle des ",v(100*e.secure_withdrawal_rate)]})]})]})]})})})}),(0,s.jsxs)("div",{className:"row",children:[(0,s.jsx)("div",{className:"col-md-4",children:(0,s.jsxs)("div",{className:"card text-white bg-success mb-3",children:[(0,s.jsx)("div",{className:"card-header",children:"Total Actifs"}),(0,s.jsxs)("div",{className:"card-body",children:[(0,s.jsx)("h5",{className:"card-title",children:x(e.total_assets)}),(0,s.jsx)("p",{className:"card-text",children:"Incluant les SCPI et tous vos investissements"})]})]})}),(0,s.jsx)("div",{className:"col-md-4",children:(0,s.jsxs)("div",{className:"card text-white bg-danger mb-3",children:[(0,s.jsx)("div",{className:"card-header",children:"Total Passifs"}),(0,s.jsxs)("div",{className:"card-body",children:[(0,s.jsx)("h5",{className:"card-title",children:x(e.total_liabilities)}),(0,s.jsx)("p",{className:"card-text",children:"Emprunts et dettes en cours"})]})]})}),(0,s.jsx)("div",{className:"col-md-4",children:(0,s.jsxs)("div",{className:"card text-white bg-info mb-3",children:[(0,s.jsx)("div",{className:"card-header",children:"Taux de Retrait S\xe9curis\xe9"}),(0,s.jsxs)("div",{className:"card-body",children:[(0,s.jsx)("h5",{className:"card-title",children:v(100*e.secure_withdrawal_rate)}),(0,s.jsx)("p",{className:"card-text",children:"Pourcentage de retrait annuel s\xe9curis\xe9"})]})]})})]}),(0,s.jsx)(U,{currentNetPatrimoine:e.current_net_patrimoine}),(0,s.jsxs)(M.A,{show:u,onHide:()=>h(!1),size:"lg",children:[(0,s.jsx)(M.A.Header,{closeButton:!0,children:(0,s.jsx)(M.A.Title,{children:"Modifier les Param\xe8tres FIRE"})}),(0,s.jsx)(M.A.Body,{children:(0,s.jsx)(q,{settings:e?{fire_target_amount:e.fire_target_amount,secure_withdrawal_rate:e.secure_withdrawal_rate}:null,onSave:()=>{h(!1),p()},onCancel:()=>h(!1)})})]})]})}},78162:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(31658);let a=async e=>[{type:"image/png",sizes:"192x192",url:(0,s.fillMetadataSegment)(".",await e.params,"icon.png")+"?d84b06edc1baa420"}]},78282:(e,t,r)=>{"use strict";function s(){return(s=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var s in r)({}).hasOwnProperty.call(r,s)&&(e[s]=r[s])}return e}).apply(null,arguments)}r.d(t,{Zw:()=>o});var a=r(12915),n=r(43210);r(40414);function i(e){return"default"+e.charAt(0).toUpperCase()+e.substr(1)}function l(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var s=r.call(e,t||"default");if("object"!=typeof s)return s;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}function o(e,t){return Object.keys(t).reduce(function(r,o){var c,d,m,u,h,p,x,v,f=r[i(o)],j=r[o],g=(0,a.A)(r,[i(o),o].map(l)),b=t[o],N=(c=e[b],d=(0,n.useRef)(void 0!==j),u=(m=(0,n.useState)(f))[0],h=m[1],p=void 0!==j,x=d.current,d.current=p,!p&&x&&u!==f&&h(f),[p?j:u,(0,n.useCallback)(function(e){for(var t=arguments.length,r=Array(t>1?t-1:0),s=1;s<t;s++)r[s-1]=arguments[s];c&&c.apply(void 0,[e].concat(r)),h(e)},[c])]),y=N[0],A=N[1];return s({},g,((v={})[o]=y,v[b]=A,v))},e)}},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},90317:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let s=r(51060).A.create({baseURL:"http://localhost:8000/api",headers:{"Content-Type":"application/json"},timeout:1e4});s.interceptors.response.use(e=>e,e=>(console.error("API Error:",e.response?.status,e.config?.url,e.message),Promise.reject(e)));let a=s},92075:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>d,routeModule:()=>u,tree:()=>c});var s=r(65239),a=r(48088),n=r(88170),i=r.n(n),l=r(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);r.d(t,o);let c={children:["",{children:["fire",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,34259)),"D:\\AI-Code\\fire_UI\\frontnextjs\\src\\app\\fire\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e),async e=>(await Promise.resolve().then(r.bind(r,78162))).default(e)],apple:[async e=>(await Promise.resolve().then(r.bind(r,94650))).default(e)],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"D:\\AI-Code\\fire_UI\\frontnextjs\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e),async e=>(await Promise.resolve().then(r.bind(r,78162))).default(e)],apple:[async e=>(await Promise.resolve().then(r.bind(r,94650))).default(e)],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["D:\\AI-Code\\fire_UI\\frontnextjs\\src\\app\\fire\\page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/fire/page",pathname:"/fire",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},93991:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},94431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l,metadata:()=>n,viewport:()=>i});var s=r(37413);r(61135),r(27209);var a=r(30004);let n={title:"FIRE Dashboard",description:"Application pour le suivi de l'objectif FIRE (Financial Independence, Retire Early)",manifest:"/manifest.json",icons:{icon:"/icon.png",apple:"/apple-icon.png"}},i={themeColor:"#000000"};function l({children:e}){return(0,s.jsx)("html",{lang:"en",children:(0,s.jsx)("body",{children:(0,s.jsxs)("div",{className:"App",children:[(0,s.jsx)(a.default,{}),(0,s.jsx)("main",{className:"container mt-4",children:e})]})})})}},94650:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(31658);let a=async e=>[{type:"image/png",sizes:"192x192",url:(0,s.fillMetadataSegment)(".",await e.params,"apple-icon.png")+"?d84b06edc1baa420"}]},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,989,517,315,36],()=>r(92075));module.exports=s})();