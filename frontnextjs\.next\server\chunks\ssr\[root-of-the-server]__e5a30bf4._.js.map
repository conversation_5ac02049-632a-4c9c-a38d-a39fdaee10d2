{"version": 3, "sources": [], "sections": [{"offset": {"line": 111, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/AI-Code/fire_UI/frontnextjs/src/components/ApiClient.ts"], "sourcesContent": ["import axios from 'axios';\r\n\r\nconst apiClient = axios.create({\r\n  baseURL: 'http://localhost:8000/api',\r\n  headers: {\r\n    'Content-Type': 'application/json',\r\n  },\r\n  timeout: 10000, // 10 second timeout\r\n});\r\n\r\n// Add response interceptor for error handling\r\napiClient.interceptors.response.use(\r\n  (response) => response,\r\n  (error) => {\r\n    console.error('API Error:', error.response?.status, error.config?.url, error.message);\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\nexport default apiClient;\r\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,YAAY,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAC7B,SAAS;IACT,SAAS;QACP,gBAAgB;IAClB;IACA,SAAS;AACX;AAEA,8CAA8C;AAC9C,UAAU,YAAY,CAAC,QAAQ,CAAC,GAAG,CACjC,CAAC,WAAa,UACd,CAAC;IACC,QAAQ,KAAK,CAAC,cAAc,MAAM,QAAQ,EAAE,QAAQ,MAAM,MAAM,EAAE,KAAK,MAAM,OAAO;IACpF,OAAO,QAAQ,MAAM,CAAC;AACxB;uCAGa", "debugId": null}}, {"offset": {"line": 135, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/AI-Code/fire_UI/frontnextjs/src/app/page.tsx"], "sourcesContent": ["'use client'; // Redevient un Client Component pour l'instant\r\n\r\nimport React, { useEffect, useState } from 'react';\r\nimport apiClient from '../components/ApiClient';\r\nimport { Doughnut } from 'react-chartjs-2';\r\nimport { Chart as ChartJS, ArcElement, Tooltip, Legend, ChartOptions } from 'chart.js';\r\nimport { ProgressBar } from 'react-bootstrap';\r\n\r\nChartJS.register(ArcElement, Tooltip, Legend);\r\n\r\n// --- Types ---\r\ninterface DashboardData {\r\n  net_patrimoine: number;\r\n  total_assets: number;\r\n  total_liabilities: number;\r\n  allocation: { [key: string]: number };\r\n}\r\ninterface AllocationTarget {\r\n  category: string; categoryKey: string; currentValue: number; currentPercent: number;\r\n  targetPercent: number; targetValue: number; amountToInvest: number; progressPercent: number;\r\n}\r\ninterface FireAllocationTargetData {\r\n  id: number; category_key: string; target_percentage: number;\r\n}\r\n// --- Fin des Types ---\r\n\r\nconst DashboardPage: React.FC = () => {\r\n  const [data, setData] = useState<DashboardData | null>(null);\r\n  const [editableAllocations, setEditableAllocations] = useState<AllocationTarget[]>([]);\r\n  const [initialTargetPercentages, setInitialTargetPercentages] = useState<Record<string, number>>({});\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const [retryCount, setRetryCount] = useState(0);\r\n  const [isSaving, setIsSaving] = useState(false);\r\n\r\n  const FIRE_TARGET_AMOUNT = 910150;\r\n  const defaultTargetAllocationsConfig: Array<{ category: string; categoryKey: string; defaultTargetPercent: number }> = [\r\n    { category: \"Liquidité\", categoryKey: \"Liquidité\", defaultTargetPercent: 2.5 },\r\n    { category: \"Bourse\", categoryKey: \"Bourse\", defaultTargetPercent: 35.5 },\r\n    { category: \"Immobilier\", categoryKey: \"Immobilier\", defaultTargetPercent: 35.0 },\r\n    { category: \"Fonds sécurisés\", categoryKey: \"Fonds sécurisés\", defaultTargetPercent: 20.0 },\r\n    { category: \"Prêts participatifs\", categoryKey: \"Prêts participatifs\", defaultTargetPercent: 2.0 },\r\n    { category: \"Crypto-Actifs\", categoryKey: \"Crypto-Actifs\", defaultTargetPercent: 5.0 }\r\n  ];\r\n\r\n  const processAllocations = (\r\n    dashboardData: DashboardData | null,\r\n    apiTargetPercentages: Record<string, number>\r\n  ): AllocationTarget[] => {\r\n    if (!dashboardData) return [];\r\n    return defaultTargetAllocationsConfig.map(defaultTarget => {\r\n      const currentValue = dashboardData.allocation[defaultTarget.categoryKey] || 0;\r\n      const currentPercent = dashboardData.total_assets > 0 ? (currentValue / dashboardData.total_assets) * 100 : 0;\r\n      const targetPercent = apiTargetPercentages[defaultTarget.categoryKey] !== undefined\r\n        ? apiTargetPercentages[defaultTarget.categoryKey]\r\n        : defaultTarget.defaultTargetPercent;\r\n      const targetValue = (FIRE_TARGET_AMOUNT * targetPercent) / 100;\r\n      const amountToInvest = Math.max(0, targetValue - currentValue);\r\n      const progressPercent = targetValue > 0 ? Math.min(100, (currentValue / targetValue) * 100) : 0;\r\n      return { category: defaultTarget.category, categoryKey: defaultTarget.categoryKey, currentValue, currentPercent, targetPercent, targetValue, amountToInvest, progressPercent };\r\n    });\r\n  };\r\n\r\n  const fetchAllData = () => {\r\n    setLoading(true);\r\n    setError(null);\r\n    Promise.all([\r\n      apiClient.get('/dashboard'),\r\n      apiClient.get('/fire-allocation-targets/')\r\n    ])\r\n    .then(([dashboardResponse, fireTargetsResponse]) => {\r\n      const dashboardData = dashboardResponse.data;\r\n      setData(dashboardData);\r\n      const fireTargetsFromAPI: FireAllocationTargetData[] = fireTargetsResponse.data;\r\n      const targetsMap = fireTargetsFromAPI.reduce((acc, current) => { acc[current.category_key] = current.target_percentage; return acc; }, {} as Record<string, number>);\r\n      setInitialTargetPercentages(targetsMap);\r\n      setEditableAllocations(processAllocations(dashboardData, targetsMap));\r\n      setLoading(false); setRetryCount(0);\r\n    })\r\n    .catch(err => {\r\n      console.error('Error fetching data for DashboardPage:', err);\r\n      if (retryCount < 2) { setRetryCount(prev => prev + 1); setTimeout(fetchAllData, 1000); }\r\n      else { setError('Erreur lors de la récupération des données du dashboard.'); setLoading(false); }\r\n    });\r\n  };\r\n\r\n  useEffect(() => { fetchAllData(); // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    if (data) {\r\n        const newProcessedAllocations = processAllocations(data, initialTargetPercentages);\r\n        const userEditedAllocations = editableAllocations.length > 0 ? editableAllocations : newProcessedAllocations;\r\n\r\n        const updatedAllocationsWithUserEdits = newProcessedAllocations.map(processedAlloc => {\r\n            const userEditedAlloc = userEditedAllocations.find(ea => ea.categoryKey === processedAlloc.categoryKey);\r\n            if (userEditedAlloc && userEditedAlloc.targetPercent !== processedAlloc.targetPercent) {\r\n                const editedTargetPercent = userEditedAlloc.targetPercent;\r\n                const targetValue = (FIRE_TARGET_AMOUNT * editedTargetPercent) / 100;\r\n                const amountToInvest = Math.max(0, targetValue - processedAlloc.currentValue);\r\n                const progressPercent = targetValue > 0 ? Math.min(100, (processedAlloc.currentValue / targetValue) * 100) : 0;\r\n                return { ...processedAlloc, targetPercent: editedTargetPercent, targetValue, amountToInvest, progressPercent };\r\n            }\r\n            return processedAlloc;\r\n        });\r\n        // S'assurer que editableAllocations est initialisé correctement la première fois ou si data/initialTargetPercentages changent\r\n        if(editableAllocations.length === 0 || initialDashboardDataChanged(data, initialTargetPercentages, prevDataRef.current?.data, prevDataRef.current?.initialTargetPercentages)) {\r\n            setEditableAllocations(updatedAllocationsWithUserEdits);\r\n        }\r\n    }\r\n    // Garder une référence aux données précédentes pour la comparaison\r\n    prevDataRef.current = { data, initialTargetPercentages };\r\n  // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [data, initialTargetPercentages]);\r\n\r\n  // Ref pour stocker data et initialTargetPercentages précédents\r\n  const prevDataRef = React.useRef<{data: DashboardData | null, initialTargetPercentages: Record<string,number>}>();\r\n\r\n  // Fonction helper pour vérifier si les données initiales ont changé\r\n  const initialDashboardDataChanged = (currentData: unknown, currentTargets: unknown, prevData: unknown, prevTargets: unknown) => {\r\n    return JSON.stringify(currentData) !== JSON.stringify(prevData) || JSON.stringify(currentTargets) !== JSON.stringify(prevTargets);\r\n  };\r\n\r\n\r\n  const handleTargetPercentChange = (categoryKey: string, newTargetPercent: string) => {\r\n    const numericValue = parseFloat(newTargetPercent);\r\n    if (isNaN(numericValue) && newTargetPercent !== \"\" && newTargetPercent !== \".\") return;\r\n    setEditableAllocations(prevAllocations =>\r\n      prevAllocations.map(alloc => {\r\n        if (alloc.categoryKey === categoryKey) {\r\n          const updatedTargetPercent = isNaN(numericValue) ? (newTargetPercent === \"\" ? 0 : alloc.targetPercent) : numericValue;\r\n          const targetValue = (FIRE_TARGET_AMOUNT * updatedTargetPercent) / 100;\r\n          const amountToInvest = Math.max(0, targetValue - alloc.currentValue);\r\n          const progressPercent = targetValue > 0 ? Math.min(100, (alloc.currentValue / targetValue) * 100) : 0;\r\n          return { ...alloc, targetPercent: updatedTargetPercent, targetValue, amountToInvest, progressPercent };\r\n        }\r\n        return alloc;\r\n      })\r\n    );\r\n  };\r\n\r\n  const handleSaveChanges = async () => {\r\n    setIsSaving(true); setError(null);\r\n    const totalTarget = editableAllocations.reduce((sum, alloc) => sum + (alloc.targetPercent || 0), 0);\r\n    if (Math.abs(totalTarget - 100) > 0.1) {\r\n        if (!window.confirm(`Le total des pourcentages cibles est ${totalTarget.toFixed(1)}%, ce qui n'est pas égal à 100%. Voulez-vous continuer quand même ?`)) {\r\n            setIsSaving(false); return;\r\n        }\r\n    }\r\n    const payload = editableAllocations.map(alloc => ({ category_key: alloc.categoryKey, target_percentage: alloc.targetPercent || 0 }));\r\n    try {\r\n      await apiClient.post('/fire-allocation-targets/batch_update/', payload);\r\n      const newInitialTargets = payload.reduce((acc, current) => { acc[current.category_key] = current.target_percentage; return acc; }, {} as Record<string, number>);\r\n      setInitialTargetPercentages(newInitialTargets); // Met à jour la base pour \"reset\" et \"hasUnsavedChanges\"\r\n    } catch (err) { console.error(\"Erreur sauvegarde allocations:\", err); setError(\"Erreur sauvegarde. Réessayez.\"); }\r\n    finally { setIsSaving(false); }\r\n  };\r\n\r\n  const handleResetChanges = () => {\r\n      if (!data) return;\r\n      setEditableAllocations(processAllocations(data, initialTargetPercentages));\r\n  };\r\n\r\n  const totalTargetPercentage = editableAllocations.reduce((sum, alloc) => sum + (alloc.targetPercent || 0), 0);\r\n  const hasUnsavedChanges = editableAllocations.some(alloc => {\r\n    const initialPercent = initialTargetPercentages[alloc.categoryKey];\r\n    const currentTarget = alloc.targetPercent || 0;\r\n    const initialTarget = initialPercent === undefined ? (defaultTargetAllocationsConfig.find(d => d.categoryKey === alloc.categoryKey)?.defaultTargetPercent || 0) : initialPercent;\r\n    return Math.abs(currentTarget - initialTarget) > 0.001;\r\n  });\r\n\r\n  if (loading && retryCount === 0) return <p>Chargement initial du dashboard...</p>;\r\n  if (error && retryCount >=2) return (<div><p className=\"text-danger\">{error}</p><button className=\"btn btn-primary\" onClick={() => {setRetryCount(0); fetchAllData();}}>Réessayer</button></div>);\r\n  if (!data && !loading) return <p>Aucune donnée de dashboard disponible ou erreur de chargement.</p>;\r\n  if (loading) return <p>Chargement du dashboard, tentative {retryCount + 1}...</p>;\r\n  if (!data) return <p>Données du dashboard non disponibles.</p>;\r\n\r\n  const formatCurrency = (value: number) => new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR', maximumFractionDigits: 0 }).format(value);\r\n  const categoryColors = { 'Liquidité': { bg: '#17a2b8', border: '#138496', hover: '#20c997' }, 'Bourse': { bg: '#007bff', border: '#0056b3', hover: '#0069d9' }, 'Crypto-Actifs': { bg: '#fd7e14', border: '#e55a00', hover: '#ff8c42' }, 'Fonds sécurisés': { bg: '#28a745', border: '#1e7e34', hover: '#34ce57' }, 'Immobilier': { bg: '#6f42c1', border: '#59359a', hover: '#7952b3' }, 'Prêts participatifs': { bg: '#dc3545', border: '#bd2130', hover: '#e4606d' } };\r\n  const labels = Object.keys(data.allocation); const values = Object.values(data.allocation); const total = values.reduce((sum, value) => sum + value, 0);\r\n  const chartData = { labels: labels, datasets: [{ label: 'Répartition du portefeuille', data: values, backgroundColor: labels.map(label => categoryColors[label as keyof typeof categoryColors]?.bg || '#6c757d'), borderColor: labels.map(label => categoryColors[label as keyof typeof categoryColors]?.border || '#495057'), hoverBackgroundColor: labels.map(label => categoryColors[label as keyof typeof categoryColors]?.hover || '#868e96'), borderWidth: 3, hoverBorderWidth: 4, borderRadius: 8, spacing: 4, }], };\r\n  const chartOptions: ChartOptions<'doughnut'> = { responsive: true, maintainAspectRatio: true, plugins: { legend: { position: 'bottom' as const, labels: { padding: 20, usePointStyle: true, pointStyle: 'circle', font: { size: 14, weight: 'bold' as const, }, generateLabels: (chart) => { const chartDt = chart.data; if (chartDt.labels && chartDt.datasets.length) { return chartDt.labels.map((label, i) => { const val = chartDt.datasets[0].data[i] as number; const perc = total > 0 ? ((val / total) * 100).toFixed(1) : '0'; const bg = Array.isArray(chartDt.datasets[0].backgroundColor) ? chartDt.datasets[0].backgroundColor[i] as string : chartDt.datasets[0].backgroundColor as string; const brd = Array.isArray(chartDt.datasets[0].borderColor) ? chartDt.datasets[0].borderColor[i] as string : chartDt.datasets[0].borderColor as string; return { text: `${label} (${perc}%)`, fillStyle: bg, strokeStyle: brd, lineWidth: 2, hidden: false, index: i, }; }); } return []; }, }, }, tooltip: { enabled: true, backgroundColor: 'rgba(0, 0, 0, 0.9)', titleColor: '#fff', bodyColor: '#fff', borderColor: '#fff', borderWidth: 1, cornerRadius: 8, displayColors: true, callbacks: { title: (ctx) => ctx[0].label || '', label: (ctx) => { const val = ctx.parsed; const perc = total > 0 ? ((val / total) * 100).toFixed(1) : '0'; return [`Valeur: ${formatCurrency(val)}`, `Pourcentage: ${perc}%`]; }, }, }, }, animation: { animateRotate: true, animateScale: true, duration: 1500, easing: 'easeInOutQuart', }, interaction: { intersect: false, mode: 'index' as const, }, onHover: (evt, elems) => { if (evt.native?.target) { (evt.native.target as HTMLElement).style.cursor = elems.length > 0 ? 'pointer' : 'default'; } }, onClick: (evt, elems) => { if (elems.length > 0) { const idx = elems[0].index; const catName = labels[idx]; const navMap: { [key: string]: string } = { 'Liquidité': 'assets', 'Bourse': 'assets', 'Crypto-Actifs': 'assets', 'Fonds sécurisés': 'assets', 'Immobilier': 'scpi', 'Prêts participatifs': 'assets' }; const targetSect = navMap[catName]; if (targetSect) { const val = values[idx]; const perc = total > 0 ? ((val / total) * 100).toFixed(1) : '0'; alert(`${catName}\\nValeur: ${formatCurrency(val)}\\nPourcentage: ${perc}%\\n\\nCliquez sur l'onglet \"${targetSect === 'scpi' ? 'SCPI' : 'Patrimoine'}\" pour gérer cette catégorie.`); } } }, };\r\n\r\n  return (\r\n    <div>\r\n      <h1 className=\"mb-4\">Dashboard</h1>\r\n      <div className=\"row\"><div className=\"col-md-4\"><div className=\"card text-white bg-primary mb-3\"><div className=\"card-header\">Patrimoine Net Total</div><div className=\"card-body\"><h5 className=\"card-title\">{formatCurrency(data.net_patrimoine)}</h5></div></div></div><div className=\"col-md-4\"><div className=\"card text-white bg-success mb-3\"><div className=\"card-header\">Total Actifs</div><div className=\"card-body\"><h5 className=\"card-title\">{formatCurrency(data.total_assets)}</h5></div></div></div><div className=\"col-md-4\"><div className=\"card text-white bg-danger mb-3\"><div className=\"card-header\">Total Passifs</div><div className=\"card-body\"><h5 className=\"card-title\">{formatCurrency(data.total_liabilities)}</h5></div></div></div></div>\r\n      <div className=\"row mt-4\"><div className=\"col-md-8 offset-md-2\"><div className=\"text-center\"><h2 className=\"mb-4\">Répartition des Actifs</h2><div style={{ maxWidth: '500px', margin: '0 auto' }}><Doughnut data={chartData} options={chartOptions} /></div></div></div></div>\r\n      <div className=\"row mt-5\"><div className=\"col-12\"><div className=\"card\">\r\n            <div className=\"card-header\"><h3 className=\"mb-0\">🎯 Allocation Cible FIRE</h3><small className=\"text-muted\">Progression vers l&apos;objectif de {formatCurrency(FIRE_TARGET_AMOUNT)} selon la stratégie documentée</small></div>\r\n            <div className=\"card-body\">\r\n              {error && editableAllocations.length === 0 && <div className=\"alert alert-danger\">{error}</div>}\r\n              {error && editableAllocations.length > 0 && <div className=\"alert alert-danger\">Erreur lors de la sauvegarde : {error}</div>}\r\n              <div className=\"table-responsive\"><table className=\"table table-striped table-hover\">\r\n                  <thead className=\"table-dark\"><tr><th>Catégorie d&apos;Actif</th><th className=\"text-end\">Valeur Actuelle</th><th className=\"text-end\">% Actuel</th><th className=\"text-end\" style={{minWidth: \"100px\"}}>% Cible</th><th className=\"text-end\">Valeur Cible</th><th className=\"text-end\">Montant à Investir</th><th className=\"text-center\">Progression</th></tr></thead>\r\n                  <tbody>{editableAllocations.map((target) => (<tr key={target.categoryKey}><td><strong>{target.category}</strong></td><td className=\"text-end\">{formatCurrency(target.currentValue)}</td><td className=\"text-end\">{target.currentPercent.toFixed(1)}%</td><td className=\"text-end\"><input type=\"number\" value={target.targetPercent === undefined ? '' : target.targetPercent.toString()} onChange={(e) => handleTargetPercentChange(target.categoryKey, e.target.value)} className=\"form-control form-control-sm text-end\" style={{ width: '70px', display: 'inline-block' }} step=\"0.1\" min=\"0\" max=\"100\"/> %</td><td className=\"text-end\">{formatCurrency(target.targetValue)}</td><td className=\"text-end\">{target.amountToInvest > 0 ? (<span className=\"text-warning\">{formatCurrency(target.amountToInvest)}</span>) : (<span className=\"text-success\">Objectif atteint</span>)}</td><td className=\"text-center\" style={{ width: '150px' }}><ProgressBar now={target.progressPercent} variant={target.progressPercent >= 100 ? 'success' : target.progressPercent >= 75 ? 'info' : target.progressPercent >= 50 ? 'warning' : 'danger'} style={{ height: '20px' }} label={`${target.progressPercent.toFixed(0)}%`}/></td></tr>))}</tbody>\r\n                  <tfoot><tr className=\"table-info\"><th>TOTAL</th><th className=\"text-end\">{formatCurrency(data.total_assets)}</th><th className=\"text-end\">100%</th><th className=\"text-end\"><strong>{totalTargetPercentage.toFixed(1)}%</strong>{Math.abs(totalTargetPercentage - 100) > 0.1 && <span className=\"text-danger ms-1\">(≠100%)</span>}</th><th className=\"text-end\">{formatCurrency(FIRE_TARGET_AMOUNT)}</th><th className=\"text-end\"><strong className=\"text-primary\">{formatCurrency(Math.max(0, FIRE_TARGET_AMOUNT - data.total_assets))}</strong></th><th className=\"text-center\"><strong>{((data.total_assets / FIRE_TARGET_AMOUNT) * 100).toFixed(1)}%</strong></th></tr></tfoot>\r\n              </table></div>\r\n              {hasUnsavedChanges && (<div className=\"alert alert-warning mt-3\">Vous avez des modifications non enregistrées.</div>)}\r\n              <div className=\"mt-3 d-flex justify-content-end\"><button className=\"btn btn-secondary me-2\" onClick={handleResetChanges} disabled={!hasUnsavedChanges || isSaving}>Réinitialiser</button><button className=\"btn btn-primary\" onClick={handleSaveChanges} disabled={isSaving || !hasUnsavedChanges}>{isSaving ? 'Sauvegarde...' : 'Sauvegarder les % Cibles'}</button></div>\r\n              <div className=\"row mt-4\"><div className=\"col-md-4\"><div className=\"card text-white bg-info\"><div className=\"card-body text-center\"><h5>Progression Globale</h5><h4>{((data.total_assets / FIRE_TARGET_AMOUNT) * 100).toFixed(1)}%</h4><small>vers l&apos;objectif FIRE</small></div></div></div><div className=\"col-md-4\"><div className=\"card text-white bg-warning\"><div className=\"card-body text-center\"><h5>Capital Restant</h5><h4>{formatCurrency(Math.max(0, FIRE_TARGET_AMOUNT - data.total_assets))}</h4><small>à investir</small></div></div></div><div className=\"col-md-4\"><div className=\"card text-white bg-success\"><div className=\"card-body text-center\"><h5>Estimation</h5><h4>{Math.max(0, Math.ceil((FIRE_TARGET_AMOUNT - data.total_assets) / (3415 * 12)))} ans</h4><small>à 3 415€/mois</small></div></div></div></div>\r\n            </div>\r\n      </div></div></div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default DashboardPage;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA,cAAc,+CAA+C;;;;;;;AAQ7D,4JAAA,CAAA,QAAO,CAAC,QAAQ,CAAC,4JAAA,CAAA,aAAU,EAAE,4JAAA,CAAA,UAAO,EAAE,4JAAA,CAAA,SAAM;AAgB5C,wBAAwB;AAExB,MAAM,gBAA0B;IAC9B,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwB;IACvD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB,EAAE;IACrF,MAAM,CAAC,0BAA0B,4BAA4B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAClG,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,MAAM,qBAAqB;IAC3B,MAAM,iCAAiH;QACrH;YAAE,UAAU;YAAa,aAAa;YAAa,sBAAsB;QAAI;QAC7E;YAAE,UAAU;YAAU,aAAa;YAAU,sBAAsB;QAAK;QACxE;YAAE,UAAU;YAAc,aAAa;YAAc,sBAAsB;QAAK;QAChF;YAAE,UAAU;YAAmB,aAAa;YAAmB,sBAAsB;QAAK;QAC1F;YAAE,UAAU;YAAuB,aAAa;YAAuB,sBAAsB;QAAI;QACjG;YAAE,UAAU;YAAiB,aAAa;YAAiB,sBAAsB;QAAI;KACtF;IAED,MAAM,qBAAqB,CACzB,eACA;QAEA,IAAI,CAAC,eAAe,OAAO,EAAE;QAC7B,OAAO,+BAA+B,GAAG,CAAC,CAAA;YACxC,MAAM,eAAe,cAAc,UAAU,CAAC,cAAc,WAAW,CAAC,IAAI;YAC5E,MAAM,iBAAiB,cAAc,YAAY,GAAG,IAAI,AAAC,eAAe,cAAc,YAAY,GAAI,MAAM;YAC5G,MAAM,gBAAgB,oBAAoB,CAAC,cAAc,WAAW,CAAC,KAAK,YACtE,oBAAoB,CAAC,cAAc,WAAW,CAAC,GAC/C,cAAc,oBAAoB;YACtC,MAAM,cAAc,AAAC,qBAAqB,gBAAiB;YAC3D,MAAM,iBAAiB,KAAK,GAAG,CAAC,GAAG,cAAc;YACjD,MAAM,kBAAkB,cAAc,IAAI,KAAK,GAAG,CAAC,KAAK,AAAC,eAAe,cAAe,OAAO;YAC9F,OAAO;gBAAE,UAAU,cAAc,QAAQ;gBAAE,aAAa,cAAc,WAAW;gBAAE;gBAAc;gBAAgB;gBAAe;gBAAa;gBAAgB;YAAgB;QAC/K;IACF;IAEA,MAAM,eAAe;QACnB,WAAW;QACX,SAAS;QACT,QAAQ,GAAG,CAAC;YACV,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAC;YACd,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAC;SACf,EACA,IAAI,CAAC,CAAC,CAAC,mBAAmB,oBAAoB;YAC7C,MAAM,gBAAgB,kBAAkB,IAAI;YAC5C,QAAQ;YACR,MAAM,qBAAiD,oBAAoB,IAAI;YAC/E,MAAM,aAAa,mBAAmB,MAAM,CAAC,CAAC,KAAK;gBAAc,GAAG,CAAC,QAAQ,YAAY,CAAC,GAAG,QAAQ,iBAAiB;gBAAE,OAAO;YAAK,GAAG,CAAC;YACxI,4BAA4B;YAC5B,uBAAuB,mBAAmB,eAAe;YACzD,WAAW;YAAQ,cAAc;QACnC,GACC,KAAK,CAAC,CAAA;YACL,QAAQ,KAAK,CAAC,0CAA0C;YACxD,IAAI,aAAa,GAAG;gBAAE,cAAc,CAAA,OAAQ,OAAO;gBAAI,WAAW,cAAc;YAAO,OAClF;gBAAE,SAAS;gBAA6D,WAAW;YAAQ;QAClG;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QAAQ,gBAAgB,uDAAuD;IACzF,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,MAAM;YACN,MAAM,0BAA0B,mBAAmB,MAAM;YACzD,MAAM,wBAAwB,oBAAoB,MAAM,GAAG,IAAI,sBAAsB;YAErF,MAAM,kCAAkC,wBAAwB,GAAG,CAAC,CAAA;gBAChE,MAAM,kBAAkB,sBAAsB,IAAI,CAAC,CAAA,KAAM,GAAG,WAAW,KAAK,eAAe,WAAW;gBACtG,IAAI,mBAAmB,gBAAgB,aAAa,KAAK,eAAe,aAAa,EAAE;oBACnF,MAAM,sBAAsB,gBAAgB,aAAa;oBACzD,MAAM,cAAc,AAAC,qBAAqB,sBAAuB;oBACjE,MAAM,iBAAiB,KAAK,GAAG,CAAC,GAAG,cAAc,eAAe,YAAY;oBAC5E,MAAM,kBAAkB,cAAc,IAAI,KAAK,GAAG,CAAC,KAAK,AAAC,eAAe,YAAY,GAAG,cAAe,OAAO;oBAC7G,OAAO;wBAAE,GAAG,cAAc;wBAAE,eAAe;wBAAqB;wBAAa;wBAAgB;oBAAgB;gBACjH;gBACA,OAAO;YACX;YACA,8HAA8H;YAC9H,IAAG,oBAAoB,MAAM,KAAK,KAAK,4BAA4B,MAAM,0BAA0B,YAAY,OAAO,EAAE,MAAM,YAAY,OAAO,EAAE,2BAA2B;gBAC1K,uBAAuB;YAC3B;QACJ;QACA,mEAAmE;QACnE,YAAY,OAAO,GAAG;YAAE;YAAM;QAAyB;IACzD,uDAAuD;IACvD,GAAG;QAAC;QAAM;KAAyB;IAEnC,+DAA+D;IAC/D,MAAM,cAAc,qMAAA,CAAA,UAAK,CAAC,MAAM;IAEhC,oEAAoE;IACpE,MAAM,8BAA8B,CAAC,aAAsB,gBAAyB,UAAmB;QACrG,OAAO,KAAK,SAAS,CAAC,iBAAiB,KAAK,SAAS,CAAC,aAAa,KAAK,SAAS,CAAC,oBAAoB,KAAK,SAAS,CAAC;IACvH;IAGA,MAAM,4BAA4B,CAAC,aAAqB;QACtD,MAAM,eAAe,WAAW;QAChC,IAAI,MAAM,iBAAiB,qBAAqB,MAAM,qBAAqB,KAAK;QAChF,uBAAuB,CAAA,kBACrB,gBAAgB,GAAG,CAAC,CAAA;gBAClB,IAAI,MAAM,WAAW,KAAK,aAAa;oBACrC,MAAM,uBAAuB,MAAM,gBAAiB,qBAAqB,KAAK,IAAI,MAAM,aAAa,GAAI;oBACzG,MAAM,cAAc,AAAC,qBAAqB,uBAAwB;oBAClE,MAAM,iBAAiB,KAAK,GAAG,CAAC,GAAG,cAAc,MAAM,YAAY;oBACnE,MAAM,kBAAkB,cAAc,IAAI,KAAK,GAAG,CAAC,KAAK,AAAC,MAAM,YAAY,GAAG,cAAe,OAAO;oBACpG,OAAO;wBAAE,GAAG,KAAK;wBAAE,eAAe;wBAAsB;wBAAa;wBAAgB;oBAAgB;gBACvG;gBACA,OAAO;YACT;IAEJ;IAEA,MAAM,oBAAoB;QACxB,YAAY;QAAO,SAAS;QAC5B,MAAM,cAAc,oBAAoB,MAAM,CAAC,CAAC,KAAK,QAAU,MAAM,CAAC,MAAM,aAAa,IAAI,CAAC,GAAG;QACjG,IAAI,KAAK,GAAG,CAAC,cAAc,OAAO,KAAK;YACnC,IAAI,CAAC,OAAO,OAAO,CAAC,CAAC,qCAAqC,EAAE,YAAY,OAAO,CAAC,GAAG,mEAAmE,CAAC,GAAG;gBACtJ,YAAY;gBAAQ;YACxB;QACJ;QACA,MAAM,UAAU,oBAAoB,GAAG,CAAC,CAAA,QAAS,CAAC;gBAAE,cAAc,MAAM,WAAW;gBAAE,mBAAmB,MAAM,aAAa,IAAI;YAAE,CAAC;QAClI,IAAI;YACF,MAAM,8HAAA,CAAA,UAAS,CAAC,IAAI,CAAC,0CAA0C;YAC/D,MAAM,oBAAoB,QAAQ,MAAM,CAAC,CAAC,KAAK;gBAAc,GAAG,CAAC,QAAQ,YAAY,CAAC,GAAG,QAAQ,iBAAiB;gBAAE,OAAO;YAAK,GAAG,CAAC;YACpI,4BAA4B,oBAAoB,yDAAyD;QAC3G,EAAE,OAAO,KAAK;YAAE,QAAQ,KAAK,CAAC,kCAAkC;YAAM,SAAS;QAAkC,SACzG;YAAE,YAAY;QAAQ;IAChC;IAEA,MAAM,qBAAqB;QACvB,IAAI,CAAC,MAAM;QACX,uBAAuB,mBAAmB,MAAM;IACpD;IAEA,MAAM,wBAAwB,oBAAoB,MAAM,CAAC,CAAC,KAAK,QAAU,MAAM,CAAC,MAAM,aAAa,IAAI,CAAC,GAAG;IAC3G,MAAM,oBAAoB,oBAAoB,IAAI,CAAC,CAAA;QACjD,MAAM,iBAAiB,wBAAwB,CAAC,MAAM,WAAW,CAAC;QAClE,MAAM,gBAAgB,MAAM,aAAa,IAAI;QAC7C,MAAM,gBAAgB,mBAAmB,YAAa,+BAA+B,IAAI,CAAC,CAAA,IAAK,EAAE,WAAW,KAAK,MAAM,WAAW,GAAG,wBAAwB,IAAK;QAClK,OAAO,KAAK,GAAG,CAAC,gBAAgB,iBAAiB;IACnD;IAEA,IAAI,WAAW,eAAe,GAAG,qBAAO,8OAAC;kBAAE;;;;;;IAC3C,IAAI,SAAS,cAAa,GAAG,qBAAQ,8OAAC;;0BAAI,8OAAC;gBAAE,WAAU;0BAAe;;;;;;0BAAU,8OAAC;gBAAO,WAAU;gBAAkB,SAAS;oBAAO,cAAc;oBAAI;gBAAe;0BAAG;;;;;;;;;;;;IACxK,IAAI,CAAC,QAAQ,CAAC,SAAS,qBAAO,8OAAC;kBAAE;;;;;;IACjC,IAAI,SAAS,qBAAO,8OAAC;;YAAE;YAAoC,aAAa;YAAE;;;;;;;IAC1E,IAAI,CAAC,MAAM,qBAAO,8OAAC;kBAAE;;;;;;IAErB,MAAM,iBAAiB,CAAC,QAAkB,IAAI,KAAK,YAAY,CAAC,SAAS;YAAE,OAAO;YAAY,UAAU;YAAO,uBAAuB;QAAE,GAAG,MAAM,CAAC;IAClJ,MAAM,iBAAiB;QAAE,aAAa;YAAE,IAAI;YAAW,QAAQ;YAAW,OAAO;QAAU;QAAG,UAAU;YAAE,IAAI;YAAW,QAAQ;YAAW,OAAO;QAAU;QAAG,iBAAiB;YAAE,IAAI;YAAW,QAAQ;YAAW,OAAO;QAAU;QAAG,mBAAmB;YAAE,IAAI;YAAW,QAAQ;YAAW,OAAO;QAAU;QAAG,cAAc;YAAE,IAAI;YAAW,QAAQ;YAAW,OAAO;QAAU;QAAG,uBAAuB;YAAE,IAAI;YAAW,QAAQ;YAAW,OAAO;QAAU;IAAE;IACxc,MAAM,SAAS,OAAO,IAAI,CAAC,KAAK,UAAU;IAAG,MAAM,SAAS,OAAO,MAAM,CAAC,KAAK,UAAU;IAAG,MAAM,QAAQ,OAAO,MAAM,CAAC,CAAC,KAAK,QAAU,MAAM,OAAO;IACrJ,MAAM,YAAY;QAAE,QAAQ;QAAQ,UAAU;YAAC;gBAAE,OAAO;gBAA+B,MAAM;gBAAQ,iBAAiB,OAAO,GAAG,CAAC,CAAA,QAAS,cAAc,CAAC,MAAqC,EAAE,MAAM;gBAAY,aAAa,OAAO,GAAG,CAAC,CAAA,QAAS,cAAc,CAAC,MAAqC,EAAE,UAAU;gBAAY,sBAAsB,OAAO,GAAG,CAAC,CAAA,QAAS,cAAc,CAAC,MAAqC,EAAE,SAAS;gBAAY,aAAa;gBAAG,kBAAkB;gBAAG,cAAc;gBAAG,SAAS;YAAG;SAAE;IAAE;IAC1f,MAAM,eAAyC;QAAE,YAAY;QAAM,qBAAqB;QAAM,SAAS;YAAE,QAAQ;gBAAE,UAAU;gBAAmB,QAAQ;oBAAE,SAAS;oBAAI,eAAe;oBAAM,YAAY;oBAAU,MAAM;wBAAE,MAAM;wBAAI,QAAQ;oBAAiB;oBAAG,gBAAgB,CAAC;wBAAY,MAAM,UAAU,MAAM,IAAI;wBAAE,IAAI,QAAQ,MAAM,IAAI,QAAQ,QAAQ,CAAC,MAAM,EAAE;4BAAE,OAAO,QAAQ,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO;gCAAQ,MAAM,MAAM,QAAQ,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE;gCAAY,MAAM,OAAO,QAAQ,IAAI,CAAC,AAAC,MAAM,QAAS,GAAG,EAAE,OAAO,CAAC,KAAK;gCAAK,MAAM,KAAK,MAAM,OAAO,CAAC,QAAQ,QAAQ,CAAC,EAAE,CAAC,eAAe,IAAI,QAAQ,QAAQ,CAAC,EAAE,CAAC,eAAe,CAAC,EAAE,GAAa,QAAQ,QAAQ,CAAC,EAAE,CAAC,eAAe;gCAAY,MAAM,MAAM,MAAM,OAAO,CAAC,QAAQ,QAAQ,CAAC,EAAE,CAAC,WAAW,IAAI,QAAQ,QAAQ,CAAC,EAAE,CAAC,WAAW,CAAC,EAAE,GAAa,QAAQ,QAAQ,CAAC,EAAE,CAAC,WAAW;gCAAY,OAAO;oCAAE,MAAM,GAAG,MAAM,EAAE,EAAE,KAAK,EAAE,CAAC;oCAAE,WAAW;oCAAI,aAAa;oCAAK,WAAW;oCAAG,QAAQ;oCAAO,OAAO;gCAAG;4BAAG;wBAAI;wBAAE,OAAO,EAAE;oBAAE;gBAAG;YAAG;YAAG,SAAS;gBAAE,SAAS;gBAAM,iBAAiB;gBAAsB,YAAY;gBAAQ,WAAW;gBAAQ,aAAa;gBAAQ,aAAa;gBAAG,cAAc;gBAAG,eAAe;gBAAM,WAAW;oBAAE,OAAO,CAAC,MAAQ,GAAG,CAAC,EAAE,CAAC,KAAK,IAAI;oBAAI,OAAO,CAAC;wBAAU,MAAM,MAAM,IAAI,MAAM;wBAAE,MAAM,OAAO,QAAQ,IAAI,CAAC,AAAC,MAAM,QAAS,GAAG,EAAE,OAAO,CAAC,KAAK;wBAAK,OAAO;4BAAC,CAAC,QAAQ,EAAE,eAAe,MAAM;4BAAE,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;yBAAC;oBAAE;gBAAG;YAAG;QAAG;QAAG,WAAW;YAAE,eAAe;YAAM,cAAc;YAAM,UAAU;YAAM,QAAQ;QAAkB;QAAG,aAAa;YAAE,WAAW;YAAO,MAAM;QAAkB;QAAG,SAAS,CAAC,KAAK;YAAY,IAAI,IAAI,MAAM,EAAE,QAAQ;gBAAG,IAAI,MAAM,CAAC,MAAM,CAAiB,KAAK,CAAC,MAAM,GAAG,MAAM,MAAM,GAAG,IAAI,YAAY;YAAW;QAAE;QAAG,SAAS,CAAC,KAAK;YAAY,IAAI,MAAM,MAAM,GAAG,GAAG;gBAAE,MAAM,MAAM,KAAK,CAAC,EAAE,CAAC,KAAK;gBAAE,MAAM,UAAU,MAAM,CAAC,IAAI;gBAAE,MAAM,SAAoC;oBAAE,aAAa;oBAAU,UAAU;oBAAU,iBAAiB;oBAAU,mBAAmB;oBAAU,cAAc;oBAAQ,uBAAuB;gBAAS;gBAAG,MAAM,aAAa,MAAM,CAAC,QAAQ;gBAAE,IAAI,YAAY;oBAAE,MAAM,MAAM,MAAM,CAAC,IAAI;oBAAE,MAAM,OAAO,QAAQ,IAAI,CAAC,AAAC,MAAM,QAAS,GAAG,EAAE,OAAO,CAAC,KAAK;oBAAK,MAAM,GAAG,QAAQ,UAAU,EAAE,eAAe,KAAK,eAAe,EAAE,KAAK,2BAA2B,EAAE,eAAe,SAAS,SAAS,aAAa,6BAA6B,CAAC;gBAAG;YAAE;QAAE;IAAG;IAE9xE,qBACE,8OAAC;;0BACC,8OAAC;gBAAG,WAAU;0BAAO;;;;;;0BACrB,8OAAC;gBAAI,WAAU;;kCAAM,8OAAC;wBAAI,WAAU;kCAAW,cAAA,8OAAC;4BAAI,WAAU;;8CAAkC,8OAAC;oCAAI,WAAU;8CAAc;;;;;;8CAA0B,8OAAC;oCAAI,WAAU;8CAAY,cAAA,8OAAC;wCAAG,WAAU;kDAAc,eAAe,KAAK,cAAc;;;;;;;;;;;;;;;;;;;;;;kCAAyB,8OAAC;wBAAI,WAAU;kCAAW,cAAA,8OAAC;4BAAI,WAAU;;8CAAkC,8OAAC;oCAAI,WAAU;8CAAc;;;;;;8CAAkB,8OAAC;oCAAI,WAAU;8CAAY,cAAA,8OAAC;wCAAG,WAAU;kDAAc,eAAe,KAAK,YAAY;;;;;;;;;;;;;;;;;;;;;;kCAAyB,8OAAC;wBAAI,WAAU;kCAAW,cAAA,8OAAC;4BAAI,WAAU;;8CAAiC,8OAAC;oCAAI,WAAU;8CAAc;;;;;;8CAAmB,8OAAC;oCAAI,WAAU;8CAAY,cAAA,8OAAC;wCAAG,WAAU;kDAAc,eAAe,KAAK,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BACzsB,8OAAC;gBAAI,WAAU;0BAAW,cAAA,8OAAC;oBAAI,WAAU;8BAAuB,cAAA,8OAAC;wBAAI,WAAU;;0CAAc,8OAAC;gCAAG,WAAU;0CAAO;;;;;;0CAA2B,8OAAC;gCAAI,OAAO;oCAAE,UAAU;oCAAS,QAAQ;gCAAS;0CAAG,cAAA,8OAAC,sJAAA,CAAA,WAAQ;oCAAC,MAAM;oCAAW,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;0BACtO,8OAAC;gBAAI,WAAU;0BAAW,cAAA,8OAAC;oBAAI,WAAU;8BAAS,cAAA,8OAAC;wBAAI,WAAU;;0CAC3D,8OAAC;gCAAI,WAAU;;kDAAc,8OAAC;wCAAG,WAAU;kDAAO;;;;;;kDAA6B,8OAAC;wCAAM,WAAU;;4CAAa;4CAAqC,eAAe;4CAAoB;;;;;;;;;;;;;0CACrL,8OAAC;gCAAI,WAAU;;oCACZ,SAAS,oBAAoB,MAAM,KAAK,mBAAK,8OAAC;wCAAI,WAAU;kDAAsB;;;;;;oCAClF,SAAS,oBAAoB,MAAM,GAAG,mBAAK,8OAAC;wCAAI,WAAU;;4CAAqB;4CAAgC;;;;;;;kDAChH,8OAAC;wCAAI,WAAU;kDAAmB,cAAA,8OAAC;4CAAM,WAAU;;8DAC/C,8OAAC;oDAAM,WAAU;8DAAa,cAAA,8OAAC;;0EAAG,8OAAC;0EAAG;;;;;;0EAA2B,8OAAC;gEAAG,WAAU;0EAAW;;;;;;0EAAoB,8OAAC;gEAAG,WAAU;0EAAW;;;;;;0EAAa,8OAAC;gEAAG,WAAU;gEAAW,OAAO;oEAAC,UAAU;gEAAO;0EAAG;;;;;;0EAAY,8OAAC;gEAAG,WAAU;0EAAW;;;;;;0EAAiB,8OAAC;gEAAG,WAAU;0EAAW;;;;;;0EAAuB,8OAAC;gEAAG,WAAU;0EAAc;;;;;;;;;;;;;;;;;8DAC3U,8OAAC;8DAAO,oBAAoB,GAAG,CAAC,CAAC,uBAAY,8OAAC;;8EAA4B,8OAAC;8EAAG,cAAA,8OAAC;kFAAQ,OAAO,QAAQ;;;;;;;;;;;8EAAe,8OAAC;oEAAG,WAAU;8EAAY,eAAe,OAAO,YAAY;;;;;;8EAAO,8OAAC;oEAAG,WAAU;;wEAAY,OAAO,cAAc,CAAC,OAAO,CAAC;wEAAG;;;;;;;8EAAM,8OAAC;oEAAG,WAAU;;sFAAW,8OAAC;4EAAM,MAAK;4EAAS,OAAO,OAAO,aAAa,KAAK,YAAY,KAAK,OAAO,aAAa,CAAC,QAAQ;4EAAI,UAAU,CAAC,IAAM,0BAA0B,OAAO,WAAW,EAAE,EAAE,MAAM,CAAC,KAAK;4EAAG,WAAU;4EAAwC,OAAO;gFAAE,OAAO;gFAAQ,SAAS;4EAAe;4EAAG,MAAK;4EAAM,KAAI;4EAAI,KAAI;;;;;;wEAAO;;;;;;;8EAAO,8OAAC;oEAAG,WAAU;8EAAY,eAAe,OAAO,WAAW;;;;;;8EAAO,8OAAC;oEAAG,WAAU;8EAAY,OAAO,cAAc,GAAG,kBAAK,8OAAC;wEAAK,WAAU;kFAAgB,eAAe,OAAO,cAAc;;;;;6FAAc,8OAAC;wEAAK,WAAU;kFAAe;;;;;;;;;;;8EAA8B,8OAAC;oEAAG,WAAU;oEAAc,OAAO;wEAAE,OAAO;oEAAQ;8EAAG,cAAA,8OAAC,kMAAA,CAAA,cAAW;wEAAC,KAAK,OAAO,eAAe;wEAAE,SAAS,OAAO,eAAe,IAAI,MAAM,YAAY,OAAO,eAAe,IAAI,KAAK,SAAS,OAAO,eAAe,IAAI,KAAK,YAAY;wEAAU,OAAO;4EAAE,QAAQ;wEAAO;wEAAG,OAAO,GAAG,OAAO,eAAe,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;;;;;;;;;;;;2DAAjmC,OAAO,WAAW;;;;;;;;;;8DACxE,8OAAC;8DAAM,cAAA,8OAAC;wDAAG,WAAU;;0EAAa,8OAAC;0EAAG;;;;;;0EAAU,8OAAC;gEAAG,WAAU;0EAAY,eAAe,KAAK,YAAY;;;;;;0EAAO,8OAAC;gEAAG,WAAU;0EAAW;;;;;;0EAAS,8OAAC;gEAAG,WAAU;;kFAAW,8OAAC;;4EAAQ,sBAAsB,OAAO,CAAC;4EAAG;;;;;;;oEAAW,KAAK,GAAG,CAAC,wBAAwB,OAAO,qBAAO,8OAAC;wEAAK,WAAU;kFAAmB;;;;;;;;;;;;0EAAoB,8OAAC;gEAAG,WAAU;0EAAY,eAAe;;;;;;0EAAyB,8OAAC;gEAAG,WAAU;0EAAW,cAAA,8OAAC;oEAAO,WAAU;8EAAgB,eAAe,KAAK,GAAG,CAAC,GAAG,qBAAqB,KAAK,YAAY;;;;;;;;;;;0EAAiB,8OAAC;gEAAG,WAAU;0EAAc,cAAA,8OAAC;;wEAAQ,CAAC,AAAC,KAAK,YAAY,GAAG,qBAAsB,GAAG,EAAE,OAAO,CAAC;wEAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oCAE1nB,mCAAsB,8OAAC;wCAAI,WAAU;kDAA2B;;;;;;kDACjE,8OAAC;wCAAI,WAAU;;0DAAkC,8OAAC;gDAAO,WAAU;gDAAyB,SAAS;gDAAoB,UAAU,CAAC,qBAAqB;0DAAU;;;;;;0DAAsB,8OAAC;gDAAO,WAAU;gDAAkB,SAAS;gDAAmB,UAAU,YAAY,CAAC;0DAAoB,WAAW,kBAAkB;;;;;;;;;;;;kDACjU,8OAAC;wCAAI,WAAU;;0DAAW,8OAAC;gDAAI,WAAU;0DAAW,cAAA,8OAAC;oDAAI,WAAU;8DAA0B,cAAA,8OAAC;wDAAI,WAAU;;0EAAwB,8OAAC;0EAAG;;;;;;0EAAwB,8OAAC;;oEAAI,CAAC,AAAC,KAAK,YAAY,GAAG,qBAAsB,GAAG,EAAE,OAAO,CAAC;oEAAG;;;;;;;0EAAM,8OAAC;0EAAM;;;;;;;;;;;;;;;;;;;;;;0DAAmD,8OAAC;gDAAI,WAAU;0DAAW,cAAA,8OAAC;oDAAI,WAAU;8DAA6B,cAAA,8OAAC;wDAAI,WAAU;;0EAAwB,8OAAC;0EAAG;;;;;;0EAAoB,8OAAC;0EAAI,eAAe,KAAK,GAAG,CAAC,GAAG,qBAAqB,KAAK,YAAY;;;;;;0EAAQ,8OAAC;0EAAM;;;;;;;;;;;;;;;;;;;;;;0DAAoC,8OAAC;gDAAI,WAAU;0DAAW,cAAA,8OAAC;oDAAI,WAAU;8DAA6B,cAAA,8OAAC;wDAAI,WAAU;;0EAAwB,8OAAC;0EAAG;;;;;;0EAAe,8OAAC;;oEAAI,KAAK,GAAG,CAAC,GAAG,KAAK,IAAI,CAAC,CAAC,qBAAqB,KAAK,YAAY,IAAI,CAAC,OAAO,EAAE;oEAAI;;;;;;;0EAAS,8OAAC;0EAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKjxB;uCAEe", "debugId": null}}]}