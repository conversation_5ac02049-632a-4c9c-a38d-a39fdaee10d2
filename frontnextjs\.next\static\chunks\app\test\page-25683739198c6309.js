(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[11],{2365:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let r=s(3464).A.create({baseURL:"http://localhost:8000/api",headers:{"Content-Type":"application/json"},timeout:1e4});r.interceptors.response.use(e=>e,e=>{var t,s;return console.error("API Error:",null==(t=e.response)?void 0:t.status,null==(s=e.config)?void 0:s.url,e.message),Promise.reject(e)});let a=r},3529:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>o});var r=s(5155),a=s(2115),n=s(2365);let o=()=>{let[e,t]=(0,a.useState)(""),[s,o]=(0,a.useState)(!1),c=async()=>{o(!0),t("Testing with Axios apiClient...");try{let e=await n.A.get("/dashboard");t("Axios Success: ".concat(JSON.stringify(e.data,null,2)))}catch(s){let e=s instanceof Error?s.message:"Unknown error";t("Axios Error: ".concat(e))}finally{o(!1)}},i=async()=>{o(!0),t("Testing with fetch (http://localhost:8000/api/dashboard)...");try{let e=await fetch("http://localhost:8000/api/dashboard");if(!e.ok)throw Error("HTTP error! status: ".concat(e.status));let s=await e.json();t("Fetch (localhost) Success: ".concat(JSON.stringify(s,null,2)))}catch(s){let e=s instanceof Error?s.message:"Unknown error";t("Fetch (localhost) Error: ".concat(e))}finally{o(!1)}},l=async()=>{o(!0),t("Testing with fetch (http://127.0.0.1:8000/api/dashboard)...");try{let e=await fetch("http://127.0.0.1:8000/api/dashboard");if(!e.ok)throw Error("HTTP error! status: ".concat(e.status));let s=await e.json();t("Fetch (127.0.0.1) Success: ".concat(JSON.stringify(s,null,2)))}catch(s){let e=s instanceof Error?s.message:"Unknown error";t("Fetch (127.0.0.1) Error: ".concat(e))}finally{o(!1)}};return(0,r.jsxs)("div",{className:"container mt-4",children:[(0,r.jsx)("h2",{children:"Test des Appels API vers le Backend"}),(0,r.jsxs)("p",{children:["Cet outil permet de v\xe9rifier la connectivit\xe9 et les r\xe9ponses de base du backend FastAPI. Assurez-vous que le serveur backend (",(0,r.jsx)("code",{children:"cd backend && uvicorn main:app --reload"}),") est en cours d'ex\xe9cution sur ",(0,r.jsx)("code",{children:"http://localhost:8000"}),"."]}),(0,r.jsxs)("div",{className:"mb-3",children:[(0,r.jsx)("button",{className:"btn btn-primary me-2 mb-2",onClick:c,disabled:s,children:"Test /dashboard (via apiClient Axios)"}),(0,r.jsx)("button",{className:"btn btn-secondary me-2 mb-2",onClick:i,disabled:s,children:"Test /dashboard (via fetch direct sur localhost:8000)"}),(0,r.jsx)("button",{className:"btn btn-info mb-2",onClick:l,disabled:s,children:"Test /dashboard (via fetch direct sur 127.0.0.1:8000)"})]}),s&&(0,r.jsx)("p",{children:"Chargement du test..."}),(0,r.jsx)("pre",{className:"bg-light p-3 border rounded",style:{whiteSpace:"pre-wrap",wordBreak:"break-all"},children:e||"Cliquez sur un bouton pour tester l'API."})]})}},5544:(e,t,s)=>{Promise.resolve().then(s.bind(s,3529))}},e=>{var t=t=>e(e.s=t);e.O(0,[464,441,684,358],()=>t(5544)),_N_E=e.O()}]);