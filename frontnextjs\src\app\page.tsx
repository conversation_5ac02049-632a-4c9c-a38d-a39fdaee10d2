'use client'; // Redevient un Client Component pour l'instant

import React, { useEffect, useState } from 'react';
import apiClient from '../components/ApiClient';
import { Doughnut } from 'react-chartjs-2';
import { Chart as ChartJS, ArcElement, Tooltip, Legend, ChartOptions } from 'chart.js';
import { ProgressBar } from 'react-bootstrap';

ChartJS.register(ArcElement, Tooltip, Legend);

// --- Types ---
interface DashboardData {
  net_patrimoine: number;
  total_assets: number;
  total_liabilities: number;
  allocation: { [key: string]: number };
}
interface AllocationTarget {
  category: string; categoryKey: string; currentValue: number; currentPercent: number;
  targetPercent: number; targetValue: number; amountToInvest: number; progressPercent: number;
}
interface FireAllocationTargetData {
  id: number; category_key: string; target_percentage: number;
}
// --- Fin des Types ---

const DashboardPage: React.FC = () => {
  const [data, setData] = useState<DashboardData | null>(null);
  const [editableAllocations, setEditableAllocations] = useState<AllocationTarget[]>([]);
  const [initialTargetPercentages, setInitialTargetPercentages] = useState<Record<string, number>>({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [retryCount, setRetryCount] = useState(0);
  const [isSaving, setIsSaving] = useState(false);

  const FIRE_TARGET_AMOUNT = 910150;
  const defaultTargetAllocationsConfig: Array<{ category: string; categoryKey: string; defaultTargetPercent: number }> = [
    { category: "Liquidité", categoryKey: "Liquidité", defaultTargetPercent: 2.5 },
    { category: "Bourse", categoryKey: "Bourse", defaultTargetPercent: 35.5 },
    { category: "Immobilier", categoryKey: "Immobilier", defaultTargetPercent: 35.0 },
    { category: "Fonds sécurisés", categoryKey: "Fonds sécurisés", defaultTargetPercent: 20.0 },
    { category: "Prêts participatifs", categoryKey: "Prêts participatifs", defaultTargetPercent: 2.0 },
    { category: "Crypto-Actifs", categoryKey: "Crypto-Actifs", defaultTargetPercent: 5.0 }
  ];

  const processAllocations = (
    dashboardData: DashboardData | null,
    apiTargetPercentages: Record<string, number>
  ): AllocationTarget[] => {
    if (!dashboardData) return [];
    return defaultTargetAllocationsConfig.map(defaultTarget => {
      const currentValue = dashboardData.allocation[defaultTarget.categoryKey] || 0;
      const currentPercent = dashboardData.total_assets > 0 ? (currentValue / dashboardData.total_assets) * 100 : 0;
      const targetPercent = apiTargetPercentages[defaultTarget.categoryKey] !== undefined
        ? apiTargetPercentages[defaultTarget.categoryKey]
        : defaultTarget.defaultTargetPercent;
      const targetValue = (FIRE_TARGET_AMOUNT * targetPercent) / 100;
      const amountToInvest = Math.max(0, targetValue - currentValue);
      const progressPercent = targetValue > 0 ? Math.min(100, (currentValue / targetValue) * 100) : 0;
      return { category: defaultTarget.category, categoryKey: defaultTarget.categoryKey, currentValue, currentPercent, targetPercent, targetValue, amountToInvest, progressPercent };
    });
  };

  const fetchAllData = () => {
    setLoading(true);
    setError(null);
    Promise.all([
      apiClient.get('/dashboard'),
      apiClient.get('/fire-allocation-targets/')
    ])
    .then(([dashboardResponse, fireTargetsResponse]) => {
      const dashboardData = dashboardResponse.data;
      setData(dashboardData);
      const fireTargetsFromAPI: FireAllocationTargetData[] = fireTargetsResponse.data;
      const targetsMap = fireTargetsFromAPI.reduce((acc, current) => { acc[current.category_key] = current.target_percentage; return acc; }, {} as Record<string, number>);
      setInitialTargetPercentages(targetsMap);
      setEditableAllocations(processAllocations(dashboardData, targetsMap));
      setLoading(false); setRetryCount(0);
    })
    .catch(err => {
      console.error('Error fetching data for DashboardPage:', err);
      if (retryCount < 2) { setRetryCount(prev => prev + 1); setTimeout(fetchAllData, 1000); }
      else { setError('Erreur lors de la récupération des données du dashboard.'); setLoading(false); }
    });
  };

  useEffect(() => { fetchAllData(); // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (data) {
        const newProcessedAllocations = processAllocations(data, initialTargetPercentages);
        const userEditedAllocations = editableAllocations.length > 0 ? editableAllocations : newProcessedAllocations;

        const updatedAllocationsWithUserEdits = newProcessedAllocations.map(processedAlloc => {
            const userEditedAlloc = userEditedAllocations.find(ea => ea.categoryKey === processedAlloc.categoryKey);
            if (userEditedAlloc && userEditedAlloc.targetPercent !== processedAlloc.targetPercent) {
                const editedTargetPercent = userEditedAlloc.targetPercent;
                const targetValue = (FIRE_TARGET_AMOUNT * editedTargetPercent) / 100;
                const amountToInvest = Math.max(0, targetValue - processedAlloc.currentValue);
                const progressPercent = targetValue > 0 ? Math.min(100, (processedAlloc.currentValue / targetValue) * 100) : 0;
                return { ...processedAlloc, targetPercent: editedTargetPercent, targetValue, amountToInvest, progressPercent };
            }
            return processedAlloc;
        });
        // S'assurer que editableAllocations est initialisé correctement la première fois ou si data/initialTargetPercentages changent
        if(editableAllocations.length === 0 || initialDashboardDataChanged(data, initialTargetPercentages, prevDataRef.current?.data, prevDataRef.current?.initialTargetPercentages)) {
            setEditableAllocations(updatedAllocationsWithUserEdits);
        }
    }
    // Garder une référence aux données précédentes pour la comparaison
    prevDataRef.current = { data, initialTargetPercentages };
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data, initialTargetPercentages]);

  // Ref pour stocker data et initialTargetPercentages précédents
  const prevDataRef = React.useRef<{data: DashboardData | null, initialTargetPercentages: Record<string,number>}>();

  // Fonction helper pour vérifier si les données initiales ont changé
  const initialDashboardDataChanged = (currentData: any, currentTargets: any, prevData: any, prevTargets: any) => {
    return JSON.stringify(currentData) !== JSON.stringify(prevData) || JSON.stringify(currentTargets) !== JSON.stringify(prevTargets);
  };


  const handleTargetPercentChange = (categoryKey: string, newTargetPercent: string) => {
    const numericValue = parseFloat(newTargetPercent);
    if (isNaN(numericValue) && newTargetPercent !== "" && newTargetPercent !== ".") return;
    setEditableAllocations(prevAllocations =>
      prevAllocations.map(alloc => {
        if (alloc.categoryKey === categoryKey) {
          const updatedTargetPercent = isNaN(numericValue) ? (newTargetPercent === "" ? 0 : alloc.targetPercent) : numericValue;
          const targetValue = (FIRE_TARGET_AMOUNT * updatedTargetPercent) / 100;
          const amountToInvest = Math.max(0, targetValue - alloc.currentValue);
          const progressPercent = targetValue > 0 ? Math.min(100, (alloc.currentValue / targetValue) * 100) : 0;
          return { ...alloc, targetPercent: updatedTargetPercent, targetValue, amountToInvest, progressPercent };
        }
        return alloc;
      })
    );
  };

  const handleSaveChanges = async () => {
    setIsSaving(true); setError(null);
    const totalTarget = editableAllocations.reduce((sum, alloc) => sum + (alloc.targetPercent || 0), 0);
    if (Math.abs(totalTarget - 100) > 0.1) {
        if (!window.confirm(`Le total des pourcentages cibles est ${totalTarget.toFixed(1)}%, ce qui n'est pas égal à 100%. Voulez-vous continuer quand même ?`)) {
            setIsSaving(false); return;
        }
    }
    const payload = editableAllocations.map(alloc => ({ category_key: alloc.categoryKey, target_percentage: alloc.targetPercent || 0 }));
    try {
      await apiClient.post('/fire-allocation-targets/batch_update/', payload);
      const newInitialTargets = payload.reduce((acc, current) => { acc[current.category_key] = current.target_percentage; return acc; }, {} as Record<string, number>);
      setInitialTargetPercentages(newInitialTargets); // Met à jour la base pour "reset" et "hasUnsavedChanges"
    } catch (err) { console.error("Erreur sauvegarde allocations:", err); setError("Erreur sauvegarde. Réessayez."); }
    finally { setIsSaving(false); }
  };

  const handleResetChanges = () => {
      if (!data) return;
      setEditableAllocations(processAllocations(data, initialTargetPercentages));
  };

  const totalTargetPercentage = editableAllocations.reduce((sum, alloc) => sum + (alloc.targetPercent || 0), 0);
  const hasUnsavedChanges = editableAllocations.some(alloc => {
    const initialPercent = initialTargetPercentages[alloc.categoryKey];
    const currentTarget = alloc.targetPercent || 0;
    const initialTarget = initialPercent === undefined ? (defaultTargetAllocationsConfig.find(d => d.categoryKey === alloc.categoryKey)?.defaultTargetPercent || 0) : initialPercent;
    return Math.abs(currentTarget - initialTarget) > 0.001;
  });

  if (loading && retryCount === 0) return <p>Chargement initial du dashboard...</p>;
  if (error && retryCount >=2) return (<div><p className="text-danger">{error}</p><button className="btn btn-primary" onClick={() => {setRetryCount(0); fetchAllData();}}>Réessayer</button></div>);
  if (!data && !loading) return <p>Aucune donnée de dashboard disponible ou erreur de chargement.</p>;
  if (loading) return <p>Chargement du dashboard, tentative {retryCount + 1}...</p>;
  if (!data) return <p>Données du dashboard non disponibles.</p>;

  const formatCurrency = (value: number) => new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR', maximumFractionDigits: 0 }).format(value);
  const categoryColors = { 'Liquidité': { bg: '#17a2b8', border: '#138496', hover: '#20c997' }, 'Bourse': { bg: '#007bff', border: '#0056b3', hover: '#0069d9' }, 'Crypto-Actifs': { bg: '#fd7e14', border: '#e55a00', hover: '#ff8c42' }, 'Fonds sécurisés': { bg: '#28a745', border: '#1e7e34', hover: '#34ce57' }, 'Immobilier': { bg: '#6f42c1', border: '#59359a', hover: '#7952b3' }, 'Prêts participatifs': { bg: '#dc3545', border: '#bd2130', hover: '#e4606d' } };
  const labels = Object.keys(data.allocation); const values = Object.values(data.allocation); const total = values.reduce((sum, value) => sum + value, 0);
  const chartData = { labels: labels, datasets: [{ label: 'Répartition du portefeuille', data: values, backgroundColor: labels.map(label => categoryColors[label as keyof typeof categoryColors]?.bg || '#6c757d'), borderColor: labels.map(label => categoryColors[label as keyof typeof categoryColors]?.border || '#495057'), hoverBackgroundColor: labels.map(label => categoryColors[label as keyof typeof categoryColors]?.hover || '#868e96'), borderWidth: 3, hoverBorderWidth: 4, borderRadius: 8, spacing: 4, }], };
  const chartOptions: ChartOptions<'doughnut'> = { responsive: true, maintainAspectRatio: true, plugins: { legend: { position: 'bottom' as const, labels: { padding: 20, usePointStyle: true, pointStyle: 'circle', font: { size: 14, weight: 'bold' as const, }, generateLabels: (chart) => { const chartDt = chart.data; if (chartDt.labels && chartDt.datasets.length) { return chartDt.labels.map((label, i) => { const val = chartDt.datasets[0].data[i] as number; const perc = total > 0 ? ((val / total) * 100).toFixed(1) : '0'; const bg = Array.isArray(chartDt.datasets[0].backgroundColor) ? chartDt.datasets[0].backgroundColor[i] as string : chartDt.datasets[0].backgroundColor as string; const brd = Array.isArray(chartDt.datasets[0].borderColor) ? chartDt.datasets[0].borderColor[i] as string : chartDt.datasets[0].borderColor as string; return { text: `${label} (${perc}%)`, fillStyle: bg, strokeStyle: brd, lineWidth: 2, hidden: false, index: i, }; }); } return []; }, }, }, tooltip: { enabled: true, backgroundColor: 'rgba(0, 0, 0, 0.9)', titleColor: '#fff', bodyColor: '#fff', borderColor: '#fff', borderWidth: 1, cornerRadius: 8, displayColors: true, callbacks: { title: (ctx) => ctx[0].label || '', label: (ctx) => { const val = ctx.parsed; const perc = total > 0 ? ((val / total) * 100).toFixed(1) : '0'; return [`Valeur: ${formatCurrency(val)}`, `Pourcentage: ${perc}%`]; }, }, }, }, animation: { animateRotate: true, animateScale: true, duration: 1500, easing: 'easeInOutQuart', }, interaction: { intersect: false, mode: 'index' as const, }, onHover: (evt, elems) => { if (evt.native?.target) { (evt.native.target as HTMLElement).style.cursor = elems.length > 0 ? 'pointer' : 'default'; } }, onClick: (evt, elems) => { if (elems.length > 0) { const idx = elems[0].index; const catName = labels[idx]; const navMap: { [key: string]: string } = { 'Liquidité': 'assets', 'Bourse': 'assets', 'Crypto-Actifs': 'assets', 'Fonds sécurisés': 'assets', 'Immobilier': 'scpi', 'Prêts participatifs': 'assets' }; const targetSect = navMap[catName]; if (targetSect) { const val = values[idx]; const perc = total > 0 ? ((val / total) * 100).toFixed(1) : '0'; alert(`${catName}\nValeur: ${formatCurrency(val)}\nPourcentage: ${perc}%\n\nCliquez sur l'onglet "${targetSect === 'scpi' ? 'SCPI' : 'Patrimoine'}" pour gérer cette catégorie.`); } } }, };

  return (
    <div>
      <h1 className="mb-4">Dashboard</h1>
      <div className="row"><div className="col-md-4"><div className="card text-white bg-primary mb-3"><div className="card-header">Patrimoine Net Total</div><div className="card-body"><h5 className="card-title">{formatCurrency(data.net_patrimoine)}</h5></div></div></div><div className="col-md-4"><div className="card text-white bg-success mb-3"><div className="card-header">Total Actifs</div><div className="card-body"><h5 className="card-title">{formatCurrency(data.total_assets)}</h5></div></div></div><div className="col-md-4"><div className="card text-white bg-danger mb-3"><div className="card-header">Total Passifs</div><div className="card-body"><h5 className="card-title">{formatCurrency(data.total_liabilities)}</h5></div></div></div></div>
      <div className="row mt-4"><div className="col-md-8 offset-md-2"><div className="text-center"><h2 className="mb-4">Répartition des Actifs</h2><div style={{ maxWidth: '500px', margin: '0 auto' }}><Doughnut data={chartData} options={chartOptions} /></div></div></div></div>
      <div className="row mt-5"><div className="col-12"><div className="card">
            <div className="card-header"><h3 className="mb-0">🎯 Allocation Cible FIRE</h3><small className="text-muted">Progression vers l'objectif de {formatCurrency(FIRE_TARGET_AMOUNT)} selon la stratégie documentée</small></div>
            <div className="card-body">
              {error && editableAllocations.length === 0 && <div className="alert alert-danger">{error}</div>}
              {error && editableAllocations.length > 0 && <div className="alert alert-danger">Erreur lors de la sauvegarde : {error}</div>}
              <div className="table-responsive"><table className="table table-striped table-hover">
                  <thead className="table-dark"><tr><th>Catégorie d'Actif</th><th className="text-end">Valeur Actuelle</th><th className="text-end">% Actuel</th><th className="text-end" style={{minWidth: "100px"}}>% Cible</th><th className="text-end">Valeur Cible</th><th className="text-end">Montant à Investir</th><th className="text-center">Progression</th></tr></thead>
                  <tbody>{editableAllocations.map((target) => (<tr key={target.categoryKey}><td><strong>{target.category}</strong></td><td className="text-end">{formatCurrency(target.currentValue)}</td><td className="text-end">{target.currentPercent.toFixed(1)}%</td><td className="text-end"><input type="number" value={target.targetPercent === undefined ? '' : target.targetPercent.toString()} onChange={(e) => handleTargetPercentChange(target.categoryKey, e.target.value)} className="form-control form-control-sm text-end" style={{ width: '70px', display: 'inline-block' }} step="0.1" min="0" max="100"/> %</td><td className="text-end">{formatCurrency(target.targetValue)}</td><td className="text-end">{target.amountToInvest > 0 ? (<span className="text-warning">{formatCurrency(target.amountToInvest)}</span>) : (<span className="text-success">Objectif atteint</span>)}</td><td className="text-center" style={{ width: '150px' }}><ProgressBar now={target.progressPercent} variant={target.progressPercent >= 100 ? 'success' : target.progressPercent >= 75 ? 'info' : target.progressPercent >= 50 ? 'warning' : 'danger'} style={{ height: '20px' }} label={`${target.progressPercent.toFixed(0)}%`}/></td></tr>))}</tbody>
                  <tfoot><tr className="table-info"><th>TOTAL</th><th className="text-end">{formatCurrency(data.total_assets)}</th><th className="text-end">100%</th><th className="text-end"><strong>{totalTargetPercentage.toFixed(1)}%</strong>{Math.abs(totalTargetPercentage - 100) > 0.1 && <span className="text-danger ms-1">(≠100%)</span>}</th><th className="text-end">{formatCurrency(FIRE_TARGET_AMOUNT)}</th><th className="text-end"><strong className="text-primary">{formatCurrency(Math.max(0, FIRE_TARGET_AMOUNT - data.total_assets))}</strong></th><th className="text-center"><strong>{((data.total_assets / FIRE_TARGET_AMOUNT) * 100).toFixed(1)}%</strong></th></tr></tfoot>
              </table></div>
              {hasUnsavedChanges && (<div className="alert alert-warning mt-3">Vous avez des modifications non enregistrées.</div>)}
              <div className="mt-3 d-flex justify-content-end"><button className="btn btn-secondary me-2" onClick={handleResetChanges} disabled={!hasUnsavedChanges || isSaving}>Réinitialiser</button><button className="btn btn-primary" onClick={handleSaveChanges} disabled={isSaving || !hasUnsavedChanges}>{isSaving ? 'Sauvegarde...' : 'Sauvegarder les % Cibles'}</button></div>
              <div className="row mt-4"><div className="col-md-4"><div className="card text-white bg-info"><div className="card-body text-center"><h5>Progression Globale</h5><h4>{((data.total_assets / FIRE_TARGET_AMOUNT) * 100).toFixed(1)}%</h4><small>vers l'objectif FIRE</small></div></div></div><div className="col-md-4"><div className="card text-white bg-warning"><div className="card-body text-center"><h5>Capital Restant</h5><h4>{formatCurrency(Math.max(0, FIRE_TARGET_AMOUNT - data.total_assets))}</h4><small>à investir</small></div></div></div><div className="col-md-4"><div className="card text-white bg-success"><div className="card-body text-center"><h5>Estimation</h5><h4>{Math.max(0, Math.ceil((FIRE_TARGET_AMOUNT - data.total_assets) / (3415 * 12)))} ans</h4><small>à 3 415€/mois</small></div></div></div></div>
            </div>
      </div></div></div>
    </div>
  );
};

export default DashboardPage;
