(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[737],{5737:function(e){e.exports=function(){"use strict";let e=new Map,t={set(t,i,n){e.has(t)||e.set(t,new Map);let s=e.get(t);s.has(i)||0===s.size?s.set(i,n):console.error(`Bootstrap doesn't allow more than one instance per element. Bound instance: ${Array.from(s.keys())[0]}.`)},get:(t,i)=>e.has(t)&&e.get(t).get(i)||null,remove(t,i){if(!e.has(t))return;let n=e.get(t);n.delete(i),0===n.size&&e.delete(t)}},i="transitionend",n=e=>(e&&window.CSS&&window.CSS.escape&&(e=e.replace(/#([^\s"#']+)/g,(e,t)=>`#${CSS.escape(t)}`)),e),s=e=>{e.dispatchEvent(new Event(i))},r=e=>!(!e||"object"!=typeof e)&&(void 0!==e.jquery&&(e=e[0]),void 0!==e.nodeType),o=e=>r(e)?e.jquery?e[0]:e:"string"==typeof e&&e.length>0?document.querySelector(n(e)):null,a=e=>{if(!r(e)||0===e.getClientRects().length)return!1;let t="visible"===getComputedStyle(e).getPropertyValue("visibility"),i=e.closest("details:not([open])");if(!i)return t;if(i!==e){let t=e.closest("summary");if(t&&t.parentNode!==i||null===t)return!1}return t},l=e=>!e||e.nodeType!==Node.ELEMENT_NODE||!!e.classList.contains("disabled")||(void 0!==e.disabled?e.disabled:e.hasAttribute("disabled")&&"false"!==e.getAttribute("disabled")),c=e=>{if(!document.documentElement.attachShadow)return null;if("function"==typeof e.getRootNode){let t=e.getRootNode();return t instanceof ShadowRoot?t:null}return e instanceof ShadowRoot?e:e.parentNode?c(e.parentNode):null},h=()=>{},d=e=>{e.offsetHeight},u=()=>window.jQuery&&!document.body.hasAttribute("data-bs-no-jquery")?window.jQuery:null,f=[],p=()=>"rtl"===document.documentElement.dir,m=e=>{var t;t=()=>{let t=u();if(t){let i=e.NAME,n=t.fn[i];t.fn[i]=e.jQueryInterface,t.fn[i].Constructor=e,t.fn[i].noConflict=()=>(t.fn[i]=n,e.jQueryInterface)}},"loading"===document.readyState?(f.length||document.addEventListener("DOMContentLoaded",()=>{for(let e of f)e()}),f.push(t)):t()},g=(e,t=[],i=e)=>"function"==typeof e?e.call(...t):i,_=(e,t,n=!0)=>{if(!n)return void g(e);let r=(e=>{if(!e)return 0;let{transitionDuration:t,transitionDelay:i}=window.getComputedStyle(e),n=Number.parseFloat(t),s=Number.parseFloat(i);return n||s?(t=t.split(",")[0],i=i.split(",")[0],1e3*(Number.parseFloat(t)+Number.parseFloat(i))):0})(t)+5,o=!1,a=({target:n})=>{n===t&&(o=!0,t.removeEventListener(i,a),g(e))};t.addEventListener(i,a),setTimeout(()=>{o||s(t)},r)},b=(e,t,i,n)=>{let s=e.length,r=e.indexOf(t);return -1===r?!i&&n?e[s-1]:e[0]:(r+=i?1:-1,n&&(r=(r+s)%s),e[Math.max(0,Math.min(r,s-1))])},v=/[^.]*(?=\..*)\.|.*/,y=/\..*/,w=/::\d+$/,A={},E=1,T={mouseenter:"mouseover",mouseleave:"mouseout"},C=new Set(["click","dblclick","mouseup","mousedown","contextmenu","mousewheel","DOMMouseScroll","mouseover","mouseout","mousemove","selectstart","selectend","keydown","keypress","keyup","orientationchange","touchstart","touchmove","touchend","touchcancel","pointerdown","pointermove","pointerup","pointerleave","pointercancel","gesturestart","gesturechange","gestureend","focus","blur","change","reset","select","submit","focusin","focusout","load","unload","beforeunload","resize","move","DOMContentLoaded","readystatechange","error","abort","scroll"]);function O(e,t){return t&&`${t}::${E++}`||e.uidEvent||E++}function x(e){let t=O(e);return e.uidEvent=t,A[t]=A[t]||{},A[t]}function k(e,t,i=null){return Object.values(e).find(e=>e.callable===t&&e.delegationSelector===i)}function L(e,t,i){let n="string"==typeof t,s=$(e);return C.has(s)||(s=e),[n,n?i:t||i,s]}function S(e,t,i,n,s){var r,o;if("string"!=typeof t||!e)return;let[a,l,c]=L(t,i,n);if(t in T){let e;e=l,l=function(t){if(!t.relatedTarget||t.relatedTarget!==t.delegateTarget&&!t.delegateTarget.contains(t.relatedTarget))return e.call(this,t)}}let h=x(e),d=h[c]||(h[c]={}),u=k(d,l,a?i:null);if(u)return void(u.oneOff=u.oneOff&&s);let f=O(l,t.replace(v,"")),p=a?(r=l,function t(n){let s=e.querySelectorAll(i);for(let{target:o}=n;o&&o!==this;o=o.parentNode)for(let a of s)if(a===o)return N(n,{delegateTarget:o}),t.oneOff&&I.off(e,n.type,i,r),r.apply(o,[n])}):(o=l,function t(i){return N(i,{delegateTarget:e}),t.oneOff&&I.off(e,i.type,o),o.apply(e,[i])});p.delegationSelector=a?i:null,p.callable=l,p.oneOff=s,p.uidEvent=f,d[f]=p,e.addEventListener(c,p,a)}function D(e,t,i,n,s){let r=k(t[i],n,s);r&&(e.removeEventListener(i,r,!!s),delete t[i][r.uidEvent])}function $(e){return T[e=e.replace(y,"")]||e}let I={on(e,t,i,n){S(e,t,i,n,!1)},one(e,t,i,n){S(e,t,i,n,!0)},off(e,t,i,n){if("string"!=typeof t||!e)return;let[s,r,o]=L(t,i,n),a=o!==t,l=x(e),c=l[o]||{},h=t.startsWith(".");if(void 0===r){if(h)for(let i of Object.keys(l)){var d=t.slice(1);for(let[t,n]of Object.entries(l[i]||{}))t.includes(d)&&D(e,l,i,n.callable,n.delegationSelector)}for(let[i,n]of Object.entries(c)){let s=i.replace(w,"");a&&!t.includes(s)||D(e,l,o,n.callable,n.delegationSelector)}}else{if(!Object.keys(c).length)return;D(e,l,o,r,s?i:null)}},trigger(e,t,i){if("string"!=typeof t||!e)return null;let n=u(),s=null,r=!0,o=!0,a=!1;t!==$(t)&&n&&(s=n.Event(t,i),n(e).trigger(s),r=!s.isPropagationStopped(),o=!s.isImmediatePropagationStopped(),a=s.isDefaultPrevented());let l=N(new Event(t,{bubbles:r,cancelable:!0}),i);return a&&l.preventDefault(),o&&e.dispatchEvent(l),l.defaultPrevented&&s&&s.preventDefault(),l}};function N(e,t={}){for(let[i,n]of Object.entries(t))try{e[i]=n}catch(t){Object.defineProperty(e,i,{configurable:!0,get:()=>n})}return e}function P(e){if("true"===e)return!0;if("false"===e)return!1;if(e===Number(e).toString())return Number(e);if(""===e||"null"===e)return null;if("string"!=typeof e)return e;try{return JSON.parse(decodeURIComponent(e))}catch(t){return e}}function M(e){return e.replace(/[A-Z]/g,e=>`-${e.toLowerCase()}`)}let j={setDataAttribute(e,t,i){e.setAttribute(`data-bs-${M(t)}`,i)},removeDataAttribute(e,t){e.removeAttribute(`data-bs-${M(t)}`)},getDataAttributes(e){if(!e)return{};let t={};for(let i of Object.keys(e.dataset).filter(e=>e.startsWith("bs")&&!e.startsWith("bsConfig"))){let n=i.replace(/^bs/,"");t[n=n.charAt(0).toLowerCase()+n.slice(1)]=P(e.dataset[i])}return t},getDataAttribute:(e,t)=>P(e.getAttribute(`data-bs-${M(t)}`))};class F{static get Default(){return{}}static get DefaultType(){return{}}static get NAME(){throw Error('You have to implement the static method "NAME", for each component!')}_getConfig(e){return e=this._mergeConfigObj(e),e=this._configAfterMerge(e),this._typeCheckConfig(e),e}_configAfterMerge(e){return e}_mergeConfigObj(e,t){let i=r(t)?j.getDataAttribute(t,"config"):{};return{...this.constructor.Default,..."object"==typeof i?i:{},...r(t)?j.getDataAttributes(t):{},..."object"==typeof e?e:{}}}_typeCheckConfig(e,t=this.constructor.DefaultType){for(let[i,n]of Object.entries(t)){let t=e[i],s=r(t)?"element":null==t?`${t}`:Object.prototype.toString.call(t).match(/\s([a-z]+)/i)[1].toLowerCase();if(!new RegExp(n).test(s))throw TypeError(`${this.constructor.NAME.toUpperCase()}: Option "${i}" provided type "${s}" but expected type "${n}".`)}}}class H extends F{constructor(e,i){super(),(e=o(e))&&(this._element=e,this._config=this._getConfig(i),t.set(this._element,this.constructor.DATA_KEY,this))}dispose(){for(let e of(t.remove(this._element,this.constructor.DATA_KEY),I.off(this._element,this.constructor.EVENT_KEY),Object.getOwnPropertyNames(this)))this[e]=null}_queueCallback(e,t,i=!0){_(e,t,i)}_getConfig(e){return e=this._mergeConfigObj(e,this._element),e=this._configAfterMerge(e),this._typeCheckConfig(e),e}static getInstance(e){return t.get(o(e),this.DATA_KEY)}static getOrCreateInstance(e,t={}){return this.getInstance(e)||new this(e,"object"==typeof t?t:null)}static get VERSION(){return"5.3.7"}static get DATA_KEY(){return`bs.${this.NAME}`}static get EVENT_KEY(){return`.${this.DATA_KEY}`}static eventName(e){return`${e}${this.EVENT_KEY}`}}let W=e=>{let t=e.getAttribute("data-bs-target");if(!t||"#"===t){let i=e.getAttribute("href");if(!i||!i.includes("#")&&!i.startsWith("."))return null;i.includes("#")&&!i.startsWith("#")&&(i=`#${i.split("#")[1]}`),t=i&&"#"!==i?i.trim():null}return t?t.split(",").map(e=>n(e)).join(","):null},z={find:(e,t=document.documentElement)=>[].concat(...Element.prototype.querySelectorAll.call(t,e)),findOne:(e,t=document.documentElement)=>Element.prototype.querySelector.call(t,e),children:(e,t)=>[].concat(...e.children).filter(e=>e.matches(t)),parents(e,t){let i=[],n=e.parentNode.closest(t);for(;n;)i.push(n),n=n.parentNode.closest(t);return i},prev(e,t){let i=e.previousElementSibling;for(;i;){if(i.matches(t))return[i];i=i.previousElementSibling}return[]},next(e,t){let i=e.nextElementSibling;for(;i;){if(i.matches(t))return[i];i=i.nextElementSibling}return[]},focusableChildren(e){let t=["a","button","input","textarea","select","details","[tabindex]",'[contenteditable="true"]'].map(e=>`${e}:not([tabindex^="-"])`).join(",");return this.find(t,e).filter(e=>!l(e)&&a(e))},getSelectorFromElement(e){let t=W(e);return t&&z.findOne(t)?t:null},getElementFromSelector(e){let t=W(e);return t?z.findOne(t):null},getMultipleElementsFromSelector(e){let t=W(e);return t?z.find(t):[]}},R=(e,t="hide")=>{let i=`click.dismiss${e.EVENT_KEY}`,n=e.NAME;I.on(document,i,`[data-bs-dismiss="${n}"]`,function(i){if(["A","AREA"].includes(this.tagName)&&i.preventDefault(),l(this))return;let s=z.getElementFromSelector(this)||this.closest(`.${n}`);e.getOrCreateInstance(s)[t]()})},B=".bs.alert",q=`close${B}`,V=`closed${B}`;class K extends H{static get NAME(){return"alert"}close(){if(I.trigger(this._element,q).defaultPrevented)return;this._element.classList.remove("show");let e=this._element.classList.contains("fade");this._queueCallback(()=>this._destroyElement(),this._element,e)}_destroyElement(){this._element.remove(),I.trigger(this._element,V),this.dispose()}static jQueryInterface(e){return this.each(function(){let t=K.getOrCreateInstance(this);if("string"==typeof e){if(void 0===t[e]||e.startsWith("_")||"constructor"===e)throw TypeError(`No method named "${e}"`);t[e](this)}})}}R(K,"close"),m(K);let Q='[data-bs-toggle="button"]';class X extends H{static get NAME(){return"button"}toggle(){this._element.setAttribute("aria-pressed",this._element.classList.toggle("active"))}static jQueryInterface(e){return this.each(function(){let t=X.getOrCreateInstance(this);"toggle"===e&&t[e]()})}}I.on(document,"click.bs.button.data-api",Q,e=>{e.preventDefault();let t=e.target.closest(Q);X.getOrCreateInstance(t).toggle()}),m(X);let Y=".bs.swipe",U=`touchstart${Y}`,G=`touchmove${Y}`,J=`touchend${Y}`,Z=`pointerdown${Y}`,ee=`pointerup${Y}`,et={endCallback:null,leftCallback:null,rightCallback:null},ei={endCallback:"(function|null)",leftCallback:"(function|null)",rightCallback:"(function|null)"};class en extends F{constructor(e,t){super(),this._element=e,e&&en.isSupported()&&(this._config=this._getConfig(t),this._deltaX=0,this._supportPointerEvents=!!window.PointerEvent,this._initEvents())}static get Default(){return et}static get DefaultType(){return ei}static get NAME(){return"swipe"}dispose(){I.off(this._element,Y)}_start(e){this._supportPointerEvents?this._eventIsPointerPenTouch(e)&&(this._deltaX=e.clientX):this._deltaX=e.touches[0].clientX}_end(e){this._eventIsPointerPenTouch(e)&&(this._deltaX=e.clientX-this._deltaX),this._handleSwipe(),g(this._config.endCallback)}_move(e){this._deltaX=e.touches&&e.touches.length>1?0:e.touches[0].clientX-this._deltaX}_handleSwipe(){let e=Math.abs(this._deltaX);if(e<=40)return;let t=e/this._deltaX;this._deltaX=0,t&&g(t>0?this._config.rightCallback:this._config.leftCallback)}_initEvents(){this._supportPointerEvents?(I.on(this._element,Z,e=>this._start(e)),I.on(this._element,ee,e=>this._end(e)),this._element.classList.add("pointer-event")):(I.on(this._element,U,e=>this._start(e)),I.on(this._element,G,e=>this._move(e)),I.on(this._element,J,e=>this._end(e)))}_eventIsPointerPenTouch(e){return this._supportPointerEvents&&("pen"===e.pointerType||"touch"===e.pointerType)}static isSupported(){return"ontouchstart"in document.documentElement||navigator.maxTouchPoints>0}}let es=".bs.carousel",er=".data-api",eo="next",ea="prev",el="left",ec="right",eh=`slide${es}`,ed=`slid${es}`,eu=`keydown${es}`,ef=`mouseenter${es}`,ep=`mouseleave${es}`,em=`dragstart${es}`,eg=`load${es}${er}`,e_=`click${es}${er}`,eb="carousel",ev="active",ey=".active",ew=".carousel-item",eA=ey+ew,eE={ArrowLeft:ec,ArrowRight:el},eT={interval:5e3,keyboard:!0,pause:"hover",ride:!1,touch:!0,wrap:!0},eC={interval:"(number|boolean)",keyboard:"boolean",pause:"(string|boolean)",ride:"(boolean|string)",touch:"boolean",wrap:"boolean"};class eO extends H{constructor(e,t){super(e,t),this._interval=null,this._activeElement=null,this._isSliding=!1,this.touchTimeout=null,this._swipeHelper=null,this._indicatorsElement=z.findOne(".carousel-indicators",this._element),this._addEventListeners(),this._config.ride===eb&&this.cycle()}static get Default(){return eT}static get DefaultType(){return eC}static get NAME(){return"carousel"}next(){this._slide(eo)}nextWhenVisible(){!document.hidden&&a(this._element)&&this.next()}prev(){this._slide(ea)}pause(){this._isSliding&&s(this._element),this._clearInterval()}cycle(){this._clearInterval(),this._updateInterval(),this._interval=setInterval(()=>this.nextWhenVisible(),this._config.interval)}_maybeEnableCycle(){this._config.ride&&(this._isSliding?I.one(this._element,ed,()=>this.cycle()):this.cycle())}to(e){let t=this._getItems();if(e>t.length-1||e<0)return;if(this._isSliding)return void I.one(this._element,ed,()=>this.to(e));let i=this._getItemIndex(this._getActive());i!==e&&this._slide(e>i?eo:ea,t[e])}dispose(){this._swipeHelper&&this._swipeHelper.dispose(),super.dispose()}_configAfterMerge(e){return e.defaultInterval=e.interval,e}_addEventListeners(){this._config.keyboard&&I.on(this._element,eu,e=>this._keydown(e)),"hover"===this._config.pause&&(I.on(this._element,ef,()=>this.pause()),I.on(this._element,ep,()=>this._maybeEnableCycle())),this._config.touch&&en.isSupported()&&this._addTouchEventListeners()}_addTouchEventListeners(){for(let e of z.find(".carousel-item img",this._element))I.on(e,em,e=>e.preventDefault());this._swipeHelper=new en(this._element,{leftCallback:()=>this._slide(this._directionToOrder(el)),rightCallback:()=>this._slide(this._directionToOrder(ec)),endCallback:()=>{"hover"===this._config.pause&&(this.pause(),this.touchTimeout&&clearTimeout(this.touchTimeout),this.touchTimeout=setTimeout(()=>this._maybeEnableCycle(),500+this._config.interval))}})}_keydown(e){if(/input|textarea/i.test(e.target.tagName))return;let t=eE[e.key];t&&(e.preventDefault(),this._slide(this._directionToOrder(t)))}_getItemIndex(e){return this._getItems().indexOf(e)}_setActiveIndicatorElement(e){if(!this._indicatorsElement)return;let t=z.findOne(ey,this._indicatorsElement);t.classList.remove(ev),t.removeAttribute("aria-current");let i=z.findOne(`[data-bs-slide-to="${e}"]`,this._indicatorsElement);i&&(i.classList.add(ev),i.setAttribute("aria-current","true"))}_updateInterval(){let e=this._activeElement||this._getActive();if(!e)return;let t=Number.parseInt(e.getAttribute("data-bs-interval"),10);this._config.interval=t||this._config.defaultInterval}_slide(e,t=null){if(this._isSliding)return;let i=this._getActive(),n=e===eo,s=t||b(this._getItems(),i,n,this._config.wrap);if(s===i)return;let r=this._getItemIndex(s),o=t=>I.trigger(this._element,t,{relatedTarget:s,direction:this._orderToDirection(e),from:this._getItemIndex(i),to:r});if(o(eh).defaultPrevented||!i||!s)return;let a=!!this._interval;this.pause(),this._isSliding=!0,this._setActiveIndicatorElement(r),this._activeElement=s;let l=n?"carousel-item-start":"carousel-item-end",c=n?"carousel-item-next":"carousel-item-prev";s.classList.add(c),d(s),i.classList.add(l),s.classList.add(l),this._queueCallback(()=>{s.classList.remove(l,c),s.classList.add(ev),i.classList.remove(ev,c,l),this._isSliding=!1,o(ed)},i,this._isAnimated()),a&&this.cycle()}_isAnimated(){return this._element.classList.contains("slide")}_getActive(){return z.findOne(eA,this._element)}_getItems(){return z.find(ew,this._element)}_clearInterval(){this._interval&&(clearInterval(this._interval),this._interval=null)}_directionToOrder(e){return p()?e===el?ea:eo:e===el?eo:ea}_orderToDirection(e){return p()?e===ea?el:ec:e===ea?ec:el}static jQueryInterface(e){return this.each(function(){let t=eO.getOrCreateInstance(this,e);if("number"!=typeof e){if("string"==typeof e){if(void 0===t[e]||e.startsWith("_")||"constructor"===e)throw TypeError(`No method named "${e}"`);t[e]()}}else t.to(e)})}}I.on(document,e_,"[data-bs-slide], [data-bs-slide-to]",function(e){let t=z.getElementFromSelector(this);if(!t||!t.classList.contains(eb))return;e.preventDefault();let i=eO.getOrCreateInstance(t),n=this.getAttribute("data-bs-slide-to");return n?i.to(n):"next"===j.getDataAttribute(this,"slide")?i.next():i.prev(),void i._maybeEnableCycle()}),I.on(window,eg,()=>{for(let e of z.find('[data-bs-ride="carousel"]'))eO.getOrCreateInstance(e)}),m(eO);let ex=".bs.collapse",ek=`show${ex}`,eL=`shown${ex}`,eS=`hide${ex}`,eD=`hidden${ex}`,e$=`click${ex}.data-api`,eI="show",eN="collapse",eP="collapsing",eM=`:scope .${eN} .${eN}`,ej='[data-bs-toggle="collapse"]',eF={parent:null,toggle:!0},eH={parent:"(null|element)",toggle:"boolean"};class eW extends H{constructor(e,t){for(let i of(super(e,t),this._isTransitioning=!1,this._triggerArray=[],z.find(ej))){let e=z.getSelectorFromElement(i),t=z.find(e).filter(e=>e===this._element);null!==e&&t.length&&this._triggerArray.push(i)}this._initializeChildren(),this._config.parent||this._addAriaAndCollapsedClass(this._triggerArray,this._isShown()),this._config.toggle&&this.toggle()}static get Default(){return eF}static get DefaultType(){return eH}static get NAME(){return"collapse"}toggle(){this._isShown()?this.hide():this.show()}show(){if(this._isTransitioning||this._isShown())return;let e=[];if(this._config.parent&&(e=this._getFirstLevelChildren(".collapse.show, .collapse.collapsing").filter(e=>e!==this._element).map(e=>eW.getOrCreateInstance(e,{toggle:!1}))),e.length&&e[0]._isTransitioning||I.trigger(this._element,ek).defaultPrevented)return;for(let t of e)t.hide();let t=this._getDimension();this._element.classList.remove(eN),this._element.classList.add(eP),this._element.style[t]=0,this._addAriaAndCollapsedClass(this._triggerArray,!0),this._isTransitioning=!0;let i=`scroll${t[0].toUpperCase()+t.slice(1)}`;this._queueCallback(()=>{this._isTransitioning=!1,this._element.classList.remove(eP),this._element.classList.add(eN,eI),this._element.style[t]="",I.trigger(this._element,eL)},this._element,!0),this._element.style[t]=`${this._element[i]}px`}hide(){if(this._isTransitioning||!this._isShown()||I.trigger(this._element,eS).defaultPrevented)return;let e=this._getDimension();for(let t of(this._element.style[e]=`${this._element.getBoundingClientRect()[e]}px`,d(this._element),this._element.classList.add(eP),this._element.classList.remove(eN,eI),this._triggerArray)){let e=z.getElementFromSelector(t);e&&!this._isShown(e)&&this._addAriaAndCollapsedClass([t],!1)}this._isTransitioning=!0,this._element.style[e]="",this._queueCallback(()=>{this._isTransitioning=!1,this._element.classList.remove(eP),this._element.classList.add(eN),I.trigger(this._element,eD)},this._element,!0)}_isShown(e=this._element){return e.classList.contains(eI)}_configAfterMerge(e){return e.toggle=!!e.toggle,e.parent=o(e.parent),e}_getDimension(){return this._element.classList.contains("collapse-horizontal")?"width":"height"}_initializeChildren(){if(this._config.parent)for(let e of this._getFirstLevelChildren(ej)){let t=z.getElementFromSelector(e);t&&this._addAriaAndCollapsedClass([e],this._isShown(t))}}_getFirstLevelChildren(e){let t=z.find(eM,this._config.parent);return z.find(e,this._config.parent).filter(e=>!t.includes(e))}_addAriaAndCollapsedClass(e,t){if(e.length)for(let i of e)i.classList.toggle("collapsed",!t),i.setAttribute("aria-expanded",t)}static jQueryInterface(e){let t={};return"string"==typeof e&&/show|hide/.test(e)&&(t.toggle=!1),this.each(function(){let i=eW.getOrCreateInstance(this,t);if("string"==typeof e){if(void 0===i[e])throw TypeError(`No method named "${e}"`);i[e]()}})}}I.on(document,e$,ej,function(e){for(let t of(("A"===e.target.tagName||e.delegateTarget&&"A"===e.delegateTarget.tagName)&&e.preventDefault(),z.getMultipleElementsFromSelector(this)))eW.getOrCreateInstance(t,{toggle:!1}).toggle()}),m(eW);var ez="bottom",eR="right",eB="left",eq="auto",eV=["top",ez,eR,eB],eK="start",eQ="clippingParents",eX="viewport",eY="popper",eU="reference",eG=eV.reduce(function(e,t){return e.concat([t+"-"+eK,t+"-end"])},[]),eJ=[].concat(eV,[eq]).reduce(function(e,t){return e.concat([t,t+"-"+eK,t+"-end"])},[]),eZ="beforeRead",e0="read",e1="afterRead",e2="beforeMain",e3="main",e5="afterMain",e7="beforeWrite",e6="write",e4="afterWrite",e8=[eZ,e0,e1,e2,e3,e5,e7,e6,e4];function e9(e){return e?(e.nodeName||"").toLowerCase():null}function te(e){if(null==e)return window;if("[object Window]"!==e.toString()){var t=e.ownerDocument;return t&&t.defaultView||window}return e}function tt(e){return e instanceof te(e).Element||e instanceof Element}function ti(e){return e instanceof te(e).HTMLElement||e instanceof HTMLElement}function tn(e){return"undefined"!=typeof ShadowRoot&&(e instanceof te(e).ShadowRoot||e instanceof ShadowRoot)}let ts={name:"applyStyles",enabled:!0,phase:"write",fn:function(e){var t=e.state;Object.keys(t.elements).forEach(function(e){var i=t.styles[e]||{},n=t.attributes[e]||{},s=t.elements[e];ti(s)&&e9(s)&&(Object.assign(s.style,i),Object.keys(n).forEach(function(e){var t=n[e];!1===t?s.removeAttribute(e):s.setAttribute(e,!0===t?"":t)}))})},effect:function(e){var t=e.state,i={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(t.elements.popper.style,i.popper),t.styles=i,t.elements.arrow&&Object.assign(t.elements.arrow.style,i.arrow),function(){Object.keys(t.elements).forEach(function(e){var n=t.elements[e],s=t.attributes[e]||{},r=Object.keys(t.styles.hasOwnProperty(e)?t.styles[e]:i[e]).reduce(function(e,t){return e[t]="",e},{});ti(n)&&e9(n)&&(Object.assign(n.style,r),Object.keys(s).forEach(function(e){n.removeAttribute(e)}))})}},requires:["computeStyles"]};function tr(e){return e.split("-")[0]}var to=Math.max,ta=Math.min,tl=Math.round;function tc(){var e=navigator.userAgentData;return null!=e&&e.brands&&Array.isArray(e.brands)?e.brands.map(function(e){return e.brand+"/"+e.version}).join(" "):navigator.userAgent}function th(){return!/^((?!chrome|android).)*safari/i.test(tc())}function td(e,t,i){void 0===t&&(t=!1),void 0===i&&(i=!1);var n=e.getBoundingClientRect(),s=1,r=1;t&&ti(e)&&(s=e.offsetWidth>0&&tl(n.width)/e.offsetWidth||1,r=e.offsetHeight>0&&tl(n.height)/e.offsetHeight||1);var o=(tt(e)?te(e):window).visualViewport,a=!th()&&i,l=(n.left+(a&&o?o.offsetLeft:0))/s,c=(n.top+(a&&o?o.offsetTop:0))/r,h=n.width/s,d=n.height/r;return{width:h,height:d,top:c,right:l+h,bottom:c+d,left:l,x:l,y:c}}function tu(e){var t=td(e),i=e.offsetWidth,n=e.offsetHeight;return 1>=Math.abs(t.width-i)&&(i=t.width),1>=Math.abs(t.height-n)&&(n=t.height),{x:e.offsetLeft,y:e.offsetTop,width:i,height:n}}function tf(e,t){var i=t.getRootNode&&t.getRootNode();if(e.contains(t))return!0;if(i&&tn(i)){var n=t;do{if(n&&e.isSameNode(n))return!0;n=n.parentNode||n.host}while(n)}return!1}function tp(e){return te(e).getComputedStyle(e)}function tm(e){return((tt(e)?e.ownerDocument:e.document)||window.document).documentElement}function tg(e){return"html"===e9(e)?e:e.assignedSlot||e.parentNode||(tn(e)?e.host:null)||tm(e)}function t_(e){return ti(e)&&"fixed"!==tp(e).position?e.offsetParent:null}function tb(e){for(var t=te(e),i=t_(e);i&&["table","td","th"].indexOf(e9(i))>=0&&"static"===tp(i).position;)i=t_(i);return i&&("html"===e9(i)||"body"===e9(i)&&"static"===tp(i).position)?t:i||function(e){var t=/firefox/i.test(tc());if(/Trident/i.test(tc())&&ti(e)&&"fixed"===tp(e).position)return null;var i=tg(e);for(tn(i)&&(i=i.host);ti(i)&&0>["html","body"].indexOf(e9(i));){var n=tp(i);if("none"!==n.transform||"none"!==n.perspective||"paint"===n.contain||-1!==["transform","perspective"].indexOf(n.willChange)||t&&"filter"===n.willChange||t&&n.filter&&"none"!==n.filter)return i;i=i.parentNode}return null}(e)||t}function tv(e){return["top","bottom"].indexOf(e)>=0?"x":"y"}function ty(e,t,i){return to(e,ta(t,i))}function tw(e){return Object.assign({},{top:0,right:0,bottom:0,left:0},e)}function tA(e,t){return t.reduce(function(t,i){return t[i]=e,t},{})}let tE={name:"arrow",enabled:!0,phase:"main",fn:function(e){var t,i=e.state,n=e.name,s=e.options,r=i.elements.arrow,o=i.modifiersData.popperOffsets,a=tr(i.placement),l=tv(a),c=[eB,eR].indexOf(a)>=0?"height":"width";if(r&&o){var h,d=(h=s.padding,tw("number"!=typeof(h="function"==typeof h?h(Object.assign({},i.rects,{placement:i.placement})):h)?h:tA(h,eV))),u=tu(r),f="y"===l?"top":eB,p="y"===l?ez:eR,m=i.rects.reference[c]+i.rects.reference[l]-o[l]-i.rects.popper[c],g=o[l]-i.rects.reference[l],_=tb(r),b=_?"y"===l?_.clientHeight||0:_.clientWidth||0:0,v=d[f],y=b-u[c]-d[p],w=b/2-u[c]/2+(m/2-g/2),A=ty(v,w,y);i.modifiersData[n]=((t={})[l]=A,t.centerOffset=A-w,t)}},effect:function(e){var t=e.state,i=e.options.element,n=void 0===i?"[data-popper-arrow]":i;null!=n&&("string"!=typeof n||(n=t.elements.popper.querySelector(n)))&&tf(t.elements.popper,n)&&(t.elements.arrow=n)},requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function tT(e){return e.split("-")[1]}var tC={top:"auto",right:"auto",bottom:"auto",left:"auto"};function tO(e){var t,i=e.popper,n=e.popperRect,s=e.placement,r=e.variation,o=e.offsets,a=e.position,l=e.gpuAcceleration,c=e.adaptive,h=e.roundOffsets,d=e.isFixed,u=o.x,f=void 0===u?0:u,p=o.y,m=void 0===p?0:p,g="function"==typeof h?h({x:f,y:m}):{x:f,y:m};f=g.x,m=g.y;var _=o.hasOwnProperty("x"),b=o.hasOwnProperty("y"),v=eB,y="top",w=window;if(c){var A=tb(i),E="clientHeight",T="clientWidth";A===te(i)&&"static"!==tp(A=tm(i)).position&&"absolute"===a&&(E="scrollHeight",T="scrollWidth"),("top"===s||(s===eB||s===eR)&&"end"===r)&&(y=ez,m-=(d&&A===w&&w.visualViewport?w.visualViewport.height:A[E])-n.height,m*=l?1:-1),s!==eB&&("top"!==s&&s!==ez||"end"!==r)||(v=eR,f-=(d&&A===w&&w.visualViewport?w.visualViewport.width:A[T])-n.width,f*=l?1:-1)}var C,O,x,k,L,S,D=Object.assign({position:a},c&&tC),$=!0===h?(C={x:f,y:m},O=te(i),x=C.x,k=C.y,{x:tl(x*(L=O.devicePixelRatio||1))/L||0,y:tl(k*L)/L||0}):{x:f,y:m};return f=$.x,m=$.y,l?Object.assign({},D,((S={})[y]=b?"0":"",S[v]=_?"0":"",S.transform=1>=(w.devicePixelRatio||1)?"translate("+f+"px, "+m+"px)":"translate3d("+f+"px, "+m+"px, 0)",S)):Object.assign({},D,((t={})[y]=b?m+"px":"",t[v]=_?f+"px":"",t.transform="",t))}let tx={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:function(e){var t=e.state,i=e.options,n=i.gpuAcceleration,s=i.adaptive,r=i.roundOffsets,o=void 0===r||r,a={placement:tr(t.placement),variation:tT(t.placement),popper:t.elements.popper,popperRect:t.rects.popper,gpuAcceleration:void 0===n||n,isFixed:"fixed"===t.options.strategy};null!=t.modifiersData.popperOffsets&&(t.styles.popper=Object.assign({},t.styles.popper,tO(Object.assign({},a,{offsets:t.modifiersData.popperOffsets,position:t.options.strategy,adaptive:void 0===s||s,roundOffsets:o})))),null!=t.modifiersData.arrow&&(t.styles.arrow=Object.assign({},t.styles.arrow,tO(Object.assign({},a,{offsets:t.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:o})))),t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-placement":t.placement})},data:{}};var tk={passive:!0};let tL={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:function(e){var t=e.state,i=e.instance,n=e.options,s=n.scroll,r=void 0===s||s,o=n.resize,a=void 0===o||o,l=te(t.elements.popper),c=[].concat(t.scrollParents.reference,t.scrollParents.popper);return r&&c.forEach(function(e){e.addEventListener("scroll",i.update,tk)}),a&&l.addEventListener("resize",i.update,tk),function(){r&&c.forEach(function(e){e.removeEventListener("scroll",i.update,tk)}),a&&l.removeEventListener("resize",i.update,tk)}},data:{}};var tS={left:"right",right:"left",bottom:"top",top:"bottom"};function tD(e){return e.replace(/left|right|bottom|top/g,function(e){return tS[e]})}var t$={start:"end",end:"start"};function tI(e){return e.replace(/start|end/g,function(e){return t$[e]})}function tN(e){var t=te(e);return{scrollLeft:t.pageXOffset,scrollTop:t.pageYOffset}}function tP(e){return td(tm(e)).left+tN(e).scrollLeft}function tM(e){var t=tp(e),i=t.overflow,n=t.overflowX,s=t.overflowY;return/auto|scroll|overlay|hidden/.test(i+s+n)}function tj(e,t){void 0===t&&(t=[]);var i,n=function e(t){return["html","body","#document"].indexOf(e9(t))>=0?t.ownerDocument.body:ti(t)&&tM(t)?t:e(tg(t))}(e),s=n===(null==(i=e.ownerDocument)?void 0:i.body),r=te(n),o=s?[r].concat(r.visualViewport||[],tM(n)?n:[]):n,a=t.concat(o);return s?a:a.concat(tj(tg(o)))}function tF(e){return Object.assign({},e,{left:e.x,top:e.y,right:e.x+e.width,bottom:e.y+e.height})}function tH(e,t,i){var n,s,r,o,a,l,c,h,d,u;return t===eX?tF(function(e,t){var i=te(e),n=tm(e),s=i.visualViewport,r=n.clientWidth,o=n.clientHeight,a=0,l=0;if(s){r=s.width,o=s.height;var c=th();(c||!c&&"fixed"===t)&&(a=s.offsetLeft,l=s.offsetTop)}return{width:r,height:o,x:a+tP(e),y:l}}(e,i)):tt(t)?((n=td(t,!1,"fixed"===i)).top=n.top+t.clientTop,n.left=n.left+t.clientLeft,n.bottom=n.top+t.clientHeight,n.right=n.left+t.clientWidth,n.width=t.clientWidth,n.height=t.clientHeight,n.x=n.left,n.y=n.top,n):tF((s=tm(e),o=tm(s),a=tN(s),l=null==(r=s.ownerDocument)?void 0:r.body,c=to(o.scrollWidth,o.clientWidth,l?l.scrollWidth:0,l?l.clientWidth:0),h=to(o.scrollHeight,o.clientHeight,l?l.scrollHeight:0,l?l.clientHeight:0),d=-a.scrollLeft+tP(s),u=-a.scrollTop,"rtl"===tp(l||o).direction&&(d+=to(o.clientWidth,l?l.clientWidth:0)-c),{width:c,height:h,x:d,y:u}))}function tW(e){var t,i=e.reference,n=e.element,s=e.placement,r=s?tr(s):null,o=s?tT(s):null,a=i.x+i.width/2-n.width/2,l=i.y+i.height/2-n.height/2;switch(r){case"top":t={x:a,y:i.y-n.height};break;case ez:t={x:a,y:i.y+i.height};break;case eR:t={x:i.x+i.width,y:l};break;case eB:t={x:i.x-n.width,y:l};break;default:t={x:i.x,y:i.y}}var c=r?tv(r):null;if(null!=c){var h="y"===c?"height":"width";switch(o){case eK:t[c]=t[c]-(i[h]/2-n[h]/2);break;case"end":t[c]=t[c]+(i[h]/2-n[h]/2)}}return t}function tz(e,t){void 0===t&&(t={});var i,n,s,r,o,a,l,c,h=t,d=h.placement,u=void 0===d?e.placement:d,f=h.strategy,p=void 0===f?e.strategy:f,m=h.boundary,g=h.rootBoundary,_=h.elementContext,b=void 0===_?eY:_,v=h.altBoundary,y=h.padding,w=void 0===y?0:y,A=tw("number"!=typeof w?w:tA(w,eV)),E=b===eY?eU:eY,T=e.rects.popper,C=e.elements[void 0!==v&&v?E:b],O=(i=tt(C)?C:C.contextElement||tm(e.elements.popper),n=void 0===m?eQ:m,s=void 0===g?eX:g,l=(a=[].concat("clippingParents"===n?(r=tj(tg(i)),tt(o=["absolute","fixed"].indexOf(tp(i).position)>=0&&ti(i)?tb(i):i)?r.filter(function(e){return tt(e)&&tf(e,o)&&"body"!==e9(e)}):[]):[].concat(n),[s]))[0],(c=a.reduce(function(e,t){var n=tH(i,t,p);return e.top=to(n.top,e.top),e.right=ta(n.right,e.right),e.bottom=ta(n.bottom,e.bottom),e.left=to(n.left,e.left),e},tH(i,l,p))).width=c.right-c.left,c.height=c.bottom-c.top,c.x=c.left,c.y=c.top,c),x=td(e.elements.reference),k=tW({reference:x,element:T,placement:u}),L=tF(Object.assign({},T,k)),S=b===eY?L:x,D={top:O.top-S.top+A.top,bottom:S.bottom-O.bottom+A.bottom,left:O.left-S.left+A.left,right:S.right-O.right+A.right},$=e.modifiersData.offset;if(b===eY&&$){var I=$[u];Object.keys(D).forEach(function(e){var t=[eR,ez].indexOf(e)>=0?1:-1,i=["top",ez].indexOf(e)>=0?"y":"x";D[e]+=I[i]*t})}return D}let tR={name:"flip",enabled:!0,phase:"main",fn:function(e){var t=e.state,i=e.options,n=e.name;if(!t.modifiersData[n]._skip){for(var s=i.mainAxis,r=void 0===s||s,o=i.altAxis,a=void 0===o||o,l=i.fallbackPlacements,c=i.padding,h=i.boundary,d=i.rootBoundary,u=i.altBoundary,f=i.flipVariations,p=void 0===f||f,m=i.allowedAutoPlacements,g=t.options.placement,_=tr(g),b=l||(_!==g&&p?function(e){if(tr(e)===eq)return[];var t=tD(e);return[tI(e),t,tI(t)]}(g):[tD(g)]),v=[g].concat(b).reduce(function(e,i){var n,s,r,o,a,l,u,f,g,_,b,v;return e.concat(tr(i)===eq?(s=(n={placement:i,boundary:h,rootBoundary:d,padding:c,flipVariations:p,allowedAutoPlacements:m}).placement,r=n.boundary,o=n.rootBoundary,a=n.padding,l=n.flipVariations,f=void 0===(u=n.allowedAutoPlacements)?eJ:u,0===(b=(_=(g=tT(s))?l?eG:eG.filter(function(e){return tT(e)===g}):eV).filter(function(e){return f.indexOf(e)>=0})).length&&(b=_),Object.keys(v=b.reduce(function(e,i){return e[i]=tz(t,{placement:i,boundary:r,rootBoundary:o,padding:a})[tr(i)],e},{})).sort(function(e,t){return v[e]-v[t]})):i)},[]),y=t.rects.reference,w=t.rects.popper,A=new Map,E=!0,T=v[0],C=0;C<v.length;C++){var O=v[C],x=tr(O),k=tT(O)===eK,L=["top",ez].indexOf(x)>=0,S=L?"width":"height",D=tz(t,{placement:O,boundary:h,rootBoundary:d,altBoundary:u,padding:c}),$=L?k?eR:eB:k?ez:"top";y[S]>w[S]&&($=tD($));var I=tD($),N=[];if(r&&N.push(D[x]<=0),a&&N.push(D[$]<=0,D[I]<=0),N.every(function(e){return e})){T=O,E=!1;break}A.set(O,N)}if(E)for(var P=function(e){var t=v.find(function(t){var i=A.get(t);if(i)return i.slice(0,e).every(function(e){return e})});if(t)return T=t,"break"},M=p?3:1;M>0&&"break"!==P(M);M--);t.placement!==T&&(t.modifiersData[n]._skip=!0,t.placement=T,t.reset=!0)}},requiresIfExists:["offset"],data:{_skip:!1}};function tB(e,t,i){return void 0===i&&(i={x:0,y:0}),{top:e.top-t.height-i.y,right:e.right-t.width+i.x,bottom:e.bottom-t.height+i.y,left:e.left-t.width-i.x}}function tq(e){return["top",eR,ez,eB].some(function(t){return e[t]>=0})}let tV={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:function(e){var t=e.state,i=e.name,n=t.rects.reference,s=t.rects.popper,r=t.modifiersData.preventOverflow,o=tz(t,{elementContext:"reference"}),a=tz(t,{altBoundary:!0}),l=tB(o,n),c=tB(a,s,r),h=tq(l),d=tq(c);t.modifiersData[i]={referenceClippingOffsets:l,popperEscapeOffsets:c,isReferenceHidden:h,hasPopperEscaped:d},t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-reference-hidden":h,"data-popper-escaped":d})}},tK={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:function(e){var t=e.state,i=e.options,n=e.name,s=i.offset,r=void 0===s?[0,0]:s,o=eJ.reduce(function(e,i){var n,s,o,a,l,c;return e[i]=(n=t.rects,o=[eB,"top"].indexOf(s=tr(i))>=0?-1:1,l=(a="function"==typeof r?r(Object.assign({},n,{placement:i})):r)[0],c=a[1],l=l||0,c=(c||0)*o,[eB,eR].indexOf(s)>=0?{x:c,y:l}:{x:l,y:c}),e},{}),a=o[t.placement],l=a.x,c=a.y;null!=t.modifiersData.popperOffsets&&(t.modifiersData.popperOffsets.x+=l,t.modifiersData.popperOffsets.y+=c),t.modifiersData[n]=o}},tQ={name:"popperOffsets",enabled:!0,phase:"read",fn:function(e){var t=e.state,i=e.name;t.modifiersData[i]=tW({reference:t.rects.reference,element:t.rects.popper,placement:t.placement})},data:{}},tX={name:"preventOverflow",enabled:!0,phase:"main",fn:function(e){var t=e.state,i=e.options,n=e.name,s=i.mainAxis,r=i.altAxis,o=i.boundary,a=i.rootBoundary,l=i.altBoundary,c=i.padding,h=i.tether,d=void 0===h||h,u=i.tetherOffset,f=void 0===u?0:u,p=tz(t,{boundary:o,rootBoundary:a,padding:c,altBoundary:l}),m=tr(t.placement),g=tT(t.placement),_=!g,b=tv(m),v="x"===b?"y":"x",y=t.modifiersData.popperOffsets,w=t.rects.reference,A=t.rects.popper,E="function"==typeof f?f(Object.assign({},t.rects,{placement:t.placement})):f,T="number"==typeof E?{mainAxis:E,altAxis:E}:Object.assign({mainAxis:0,altAxis:0},E),C=t.modifiersData.offset?t.modifiersData.offset[t.placement]:null,O={x:0,y:0};if(y){if(void 0===s||s){var x,k="y"===b?"top":eB,L="y"===b?ez:eR,S="y"===b?"height":"width",D=y[b],$=D+p[k],I=D-p[L],N=d?-A[S]/2:0,P=g===eK?w[S]:A[S],M=g===eK?-A[S]:-w[S],j=t.elements.arrow,F=d&&j?tu(j):{width:0,height:0},H=t.modifiersData["arrow#persistent"]?t.modifiersData["arrow#persistent"].padding:{top:0,right:0,bottom:0,left:0},W=H[k],z=H[L],R=ty(0,w[S],F[S]),B=_?w[S]/2-N-R-W-T.mainAxis:P-R-W-T.mainAxis,q=_?-w[S]/2+N+R+z+T.mainAxis:M+R+z+T.mainAxis,V=t.elements.arrow&&tb(t.elements.arrow),K=V?"y"===b?V.clientTop||0:V.clientLeft||0:0,Q=null!=(x=null==C?void 0:C[b])?x:0,X=ty(d?ta($,D+B-Q-K):$,D,d?to(I,D+q-Q):I);y[b]=X,O[b]=X-D}if(void 0!==r&&r){var Y,U,G="x"===b?"top":eB,J="x"===b?ez:eR,Z=y[v],ee="y"===v?"height":"width",et=Z+p[G],ei=Z-p[J],en=-1!==["top",eB].indexOf(m),es=null!=(U=null==C?void 0:C[v])?U:0,er=en?et:Z-w[ee]-A[ee]-es+T.altAxis,eo=en?Z+w[ee]+A[ee]-es-T.altAxis:ei,ea=d&&en?(Y=ty(er,Z,eo))>eo?eo:Y:ty(d?er:et,Z,d?eo:ei);y[v]=ea,O[v]=ea-Z}t.modifiersData[n]=O}},requiresIfExists:["offset"]};var tY={placement:"bottom",modifiers:[],strategy:"absolute"};function tU(){for(var e=arguments.length,t=Array(e),i=0;i<e;i++)t[i]=arguments[i];return!t.some(function(e){return!(e&&"function"==typeof e.getBoundingClientRect)})}function tG(e){void 0===e&&(e={});var t=e,i=t.defaultModifiers,n=void 0===i?[]:i,s=t.defaultOptions,r=void 0===s?tY:s;return function(e,t,i){void 0===i&&(i=r);var s,o,a={placement:"bottom",orderedModifiers:[],options:Object.assign({},tY,r),modifiersData:{},elements:{reference:e,popper:t},attributes:{},styles:{}},l=[],c=!1,h={state:a,setOptions:function(i){var s="function"==typeof i?i(a.options):i;d(),a.options=Object.assign({},r,a.options,s),a.scrollParents={reference:tt(e)?tj(e):e.contextElement?tj(e.contextElement):[],popper:tj(t)};var o,c,u,f,p,m,g=(o=Object.keys(m=[].concat(n,a.options.modifiers).reduce(function(e,t){var i=e[t.name];return e[t.name]=i?Object.assign({},i,t,{options:Object.assign({},i.options,t.options),data:Object.assign({},i.data,t.data)}):t,e},{})).map(function(e){return m[e]}),c=new Map,u=new Set,f=[],o.forEach(function(e){c.set(e.name,e)}),o.forEach(function(e){u.has(e.name)||function e(t){u.add(t.name),[].concat(t.requires||[],t.requiresIfExists||[]).forEach(function(t){if(!u.has(t)){var i=c.get(t);i&&e(i)}}),f.push(t)}(e)}),p=f,e8.reduce(function(e,t){return e.concat(p.filter(function(e){return e.phase===t}))},[]));return a.orderedModifiers=g.filter(function(e){return e.enabled}),a.orderedModifiers.forEach(function(e){var t=e.name,i=e.options,n=e.effect;if("function"==typeof n){var s=n({state:a,name:t,instance:h,options:void 0===i?{}:i});l.push(s||function(){})}}),h.update()},forceUpdate:function(){if(!c){var e=a.elements,t=e.reference,i=e.popper;if(tU(t,i)){a.rects={reference:(n=tb(i),s="fixed"===a.options.strategy,d=ti(n),u=ti(n)&&(o=tl((r=n.getBoundingClientRect()).width)/n.offsetWidth||1,l=tl(r.height)/n.offsetHeight||1,1!==o||1!==l),f=tm(n),p=td(t,u,s),m={scrollLeft:0,scrollTop:0},g={x:0,y:0},(d||!d&&!s)&&(("body"!==e9(n)||tM(f))&&(m=n!==te(n)&&ti(n)?{scrollLeft:n.scrollLeft,scrollTop:n.scrollTop}:tN(n)),ti(n)?((g=td(n,!0)).x+=n.clientLeft,g.y+=n.clientTop):f&&(g.x=tP(f))),{x:p.left+m.scrollLeft-g.x,y:p.top+m.scrollTop-g.y,width:p.width,height:p.height}),popper:tu(i)},a.reset=!1,a.placement=a.options.placement,a.orderedModifiers.forEach(function(e){return a.modifiersData[e.name]=Object.assign({},e.data)});for(var n,s,r,o,l,d,u,f,p,m,g,_=0;_<a.orderedModifiers.length;_++)if(!0!==a.reset){var b=a.orderedModifiers[_],v=b.fn,y=b.options,w=void 0===y?{}:y,A=b.name;"function"==typeof v&&(a=v({state:a,options:w,name:A,instance:h})||a)}else a.reset=!1,_=-1}}},update:(s=function(){return new Promise(function(e){h.forceUpdate(),e(a)})},function(){return o||(o=new Promise(function(e){Promise.resolve().then(function(){o=void 0,e(s())})})),o}),destroy:function(){d(),c=!0}};if(!tU(e,t))return h;function d(){l.forEach(function(e){return e()}),l=[]}return h.setOptions(i).then(function(e){!c&&i.onFirstUpdate&&i.onFirstUpdate(e)}),h}}var tJ=tG(),tZ=tG({defaultModifiers:[tL,tQ,tx,ts]}),t0=tG({defaultModifiers:[tL,tQ,tx,ts,tK,tR,tX,tE,tV]});let t1=Object.freeze(Object.defineProperty({__proto__:null,afterMain:e5,afterRead:e1,afterWrite:e4,applyStyles:ts,arrow:tE,auto:eq,basePlacements:eV,beforeMain:e2,beforeRead:eZ,beforeWrite:e7,bottom:ez,clippingParents:eQ,computeStyles:tx,createPopper:t0,createPopperBase:tJ,createPopperLite:tZ,detectOverflow:tz,end:"end",eventListeners:tL,flip:tR,hide:tV,left:eB,main:e3,modifierPhases:e8,offset:tK,placements:eJ,popper:eY,popperGenerator:tG,popperOffsets:tQ,preventOverflow:tX,read:e0,reference:eU,right:eR,start:eK,top:"top",variationPlacements:eG,viewport:eX,write:e6},Symbol.toStringTag,{value:"Module"})),t2="dropdown",t3=".bs.dropdown",t5=".data-api",t7="ArrowDown",t6=`hide${t3}`,t4=`hidden${t3}`,t8=`show${t3}`,t9=`shown${t3}`,ie=`click${t3}${t5}`,it=`keydown${t3}${t5}`,ii=`keyup${t3}${t5}`,is="show",ir='[data-bs-toggle="dropdown"]:not(.disabled):not(:disabled)',io=`${ir}.${is}`,ia=".dropdown-menu",il=p()?"top-end":"top-start",ic=p()?"top-start":"top-end",ih=p()?"bottom-end":"bottom-start",id=p()?"bottom-start":"bottom-end",iu=p()?"left-start":"right-start",ip=p()?"right-start":"left-start",im={autoClose:!0,boundary:"clippingParents",display:"dynamic",offset:[0,2],popperConfig:null,reference:"toggle"},ig={autoClose:"(boolean|string)",boundary:"(string|element)",display:"string",offset:"(array|string|function)",popperConfig:"(null|object|function)",reference:"(string|element|object)"};class i_ extends H{constructor(e,t){super(e,t),this._popper=null,this._parent=this._element.parentNode,this._menu=z.next(this._element,ia)[0]||z.prev(this._element,ia)[0]||z.findOne(ia,this._parent),this._inNavbar=this._detectNavbar()}static get Default(){return im}static get DefaultType(){return ig}static get NAME(){return t2}toggle(){return this._isShown()?this.hide():this.show()}show(){if(l(this._element)||this._isShown())return;let e={relatedTarget:this._element};if(!I.trigger(this._element,t8,e).defaultPrevented){if(this._createPopper(),"ontouchstart"in document.documentElement&&!this._parent.closest(".navbar-nav"))for(let e of[].concat(...document.body.children))I.on(e,"mouseover",h);this._element.focus(),this._element.setAttribute("aria-expanded",!0),this._menu.classList.add(is),this._element.classList.add(is),I.trigger(this._element,t9,e)}}hide(){if(l(this._element)||!this._isShown())return;let e={relatedTarget:this._element};this._completeHide(e)}dispose(){this._popper&&this._popper.destroy(),super.dispose()}update(){this._inNavbar=this._detectNavbar(),this._popper&&this._popper.update()}_completeHide(e){if(!I.trigger(this._element,t6,e).defaultPrevented){if("ontouchstart"in document.documentElement)for(let e of[].concat(...document.body.children))I.off(e,"mouseover",h);this._popper&&this._popper.destroy(),this._menu.classList.remove(is),this._element.classList.remove(is),this._element.setAttribute("aria-expanded","false"),j.removeDataAttribute(this._menu,"popper"),I.trigger(this._element,t4,e),this._element.focus()}}_getConfig(e){if("object"==typeof(e=super._getConfig(e)).reference&&!r(e.reference)&&"function"!=typeof e.reference.getBoundingClientRect)throw TypeError(`${t2.toUpperCase()}: Option "reference" provided type "object" without a required "getBoundingClientRect" method.`);return e}_createPopper(){if(void 0===t1)throw TypeError("Bootstrap's dropdowns require Popper (https://popper.js.org/docs/v2/)");let e=this._element;"parent"===this._config.reference?e=this._parent:r(this._config.reference)?e=o(this._config.reference):"object"==typeof this._config.reference&&(e=this._config.reference);let t=this._getPopperConfig();this._popper=t0(e,this._menu,t)}_isShown(){return this._menu.classList.contains(is)}_getPlacement(){let e=this._parent;if(e.classList.contains("dropend"))return iu;if(e.classList.contains("dropstart"))return ip;if(e.classList.contains("dropup-center"))return"top";if(e.classList.contains("dropdown-center"))return"bottom";let t="end"===getComputedStyle(this._menu).getPropertyValue("--bs-position").trim();return e.classList.contains("dropup")?t?ic:il:t?id:ih}_detectNavbar(){return null!==this._element.closest(".navbar")}_getOffset(){let{offset:e}=this._config;return"string"==typeof e?e.split(",").map(e=>Number.parseInt(e,10)):"function"==typeof e?t=>e(t,this._element):e}_getPopperConfig(){let e={placement:this._getPlacement(),modifiers:[{name:"preventOverflow",options:{boundary:this._config.boundary}},{name:"offset",options:{offset:this._getOffset()}}]};return(this._inNavbar||"static"===this._config.display)&&(j.setDataAttribute(this._menu,"popper","static"),e.modifiers=[{name:"applyStyles",enabled:!1}]),{...e,...g(this._config.popperConfig,[void 0,e])}}_selectMenuItem({key:e,target:t}){let i=z.find(".dropdown-menu .dropdown-item:not(.disabled):not(:disabled)",this._menu).filter(e=>a(e));i.length&&b(i,t,e===t7,!i.includes(t)).focus()}static jQueryInterface(e){return this.each(function(){let t=i_.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===t[e])throw TypeError(`No method named "${e}"`);t[e]()}})}static clearMenus(e){if(2!==e.button&&("keyup"!==e.type||"Tab"===e.key))for(let t of z.find(io)){let i=i_.getInstance(t);if(!i||!1===i._config.autoClose)continue;let n=e.composedPath(),s=n.includes(i._menu);if(n.includes(i._element)||"inside"===i._config.autoClose&&!s||"outside"===i._config.autoClose&&s||i._menu.contains(e.target)&&("keyup"===e.type&&"Tab"===e.key||/input|select|option|textarea|form/i.test(e.target.tagName)))continue;let r={relatedTarget:i._element};"click"===e.type&&(r.clickEvent=e),i._completeHide(r)}}static dataApiKeydownHandler(e){let t=/input|textarea/i.test(e.target.tagName),i="Escape"===e.key,n=["ArrowUp",t7].includes(e.key);if(!n&&!i||t&&!i)return;e.preventDefault();let s=this.matches(ir)?this:z.prev(this,ir)[0]||z.next(this,ir)[0]||z.findOne(ir,e.delegateTarget.parentNode),r=i_.getOrCreateInstance(s);if(n)return e.stopPropagation(),r.show(),void r._selectMenuItem(e);r._isShown()&&(e.stopPropagation(),r.hide(),s.focus())}}I.on(document,it,ir,i_.dataApiKeydownHandler),I.on(document,it,ia,i_.dataApiKeydownHandler),I.on(document,ie,i_.clearMenus),I.on(document,ii,i_.clearMenus),I.on(document,ie,ir,function(e){e.preventDefault(),i_.getOrCreateInstance(this).toggle()}),m(i_);let ib="backdrop",iv="show",iy=`mousedown.bs.${ib}`,iw={className:"modal-backdrop",clickCallback:null,isAnimated:!1,isVisible:!0,rootElement:"body"},iA={className:"string",clickCallback:"(function|null)",isAnimated:"boolean",isVisible:"boolean",rootElement:"(element|string)"};class iE extends F{constructor(e){super(),this._config=this._getConfig(e),this._isAppended=!1,this._element=null}static get Default(){return iw}static get DefaultType(){return iA}static get NAME(){return ib}show(e){if(!this._config.isVisible)return void g(e);this._append();let t=this._getElement();this._config.isAnimated&&d(t),t.classList.add(iv),this._emulateAnimation(()=>{g(e)})}hide(e){this._config.isVisible?(this._getElement().classList.remove(iv),this._emulateAnimation(()=>{this.dispose(),g(e)})):g(e)}dispose(){this._isAppended&&(I.off(this._element,iy),this._element.remove(),this._isAppended=!1)}_getElement(){if(!this._element){let e=document.createElement("div");e.className=this._config.className,this._config.isAnimated&&e.classList.add("fade"),this._element=e}return this._element}_configAfterMerge(e){return e.rootElement=o(e.rootElement),e}_append(){if(this._isAppended)return;let e=this._getElement();this._config.rootElement.append(e),I.on(e,iy,()=>{g(this._config.clickCallback)}),this._isAppended=!0}_emulateAnimation(e){_(e,this._getElement(),this._config.isAnimated)}}let iT=".bs.focustrap",iC=`focusin${iT}`,iO=`keydown.tab${iT}`,ix="backward",ik={autofocus:!0,trapElement:null},iL={autofocus:"boolean",trapElement:"element"};class iS extends F{constructor(e){super(),this._config=this._getConfig(e),this._isActive=!1,this._lastTabNavDirection=null}static get Default(){return ik}static get DefaultType(){return iL}static get NAME(){return"focustrap"}activate(){this._isActive||(this._config.autofocus&&this._config.trapElement.focus(),I.off(document,iT),I.on(document,iC,e=>this._handleFocusin(e)),I.on(document,iO,e=>this._handleKeydown(e)),this._isActive=!0)}deactivate(){this._isActive&&(this._isActive=!1,I.off(document,iT))}_handleFocusin(e){let{trapElement:t}=this._config;if(e.target===document||e.target===t||t.contains(e.target))return;let i=z.focusableChildren(t);0===i.length?t.focus():this._lastTabNavDirection===ix?i[i.length-1].focus():i[0].focus()}_handleKeydown(e){"Tab"===e.key&&(this._lastTabNavDirection=e.shiftKey?ix:"forward")}}let iD=".fixed-top, .fixed-bottom, .is-fixed, .sticky-top",i$=".sticky-top",iI="padding-right",iN="margin-right";class iP{constructor(){this._element=document.body}getWidth(){let e=document.documentElement.clientWidth;return Math.abs(window.innerWidth-e)}hide(){let e=this.getWidth();this._disableOverFlow(),this._setElementAttributes(this._element,iI,t=>t+e),this._setElementAttributes(iD,iI,t=>t+e),this._setElementAttributes(i$,iN,t=>t-e)}reset(){this._resetElementAttributes(this._element,"overflow"),this._resetElementAttributes(this._element,iI),this._resetElementAttributes(iD,iI),this._resetElementAttributes(i$,iN)}isOverflowing(){return this.getWidth()>0}_disableOverFlow(){this._saveInitialAttribute(this._element,"overflow"),this._element.style.overflow="hidden"}_setElementAttributes(e,t,i){let n=this.getWidth();this._applyManipulationCallback(e,e=>{if(e!==this._element&&window.innerWidth>e.clientWidth+n)return;this._saveInitialAttribute(e,t);let s=window.getComputedStyle(e).getPropertyValue(t);e.style.setProperty(t,`${i(Number.parseFloat(s))}px`)})}_saveInitialAttribute(e,t){let i=e.style.getPropertyValue(t);i&&j.setDataAttribute(e,t,i)}_resetElementAttributes(e,t){this._applyManipulationCallback(e,e=>{let i=j.getDataAttribute(e,t);null!==i?(j.removeDataAttribute(e,t),e.style.setProperty(t,i)):e.style.removeProperty(t)})}_applyManipulationCallback(e,t){if(r(e))t(e);else for(let i of z.find(e,this._element))t(i)}}let iM=".bs.modal",ij=`hide${iM}`,iF=`hidePrevented${iM}`,iH=`hidden${iM}`,iW=`show${iM}`,iz=`shown${iM}`,iR=`resize${iM}`,iB=`click.dismiss${iM}`,iq=`mousedown.dismiss${iM}`,iV=`keydown.dismiss${iM}`,iK=`click${iM}.data-api`,iQ="modal-open",iX="show",iY="modal-static",iU={backdrop:!0,focus:!0,keyboard:!0},iG={backdrop:"(boolean|string)",focus:"boolean",keyboard:"boolean"};class iJ extends H{constructor(e,t){super(e,t),this._dialog=z.findOne(".modal-dialog",this._element),this._backdrop=this._initializeBackDrop(),this._focustrap=this._initializeFocusTrap(),this._isShown=!1,this._isTransitioning=!1,this._scrollBar=new iP,this._addEventListeners()}static get Default(){return iU}static get DefaultType(){return iG}static get NAME(){return"modal"}toggle(e){return this._isShown?this.hide():this.show(e)}show(e){this._isShown||this._isTransitioning||I.trigger(this._element,iW,{relatedTarget:e}).defaultPrevented||(this._isShown=!0,this._isTransitioning=!0,this._scrollBar.hide(),document.body.classList.add(iQ),this._adjustDialog(),this._backdrop.show(()=>this._showElement(e)))}hide(){this._isShown&&!this._isTransitioning&&(I.trigger(this._element,ij).defaultPrevented||(this._isShown=!1,this._isTransitioning=!0,this._focustrap.deactivate(),this._element.classList.remove(iX),this._queueCallback(()=>this._hideModal(),this._element,this._isAnimated())))}dispose(){I.off(window,iM),I.off(this._dialog,iM),this._backdrop.dispose(),this._focustrap.deactivate(),super.dispose()}handleUpdate(){this._adjustDialog()}_initializeBackDrop(){return new iE({isVisible:!!this._config.backdrop,isAnimated:this._isAnimated()})}_initializeFocusTrap(){return new iS({trapElement:this._element})}_showElement(e){document.body.contains(this._element)||document.body.append(this._element),this._element.style.display="block",this._element.removeAttribute("aria-hidden"),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),this._element.scrollTop=0;let t=z.findOne(".modal-body",this._dialog);t&&(t.scrollTop=0),d(this._element),this._element.classList.add(iX),this._queueCallback(()=>{this._config.focus&&this._focustrap.activate(),this._isTransitioning=!1,I.trigger(this._element,iz,{relatedTarget:e})},this._dialog,this._isAnimated())}_addEventListeners(){I.on(this._element,iV,e=>{"Escape"===e.key&&(this._config.keyboard?this.hide():this._triggerBackdropTransition())}),I.on(window,iR,()=>{this._isShown&&!this._isTransitioning&&this._adjustDialog()}),I.on(this._element,iq,e=>{I.one(this._element,iB,t=>{this._element===e.target&&this._element===t.target&&("static"!==this._config.backdrop?this._config.backdrop&&this.hide():this._triggerBackdropTransition())})})}_hideModal(){this._element.style.display="none",this._element.setAttribute("aria-hidden",!0),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._isTransitioning=!1,this._backdrop.hide(()=>{document.body.classList.remove(iQ),this._resetAdjustments(),this._scrollBar.reset(),I.trigger(this._element,iH)})}_isAnimated(){return this._element.classList.contains("fade")}_triggerBackdropTransition(){if(I.trigger(this._element,iF).defaultPrevented)return;let e=this._element.scrollHeight>document.documentElement.clientHeight,t=this._element.style.overflowY;"hidden"===t||this._element.classList.contains(iY)||(e||(this._element.style.overflowY="hidden"),this._element.classList.add(iY),this._queueCallback(()=>{this._element.classList.remove(iY),this._queueCallback(()=>{this._element.style.overflowY=t},this._dialog)},this._dialog),this._element.focus())}_adjustDialog(){let e=this._element.scrollHeight>document.documentElement.clientHeight,t=this._scrollBar.getWidth(),i=t>0;if(i&&!e){let e=p()?"paddingLeft":"paddingRight";this._element.style[e]=`${t}px`}if(!i&&e){let e=p()?"paddingRight":"paddingLeft";this._element.style[e]=`${t}px`}}_resetAdjustments(){this._element.style.paddingLeft="",this._element.style.paddingRight=""}static jQueryInterface(e,t){return this.each(function(){let i=iJ.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===i[e])throw TypeError(`No method named "${e}"`);i[e](t)}})}}I.on(document,iK,'[data-bs-toggle="modal"]',function(e){let t=z.getElementFromSelector(this);["A","AREA"].includes(this.tagName)&&e.preventDefault(),I.one(t,iW,e=>{e.defaultPrevented||I.one(t,iH,()=>{a(this)&&this.focus()})});let i=z.findOne(".modal.show");i&&iJ.getInstance(i).hide(),iJ.getOrCreateInstance(t).toggle(this)}),R(iJ),m(iJ);let iZ=".bs.offcanvas",i0=".data-api",i1=`load${iZ}${i0}`,i2="show",i3="showing",i5="hiding",i7=".offcanvas.show",i6=`show${iZ}`,i4=`shown${iZ}`,i8=`hide${iZ}`,i9=`hidePrevented${iZ}`,ne=`hidden${iZ}`,nt=`resize${iZ}`,ni=`click${iZ}${i0}`,nn=`keydown.dismiss${iZ}`,ns={backdrop:!0,keyboard:!0,scroll:!1},nr={backdrop:"(boolean|string)",keyboard:"boolean",scroll:"boolean"};class no extends H{constructor(e,t){super(e,t),this._isShown=!1,this._backdrop=this._initializeBackDrop(),this._focustrap=this._initializeFocusTrap(),this._addEventListeners()}static get Default(){return ns}static get DefaultType(){return nr}static get NAME(){return"offcanvas"}toggle(e){return this._isShown?this.hide():this.show(e)}show(e){this._isShown||I.trigger(this._element,i6,{relatedTarget:e}).defaultPrevented||(this._isShown=!0,this._backdrop.show(),this._config.scroll||(new iP).hide(),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),this._element.classList.add(i3),this._queueCallback(()=>{this._config.scroll&&!this._config.backdrop||this._focustrap.activate(),this._element.classList.add(i2),this._element.classList.remove(i3),I.trigger(this._element,i4,{relatedTarget:e})},this._element,!0))}hide(){this._isShown&&(I.trigger(this._element,i8).defaultPrevented||(this._focustrap.deactivate(),this._element.blur(),this._isShown=!1,this._element.classList.add(i5),this._backdrop.hide(),this._queueCallback(()=>{this._element.classList.remove(i2,i5),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._config.scroll||(new iP).reset(),I.trigger(this._element,ne)},this._element,!0)))}dispose(){this._backdrop.dispose(),this._focustrap.deactivate(),super.dispose()}_initializeBackDrop(){let e=!!this._config.backdrop;return new iE({className:"offcanvas-backdrop",isVisible:e,isAnimated:!0,rootElement:this._element.parentNode,clickCallback:e?()=>{"static"!==this._config.backdrop?this.hide():I.trigger(this._element,i9)}:null})}_initializeFocusTrap(){return new iS({trapElement:this._element})}_addEventListeners(){I.on(this._element,nn,e=>{"Escape"===e.key&&(this._config.keyboard?this.hide():I.trigger(this._element,i9))})}static jQueryInterface(e){return this.each(function(){let t=no.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===t[e]||e.startsWith("_")||"constructor"===e)throw TypeError(`No method named "${e}"`);t[e](this)}})}}I.on(document,ni,'[data-bs-toggle="offcanvas"]',function(e){let t=z.getElementFromSelector(this);if(["A","AREA"].includes(this.tagName)&&e.preventDefault(),l(this))return;I.one(t,ne,()=>{a(this)&&this.focus()});let i=z.findOne(i7);i&&i!==t&&no.getInstance(i).hide(),no.getOrCreateInstance(t).toggle(this)}),I.on(window,i1,()=>{for(let e of z.find(i7))no.getOrCreateInstance(e).show()}),I.on(window,nt,()=>{for(let e of z.find("[aria-modal][class*=show][class*=offcanvas-]"))"fixed"!==getComputedStyle(e).position&&no.getOrCreateInstance(e).hide()}),R(no),m(no);let na={"*":["class","dir","id","lang","role",/^aria-[\w-]*$/i],a:["target","href","title","rel"],area:[],b:[],br:[],col:[],code:[],dd:[],div:[],dl:[],dt:[],em:[],hr:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],i:[],img:["src","srcset","alt","title","width","height"],li:[],ol:[],p:[],pre:[],s:[],small:[],span:[],sub:[],sup:[],strong:[],u:[],ul:[]},nl=new Set(["background","cite","href","itemtype","longdesc","poster","src","xlink:href"]),nc=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:/?#]*(?:[/?#]|$))/i,nh=(e,t)=>{let i=e.nodeName.toLowerCase();return t.includes(i)?!nl.has(i)||!!nc.test(e.nodeValue):t.filter(e=>e instanceof RegExp).some(e=>e.test(i))},nd={allowList:na,content:{},extraClass:"",html:!1,sanitize:!0,sanitizeFn:null,template:"<div></div>"},nu={allowList:"object",content:"object",extraClass:"(string|function)",html:"boolean",sanitize:"boolean",sanitizeFn:"(null|function)",template:"string"},nf={entry:"(string|element|function|null)",selector:"(string|element)"};class np extends F{constructor(e){super(),this._config=this._getConfig(e)}static get Default(){return nd}static get DefaultType(){return nu}static get NAME(){return"TemplateFactory"}getContent(){return Object.values(this._config.content).map(e=>this._resolvePossibleFunction(e)).filter(Boolean)}hasContent(){return this.getContent().length>0}changeContent(e){return this._checkContent(e),this._config.content={...this._config.content,...e},this}toHtml(){let e=document.createElement("div");for(let[t,i]of(e.innerHTML=this._maybeSanitize(this._config.template),Object.entries(this._config.content)))this._setContent(e,i,t);let t=e.children[0],i=this._resolvePossibleFunction(this._config.extraClass);return i&&t.classList.add(...i.split(" ")),t}_typeCheckConfig(e){super._typeCheckConfig(e),this._checkContent(e.content)}_checkContent(e){for(let[t,i]of Object.entries(e))super._typeCheckConfig({selector:t,entry:i},nf)}_setContent(e,t,i){let n=z.findOne(i,e);n&&((t=this._resolvePossibleFunction(t))?r(t)?this._putElementInTemplate(o(t),n):this._config.html?n.innerHTML=this._maybeSanitize(t):n.textContent=t:n.remove())}_maybeSanitize(e){return this._config.sanitize?function(e,t,i){if(!e.length)return e;if(i&&"function"==typeof i)return i(e);let n=(new window.DOMParser).parseFromString(e,"text/html");for(let e of[].concat(...n.body.querySelectorAll("*"))){let i=e.nodeName.toLowerCase();if(!Object.keys(t).includes(i)){e.remove();continue}let n=[].concat(...e.attributes),s=[].concat(t["*"]||[],t[i]||[]);for(let t of n)nh(t,s)||e.removeAttribute(t.nodeName)}return n.body.innerHTML}(e,this._config.allowList,this._config.sanitizeFn):e}_resolvePossibleFunction(e){return g(e,[void 0,this])}_putElementInTemplate(e,t){if(this._config.html)return t.innerHTML="",void t.append(e);t.textContent=e.textContent}}let nm=new Set(["sanitize","allowList","sanitizeFn"]),ng="fade",n_="show",nb=".modal",nv="hide.bs.modal",ny="hover",nw="focus",nA="click",nE={AUTO:"auto",TOP:"top",RIGHT:p()?"left":"right",BOTTOM:"bottom",LEFT:p()?"right":"left"},nT={allowList:na,animation:!0,boundary:"clippingParents",container:!1,customClass:"",delay:0,fallbackPlacements:["top","right","bottom","left"],html:!1,offset:[0,6],placement:"top",popperConfig:null,sanitize:!0,sanitizeFn:null,selector:!1,template:'<div class="tooltip" role="tooltip"><div class="tooltip-arrow"></div><div class="tooltip-inner"></div></div>',title:"",trigger:"hover focus"},nC={allowList:"object",animation:"boolean",boundary:"(string|element)",container:"(string|element|boolean)",customClass:"(string|function)",delay:"(number|object)",fallbackPlacements:"array",html:"boolean",offset:"(array|string|function)",placement:"(string|function)",popperConfig:"(null|object|function)",sanitize:"boolean",sanitizeFn:"(null|function)",selector:"(string|boolean)",template:"string",title:"(string|element|function)",trigger:"string"};class nO extends H{constructor(e,t){if(void 0===t1)throw TypeError("Bootstrap's tooltips require Popper (https://popper.js.org/docs/v2/)");super(e,t),this._isEnabled=!0,this._timeout=0,this._isHovered=null,this._activeTrigger={},this._popper=null,this._templateFactory=null,this._newContent=null,this.tip=null,this._setListeners(),this._config.selector||this._fixTitle()}static get Default(){return nT}static get DefaultType(){return nC}static get NAME(){return"tooltip"}enable(){this._isEnabled=!0}disable(){this._isEnabled=!1}toggleEnabled(){this._isEnabled=!this._isEnabled}toggle(){this._isEnabled&&(this._isShown()?this._leave():this._enter())}dispose(){clearTimeout(this._timeout),I.off(this._element.closest(nb),nv,this._hideModalHandler),this._element.getAttribute("data-bs-original-title")&&this._element.setAttribute("title",this._element.getAttribute("data-bs-original-title")),this._disposePopper(),super.dispose()}show(){if("none"===this._element.style.display)throw Error("Please use show on visible elements");if(!this._isWithContent()||!this._isEnabled)return;let e=I.trigger(this._element,this.constructor.eventName("show")),t=(c(this._element)||this._element.ownerDocument.documentElement).contains(this._element);if(e.defaultPrevented||!t)return;this._disposePopper();let i=this._getTipElement();this._element.setAttribute("aria-describedby",i.getAttribute("id"));let{container:n}=this._config;if(this._element.ownerDocument.documentElement.contains(this.tip)||(n.append(i),I.trigger(this._element,this.constructor.eventName("inserted"))),this._popper=this._createPopper(i),i.classList.add(n_),"ontouchstart"in document.documentElement)for(let e of[].concat(...document.body.children))I.on(e,"mouseover",h);this._queueCallback(()=>{I.trigger(this._element,this.constructor.eventName("shown")),!1===this._isHovered&&this._leave(),this._isHovered=!1},this.tip,this._isAnimated())}hide(){if(this._isShown()&&!I.trigger(this._element,this.constructor.eventName("hide")).defaultPrevented){if(this._getTipElement().classList.remove(n_),"ontouchstart"in document.documentElement)for(let e of[].concat(...document.body.children))I.off(e,"mouseover",h);this._activeTrigger[nA]=!1,this._activeTrigger[nw]=!1,this._activeTrigger[ny]=!1,this._isHovered=null,this._queueCallback(()=>{this._isWithActiveTrigger()||(this._isHovered||this._disposePopper(),this._element.removeAttribute("aria-describedby"),I.trigger(this._element,this.constructor.eventName("hidden")))},this.tip,this._isAnimated())}}update(){this._popper&&this._popper.update()}_isWithContent(){return!!this._getTitle()}_getTipElement(){return this.tip||(this.tip=this._createTipElement(this._newContent||this._getContentForTemplate())),this.tip}_createTipElement(e){let t=this._getTemplateFactory(e).toHtml();if(!t)return null;t.classList.remove(ng,n_),t.classList.add(`bs-${this.constructor.NAME}-auto`);let i=(e=>{do e+=Math.floor(1e6*Math.random());while(document.getElementById(e));return e})(this.constructor.NAME).toString();return t.setAttribute("id",i),this._isAnimated()&&t.classList.add(ng),t}setContent(e){this._newContent=e,this._isShown()&&(this._disposePopper(),this.show())}_getTemplateFactory(e){return this._templateFactory?this._templateFactory.changeContent(e):this._templateFactory=new np({...this._config,content:e,extraClass:this._resolvePossibleFunction(this._config.customClass)}),this._templateFactory}_getContentForTemplate(){return{".tooltip-inner":this._getTitle()}}_getTitle(){return this._resolvePossibleFunction(this._config.title)||this._element.getAttribute("data-bs-original-title")}_initializeOnDelegatedTarget(e){return this.constructor.getOrCreateInstance(e.delegateTarget,this._getDelegateConfig())}_isAnimated(){return this._config.animation||this.tip&&this.tip.classList.contains(ng)}_isShown(){return this.tip&&this.tip.classList.contains(n_)}_createPopper(e){let t=nE[g(this._config.placement,[this,e,this._element]).toUpperCase()];return t0(this._element,e,this._getPopperConfig(t))}_getOffset(){let{offset:e}=this._config;return"string"==typeof e?e.split(",").map(e=>Number.parseInt(e,10)):"function"==typeof e?t=>e(t,this._element):e}_resolvePossibleFunction(e){return g(e,[this._element,this._element])}_getPopperConfig(e){let t={placement:e,modifiers:[{name:"flip",options:{fallbackPlacements:this._config.fallbackPlacements}},{name:"offset",options:{offset:this._getOffset()}},{name:"preventOverflow",options:{boundary:this._config.boundary}},{name:"arrow",options:{element:`.${this.constructor.NAME}-arrow`}},{name:"preSetPlacement",enabled:!0,phase:"beforeMain",fn:e=>{this._getTipElement().setAttribute("data-popper-placement",e.state.placement)}}]};return{...t,...g(this._config.popperConfig,[void 0,t])}}_setListeners(){for(let e of this._config.trigger.split(" "))if("click"===e)I.on(this._element,this.constructor.eventName("click"),this._config.selector,e=>{let t=this._initializeOnDelegatedTarget(e);t._activeTrigger[nA]=!(t._isShown()&&t._activeTrigger[nA]),t.toggle()});else if("manual"!==e){let t=e===ny?this.constructor.eventName("mouseenter"):this.constructor.eventName("focusin"),i=e===ny?this.constructor.eventName("mouseleave"):this.constructor.eventName("focusout");I.on(this._element,t,this._config.selector,e=>{let t=this._initializeOnDelegatedTarget(e);t._activeTrigger["focusin"===e.type?nw:ny]=!0,t._enter()}),I.on(this._element,i,this._config.selector,e=>{let t=this._initializeOnDelegatedTarget(e);t._activeTrigger["focusout"===e.type?nw:ny]=t._element.contains(e.relatedTarget),t._leave()})}this._hideModalHandler=()=>{this._element&&this.hide()},I.on(this._element.closest(nb),nv,this._hideModalHandler)}_fixTitle(){let e=this._element.getAttribute("title");e&&(this._element.getAttribute("aria-label")||this._element.textContent.trim()||this._element.setAttribute("aria-label",e),this._element.setAttribute("data-bs-original-title",e),this._element.removeAttribute("title"))}_enter(){this._isShown()||this._isHovered?this._isHovered=!0:(this._isHovered=!0,this._setTimeout(()=>{this._isHovered&&this.show()},this._config.delay.show))}_leave(){this._isWithActiveTrigger()||(this._isHovered=!1,this._setTimeout(()=>{this._isHovered||this.hide()},this._config.delay.hide))}_setTimeout(e,t){clearTimeout(this._timeout),this._timeout=setTimeout(e,t)}_isWithActiveTrigger(){return Object.values(this._activeTrigger).includes(!0)}_getConfig(e){let t=j.getDataAttributes(this._element);for(let e of Object.keys(t))nm.has(e)&&delete t[e];return e={...t,..."object"==typeof e&&e?e:{}},e=this._mergeConfigObj(e),e=this._configAfterMerge(e),this._typeCheckConfig(e),e}_configAfterMerge(e){return e.container=!1===e.container?document.body:o(e.container),"number"==typeof e.delay&&(e.delay={show:e.delay,hide:e.delay}),"number"==typeof e.title&&(e.title=e.title.toString()),"number"==typeof e.content&&(e.content=e.content.toString()),e}_getDelegateConfig(){let e={};for(let[t,i]of Object.entries(this._config))this.constructor.Default[t]!==i&&(e[t]=i);return e.selector=!1,e.trigger="manual",e}_disposePopper(){this._popper&&(this._popper.destroy(),this._popper=null),this.tip&&(this.tip.remove(),this.tip=null)}static jQueryInterface(e){return this.each(function(){let t=nO.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===t[e])throw TypeError(`No method named "${e}"`);t[e]()}})}}m(nO);let nx={...nO.Default,content:"",offset:[0,8],placement:"right",template:'<div class="popover" role="tooltip"><div class="popover-arrow"></div><h3 class="popover-header"></h3><div class="popover-body"></div></div>',trigger:"click"},nk={...nO.DefaultType,content:"(null|string|element|function)"};class nL extends nO{static get Default(){return nx}static get DefaultType(){return nk}static get NAME(){return"popover"}_isWithContent(){return this._getTitle()||this._getContent()}_getContentForTemplate(){return{".popover-header":this._getTitle(),".popover-body":this._getContent()}}_getContent(){return this._resolvePossibleFunction(this._config.content)}static jQueryInterface(e){return this.each(function(){let t=nL.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===t[e])throw TypeError(`No method named "${e}"`);t[e]()}})}}m(nL);let nS=".bs.scrollspy",nD=`activate${nS}`,n$=`click${nS}`,nI=`load${nS}.data-api`,nN="active",nP="[href]",nM=".nav-link",nj=`${nM}, .nav-item > ${nM}, .list-group-item`,nF={offset:null,rootMargin:"0px 0px -25%",smoothScroll:!1,target:null,threshold:[.1,.5,1]},nH={offset:"(number|null)",rootMargin:"string",smoothScroll:"boolean",target:"element",threshold:"array"};class nW extends H{constructor(e,t){super(e,t),this._targetLinks=new Map,this._observableSections=new Map,this._rootElement="visible"===getComputedStyle(this._element).overflowY?null:this._element,this._activeTarget=null,this._observer=null,this._previousScrollData={visibleEntryTop:0,parentScrollTop:0},this.refresh()}static get Default(){return nF}static get DefaultType(){return nH}static get NAME(){return"scrollspy"}refresh(){for(let e of(this._initializeTargetsAndObservables(),this._maybeEnableSmoothScroll(),this._observer?this._observer.disconnect():this._observer=this._getNewObserver(),this._observableSections.values()))this._observer.observe(e)}dispose(){this._observer.disconnect(),super.dispose()}_configAfterMerge(e){return e.target=o(e.target)||document.body,e.rootMargin=e.offset?`${e.offset}px 0px -30%`:e.rootMargin,"string"==typeof e.threshold&&(e.threshold=e.threshold.split(",").map(e=>Number.parseFloat(e))),e}_maybeEnableSmoothScroll(){this._config.smoothScroll&&(I.off(this._config.target,n$),I.on(this._config.target,n$,nP,e=>{let t=this._observableSections.get(e.target.hash);if(t){e.preventDefault();let i=this._rootElement||window,n=t.offsetTop-this._element.offsetTop;if(i.scrollTo)return void i.scrollTo({top:n,behavior:"smooth"});i.scrollTop=n}}))}_getNewObserver(){return new IntersectionObserver(e=>this._observerCallback(e),{root:this._rootElement,threshold:this._config.threshold,rootMargin:this._config.rootMargin})}_observerCallback(e){let t=e=>this._targetLinks.get(`#${e.target.id}`),i=e=>{this._previousScrollData.visibleEntryTop=e.target.offsetTop,this._process(t(e))},n=(this._rootElement||document.documentElement).scrollTop,s=n>=this._previousScrollData.parentScrollTop;for(let r of(this._previousScrollData.parentScrollTop=n,e)){if(!r.isIntersecting){this._activeTarget=null,this._clearActiveClass(t(r));continue}let e=r.target.offsetTop>=this._previousScrollData.visibleEntryTop;if(s&&e){if(i(r),!n)return}else s||e||i(r)}}_initializeTargetsAndObservables(){for(let e of(this._targetLinks=new Map,this._observableSections=new Map,z.find(nP,this._config.target))){if(!e.hash||l(e))continue;let t=z.findOne(decodeURI(e.hash),this._element);a(t)&&(this._targetLinks.set(decodeURI(e.hash),e),this._observableSections.set(e.hash,t))}}_process(e){this._activeTarget!==e&&(this._clearActiveClass(this._config.target),this._activeTarget=e,e.classList.add(nN),this._activateParents(e),I.trigger(this._element,nD,{relatedTarget:e}))}_activateParents(e){if(e.classList.contains("dropdown-item"))z.findOne(".dropdown-toggle",e.closest(".dropdown")).classList.add(nN);else for(let t of z.parents(e,".nav, .list-group"))for(let e of z.prev(t,nj))e.classList.add(nN)}_clearActiveClass(e){for(let t of(e.classList.remove(nN),z.find(`${nP}.${nN}`,e)))t.classList.remove(nN)}static jQueryInterface(e){return this.each(function(){let t=nW.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===t[e]||e.startsWith("_")||"constructor"===e)throw TypeError(`No method named "${e}"`);t[e]()}})}}I.on(window,nI,()=>{for(let e of z.find('[data-bs-spy="scroll"]'))nW.getOrCreateInstance(e)}),m(nW);let nz=".bs.tab",nR=`hide${nz}`,nB=`hidden${nz}`,nq=`show${nz}`,nV=`shown${nz}`,nK=`click${nz}`,nQ=`keydown${nz}`,nX=`load${nz}`,nY="ArrowRight",nU="ArrowDown",nG="Home",nJ="active",nZ="fade",n0="show",n1=".dropdown-toggle",n2=`:not(${n1})`,n3='[data-bs-toggle="tab"], [data-bs-toggle="pill"], [data-bs-toggle="list"]',n5=`.nav-link${n2}, .list-group-item${n2}, [role="tab"]${n2}, ${n3}`,n7=`.${nJ}[data-bs-toggle="tab"], .${nJ}[data-bs-toggle="pill"], .${nJ}[data-bs-toggle="list"]`;class n6 extends H{constructor(e){super(e),this._parent=this._element.closest('.list-group, .nav, [role="tablist"]'),this._parent&&(this._setInitialAttributes(this._parent,this._getChildren()),I.on(this._element,nQ,e=>this._keydown(e)))}static get NAME(){return"tab"}show(){let e=this._element;if(this._elemIsActive(e))return;let t=this._getActiveElem(),i=t?I.trigger(t,nR,{relatedTarget:e}):null;I.trigger(e,nq,{relatedTarget:t}).defaultPrevented||i&&i.defaultPrevented||(this._deactivate(t,e),this._activate(e,t))}_activate(e,t){e&&(e.classList.add(nJ),this._activate(z.getElementFromSelector(e)),this._queueCallback(()=>{"tab"===e.getAttribute("role")?(e.removeAttribute("tabindex"),e.setAttribute("aria-selected",!0),this._toggleDropDown(e,!0),I.trigger(e,nV,{relatedTarget:t})):e.classList.add(n0)},e,e.classList.contains(nZ)))}_deactivate(e,t){e&&(e.classList.remove(nJ),e.blur(),this._deactivate(z.getElementFromSelector(e)),this._queueCallback(()=>{"tab"===e.getAttribute("role")?(e.setAttribute("aria-selected",!1),e.setAttribute("tabindex","-1"),this._toggleDropDown(e,!1),I.trigger(e,nB,{relatedTarget:t})):e.classList.remove(n0)},e,e.classList.contains(nZ)))}_keydown(e){let t;if(!["ArrowLeft",nY,"ArrowUp",nU,nG,"End"].includes(e.key))return;e.stopPropagation(),e.preventDefault();let i=this._getChildren().filter(e=>!l(e));if([nG,"End"].includes(e.key))t=i[e.key===nG?0:i.length-1];else{let n=[nY,nU].includes(e.key);t=b(i,e.target,n,!0)}t&&(t.focus({preventScroll:!0}),n6.getOrCreateInstance(t).show())}_getChildren(){return z.find(n5,this._parent)}_getActiveElem(){return this._getChildren().find(e=>this._elemIsActive(e))||null}_setInitialAttributes(e,t){for(let i of(this._setAttributeIfNotExists(e,"role","tablist"),t))this._setInitialAttributesOnChild(i)}_setInitialAttributesOnChild(e){e=this._getInnerElement(e);let t=this._elemIsActive(e),i=this._getOuterElement(e);e.setAttribute("aria-selected",t),i!==e&&this._setAttributeIfNotExists(i,"role","presentation"),t||e.setAttribute("tabindex","-1"),this._setAttributeIfNotExists(e,"role","tab"),this._setInitialAttributesOnTargetPanel(e)}_setInitialAttributesOnTargetPanel(e){let t=z.getElementFromSelector(e);t&&(this._setAttributeIfNotExists(t,"role","tabpanel"),e.id&&this._setAttributeIfNotExists(t,"aria-labelledby",`${e.id}`))}_toggleDropDown(e,t){let i=this._getOuterElement(e);if(!i.classList.contains("dropdown"))return;let n=(e,n)=>{let s=z.findOne(e,i);s&&s.classList.toggle(n,t)};n(n1,nJ),n(".dropdown-menu",n0),i.setAttribute("aria-expanded",t)}_setAttributeIfNotExists(e,t,i){e.hasAttribute(t)||e.setAttribute(t,i)}_elemIsActive(e){return e.classList.contains(nJ)}_getInnerElement(e){return e.matches(n5)?e:z.findOne(n5,e)}_getOuterElement(e){return e.closest(".nav-item, .list-group-item")||e}static jQueryInterface(e){return this.each(function(){let t=n6.getOrCreateInstance(this);if("string"==typeof e){if(void 0===t[e]||e.startsWith("_")||"constructor"===e)throw TypeError(`No method named "${e}"`);t[e]()}})}}I.on(document,nK,n3,function(e){["A","AREA"].includes(this.tagName)&&e.preventDefault(),l(this)||n6.getOrCreateInstance(this).show()}),I.on(window,nX,()=>{for(let e of z.find(n7))n6.getOrCreateInstance(e)}),m(n6);let n4=".bs.toast",n8=`mouseover${n4}`,n9=`mouseout${n4}`,se=`focusin${n4}`,st=`focusout${n4}`,si=`hide${n4}`,sn=`hidden${n4}`,ss=`show${n4}`,sr=`shown${n4}`,so="hide",sa="show",sl="showing",sc={animation:"boolean",autohide:"boolean",delay:"number"},sh={animation:!0,autohide:!0,delay:5e3};class sd extends H{constructor(e,t){super(e,t),this._timeout=null,this._hasMouseInteraction=!1,this._hasKeyboardInteraction=!1,this._setListeners()}static get Default(){return sh}static get DefaultType(){return sc}static get NAME(){return"toast"}show(){I.trigger(this._element,ss).defaultPrevented||(this._clearTimeout(),this._config.animation&&this._element.classList.add("fade"),this._element.classList.remove(so),d(this._element),this._element.classList.add(sa,sl),this._queueCallback(()=>{this._element.classList.remove(sl),I.trigger(this._element,sr),this._maybeScheduleHide()},this._element,this._config.animation))}hide(){this.isShown()&&(I.trigger(this._element,si).defaultPrevented||(this._element.classList.add(sl),this._queueCallback(()=>{this._element.classList.add(so),this._element.classList.remove(sl,sa),I.trigger(this._element,sn)},this._element,this._config.animation)))}dispose(){this._clearTimeout(),this.isShown()&&this._element.classList.remove(sa),super.dispose()}isShown(){return this._element.classList.contains(sa)}_maybeScheduleHide(){this._config.autohide&&(this._hasMouseInteraction||this._hasKeyboardInteraction||(this._timeout=setTimeout(()=>{this.hide()},this._config.delay)))}_onInteraction(e,t){switch(e.type){case"mouseover":case"mouseout":this._hasMouseInteraction=t;break;case"focusin":case"focusout":this._hasKeyboardInteraction=t}if(t)return void this._clearTimeout();let i=e.relatedTarget;this._element===i||this._element.contains(i)||this._maybeScheduleHide()}_setListeners(){I.on(this._element,n8,e=>this._onInteraction(e,!0)),I.on(this._element,n9,e=>this._onInteraction(e,!1)),I.on(this._element,se,e=>this._onInteraction(e,!0)),I.on(this._element,st,e=>this._onInteraction(e,!1))}_clearTimeout(){clearTimeout(this._timeout),this._timeout=null}static jQueryInterface(e){return this.each(function(){let t=sd.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===t[e])throw TypeError(`No method named "${e}"`);t[e](this)}})}}return R(sd),m(sd),{Alert:K,Button:X,Carousel:eO,Collapse:eW,Dropdown:i_,Modal:iJ,Offcanvas:no,Popover:nL,ScrollSpy:nW,Tab:n6,Toast:sd,Tooltip:nO}}()}}]);