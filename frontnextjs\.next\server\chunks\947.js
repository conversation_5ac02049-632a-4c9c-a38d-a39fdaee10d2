"use strict";exports.id=947,exports.ids=[947],exports.modules={29947:(t,e,i)=>{i.d(e,{N1:()=>d,nu:()=>c});var s=i(43210),a=i(54758);let n="label";function r(t,e){"function"==typeof t?t(e):t&&(t.current=e)}function o(t,e){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:n,s=[];t.datasets=e.map(e=>{let a=t.datasets.find(t=>t[i]===e[i]);return!a||!e.data||s.includes(a)?{...e}:(s.push(a),Object.assign(a,e),a)})}let l=(0,s.forwardRef)(function(t,e){let{height:i=150,width:l=300,redraw:h=!1,datasetIdKey:d,type:c,data:u,options:f,plugins:g=[],fallbackContent:p,updateMode:m,...b}=t,x=(0,s.useRef)(null),_=(0,s.useRef)(null),y=()=>{x.current&&(_.current=new a.t1(x.current,{type:c,data:function(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:n,i={labels:[],datasets:[]};return i.labels=t.labels,o(i,t.datasets,e),i}(u,d),options:f&&{...f},plugins:g}),r(e,_.current))},v=()=>{r(e,null),_.current&&(_.current.destroy(),_.current=null)};return(0,s.useEffect)(()=>{!h&&_.current&&f&&function(t,e){let i=t.options;i&&e&&Object.assign(i,e)}(_.current,f)},[h,f]),(0,s.useEffect)(()=>{!h&&_.current&&(_.current.config.data.labels=u.labels)},[h,u.labels]),(0,s.useEffect)(()=>{!h&&_.current&&u.datasets&&o(_.current.config.data,u.datasets,d)},[h,u.datasets]),(0,s.useEffect)(()=>{_.current&&(h?(v(),setTimeout(y)):_.current.update(m))},[h,f,u.labels,u.datasets,m]),(0,s.useEffect)(()=>{_.current&&(v(),setTimeout(y))},[c]),(0,s.useEffect)(()=>(y(),()=>v()),[]),s.createElement("canvas",{ref:x,role:"img",height:i,width:l,...b},p)});function h(t,e){return a.t1.register(e),(0,s.forwardRef)((e,i)=>s.createElement(l,{...e,ref:i,type:t}))}let d=h("line",a.ZT),c=h("doughnut",a.ju)},54758:(t,e,i)=>{i.d(e,{Bs:()=>tJ,FN:()=>t9,Hg:()=>tp,No:()=>t6,PP:()=>ej,Qw:()=>h,ZT:()=>T,dN:()=>e_,hE:()=>eS,ju:()=>C,kc:()=>eY,m_:()=>eV,s$:()=>ek,t1:()=>tq});var s=i(58022);class a{constructor(){this._request=null,this._charts=new Map,this._running=!1,this._lastDate=void 0}_notify(t,e,i,s){let a=e.listeners[s],n=e.duration;a.forEach(s=>s({chart:t,initial:e.initial,numSteps:n,currentStep:Math.min(i-e.start,n)}))}_refresh(){this._request||(this._running=!0,this._request=s.r.call(window,()=>{this._update(),this._request=null,this._running&&this._refresh()}))}_update(t=Date.now()){let e=0;this._charts.forEach((i,s)=>{let a;if(!i.running||!i.items.length)return;let n=i.items,r=n.length-1,o=!1;for(;r>=0;--r)(a=n[r])._active?(a._total>i.duration&&(i.duration=a._total),a.tick(t),o=!0):(n[r]=n[n.length-1],n.pop());o&&(s.draw(),this._notify(s,i,t,"progress")),n.length||(i.running=!1,this._notify(s,i,t,"complete"),i.initial=!1),e+=n.length}),this._lastDate=t,0===e&&(this._running=!1)}_getAnims(t){let e=this._charts,i=e.get(t);return i||(i={running:!1,initial:!0,items:[],listeners:{complete:[],progress:[]}},e.set(t,i)),i}listen(t,e,i){this._getAnims(t).listeners[e].push(i)}add(t,e){e&&e.length&&this._getAnims(t).items.push(...e)}has(t){return this._getAnims(t).items.length>0}start(t){let e=this._charts.get(t);e&&(e.running=!0,e.start=Date.now(),e.duration=e.items.reduce((t,e)=>Math.max(t,e._duration),0),this._refresh())}running(t){if(!this._running)return!1;let e=this._charts.get(t);return!!e&&!!e.running&&!!e.items.length}stop(t){let e=this._charts.get(t);if(!e||!e.items.length)return;let i=e.items,s=i.length-1;for(;s>=0;--s)i[s].cancel();e.items=[],this._notify(t,e,Date.now(),"complete")}remove(t){return this._charts.delete(t)}}var n=new a;let r="transparent",o={boolean:(t,e,i)=>i>.5?e:t,color(t,e,i){let a=(0,s.c)(t||r),n=a.valid&&(0,s.c)(e||r);return n&&n.valid?n.mix(a,i).hexString():e},number:(t,e,i)=>t+(e-t)*i};class l{constructor(t,e,i,a){let n=e[i];a=(0,s.a)([t.to,a,n,t.from]);let r=(0,s.a)([t.from,n,a]);this._active=!0,this._fn=t.fn||o[t.type||typeof r],this._easing=s.e[t.easing]||s.e.linear,this._start=Math.floor(Date.now()+(t.delay||0)),this._duration=this._total=Math.floor(t.duration),this._loop=!!t.loop,this._target=e,this._prop=i,this._from=r,this._to=a,this._promises=void 0}active(){return this._active}update(t,e,i){if(this._active){this._notify(!1);let a=this._target[this._prop],n=i-this._start,r=this._duration-n;this._start=i,this._duration=Math.floor(Math.max(r,t.duration)),this._total+=n,this._loop=!!t.loop,this._to=(0,s.a)([t.to,e,a,t.from]),this._from=(0,s.a)([t.from,a,e])}}cancel(){this._active&&(this.tick(Date.now()),this._active=!1,this._notify(!1))}tick(t){let e,i=t-this._start,s=this._duration,a=this._prop,n=this._from,r=this._loop,o=this._to;if(this._active=n!==o&&(r||i<s),!this._active){this._target[a]=o,this._notify(!0);return}if(i<0){this._target[a]=n;return}e=i/s%2,e=r&&e>1?2-e:e,e=this._easing(Math.min(1,Math.max(0,e))),this._target[a]=this._fn(n,o,e)}wait(){let t=this._promises||(this._promises=[]);return new Promise((e,i)=>{t.push({res:e,rej:i})})}_notify(t){let e=t?"res":"rej",i=this._promises||[];for(let t=0;t<i.length;t++)i[t][e]()}}class h{constructor(t,e){this._chart=t,this._properties=new Map,this.configure(e)}configure(t){if(!(0,s.i)(t))return;let e=Object.keys(s.d.animation),i=this._properties;Object.getOwnPropertyNames(t).forEach(a=>{let n=t[a];if(!(0,s.i)(n))return;let r={};for(let t of e)r[t]=n[t];((0,s.b)(n.properties)&&n.properties||[a]).forEach(t=>{t!==a&&i.has(t)||i.set(t,r)})})}_animateOptions(t,e){let i=e.options,s=function(t,e){if(!e)return;let i=t.options;if(!i){t.options=e;return}return i.$shared&&(t.options=i=Object.assign({},i,{$shared:!1,$animations:{}})),i}(t,i);if(!s)return[];let a=this._createAnimations(s,i);return i.$shared&&(function(t,e){let i=[],s=Object.keys(e);for(let e=0;e<s.length;e++){let a=t[s[e]];a&&a.active()&&i.push(a.wait())}return Promise.all(i)})(t.options.$animations,i).then(()=>{t.options=i},()=>{}),a}_createAnimations(t,e){let i,s=this._properties,a=[],n=t.$animations||(t.$animations={}),r=Object.keys(e),o=Date.now();for(i=r.length-1;i>=0;--i){let h=r[i];if("$"===h.charAt(0))continue;if("options"===h){a.push(...this._animateOptions(t,e));continue}let d=e[h],c=n[h],u=s.get(h);if(c)if(u&&c.active()){c.update(u,d,o);continue}else c.cancel();if(!u||!u.duration){t[h]=d;continue}n[h]=c=new l(u,t,h,d),a.push(c)}return a}update(t,e){if(0===this._properties.size)return void Object.assign(t,e);let i=this._createAnimations(t,e);if(i.length)return n.add(this._chart,i),!0}}function d(t,e){let i=t&&t.options||{},s=i.reverse,a=void 0===i.min?e:0,n=void 0===i.max?e:0;return{start:s?n:a,end:s?a:n}}function c(t,e){let i,s,a=[],n=t._getSortedDatasetMetas(e);for(i=0,s=n.length;i<s;++i)a.push(n[i].index);return a}function u(t,e,i,a={}){let n,r,o,l,h=t.keys,d="single"===a.mode;if(null===e)return;let c=!1;for(n=0,r=h.length;n<r;++n){if((o=+h[n])===i){if(c=!0,a.all)continue;break}l=t.values[o],(0,s.g)(l)&&(d||0===e||(0,s.s)(e)===(0,s.s)(l))&&(e+=l)}return c||a.all?e:0}function f(t,e){let i=t&&t.options.stacked;return i||void 0===i&&void 0!==e.stack}function g(t,e,i,s){for(let a of e.getMatchingVisibleMetas(s).reverse()){let e=t[a.index];if(i&&e>0||!i&&e<0)return a.index}return null}function p(t,e){let i,{chart:s,_cachedMeta:a}=t,n=s._stacks||(s._stacks={}),{iScale:r,vScale:o,index:l}=a,h=r.axis,d=o.axis,c=`${r.id}.${o.id}.${a.stack||a.type}`,u=e.length;for(let t=0;t<u;++t){let s=e[t],{[h]:r,[d]:u}=s;(i=(s._stacks||(s._stacks={}))[d]=function(t,e,i){let s=t[e]||(t[e]={});return s[i]||(s[i]={})}(n,c,r))[l]=u,i._top=g(i,o,!0,a.type),i._bottom=g(i,o,!1,a.type),(i._visualValues||(i._visualValues={}))[l]=u}}function m(t,e){let i=t.scales;return Object.keys(i).filter(t=>i[t].axis===e).shift()}function b(t,e){let i=t.controller.index,s=t.vScale&&t.vScale.axis;if(s)for(let a of e=e||t._parsed){let t=a._stacks;if(!t||void 0===t[s]||void 0===t[s][i])return;delete t[s][i],void 0!==t[s]._visualValues&&void 0!==t[s]._visualValues[i]&&delete t[s]._visualValues[i]}}let x=t=>"reset"===t||"none"===t,_=(t,e)=>e?t:Object.assign({},t),y=(t,e,i)=>t&&!e.hidden&&e._stacked&&{keys:c(i,!0),values:null};class v{static defaults={};static datasetElementType=null;static dataElementType=null;constructor(t,e){this.chart=t,this._ctx=t.ctx,this.index=e,this._cachedDataOpts={},this._cachedMeta=this.getMeta(),this._type=this._cachedMeta.type,this.options=void 0,this._parsing=!1,this._data=void 0,this._objectData=void 0,this._sharedOptions=void 0,this._drawStart=void 0,this._drawCount=void 0,this.enableOptionSharing=!1,this.supportsDecimation=!1,this.$context=void 0,this._syncList=[],this.datasetElementType=new.target.datasetElementType,this.dataElementType=new.target.dataElementType,this.initialize()}initialize(){let t=this._cachedMeta;this.configure(),this.linkScales(),t._stacked=f(t.vScale,t),this.addElements(),this.options.fill&&!this.chart.isPluginEnabled("filler")&&console.warn("Tried to use the 'fill' option without the 'Filler' plugin enabled. Please import and register the 'Filler' plugin and make sure it is not disabled in the options")}updateIndex(t){this.index!==t&&b(this._cachedMeta),this.index=t}linkScales(){let t=this.chart,e=this._cachedMeta,i=this.getDataset(),a=(t,e,i,s)=>"x"===t?e:"r"===t?s:i,n=e.xAxisID=(0,s.v)(i.xAxisID,m(t,"x")),r=e.yAxisID=(0,s.v)(i.yAxisID,m(t,"y")),o=e.rAxisID=(0,s.v)(i.rAxisID,m(t,"r")),l=e.indexAxis,h=e.iAxisID=a(l,n,r,o),d=e.vAxisID=a(l,r,n,o);e.xScale=this.getScaleForId(n),e.yScale=this.getScaleForId(r),e.rScale=this.getScaleForId(o),e.iScale=this.getScaleForId(h),e.vScale=this.getScaleForId(d)}getDataset(){return this.chart.data.datasets[this.index]}getMeta(){return this.chart.getDatasetMeta(this.index)}getScaleForId(t){return this.chart.scales[t]}_getOtherScale(t){let e=this._cachedMeta;return t===e.iScale?e.vScale:e.iScale}reset(){this._update("reset")}_destroy(){let t=this._cachedMeta;this._data&&(0,s.u)(this._data,this),t._stacked&&b(t)}_dataCheck(){let t=this.getDataset(),e=t.data||(t.data=[]),i=this._data;if((0,s.i)(e)){let t=this._cachedMeta;this._data=function(t,e){let i,s,a,{iScale:n,vScale:r}=e,o="x"===n.axis?"x":"y",l="x"===r.axis?"x":"y",h=Object.keys(t),d=Array(h.length);for(i=0,s=h.length;i<s;++i)a=h[i],d[i]={[o]:a,[l]:t[a]};return d}(e,t)}else if(i!==e){if(i){(0,s.u)(i,this);let t=this._cachedMeta;b(t),t._parsed=[]}e&&Object.isExtensible(e)&&(0,s.l)(e,this),this._syncList=[],this._data=e}}addElements(){let t=this._cachedMeta;this._dataCheck(),this.datasetElementType&&(t.dataset=new this.datasetElementType)}buildOrUpdateElements(t){let e=this._cachedMeta,i=this.getDataset(),s=!1;this._dataCheck();let a=e._stacked;e._stacked=f(e.vScale,e),e.stack!==i.stack&&(s=!0,b(e),e.stack=i.stack),this._resyncElements(t),(s||a!==e._stacked)&&(p(this,e._parsed),e._stacked=f(e.vScale,e))}configure(){let t=this.chart.config,e=t.datasetScopeKeys(this._type),i=t.getOptionScopes(this.getDataset(),e,!0);this.options=t.createResolver(i,this.getContext()),this._parsing=this.options.parsing,this._cachedDataOpts={}}parse(t,e){let i,a,n,{_cachedMeta:r,_data:o}=this,{iScale:l,_stacked:h}=r,d=l.axis,c=0===t&&e===o.length||r._sorted,u=t>0&&r._parsed[t-1];if(!1===this._parsing)r._parsed=o,r._sorted=!0,n=o;else{n=(0,s.b)(o[t])?this.parseArrayData(r,o,t,e):(0,s.i)(o[t])?this.parseObjectData(r,o,t,e):this.parsePrimitiveData(r,o,t,e);let l=()=>null===a[d]||u&&a[d]<u[d];for(i=0;i<e;++i)r._parsed[i+t]=a=n[i],c&&(l()&&(c=!1),u=a);r._sorted=c}h&&p(this,n)}parsePrimitiveData(t,e,i,s){let a,n,{iScale:r,vScale:o}=t,l=r.axis,h=o.axis,d=r.getLabels(),c=r===o,u=Array(s);for(a=0;a<s;++a)n=a+i,u[a]={[l]:c||r.parse(d[n],n),[h]:o.parse(e[n],n)};return u}parseArrayData(t,e,i,s){let a,n,r,{xScale:o,yScale:l}=t,h=Array(s);for(a=0;a<s;++a)r=e[n=a+i],h[a]={x:o.parse(r[0],n),y:l.parse(r[1],n)};return h}parseObjectData(t,e,i,a){let n,r,o,{xScale:l,yScale:h}=t,{xAxisKey:d="x",yAxisKey:c="y"}=this._parsing,u=Array(a);for(n=0;n<a;++n)o=e[r=n+i],u[n]={x:l.parse((0,s.f)(o,d),r),y:h.parse((0,s.f)(o,c),r)};return u}getParsed(t){return this._cachedMeta._parsed[t]}getDataElement(t){return this._cachedMeta.data[t]}applyStack(t,e,i){let s=this.chart,a=this._cachedMeta,n=e[t.axis];return u({keys:c(s,!0),values:e._stacks[t.axis]._visualValues},n,a.index,{mode:i})}updateRangeFromParsed(t,e,i,s){let a=i[e.axis],n=null===a?NaN:a,r=s&&i._stacks[e.axis];s&&r&&(s.values=r,n=u(s,a,this._cachedMeta.index)),t.min=Math.min(t.min,n),t.max=Math.max(t.max,n)}getMinMax(t,e){let i,a,n=this._cachedMeta,r=n._parsed,o=n._sorted&&t===n.iScale,l=r.length,h=this._getOtherScale(t),d=y(e,n,this.chart),c={min:Number.POSITIVE_INFINITY,max:Number.NEGATIVE_INFINITY},{min:u,max:f}=function(t){let{min:e,max:i,minDefined:s,maxDefined:a}=t.getUserBounds();return{min:s?e:Number.NEGATIVE_INFINITY,max:a?i:Number.POSITIVE_INFINITY}}(h);function g(){let e=(a=r[i])[h.axis];return!(0,s.g)(a[t.axis])||u>e||f<e}for(i=0;i<l&&(g()||(this.updateRangeFromParsed(c,t,a,d),!o));++i);if(o){for(i=l-1;i>=0;--i)if(!g()){this.updateRangeFromParsed(c,t,a,d);break}}return c}getAllParsedValues(t){let e,i,a,n=this._cachedMeta._parsed,r=[];for(e=0,i=n.length;e<i;++e)a=n[e][t.axis],(0,s.g)(a)&&r.push(a);return r}getMaxOverflow(){return!1}getLabelAndValue(t){let e=this._cachedMeta,i=e.iScale,s=e.vScale,a=this.getParsed(t);return{label:i?""+i.getLabelForValue(a[i.axis]):"",value:s?""+s.getLabelForValue(a[s.axis]):""}}_update(t){var e;let i,a,n,r,o=this._cachedMeta;this.update(t||"default"),e=(0,s.v)(this.options.clip,function(t,e,i){if(!1===i)return!1;let s=d(t,i),a=d(e,i);return{top:a.end,right:s.end,bottom:a.start,left:s.start}}(o.xScale,o.yScale,this.getMaxOverflow())),(0,s.i)(e)?(i=e.top,a=e.right,n=e.bottom,r=e.left):i=a=n=r=e,o._clip={top:i,right:a,bottom:n,left:r,disabled:!1===e}}update(t){}draw(){let t,e=this._ctx,i=this.chart,s=this._cachedMeta,a=s.data||[],n=i.chartArea,r=[],o=this._drawStart||0,l=this._drawCount||a.length-o,h=this.options.drawActiveElementsOnTop;for(s.dataset&&s.dataset.draw(e,n,o,l),t=o;t<o+l;++t){let i=a[t];i.hidden||(i.active&&h?r.push(i):i.draw(e,n))}for(t=0;t<r.length;++t)r[t].draw(e,n)}getStyle(t,e){let i=e?"active":"default";return void 0===t&&this._cachedMeta.dataset?this.resolveDatasetElementOptions(i):this.resolveDataElementOptions(t||0,i)}getContext(t,e,i){var a,n,r;let o,l=this.getDataset();if(t>=0&&t<this._cachedMeta.data.length){let e=this._cachedMeta.data[t];(o=e.$context||(a=this.getContext(),e.$context=(0,s.j)(a,{active:!1,dataIndex:t,parsed:void 0,raw:void 0,element:e,index:t,mode:"default",type:"data"}))).parsed=this.getParsed(t),o.raw=l.data[t],o.index=o.dataIndex=t}else(o=this.$context||(this.$context=(n=this.chart.getContext(),r=this.index,(0,s.j)(n,{active:!1,dataset:void 0,datasetIndex:r,index:r,mode:"default",type:"dataset"})))).dataset=l,o.index=o.datasetIndex=this.index;return o.active=!!e,o.mode=i,o}resolveDatasetElementOptions(t){return this._resolveElementOptions(this.datasetElementType.id,t)}resolveDataElementOptions(t,e){return this._resolveElementOptions(this.dataElementType.id,e,t)}_resolveElementOptions(t,e="default",i){let a="active"===e,n=this._cachedDataOpts,r=t+"-"+e,o=n[r],l=this.enableOptionSharing&&(0,s.h)(i);if(o)return _(o,l);let h=this.chart.config,d=h.datasetElementScopeKeys(this._type,t),c=a?[`${t}Hover`,"hover",t,""]:[t,""],u=h.getOptionScopes(this.getDataset(),d),f=Object.keys(s.d.elements[t]),g=h.resolveNamedOptions(u,f,()=>this.getContext(i,a,e),c);return g.$shared&&(g.$shared=l,n[r]=Object.freeze(_(g,l))),g}_resolveAnimations(t,e,i){let s,a=this.chart,n=this._cachedDataOpts,r=`animation-${e}`,o=n[r];if(o)return o;if(!1!==a.options.animation){let a=this.chart.config,n=a.datasetAnimationScopeKeys(this._type,e),r=a.getOptionScopes(this.getDataset(),n);s=a.createResolver(r,this.getContext(t,i,e))}let l=new h(a,s&&s.animations);return s&&s._cacheable&&(n[r]=Object.freeze(l)),l}getSharedOptions(t){if(t.$shared)return this._sharedOptions||(this._sharedOptions=Object.assign({},t))}includeOptions(t,e){return!e||x(t)||this.chart._animationsDisabled}_getSharedOptions(t,e){let i=this.resolveDataElementOptions(t,e),s=this._sharedOptions,a=this.getSharedOptions(i),n=this.includeOptions(e,a)||a!==s;return this.updateSharedOptions(a,e,i),{sharedOptions:a,includeOptions:n}}updateElement(t,e,i,s){x(s)?Object.assign(t,i):this._resolveAnimations(e,s).update(t,i)}updateSharedOptions(t,e,i){t&&!x(e)&&this._resolveAnimations(void 0,e).update(t,i)}_setStyle(t,e,i,s){t.active=s;let a=this.getStyle(e,s);this._resolveAnimations(e,i,s).update(t,{options:!s&&this.getSharedOptions(a)||a})}removeHoverStyle(t,e,i){this._setStyle(t,i,"active",!1)}setHoverStyle(t,e,i){this._setStyle(t,i,"active",!0)}_removeDatasetHoverStyle(){let t=this._cachedMeta.dataset;t&&this._setStyle(t,void 0,"active",!1)}_setDatasetHoverStyle(){let t=this._cachedMeta.dataset;t&&this._setStyle(t,void 0,"active",!0)}_resyncElements(t){let e=this._data,i=this._cachedMeta.data;for(let[t,e,i]of this._syncList)this[t](e,i);this._syncList=[];let s=i.length,a=e.length,n=Math.min(a,s);n&&this.parse(0,n),a>s?this._insertElements(s,a-s,t):a<s&&this._removeElements(a,s-a)}_insertElements(t,e,i=!0){let s,a=this._cachedMeta,n=a.data,r=t+e,o=t=>{for(t.length+=e,s=t.length-1;s>=r;s--)t[s]=t[s-e]};for(o(n),s=t;s<r;++s)n[s]=new this.dataElementType;this._parsing&&o(a._parsed),this.parse(t,e),i&&this.updateElements(n,t,e,"reset")}updateElements(t,e,i,s){}_removeElements(t,e){let i=this._cachedMeta;if(this._parsing){let s=i._parsed.splice(t,e);i._stacked&&b(i,s)}i.data.splice(t,e)}_sync(t){if(this._parsing)this._syncList.push(t);else{let[e,i,s]=t;this[e](i,s)}this.chart._dataChanges.push([this.index,...t])}_onDataPush(){let t=arguments.length;this._sync(["_insertElements",this.getDataset().data.length-t,t])}_onDataPop(){this._sync(["_removeElements",this._cachedMeta.data.length-1,1])}_onDataShift(){this._sync(["_removeElements",0,1])}_onDataSplice(t,e){e&&this._sync(["_removeElements",t,e]);let i=arguments.length-2;i&&this._sync(["_insertElements",t,i])}_onDataUnshift(){this._sync(["_insertElements",0,arguments.length])}}function M(t,e,i,a){return(0,s.b)(t)?!function(t,e,i,s){let a=i.parse(t[0],s),n=i.parse(t[1],s),r=Math.min(a,n),o=Math.max(a,n),l=r,h=o;Math.abs(r)>Math.abs(o)&&(l=o,h=r),e[i.axis]=h,e._custom={barStart:l,barEnd:h,start:a,end:n,min:r,max:o}}(t,e,i,a):e[i.axis]=i.parse(t,a),e}function w(t,e,i,s){let a,n,r,o,l=t.iScale,h=t.vScale,d=l.getLabels(),c=l===h,u=[];for(a=i,n=i+s;a<n;++a)o=e[a],(r={})[l.axis]=c||l.parse(d[a],a),u.push(M(o,r,h,a));return u}function k(t){return t&&void 0!==t.barStart&&void 0!==t.barEnd}function P(t,e,i,s){var a,n,r;return t=s?S((a=t,n=e,r=i,t=a===n?r:a===r?n:a),i,e):S(t,e,i)}function S(t,e,i){return"start"===t?e:"end"===t?i:t}class D extends v{static id="bar";static defaults={datasetElementType:!1,dataElementType:"bar",categoryPercentage:.8,barPercentage:.9,grouped:!0,animations:{numbers:{type:"number",properties:["x","y","base","width","height"]}}};static overrides={scales:{_index_:{type:"category",offset:!0,grid:{offset:!0}},_value_:{type:"linear",beginAtZero:!0}}};parsePrimitiveData(t,e,i,s){return w(t,e,i,s)}parseArrayData(t,e,i,s){return w(t,e,i,s)}parseObjectData(t,e,i,a){let n,r,o,l,{iScale:h,vScale:d}=t,{xAxisKey:c="x",yAxisKey:u="y"}=this._parsing,f="x"===h.axis?c:u,g="x"===d.axis?c:u,p=[];for(n=i,r=i+a;n<r;++n)l=e[n],(o={})[h.axis]=h.parse((0,s.f)(l,f),n),p.push(M((0,s.f)(l,g),o,d,n));return p}updateRangeFromParsed(t,e,i,s){super.updateRangeFromParsed(t,e,i,s);let a=i._custom;a&&e===this._cachedMeta.vScale&&(t.min=Math.min(t.min,a.min),t.max=Math.max(t.max,a.max))}getMaxOverflow(){return 0}getLabelAndValue(t){let{iScale:e,vScale:i}=this._cachedMeta,s=this.getParsed(t),a=s._custom,n=k(a)?"["+a.start+", "+a.end+"]":""+i.getLabelForValue(s[i.axis]);return{label:""+e.getLabelForValue(s[e.axis]),value:n}}initialize(){this.enableOptionSharing=!0,super.initialize(),this._cachedMeta.stack=this.getDataset().stack}update(t){let e=this._cachedMeta;this.updateElements(e.data,0,e.data.length,t)}updateElements(t,e,i,a){let n="reset"===a,{index:r,_cachedMeta:{vScale:o}}=this,l=o.getBasePixel(),h=o.isHorizontal(),d=this._getRuler(),{sharedOptions:c,includeOptions:u}=this._getSharedOptions(e,a);for(let f=e;f<e+i;f++){let e=this.getParsed(f),i=n||(0,s.k)(e[o.axis])?{base:l,head:l}:this._calculateBarValuePixels(f),g=this._calculateBarIndexPixels(f,d),p=(e._stacks||{})[o.axis],m={horizontal:h,base:i.base,enableBorderRadius:!p||k(e._custom)||r===p._top||r===p._bottom,x:h?i.head:g.center,y:h?g.center:i.head,height:h?g.size:Math.abs(i.size),width:h?Math.abs(i.size):g.size};u&&(m.options=c||this.resolveDataElementOptions(f,t[f].active?"active":a));let b=m.options||t[f].options;!function(t,e,i,s){let a,n,r,o,l,h=e.borderSkipped,d={};if(!h){t.borderSkipped=d;return}if(!0===h){t.borderSkipped={top:!0,right:!0,bottom:!0,left:!0};return}let{start:c,end:u,reverse:f,top:g,bottom:p}=(t.horizontal?(a=t.base>t.x,n="left",r="right"):(a=t.base<t.y,n="bottom",r="top"),a?(o="end",l="start"):(o="start",l="end"),{start:n,end:r,reverse:a,top:o,bottom:l});"middle"===h&&i&&(t.enableBorderRadius=!0,(i._top||0)===s?h=g:(i._bottom||0)===s?h=p:(d[P(p,c,u,f)]=!0,h=g)),d[P(h,c,u,f)]=!0,t.borderSkipped=d}(m,b,p,r),function(t,{inflateAmount:e},i){t.inflateAmount="auto"===e?.33*(1===i):e}(m,b,d.ratio),this.updateElement(t[f],f,m,a)}}_getStacks(t,e){let{iScale:i}=this._cachedMeta,a=i.getMatchingVisibleMetas(this._type).filter(t=>t.controller.options.grouped),n=i.options.stacked,r=[],o=this._cachedMeta.controller.getParsed(e),l=o&&o[i.axis],h=t=>{let e=t._parsed.find(t=>t[i.axis]===l),a=e&&e[t.vScale.axis];if((0,s.k)(a)||isNaN(a))return!0};for(let i of a)if(!(void 0!==e&&h(i))&&((!1===n||-1===r.indexOf(i.stack)||void 0===n&&void 0===i.stack)&&r.push(i.stack),i.index===t))break;return r.length||r.push(void 0),r}_getStackCount(t){return this._getStacks(void 0,t).length}_getAxisCount(){return this._getAxis().length}getFirstScaleIdForIndexAxis(){let t=this.chart.scales,e=this.chart.options.indexAxis;return Object.keys(t).filter(i=>t[i].axis===e).shift()}_getAxis(){let t={},e=this.getFirstScaleIdForIndexAxis();for(let i of this.chart.data.datasets)t[(0,s.v)("x"===this.chart.options.indexAxis?i.xAxisID:i.yAxisID,e)]=!0;return Object.keys(t)}_getStackIndex(t,e,i){let s=this._getStacks(t,i),a=void 0!==e?s.indexOf(e):-1;return -1===a?s.length-1:a}_getRuler(){let t,e,i=this.options,a=this._cachedMeta,n=a.iScale,r=[];for(t=0,e=a.data.length;t<e;++t)r.push(n.getPixelForValue(this.getParsed(t)[n.axis],t));let o=i.barThickness;return{min:o||function(t){let e,i,a,n,r=t.iScale,o=function(t,e){if(!t._cache.$bar){let i=t.getMatchingVisibleMetas(e),a=[];for(let e=0,s=i.length;e<s;e++)a=a.concat(i[e].controller.getAllParsedValues(t));t._cache.$bar=(0,s._)(a.sort((t,e)=>t-e))}return t._cache.$bar}(r,t.type),l=r._length,h=()=>{32767!==a&&-32768!==a&&((0,s.h)(n)&&(l=Math.min(l,Math.abs(a-n)||l)),n=a)};for(e=0,i=o.length;e<i;++e)a=r.getPixelForValue(o[e]),h();for(e=0,n=void 0,i=r.ticks.length;e<i;++e)a=r.getPixelForTick(e),h();return l}(a),pixels:r,start:n._startPixel,end:n._endPixel,stackCount:this._getStackCount(),scale:n,grouped:i.grouped,ratio:o?1:i.categoryPercentage*i.barPercentage}}_calculateBarValuePixels(t){let e,i,{_cachedMeta:{vScale:a,_stacked:n,index:r},options:{base:o,minBarLength:l}}=this,h=o||0,d=this.getParsed(t),c=d._custom,u=k(c),f=d[a.axis],g=0,p=n?this.applyStack(a,d,n):f;p!==f&&(g=p-f,p=f),u&&(f=c.barStart,p=c.barEnd-c.barStart,0!==f&&(0,s.s)(f)!==(0,s.s)(c.barEnd)&&(g=0),g+=f);let m=(0,s.k)(o)||u?g:o,b=a.getPixelForValue(m);if(Math.abs(i=(e=this.chart.getDataVisibility(t)?a.getPixelForValue(g+p):b)-b)<l){var x;i=(0!==(x=i)?(0,s.s)(x):(a.isHorizontal()?1:-1)*(a.min>=h?1:-1))*l,f===h&&(b-=i/2);let t=a.getPixelForDecimal(0),o=a.getPixelForDecimal(1),c=Math.min(t,o);e=(b=Math.max(Math.min(b,Math.max(t,o)),c))+i,n&&!u&&(d._stacks[a.axis]._visualValues[r]=a.getValueForPixel(e)-a.getValueForPixel(b))}if(b===a.getPixelForValue(h)){let t=(0,s.s)(i)*a.getLineWidthForValue(h)/2;b+=t,i-=t}return{size:i,base:b,head:e,center:e+i/2}}_calculateBarIndexPixels(t,e){let i,a,n=e.scale,r=this.options,o=r.skipNull,l=(0,s.v)(r.maxBarThickness,1/0),h=this._getAxisCount();if(e.grouped){let n=o?this._getStackCount(t):e.stackCount,d="flex"===r.barThickness?function(t,e,i,s){let a=e.pixels,n=a[t],r=t>0?a[t-1]:null,o=t<a.length-1?a[t+1]:null,l=i.categoryPercentage;null===r&&(r=n-(null===o?e.end-e.start:o-n)),null===o&&(o=n+n-r);let h=n-(n-Math.min(r,o))/2*l;return{chunk:Math.abs(o-r)/2*l/s,ratio:i.barPercentage,start:h}}(t,e,r,n*h):function(t,e,i,a){let n,r,o=i.barThickness;return(0,s.k)(o)?(n=e.min*i.categoryPercentage,r=i.barPercentage):(n=o*a,r=1),{chunk:n/a,ratio:r,start:e.pixels[t]-n/2}}(t,e,r,n*h),c="x"===this.chart.options.indexAxis?this.getDataset().xAxisID:this.getDataset().yAxisID,u=this._getAxis().indexOf((0,s.v)(c,this.getFirstScaleIdForIndexAxis())),f=this._getStackIndex(this.index,this._cachedMeta.stack,o?t:void 0)+u;i=d.start+d.chunk*f+d.chunk/2,a=Math.min(l,d.chunk*d.ratio)}else i=n.getPixelForValue(this.getParsed(t)[n.axis],t),a=Math.min(l,e.min*e.ratio);return{base:i-a/2,head:i+a/2,center:i,size:a}}draw(){let t=this._cachedMeta,e=t.vScale,i=t.data,s=i.length,a=0;for(;a<s;++a)null===this.getParsed(a)[e.axis]||i[a].hidden||i[a].draw(this._ctx)}}class O extends v{static id="bubble";static defaults={datasetElementType:!1,dataElementType:"point",animations:{numbers:{type:"number",properties:["x","y","borderWidth","radius"]}}};static overrides={scales:{x:{type:"linear"},y:{type:"linear"}}};initialize(){this.enableOptionSharing=!0,super.initialize()}parsePrimitiveData(t,e,i,s){let a=super.parsePrimitiveData(t,e,i,s);for(let t=0;t<a.length;t++)a[t]._custom=this.resolveDataElementOptions(t+i).radius;return a}parseArrayData(t,e,i,a){let n=super.parseArrayData(t,e,i,a);for(let t=0;t<n.length;t++){let a=e[i+t];n[t]._custom=(0,s.v)(a[2],this.resolveDataElementOptions(t+i).radius)}return n}parseObjectData(t,e,i,a){let n=super.parseObjectData(t,e,i,a);for(let t=0;t<n.length;t++){let a=e[i+t];n[t]._custom=(0,s.v)(a&&a.r&&+a.r,this.resolveDataElementOptions(t+i).radius)}return n}getMaxOverflow(){let t=this._cachedMeta.data,e=0;for(let i=t.length-1;i>=0;--i)e=Math.max(e,t[i].size(this.resolveDataElementOptions(i))/2);return e>0&&e}getLabelAndValue(t){let e=this._cachedMeta,i=this.chart.data.labels||[],{xScale:s,yScale:a}=e,n=this.getParsed(t),r=s.getLabelForValue(n.x),o=a.getLabelForValue(n.y),l=n._custom;return{label:i[t]||"",value:"("+r+", "+o+(l?", "+l:"")+")"}}update(t){let e=this._cachedMeta.data;this.updateElements(e,0,e.length,t)}updateElements(t,e,i,s){let a="reset"===s,{iScale:n,vScale:r}=this._cachedMeta,{sharedOptions:o,includeOptions:l}=this._getSharedOptions(e,s),h=n.axis,d=r.axis;for(let c=e;c<e+i;c++){let e=t[c],i=!a&&this.getParsed(c),u={},f=u[h]=a?n.getPixelForDecimal(.5):n.getPixelForValue(i[h]),g=u[d]=a?r.getBasePixel():r.getPixelForValue(i[d]);u.skip=isNaN(f)||isNaN(g),l&&(u.options=o||this.resolveDataElementOptions(c,e.active?"active":s),a&&(u.options.radius=0)),this.updateElement(e,c,u,s)}}resolveDataElementOptions(t,e){let i=this.getParsed(t),a=super.resolveDataElementOptions(t,e);a.$shared&&(a=Object.assign({},a,{$shared:!1}));let n=a.radius;return"active"!==e&&(a.radius=0),a.radius+=(0,s.v)(i&&i._custom,n),a}}class C extends v{static id="doughnut";static defaults={datasetElementType:!1,dataElementType:"arc",animation:{animateRotate:!0,animateScale:!1},animations:{numbers:{type:"number",properties:["circumference","endAngle","innerRadius","outerRadius","startAngle","x","y","offset","borderWidth","spacing"]}},cutout:"50%",rotation:0,circumference:360,radius:"100%",spacing:0,indexAxis:"r"};static descriptors={_scriptable:t=>"spacing"!==t,_indexable:t=>"spacing"!==t&&!t.startsWith("borderDash")&&!t.startsWith("hoverBorderDash")};static overrides={aspectRatio:1,plugins:{legend:{labels:{generateLabels(t){let e=t.data;if(e.labels.length&&e.datasets.length){let{labels:{pointStyle:i,color:s}}=t.legend.options;return e.labels.map((e,a)=>{let n=t.getDatasetMeta(0).controller.getStyle(a);return{text:e,fillStyle:n.backgroundColor,strokeStyle:n.borderColor,fontColor:s,lineWidth:n.borderWidth,pointStyle:i,hidden:!t.getDataVisibility(a),index:a}})}return[]}},onClick(t,e,i){i.chart.toggleDataVisibility(e.index),i.chart.update()}}}};constructor(t,e){super(t,e),this.enableOptionSharing=!0,this.innerRadius=void 0,this.outerRadius=void 0,this.offsetX=void 0,this.offsetY=void 0}linkScales(){}parse(t,e){let i=this.getDataset().data,a=this._cachedMeta;if(!1===this._parsing)a._parsed=i;else{let n,r,o=t=>+i[t];if((0,s.i)(i[t])){let{key:t="value"}=this._parsing;o=e=>+(0,s.f)(i[e],t)}for(n=t,r=t+e;n<r;++n)a._parsed[n]=o(n)}}_getRotation(){return(0,s.t)(this.options.rotation-90)}_getCircumference(){return(0,s.t)(this.options.circumference)}_getRotationExtents(){let t=s.T,e=-s.T;for(let i=0;i<this.chart.data.datasets.length;++i)if(this.chart.isDatasetVisible(i)&&this.chart.getDatasetMeta(i).type===this._type){let s=this.chart.getDatasetMeta(i).controller,a=s._getRotation(),n=s._getCircumference();t=Math.min(t,a),e=Math.max(e,a+n)}return{rotation:t,circumference:e-t}}update(t){let{chartArea:e}=this.chart,i=this._cachedMeta,a=i.data,n=this.getMaxBorderWidth()+this.getMaxOffset(a)+this.options.spacing,r=Math.max((Math.min(e.width,e.height)-n)/2,0),o=Math.min((0,s.m)(this.options.cutout,r),1),l=this._getRingWeight(this.index),{circumference:h,rotation:d}=this._getRotationExtents(),{ratioX:c,ratioY:u,offsetX:f,offsetY:g}=function(t,e,i){let a=1,n=1,r=0,o=0;if(e<s.T){let l=t+e,h=Math.cos(t),d=Math.sin(t),c=Math.cos(l),u=Math.sin(l),f=(e,a,n)=>(0,s.p)(e,t,l,!0)?1:Math.max(a,a*i,n,n*i),g=(e,a,n)=>(0,s.p)(e,t,l,!0)?-1:Math.min(a,a*i,n,n*i),p=f(0,h,c),m=f(s.H,d,u),b=g(s.P,h,c),x=g(s.P+s.H,d,u);a=(p-b)/2,n=(m-x)/2,r=-(p+b)/2,o=-(m+x)/2}return{ratioX:a,ratioY:n,offsetX:r,offsetY:o}}(d,h,o),p=Math.max(Math.min((e.width-n)/c,(e.height-n)/u)/2,0),m=(0,s.n)(this.options.radius,p),b=Math.max(m*o,0),x=(m-b)/this._getVisibleDatasetWeightTotal();this.offsetX=f*m,this.offsetY=g*m,i.total=this.calculateTotal(),this.outerRadius=m-x*this._getRingWeightOffset(this.index),this.innerRadius=Math.max(this.outerRadius-x*l,0),this.updateElements(a,0,a.length,t)}_circumference(t,e){let i=this.options,a=this._cachedMeta,n=this._getCircumference();return e&&i.animation.animateRotate||!this.chart.getDataVisibility(t)||null===a._parsed[t]||a.data[t].hidden?0:this.calculateCircumference(a._parsed[t]*n/s.T)}updateElements(t,e,i,s){let a,n="reset"===s,r=this.chart,o=r.chartArea,l=r.options.animation,h=(o.left+o.right)/2,d=(o.top+o.bottom)/2,c=n&&l.animateScale,u=c?0:this.innerRadius,f=c?0:this.outerRadius,{sharedOptions:g,includeOptions:p}=this._getSharedOptions(e,s),m=this._getRotation();for(a=0;a<e;++a)m+=this._circumference(a,n);for(a=e;a<e+i;++a){let e=this._circumference(a,n),i=t[a],r={x:h+this.offsetX,y:d+this.offsetY,startAngle:m,endAngle:m+e,circumference:e,outerRadius:f,innerRadius:u};p&&(r.options=g||this.resolveDataElementOptions(a,i.active?"active":s)),m+=e,this.updateElement(i,a,r,s)}}calculateTotal(){let t,e=this._cachedMeta,i=e.data,s=0;for(t=0;t<i.length;t++){let a=e._parsed[t];null!==a&&!isNaN(a)&&this.chart.getDataVisibility(t)&&!i[t].hidden&&(s+=Math.abs(a))}return s}calculateCircumference(t){let e=this._cachedMeta.total;return e>0&&!isNaN(t)?s.T*(Math.abs(t)/e):0}getLabelAndValue(t){let e=this._cachedMeta,i=this.chart,a=i.data.labels||[],n=(0,s.o)(e._parsed[t],i.options.locale);return{label:a[t]||"",value:n}}getMaxBorderWidth(t){let e,i,s,a,n,r=0,o=this.chart;if(!t){for(e=0,i=o.data.datasets.length;e<i;++e)if(o.isDatasetVisible(e)){t=(s=o.getDatasetMeta(e)).data,a=s.controller;break}}if(!t)return 0;for(e=0,i=t.length;e<i;++e)"inner"!==(n=a.resolveDataElementOptions(e)).borderAlign&&(r=Math.max(r,n.borderWidth||0,n.hoverBorderWidth||0));return r}getMaxOffset(t){let e=0;for(let i=0,s=t.length;i<s;++i){let t=this.resolveDataElementOptions(i);e=Math.max(e,t.offset||0,t.hoverOffset||0)}return e}_getRingWeightOffset(t){let e=0;for(let i=0;i<t;++i)this.chart.isDatasetVisible(i)&&(e+=this._getRingWeight(i));return e}_getRingWeight(t){return Math.max((0,s.v)(this.chart.data.datasets[t].weight,1),0)}_getVisibleDatasetWeightTotal(){return this._getRingWeightOffset(this.chart.data.datasets.length)||1}}class T extends v{static id="line";static defaults={datasetElementType:"line",dataElementType:"point",showLine:!0,spanGaps:!1};static overrides={scales:{_index_:{type:"category"},_value_:{type:"linear"}}};initialize(){this.enableOptionSharing=!0,this.supportsDecimation=!0,super.initialize()}update(t){let e=this._cachedMeta,{dataset:i,data:a=[],_dataset:n}=e,r=this.chart._animationsDisabled,{start:o,count:l}=(0,s.q)(e,a,r);this._drawStart=o,this._drawCount=l,(0,s.w)(e)&&(o=0,l=a.length),i._chart=this.chart,i._datasetIndex=this.index,i._decimated=!!n._decimated,i.points=a;let h=this.resolveDatasetElementOptions(t);this.options.showLine||(h.borderWidth=0),h.segment=this.options.segment,this.updateElement(i,void 0,{animated:!r,options:h},t),this.updateElements(a,o,l,t)}updateElements(t,e,i,a){let n="reset"===a,{iScale:r,vScale:o,_stacked:l,_dataset:h}=this._cachedMeta,{sharedOptions:d,includeOptions:c}=this._getSharedOptions(e,a),u=r.axis,f=o.axis,{spanGaps:g,segment:p}=this.options,m=(0,s.x)(g)?g:Number.POSITIVE_INFINITY,b=this.chart._animationsDisabled||n||"none"===a,x=e+i,_=t.length,y=e>0&&this.getParsed(e-1);for(let i=0;i<_;++i){let g=t[i],_=b?g:{};if(i<e||i>=x){_.skip=!0;continue}let v=this.getParsed(i),M=(0,s.k)(v[f]),w=_[u]=r.getPixelForValue(v[u],i),k=_[f]=n||M?o.getBasePixel():o.getPixelForValue(l?this.applyStack(o,v,l):v[f],i);_.skip=isNaN(w)||isNaN(k)||M,_.stop=i>0&&Math.abs(v[u]-y[u])>m,p&&(_.parsed=v,_.raw=h.data[i]),c&&(_.options=d||this.resolveDataElementOptions(i,g.active?"active":a)),b||this.updateElement(g,i,_,a),y=v}}getMaxOverflow(){let t=this._cachedMeta,e=t.dataset,i=e.options&&e.options.borderWidth||0,s=t.data||[];return s.length?Math.max(i,s[0].size(this.resolveDataElementOptions(0)),s[s.length-1].size(this.resolveDataElementOptions(s.length-1)))/2:i}draw(){let t=this._cachedMeta;t.dataset.updateControlPoints(this.chart.chartArea,t.iScale.axis),super.draw()}}class A extends v{static id="polarArea";static defaults={dataElementType:"arc",animation:{animateRotate:!0,animateScale:!0},animations:{numbers:{type:"number",properties:["x","y","startAngle","endAngle","innerRadius","outerRadius"]}},indexAxis:"r",startAngle:0};static overrides={aspectRatio:1,plugins:{legend:{labels:{generateLabels(t){let e=t.data;if(e.labels.length&&e.datasets.length){let{labels:{pointStyle:i,color:s}}=t.legend.options;return e.labels.map((e,a)=>{let n=t.getDatasetMeta(0).controller.getStyle(a);return{text:e,fillStyle:n.backgroundColor,strokeStyle:n.borderColor,fontColor:s,lineWidth:n.borderWidth,pointStyle:i,hidden:!t.getDataVisibility(a),index:a}})}return[]}},onClick(t,e,i){i.chart.toggleDataVisibility(e.index),i.chart.update()}}},scales:{r:{type:"radialLinear",angleLines:{display:!1},beginAtZero:!0,grid:{circular:!0},pointLabels:{display:!1},startAngle:0}}};constructor(t,e){super(t,e),this.innerRadius=void 0,this.outerRadius=void 0}getLabelAndValue(t){let e=this._cachedMeta,i=this.chart,a=i.data.labels||[],n=(0,s.o)(e._parsed[t].r,i.options.locale);return{label:a[t]||"",value:n}}parseObjectData(t,e,i,a){return s.y.bind(this)(t,e,i,a)}update(t){let e=this._cachedMeta.data;this._updateRadius(),this.updateElements(e,0,e.length,t)}getMinMax(){let t=this._cachedMeta,e={min:Number.POSITIVE_INFINITY,max:Number.NEGATIVE_INFINITY};return t.data.forEach((t,i)=>{let s=this.getParsed(i).r;!isNaN(s)&&this.chart.getDataVisibility(i)&&(s<e.min&&(e.min=s),s>e.max&&(e.max=s))}),e}_updateRadius(){let t=this.chart,e=t.chartArea,i=t.options,s=Math.max(Math.min(e.right-e.left,e.bottom-e.top)/2,0),a=Math.max(i.cutoutPercentage?s/100*i.cutoutPercentage:1,0),n=(s-a)/t.getVisibleDatasetCount();this.outerRadius=s-n*this.index,this.innerRadius=this.outerRadius-n}updateElements(t,e,i,a){let n,r="reset"===a,o=this.chart,l=o.options.animation,h=this._cachedMeta.rScale,d=h.xCenter,c=h.yCenter,u=h.getIndexAngle(0)-.5*s.P,f=u,g=360/this.countVisibleElements();for(n=0;n<e;++n)f+=this._computeAngle(n,a,g);for(n=e;n<e+i;n++){let e=t[n],i=f,s=f+this._computeAngle(n,a,g),p=o.getDataVisibility(n)?h.getDistanceFromCenterForValue(this.getParsed(n).r):0;f=s,r&&(l.animateScale&&(p=0),l.animateRotate&&(i=s=u));let m={x:d,y:c,innerRadius:0,outerRadius:p,startAngle:i,endAngle:s,options:this.resolveDataElementOptions(n,e.active?"active":a)};this.updateElement(e,n,m,a)}}countVisibleElements(){let t=this._cachedMeta,e=0;return t.data.forEach((t,i)=>{!isNaN(this.getParsed(i).r)&&this.chart.getDataVisibility(i)&&e++}),e}_computeAngle(t,e,i){return this.chart.getDataVisibility(t)?(0,s.t)(this.resolveDataElementOptions(t,e).angle||i):0}}class E extends C{static id="pie";static defaults={cutout:0,rotation:0,circumference:360,radius:"100%"}}class L extends v{static id="radar";static defaults={datasetElementType:"line",dataElementType:"point",indexAxis:"r",showLine:!0,elements:{line:{fill:"start"}}};static overrides={aspectRatio:1,scales:{r:{type:"radialLinear"}}};getLabelAndValue(t){let e=this._cachedMeta.vScale,i=this.getParsed(t);return{label:e.getLabels()[t],value:""+e.getLabelForValue(i[e.axis])}}parseObjectData(t,e,i,a){return s.y.bind(this)(t,e,i,a)}update(t){let e=this._cachedMeta,i=e.dataset,s=e.data||[],a=e.iScale.getLabels();if(i.points=s,"resize"!==t){let e=this.resolveDatasetElementOptions(t);this.options.showLine||(e.borderWidth=0);let n={_loop:!0,_fullLoop:a.length===s.length,options:e};this.updateElement(i,void 0,n,t)}this.updateElements(s,0,s.length,t)}updateElements(t,e,i,s){let a=this._cachedMeta.rScale,n="reset"===s;for(let r=e;r<e+i;r++){let e=t[r],i=this.resolveDataElementOptions(r,e.active?"active":s),o=a.getPointPositionForValue(r,this.getParsed(r).r),l=n?a.xCenter:o.x,h=n?a.yCenter:o.y,d={x:l,y:h,angle:o.angle,skip:isNaN(l)||isNaN(h),options:i};this.updateElement(e,r,d,s)}}}class F extends v{static id="scatter";static defaults={datasetElementType:!1,dataElementType:"point",showLine:!1,fill:!1};static overrides={interaction:{mode:"point"},scales:{x:{type:"linear"},y:{type:"linear"}}};getLabelAndValue(t){let e=this._cachedMeta,i=this.chart.data.labels||[],{xScale:s,yScale:a}=e,n=this.getParsed(t),r=s.getLabelForValue(n.x),o=a.getLabelForValue(n.y);return{label:i[t]||"",value:"("+r+", "+o+")"}}update(t){let e=this._cachedMeta,{data:i=[]}=e,a=this.chart._animationsDisabled,{start:n,count:r}=(0,s.q)(e,i,a);if(this._drawStart=n,this._drawCount=r,(0,s.w)(e)&&(n=0,r=i.length),this.options.showLine){this.datasetElementType||this.addElements();let{dataset:s,_dataset:n}=e;s._chart=this.chart,s._datasetIndex=this.index,s._decimated=!!n._decimated,s.points=i;let r=this.resolveDatasetElementOptions(t);r.segment=this.options.segment,this.updateElement(s,void 0,{animated:!a,options:r},t)}else this.datasetElementType&&(delete e.dataset,this.datasetElementType=!1);this.updateElements(i,n,r,t)}addElements(){let{showLine:t}=this.options;!this.datasetElementType&&t&&(this.datasetElementType=this.chart.registry.getElement("line")),super.addElements()}updateElements(t,e,i,a){let n="reset"===a,{iScale:r,vScale:o,_stacked:l,_dataset:h}=this._cachedMeta,d=this.resolveDataElementOptions(e,a),c=this.getSharedOptions(d),u=this.includeOptions(a,c),f=r.axis,g=o.axis,{spanGaps:p,segment:m}=this.options,b=(0,s.x)(p)?p:Number.POSITIVE_INFINITY,x=this.chart._animationsDisabled||n||"none"===a,_=e>0&&this.getParsed(e-1);for(let d=e;d<e+i;++d){let e=t[d],i=this.getParsed(d),p=x?e:{},y=(0,s.k)(i[g]),v=p[f]=r.getPixelForValue(i[f],d),M=p[g]=n||y?o.getBasePixel():o.getPixelForValue(l?this.applyStack(o,i,l):i[g],d);p.skip=isNaN(v)||isNaN(M)||y,p.stop=d>0&&Math.abs(i[f]-_[f])>b,m&&(p.parsed=i,p.raw=h.data[d]),u&&(p.options=c||this.resolveDataElementOptions(d,e.active?"active":a)),x||this.updateElement(e,d,p,a),_=i}this.updateSharedOptions(c,a,d)}getMaxOverflow(){let t=this._cachedMeta,e=t.data||[];if(!this.options.showLine){let t=0;for(let i=e.length-1;i>=0;--i)t=Math.max(t,e[i].size(this.resolveDataElementOptions(i))/2);return t>0&&t}let i=t.dataset,s=i.options&&i.options.borderWidth||0;return e.length?Math.max(s,e[0].size(this.resolveDataElementOptions(0)),e[e.length-1].size(this.resolveDataElementOptions(e.length-1)))/2:s}}function R(){throw Error("This method is not implemented: Check that a complete date adapter is provided.")}class I{static override(t){Object.assign(I.prototype,t)}options;constructor(t){this.options=t||{}}init(){}formats(){return R()}parse(){return R()}format(){return R()}add(){return R()}diff(){return R()}startOf(){return R()}endOf(){return R()}}var z={_date:I};function V(t,e,i,a,n){let r=t.getSortedVisibleDatasetMetas(),o=i[e];for(let t=0,i=r.length;t<i;++t){let{index:i,data:l}=r[t],{lo:h,hi:d}=function(t,e,i,a){let{controller:n,data:r,_sorted:o}=t,l=n._cachedMeta.iScale,h=t.dataset&&t.dataset.options?t.dataset.options.spanGaps:null;if(l&&e===l.axis&&"r"!==e&&o&&r.length){let o=l._reversePixels?s.A:s.B;if(a){if(n._sharedOptions){let t=r[0],s="function"==typeof t.getRange&&t.getRange(e);if(s){let t=o(r,e,i-s),a=o(r,e,i+s);return{lo:t.lo,hi:a.hi}}}}else{let a=o(r,e,i);if(h){let{vScale:e}=n._cachedMeta,{_parsed:i}=t,r=i.slice(0,a.lo+1).reverse().findIndex(t=>!(0,s.k)(t[e.axis]));a.lo-=Math.max(0,r);let o=i.slice(a.hi).findIndex(t=>!(0,s.k)(t[e.axis]));a.hi+=Math.max(0,o)}return a}}return{lo:0,hi:r.length-1}}(r[t],e,o,n);for(let t=h;t<=d;++t){let e=l[t];e.skip||a(e,i,t)}}}function N(t,e,i,a,n){let r=[];return(n||t.isPointInArea(e))&&V(t,i,e,function(i,o,l){(n||(0,s.C)(i,t.chartArea,0))&&i.inRange(e.x,e.y,a)&&r.push({element:i,datasetIndex:o,index:l})},!0),r}function B(t,e,i,a,n,r){let o;return r||t.isPointInArea(e)?"r"!==i||a?function(t,e,i,s,a,n){let r=[],o=function(t){let e=-1!==t.indexOf("x"),i=-1!==t.indexOf("y");return function(t,s){return Math.sqrt(Math.pow(e?Math.abs(t.x-s.x):0,2)+Math.pow(i?Math.abs(t.y-s.y):0,2))}}(i),l=Number.POSITIVE_INFINITY;return V(t,i,e,function(i,h,d){let c=i.inRange(e.x,e.y,a);if(s&&!c)return;let u=i.getCenterPoint(a);if(!(n||t.isPointInArea(u))&&!c)return;let f=o(e,u);f<l?(r=[{element:i,datasetIndex:h,index:d}],l=f):f===l&&r.push({element:i,datasetIndex:h,index:d})}),r}(t,e,i,a,n,r):(o=[],V(t,i,e,function(t,i,a){let{startAngle:r,endAngle:l}=t.getProps(["startAngle","endAngle"],n),{angle:h}=(0,s.D)(t,{x:e.x,y:e.y});(0,s.p)(h,r,l)&&o.push({element:t,datasetIndex:i,index:a})}),o):[]}function H(t,e,i,s,a){let n=[],r="x"===i?"inXRange":"inYRange",o=!1;return(V(t,i,e,(t,s,l)=>{t[r]&&t[r](e[i],a)&&(n.push({element:t,datasetIndex:s,index:l}),o=o||t.inRange(e.x,e.y,a))}),s&&!o)?[]:n}var j={modes:{index(t,e,i,a){let n=(0,s.z)(e,t),r=i.axis||"x",o=i.includeInvisible||!1,l=i.intersect?N(t,n,r,a,o):B(t,n,r,!1,a,o),h=[];return l.length?(t.getSortedVisibleDatasetMetas().forEach(t=>{let e=l[0].index,i=t.data[e];i&&!i.skip&&h.push({element:i,datasetIndex:t.index,index:e})}),h):[]},dataset(t,e,i,a){let n=(0,s.z)(e,t),r=i.axis||"xy",o=i.includeInvisible||!1,l=i.intersect?N(t,n,r,a,o):B(t,n,r,!1,a,o);if(l.length>0){let e=l[0].datasetIndex,i=t.getDatasetMeta(e).data;l=[];for(let t=0;t<i.length;++t)l.push({element:i[t],datasetIndex:e,index:t})}return l},point(t,e,i,a){let n=(0,s.z)(e,t);return N(t,n,i.axis||"xy",a,i.includeInvisible||!1)},nearest(t,e,i,a){let n=(0,s.z)(e,t),r=i.axis||"xy",o=i.includeInvisible||!1;return B(t,n,r,i.intersect,a,o)},x(t,e,i,a){let n=(0,s.z)(e,t);return H(t,n,"x",i.intersect,a)},y(t,e,i,a){let n=(0,s.z)(e,t);return H(t,n,"y",i.intersect,a)}}};let W=["left","top","right","bottom"];function $(t,e){return t.filter(t=>t.pos===e)}function Y(t,e){return t.filter(t=>-1===W.indexOf(t.pos)&&t.box.axis===e)}function U(t,e){return t.sort((t,i)=>{let s=e?i:t,a=e?t:i;return s.weight===a.weight?s.index-a.index:s.weight-a.weight})}function Q(t,e,i,s){return Math.max(t[i],e[i])+Math.max(t[s],e[s])}function X(t,e){t.top=Math.max(t.top,e.top),t.left=Math.max(t.left,e.left),t.bottom=Math.max(t.bottom,e.bottom),t.right=Math.max(t.right,e.right)}function q(t,e,i,a){let n,r,o,l,h,d,c=[];for(n=0,r=t.length,h=0;n<r;++n){(l=(o=t[n]).box).update(o.width||e.w,o.height||e.h,function(t,e){let i=e.maxPadding;var s=t?["left","right"]:["top","bottom"];let a={left:0,top:0,right:0,bottom:0};return s.forEach(t=>{a[t]=Math.max(e[t],i[t])}),a}(o.horizontal,e));let{same:r,other:u}=function(t,e,i,a){let{pos:n,box:r}=i,o=t.maxPadding;if(!(0,s.i)(n)){i.size&&(t[n]-=i.size);let e=a[i.stack]||{size:0,count:1};e.size=Math.max(e.size,i.horizontal?r.height:r.width),i.size=e.size/e.count,t[n]+=i.size}r.getPadding&&X(o,r.getPadding());let l=Math.max(0,e.outerWidth-Q(o,t,"left","right")),h=Math.max(0,e.outerHeight-Q(o,t,"top","bottom")),d=l!==t.w,c=h!==t.h;return t.w=l,t.h=h,i.horizontal?{same:d,other:c}:{same:c,other:d}}(e,i,o,a);h|=r&&c.length,d=d||u,l.fullSize||c.push(o)}return h&&q(c,e,i,a)||d}function G(t,e,i,s,a){t.top=i,t.left=e,t.right=e+s,t.bottom=i+a,t.width=s,t.height=a}function K(t,e,i,a){let n=i.padding,{x:r,y:o}=e;for(let l of t){let t=l.box,h=a[l.stack]||{count:1,placed:0,weight:1},d=l.stackWeight/h.weight||1;if(l.horizontal){let a=e.w*d,r=h.size||t.height;(0,s.h)(h.start)&&(o=h.start),t.fullSize?G(t,n.left,o,i.outerWidth-n.right-n.left,r):G(t,e.left+h.placed,o,a,r),h.start=o,h.placed+=a,o=t.bottom}else{let a=e.h*d,o=h.size||t.width;(0,s.h)(h.start)&&(r=h.start),t.fullSize?G(t,r,n.top,o,i.outerHeight-n.bottom-n.top):G(t,r,e.top+h.placed,o,a),h.start=r,h.placed+=a,r=t.right}}e.x=r,e.y=o}var Z={addBox(t,e){t.boxes||(t.boxes=[]),e.fullSize=e.fullSize||!1,e.position=e.position||"top",e.weight=e.weight||0,e._layers=e._layers||function(){return[{z:0,draw(t){e.draw(t)}}]},t.boxes.push(e)},removeBox(t,e){let i=t.boxes?t.boxes.indexOf(e):-1;-1!==i&&t.boxes.splice(i,1)},configure(t,e,i){e.fullSize=i.fullSize,e.position=i.position,e.weight=i.weight},update(t,e,i,a){if(!t)return;let n=(0,s.E)(t.options.layout.padding),r=Math.max(e-n.width,0),o=Math.max(i-n.height,0),l=function(t){let e=function(t){let e,i,s,a,n,r,o=[];for(e=0,i=(t||[]).length;e<i;++e)s=t[e],({position:a,options:{stack:n,stackWeight:r=1}}=s),o.push({index:e,box:s,pos:a,horizontal:s.isHorizontal(),weight:s.weight,stack:n&&a+n,stackWeight:r});return o}(t),i=U(e.filter(t=>t.box.fullSize),!0),s=U($(e,"left"),!0),a=U($(e,"right")),n=U($(e,"top"),!0),r=U($(e,"bottom")),o=Y(e,"x"),l=Y(e,"y");return{fullSize:i,leftAndTop:s.concat(n),rightAndBottom:a.concat(l).concat(r).concat(o),chartArea:$(e,"chartArea"),vertical:s.concat(a).concat(l),horizontal:n.concat(r).concat(o)}}(t.boxes),h=l.vertical,d=l.horizontal;(0,s.F)(t.boxes,t=>{"function"==typeof t.beforeLayout&&t.beforeLayout()});let c=Object.freeze({outerWidth:e,outerHeight:i,padding:n,availableWidth:r,availableHeight:o,vBoxMaxWidth:r/2/(h.reduce((t,e)=>e.box.options&&!1===e.box.options.display?t:t+1,0)||1),hBoxMaxHeight:o/2}),u=Object.assign({},n);X(u,(0,s.E)(a));let f=Object.assign({maxPadding:u,w:r,h:o,x:n.left,y:n.top},n),g=function(t,e){let i,s,a,n=function(t){let e={};for(let i of t){let{stack:t,pos:s,stackWeight:a}=i;if(!t||!W.includes(s))continue;let n=e[t]||(e[t]={count:0,placed:0,weight:0,size:0});n.count++,n.weight+=a}return e}(t),{vBoxMaxWidth:r,hBoxMaxHeight:o}=e;for(i=0,s=t.length;i<s;++i){let{fullSize:s}=(a=t[i]).box,l=n[a.stack],h=l&&a.stackWeight/l.weight;a.horizontal?(a.width=h?h*r:s&&e.availableWidth,a.height=o):(a.width=r,a.height=h?h*o:s&&e.availableHeight)}return n}(h.concat(d),c);q(l.fullSize,f,c,g),q(h,f,c,g),q(d,f,c,g)&&q(h,f,c,g);let p=f.maxPadding;function m(t){let e=Math.max(p[t]-f[t],0);return f[t]+=e,e}f.y+=m("top"),f.x+=m("left"),m("right"),m("bottom"),K(l.leftAndTop,f,c,g),f.x+=f.w,f.y+=f.h,K(l.rightAndBottom,f,c,g),t.chartArea={left:f.left,top:f.top,right:f.left+f.w,bottom:f.top+f.h,height:f.h,width:f.w},(0,s.F)(l.chartArea,e=>{let i=e.box;Object.assign(i,t.chartArea),i.update(f.w,f.h,{left:0,top:0,right:0,bottom:0})})}};class J{acquireContext(t,e){}releaseContext(t){return!1}addEventListener(t,e,i){}removeEventListener(t,e,i){}getDevicePixelRatio(){return 1}getMaximumSize(t,e,i,s){return e=Math.max(0,e||t.width),i=i||t.height,{width:e,height:Math.max(0,s?Math.floor(e/s):i)}}isAttached(t){return!0}updateConfig(t){}}class tt extends J{acquireContext(t){return t&&t.getContext&&t.getContext("2d")||null}updateConfig(t){t.options.animation=!1}}let te="$chartjs",ti={touchstart:"mousedown",touchmove:"mousemove",touchend:"mouseup",pointerenter:"mouseenter",pointerdown:"mousedown",pointermove:"mousemove",pointerup:"mouseup",pointerleave:"mouseout",pointerout:"mouseout"},ts=t=>null===t||""===t,ta=!!s.K&&{passive:!0};function tn(t,e){for(let i of t)if(i===e||i.contains(e))return!0}function tr(t,e,i){let s=t.canvas,a=new MutationObserver(t=>{let e=!1;for(let i of t)e=(e=e||tn(i.addedNodes,s))&&!tn(i.removedNodes,s);e&&i()});return a.observe(document,{childList:!0,subtree:!0}),a}function to(t,e,i){let s=t.canvas,a=new MutationObserver(t=>{let e=!1;for(let i of t)e=(e=e||tn(i.removedNodes,s))&&!tn(i.addedNodes,s);e&&i()});return a.observe(document,{childList:!0,subtree:!0}),a}let tl=new Map,th=0;function td(){let t=window.devicePixelRatio;t!==th&&(th=t,tl.forEach((e,i)=>{i.currentDevicePixelRatio!==t&&e()}))}function tc(t,e,i){let a=t.canvas,n=a&&(0,s.I)(a);if(!n)return;let r=(0,s.L)((t,e)=>{let s=n.clientWidth;i(t,e),s<n.clientWidth&&i()},window),o=new ResizeObserver(t=>{let e=t[0],i=e.contentRect.width,s=e.contentRect.height;(0!==i||0!==s)&&r(i,s)});return o.observe(n),tl.size||window.addEventListener("resize",td),tl.set(t,r),o}function tu(t,e,i){i&&i.disconnect(),"resize"===e&&(tl.delete(t),tl.size||window.removeEventListener("resize",td))}function tf(t,e,i){let a=t.canvas,n=(0,s.L)(e=>{null!==t.ctx&&i(function(t,e){let i=ti[t.type]||t.type,{x:a,y:n}=(0,s.z)(t,e);return{type:i,chart:e,native:t,x:void 0!==a?a:null,y:void 0!==n?n:null}}(e,t))},t);return a&&a.addEventListener(e,n,ta),n}class tg extends J{acquireContext(t,e){let i=t&&t.getContext&&t.getContext("2d");return i&&i.canvas===t?(!function(t,e){let i=t.style,a=t.getAttribute("height"),n=t.getAttribute("width");if(t[te]={initial:{height:a,width:n,style:{display:i.display,height:i.height,width:i.width}}},i.display=i.display||"block",i.boxSizing=i.boxSizing||"border-box",ts(n)){let e=(0,s.J)(t,"width");void 0!==e&&(t.width=e)}if(ts(a))if(""===t.style.height)t.height=t.width/(e||2);else{let e=(0,s.J)(t,"height");void 0!==e&&(t.height=e)}}(t,e),i):null}releaseContext(t){let e=t.canvas;if(!e[te])return!1;let i=e[te].initial;["height","width"].forEach(t=>{let a=i[t];(0,s.k)(a)?e.removeAttribute(t):e.setAttribute(t,a)});let a=i.style||{};return Object.keys(a).forEach(t=>{e.style[t]=a[t]}),e.width=e.width,delete e[te],!0}addEventListener(t,e,i){this.removeEventListener(t,e);let s=t.$proxies||(t.$proxies={}),a={attach:tr,detach:to,resize:tc}[e]||tf;s[e]=a(t,e,i)}removeEventListener(t,e){let i=t.$proxies||(t.$proxies={}),s=i[e];s&&((({attach:tu,detach:tu,resize:tu})[e]||function(t,e,i){t&&t.canvas&&t.canvas.removeEventListener(e,i,ta)})(t,e,s),i[e]=void 0)}getDevicePixelRatio(){return window.devicePixelRatio}getMaximumSize(t,e,i,a){return(0,s.G)(t,e,i,a)}isAttached(t){let e=t&&(0,s.I)(t);return!!(e&&e.isConnected)}}class tp{static defaults={};static defaultRoutes=void 0;x;y;active=!1;options;$animations;tooltipPosition(t){let{x:e,y:i}=this.getProps(["x","y"],t);return{x:e,y:i}}hasValue(){return(0,s.x)(this.x)&&(0,s.x)(this.y)}getProps(t,e){let i=this.$animations;if(!e||!i)return this;let s={};return t.forEach(t=>{s[t]=i[t]&&i[t].active()?i[t]._to:this[t]}),s}}function tm(t,e,i,a,n){let r,o,l,h=(0,s.v)(a,0),d=Math.min((0,s.v)(n,t.length),t.length),c=0;for(i=Math.ceil(i),n&&(i=(r=n-a)/Math.floor(r/i)),l=h;l<0;)l=Math.round(h+ ++c*i);for(o=Math.max(h,0);o<d;o++)o===l&&(e.push(t[o]),l=Math.round(h+ ++c*i))}let tb=t=>"left"===t?"right":"right"===t?"left":t,tx=(t,e,i)=>"top"===e||"left"===e?t[e]+i:t[e]-i,t_=(t,e)=>Math.min(e||t,t);function ty(t,e){let i=[],s=t.length/e,a=t.length,n=0;for(;n<a;n+=s)i.push(t[Math.floor(n)]);return i}function tv(t){return t.drawTicks?t.tickLength:0}function tM(t,e){if(!t.display)return 0;let i=(0,s.a0)(t.font,e),a=(0,s.E)(t.padding);return((0,s.b)(t.text)?t.text.length:1)*i.lineHeight+a.height}class tw extends tp{constructor(t){super(),this.id=t.id,this.type=t.type,this.options=void 0,this.ctx=t.ctx,this.chart=t.chart,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this._margins={left:0,right:0,top:0,bottom:0},this.maxWidth=void 0,this.maxHeight=void 0,this.paddingTop=void 0,this.paddingBottom=void 0,this.paddingLeft=void 0,this.paddingRight=void 0,this.axis=void 0,this.labelRotation=void 0,this.min=void 0,this.max=void 0,this._range=void 0,this.ticks=[],this._gridLineItems=null,this._labelItems=null,this._labelSizes=null,this._length=0,this._maxLength=0,this._longestTextCache={},this._startPixel=void 0,this._endPixel=void 0,this._reversePixels=!1,this._userMax=void 0,this._userMin=void 0,this._suggestedMax=void 0,this._suggestedMin=void 0,this._ticksLength=0,this._borderValue=0,this._cache={},this._dataLimitsCached=!1,this.$context=void 0}init(t){this.options=t.setContext(this.getContext()),this.axis=t.axis,this._userMin=this.parse(t.min),this._userMax=this.parse(t.max),this._suggestedMin=this.parse(t.suggestedMin),this._suggestedMax=this.parse(t.suggestedMax)}parse(t,e){return t}getUserBounds(){let{_userMin:t,_userMax:e,_suggestedMin:i,_suggestedMax:a}=this;return t=(0,s.O)(t,Number.POSITIVE_INFINITY),e=(0,s.O)(e,Number.NEGATIVE_INFINITY),i=(0,s.O)(i,Number.POSITIVE_INFINITY),a=(0,s.O)(a,Number.NEGATIVE_INFINITY),{min:(0,s.O)(t,i),max:(0,s.O)(e,a),minDefined:(0,s.g)(t),maxDefined:(0,s.g)(e)}}getMinMax(t){let e,{min:i,max:a,minDefined:n,maxDefined:r}=this.getUserBounds();if(n&&r)return{min:i,max:a};let o=this.getMatchingVisibleMetas();for(let s=0,l=o.length;s<l;++s)e=o[s].controller.getMinMax(this,t),n||(i=Math.min(i,e.min)),r||(a=Math.max(a,e.max));return i=r&&i>a?a:i,a=n&&i>a?i:a,{min:(0,s.O)(i,(0,s.O)(a,i)),max:(0,s.O)(a,(0,s.O)(i,a))}}getPadding(){return{left:this.paddingLeft||0,top:this.paddingTop||0,right:this.paddingRight||0,bottom:this.paddingBottom||0}}getTicks(){return this.ticks}getLabels(){let t=this.chart.data;return this.options.labels||(this.isHorizontal()?t.xLabels:t.yLabels)||t.labels||[]}getLabelItems(t=this.chart.chartArea){return this._labelItems||(this._labelItems=this._computeLabelItems(t))}beforeLayout(){this._cache={},this._dataLimitsCached=!1}beforeUpdate(){(0,s.Q)(this.options.beforeUpdate,[this])}update(t,e,i){let{beginAtZero:a,grace:n,ticks:r}=this.options,o=r.sampleSize;this.beforeUpdate(),this.maxWidth=t,this.maxHeight=e,this._margins=i=Object.assign({left:0,right:0,top:0,bottom:0},i),this.ticks=null,this._labelSizes=null,this._gridLineItems=null,this._labelItems=null,this.beforeSetDimensions(),this.setDimensions(),this.afterSetDimensions(),this._maxLength=this.isHorizontal()?this.width+i.left+i.right:this.height+i.top+i.bottom,this._dataLimitsCached||(this.beforeDataLimits(),this.determineDataLimits(),this.afterDataLimits(),this._range=(0,s.R)(this,n,a),this._dataLimitsCached=!0),this.beforeBuildTicks(),this.ticks=this.buildTicks()||[],this.afterBuildTicks();let l=o<this.ticks.length;this._convertTicksToLabels(l?ty(this.ticks,o):this.ticks),this.configure(),this.beforeCalculateLabelRotation(),this.calculateLabelRotation(),this.afterCalculateLabelRotation(),r.display&&(r.autoSkip||"auto"===r.source)&&(this.ticks=function(t,e){let i=t.options.ticks,a=function(t){let e=t.options.offset,i=t._tickSize();return Math.floor(Math.min(t._length/i+ +!e,t._maxLength/i))}(t),n=Math.min(i.maxTicksLimit||a,a),r=i.major.enabled?function(t){let e,i,s=[];for(e=0,i=t.length;e<i;e++)t[e].major&&s.push(e);return s}(e):[],o=r.length,l=r[0],h=r[o-1],d=[];if(o>n)return function(t,e,i,s){let a,n=0,r=i[0];for(a=0,s=Math.ceil(s);a<t.length;a++)a===r&&(e.push(t[a]),r=i[++n*s])}(e,d,r,o/n),d;let c=function(t,e,i){let a=function(t){let e,i,s=t.length;if(s<2)return!1;for(i=t[0],e=1;e<s;++e)if(t[e]-t[e-1]!==i)return!1;return i}(t),n=e.length/i;if(!a)return Math.max(n,1);let r=(0,s.N)(a);for(let t=0,e=r.length-1;t<e;t++){let e=r[t];if(e>n)return e}return Math.max(n,1)}(r,e,n);if(o>0){let t,i,a=o>1?Math.round((h-l)/(o-1)):null;for(tm(e,d,c,(0,s.k)(a)?0:l-a,l),t=0,i=o-1;t<i;t++)tm(e,d,c,r[t],r[t+1]);return tm(e,d,c,h,(0,s.k)(a)?e.length:h+a),d}return tm(e,d,c),d}(this,this.ticks),this._labelSizes=null,this.afterAutoSkip()),l&&this._convertTicksToLabels(this.ticks),this.beforeFit(),this.fit(),this.afterFit(),this.afterUpdate()}configure(){let t,e,i=this.options.reverse;this.isHorizontal()?(t=this.left,e=this.right):(t=this.top,e=this.bottom,i=!i),this._startPixel=t,this._endPixel=e,this._reversePixels=i,this._length=e-t,this._alignToPixels=this.options.alignToPixels}afterUpdate(){(0,s.Q)(this.options.afterUpdate,[this])}beforeSetDimensions(){(0,s.Q)(this.options.beforeSetDimensions,[this])}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=0,this.right=this.width):(this.height=this.maxHeight,this.top=0,this.bottom=this.height),this.paddingLeft=0,this.paddingTop=0,this.paddingRight=0,this.paddingBottom=0}afterSetDimensions(){(0,s.Q)(this.options.afterSetDimensions,[this])}_callHooks(t){this.chart.notifyPlugins(t,this.getContext()),(0,s.Q)(this.options[t],[this])}beforeDataLimits(){this._callHooks("beforeDataLimits")}determineDataLimits(){}afterDataLimits(){this._callHooks("afterDataLimits")}beforeBuildTicks(){this._callHooks("beforeBuildTicks")}buildTicks(){return[]}afterBuildTicks(){this._callHooks("afterBuildTicks")}beforeTickToLabelConversion(){(0,s.Q)(this.options.beforeTickToLabelConversion,[this])}generateTickLabels(t){let e,i,a,n=this.options.ticks;for(e=0,i=t.length;e<i;e++)(a=t[e]).label=(0,s.Q)(n.callback,[a.value,e,t],this)}afterTickToLabelConversion(){(0,s.Q)(this.options.afterTickToLabelConversion,[this])}beforeCalculateLabelRotation(){(0,s.Q)(this.options.beforeCalculateLabelRotation,[this])}calculateLabelRotation(){let t,e,i,a=this.options,n=a.ticks,r=t_(this.ticks.length,a.ticks.maxTicksLimit),o=n.minRotation||0,l=n.maxRotation,h=o;if(!this._isVisible()||!n.display||o>=l||r<=1||!this.isHorizontal()){this.labelRotation=o;return}let d=this._getLabelSizes(),c=d.widest.width,u=d.highest.height,f=(0,s.S)(this.chart.width-c,0,this.maxWidth);c+6>(t=a.offset?this.maxWidth/r:f/(r-1))&&(t=f/(r-(a.offset?.5:1)),e=this.maxHeight-tv(a.grid)-n.padding-tM(a.title,this.chart.options.font),i=Math.sqrt(c*c+u*u),h=Math.max(o,Math.min(l,h=(0,s.U)(Math.min(Math.asin((0,s.S)((d.highest.height+6)/t,-1,1)),Math.asin((0,s.S)(e/i,-1,1))-Math.asin((0,s.S)(u/i,-1,1))))))),this.labelRotation=h}afterCalculateLabelRotation(){(0,s.Q)(this.options.afterCalculateLabelRotation,[this])}afterAutoSkip(){}beforeFit(){(0,s.Q)(this.options.beforeFit,[this])}fit(){let t={width:0,height:0},{chart:e,options:{ticks:i,title:a,grid:n}}=this,r=this._isVisible(),o=this.isHorizontal();if(r){let r=tM(a,e.options.font);if(o?(t.width=this.maxWidth,t.height=tv(n)+r):(t.height=this.maxHeight,t.width=tv(n)+r),i.display&&this.ticks.length){let{first:e,last:a,widest:n,highest:r}=this._getLabelSizes(),l=2*i.padding,h=(0,s.t)(this.labelRotation),d=Math.cos(h),c=Math.sin(h);if(o){let e=i.mirror?0:c*n.width+d*r.height;t.height=Math.min(this.maxHeight,t.height+e+l)}else{let e=i.mirror?0:d*n.width+c*r.height;t.width=Math.min(this.maxWidth,t.width+e+l)}this._calculatePadding(e,a,c,d)}}this._handleMargins(),o?(this.width=this._length=e.width-this._margins.left-this._margins.right,this.height=t.height):(this.width=t.width,this.height=this._length=e.height-this._margins.top-this._margins.bottom)}_calculatePadding(t,e,i,s){let{ticks:{align:a,padding:n},position:r}=this.options,o=0!==this.labelRotation,l="top"!==r&&"x"===this.axis;if(this.isHorizontal()){let r=this.getPixelForTick(0)-this.left,h=this.right-this.getPixelForTick(this.ticks.length-1),d=0,c=0;o?l?(d=s*t.width,c=i*e.height):(d=i*t.height,c=s*e.width):"start"===a?c=e.width:"end"===a?d=t.width:"inner"!==a&&(d=t.width/2,c=e.width/2),this.paddingLeft=Math.max((d-r+n)*this.width/(this.width-r),0),this.paddingRight=Math.max((c-h+n)*this.width/(this.width-h),0)}else{let i=e.height/2,s=t.height/2;"start"===a?(i=0,s=t.height):"end"===a&&(i=e.height,s=0),this.paddingTop=i+n,this.paddingBottom=s+n}}_handleMargins(){this._margins&&(this._margins.left=Math.max(this.paddingLeft,this._margins.left),this._margins.top=Math.max(this.paddingTop,this._margins.top),this._margins.right=Math.max(this.paddingRight,this._margins.right),this._margins.bottom=Math.max(this.paddingBottom,this._margins.bottom))}afterFit(){(0,s.Q)(this.options.afterFit,[this])}isHorizontal(){let{axis:t,position:e}=this.options;return"top"===e||"bottom"===e||"x"===t}isFullSize(){return this.options.fullSize}_convertTicksToLabels(t){let e,i;for(this.beforeTickToLabelConversion(),this.generateTickLabels(t),e=0,i=t.length;e<i;e++)(0,s.k)(t[e].label)&&(t.splice(e,1),i--,e--);this.afterTickToLabelConversion()}_getLabelSizes(){let t=this._labelSizes;if(!t){let e=this.options.ticks.sampleSize,i=this.ticks;e<i.length&&(i=ty(i,e)),this._labelSizes=t=this._computeLabelSizes(i,i.length,this.options.ticks.maxTicksLimit)}return t}_computeLabelSizes(t,e,i){let a,n,r,o,l,h,d,c,u,f,g,{ctx:p,_longestTextCache:m}=this,b=[],x=[],_=Math.floor(e/t_(e,i)),y=0,v=0;for(a=0;a<e;a+=_){if(o=t[a].label,p.font=h=(l=this._resolveTickFontOptions(a)).string,d=m[h]=m[h]||{data:{},gc:[]},c=l.lineHeight,u=f=0,(0,s.k)(o)||(0,s.b)(o)){if((0,s.b)(o))for(n=0,r=o.length;n<r;++n)g=o[n],(0,s.k)(g)||(0,s.b)(g)||(u=(0,s.V)(p,d.data,d.gc,u,g),f+=c)}else u=(0,s.V)(p,d.data,d.gc,u,o),f=c;b.push(u),x.push(f),y=Math.max(u,y),v=Math.max(f,v)}(0,s.F)(m,t=>{let i,s=t.gc,a=s.length/2;if(a>e){for(i=0;i<a;++i)delete t.data[s[i]];s.splice(0,a)}});let M=b.indexOf(y),w=x.indexOf(v),k=t=>({width:b[t]||0,height:x[t]||0});return{first:k(0),last:k(e-1),widest:k(M),highest:k(w),widths:b,heights:x}}getLabelForValue(t){return t}getPixelForValue(t,e){return NaN}getValueForPixel(t){}getPixelForTick(t){let e=this.ticks;return t<0||t>e.length-1?null:this.getPixelForValue(e[t].value)}getPixelForDecimal(t){this._reversePixels&&(t=1-t);let e=this._startPixel+t*this._length;return(0,s.W)(this._alignToPixels?(0,s.X)(this.chart,e,0):e)}getDecimalForPixel(t){let e=(t-this._startPixel)/this._length;return this._reversePixels?1-e:e}getBasePixel(){return this.getPixelForValue(this.getBaseValue())}getBaseValue(){let{min:t,max:e}=this;return t<0&&e<0?e:t>0&&e>0?t:0}getContext(t){var e,i;let a=this.ticks||[];if(t>=0&&t<a.length){let i=a[t];return i.$context||(e=this.getContext(),i.$context=(0,s.j)(e,{tick:i,index:t,type:"tick"}))}return this.$context||(this.$context=(i=this.chart.getContext(),(0,s.j)(i,{scale:this,type:"scale"})))}_tickSize(){let t=this.options.ticks,e=(0,s.t)(this.labelRotation),i=Math.abs(Math.cos(e)),a=Math.abs(Math.sin(e)),n=this._getLabelSizes(),r=t.autoSkipPadding||0,o=n?n.widest.width+r:0,l=n?n.highest.height+r:0;return this.isHorizontal()?l*i>o*a?o/i:l/a:l*a<o*i?l/i:o/a}_isVisible(){let t=this.options.display;return"auto"!==t?!!t:this.getMatchingVisibleMetas().length>0}_computeGridLineItems(t){let e,i,a,n,r,o,l,h,d,c,u,f,g=this.axis,p=this.chart,m=this.options,{grid:b,position:x,border:_}=m,y=b.offset,v=this.isHorizontal(),M=this.ticks.length+ +!!y,w=tv(b),k=[],P=_.setContext(this.getContext()),S=P.display?P.width:0,D=S/2,O=function(t){return(0,s.X)(p,t,S)};if("top"===x)e=O(this.bottom),o=this.bottom-w,h=e-D,c=O(t.top)+D,f=t.bottom;else if("bottom"===x)e=O(this.top),c=t.top,f=O(t.bottom)-D,o=e+D,h=this.top+w;else if("left"===x)e=O(this.right),r=this.right-w,l=e-D,d=O(t.left)+D,u=t.right;else if("right"===x)e=O(this.left),d=t.left,u=O(t.right)-D,r=e+D,l=this.left+w;else if("x"===g){if("center"===x)e=O((t.top+t.bottom)/2+.5);else if((0,s.i)(x)){let t=Object.keys(x)[0],i=x[t];e=O(this.chart.scales[t].getPixelForValue(i))}c=t.top,f=t.bottom,h=(o=e+D)+w}else if("y"===g){if("center"===x)e=O((t.left+t.right)/2);else if((0,s.i)(x)){let t=Object.keys(x)[0],i=x[t];e=O(this.chart.scales[t].getPixelForValue(i))}l=(r=e-D)-w,d=t.left,u=t.right}let C=(0,s.v)(m.ticks.maxTicksLimit,M),T=Math.max(1,Math.ceil(M/C));for(i=0;i<M;i+=T){let t=this.getContext(i),e=b.setContext(t),g=_.setContext(t),m=e.lineWidth,x=e.color,M=g.dash||[],w=g.dashOffset,P=e.tickWidth,S=e.tickColor,D=e.tickBorderDash||[],O=e.tickBorderDashOffset;void 0!==(a=function(t,e,i){let s,a=t.ticks.length,n=Math.min(e,a-1),r=t._startPixel,o=t._endPixel,l=t.getPixelForTick(n);if(!i||(s=1===a?Math.max(l-r,o-l):0===e?(t.getPixelForTick(1)-l)/2:(l-t.getPixelForTick(n-1))/2,!((l+=n<e?s:-s)<r-1e-6)&&!(l>o+1e-6)))return l}(this,i,y))&&(n=(0,s.X)(p,a,m),v?r=l=d=u=n:o=h=c=f=n,k.push({tx1:r,ty1:o,tx2:l,ty2:h,x1:d,y1:c,x2:u,y2:f,width:m,color:x,borderDash:M,borderDashOffset:w,tickWidth:P,tickColor:S,tickBorderDash:D,tickBorderDashOffset:O}))}return this._ticksLength=M,this._borderValue=e,k}_computeLabelItems(t){let e,i,a,n,r,o,l,h,d,c,u,f=this.axis,g=this.options,{position:p,ticks:m}=g,b=this.isHorizontal(),x=this.ticks,{align:_,crossAlign:y,padding:v,mirror:M}=m,w=tv(g.grid),k=w+v,P=M?-v:k,S=-(0,s.t)(this.labelRotation),D=[],O="middle";if("top"===p)r=this.bottom-P,o=this._getXAxisLabelAlignment();else if("bottom"===p)r=this.top+P,o=this._getXAxisLabelAlignment();else if("left"===p){let t=this._getYAxisLabelAlignment(w);o=t.textAlign,n=t.x}else if("right"===p){let t=this._getYAxisLabelAlignment(w);o=t.textAlign,n=t.x}else if("x"===f){if("center"===p)r=(t.top+t.bottom)/2+k;else if((0,s.i)(p)){let t=Object.keys(p)[0],e=p[t];r=this.chart.scales[t].getPixelForValue(e)+k}o=this._getXAxisLabelAlignment()}else if("y"===f){if("center"===p)n=(t.left+t.right)/2-k;else if((0,s.i)(p)){let t=Object.keys(p)[0],e=p[t];n=this.chart.scales[t].getPixelForValue(e)}o=this._getYAxisLabelAlignment(w).textAlign}"y"===f&&("start"===_?O="top":"end"===_&&(O="bottom"));let C=this._getLabelSizes();for(e=0,i=x.length;e<i;++e){let t;a=x[e].label;let f=m.setContext(this.getContext(e));l=this.getPixelForTick(e)+m.labelOffset,d=(h=this._resolveTickFontOptions(e)).lineHeight;let g=(c=(0,s.b)(a)?a.length:1)/2,_=f.color,v=f.textStrokeColor,w=f.textStrokeWidth,k=o;if(b?(n=l,"inner"===o&&(k=e===i-1?this.options.reverse?"left":"right":0===e?this.options.reverse?"right":"left":"center"),u="top"===p?"near"===y||0!==S?-c*d+d/2:"center"===y?-C.highest.height/2-g*d+d:-C.highest.height+d/2:"near"===y||0!==S?d/2:"center"===y?C.highest.height/2-g*d:C.highest.height-c*d,M&&(u*=-1),0===S||f.showLabelBackdrop||(n+=d/2*Math.sin(S))):(r=l,u=(1-c)*d/2),f.showLabelBackdrop){let a=(0,s.E)(f.backdropPadding),n=C.heights[e],r=C.widths[e],l=u-a.top,h=0-a.left;switch(O){case"middle":l-=n/2;break;case"bottom":l-=n}switch(o){case"center":h-=r/2;break;case"right":h-=r;break;case"inner":e===i-1?h-=r:e>0&&(h-=r/2)}t={left:h,top:l,width:r+a.width,height:n+a.height,color:f.backdropColor}}D.push({label:a,font:h,textOffset:u,options:{rotation:S,color:_,strokeColor:v,strokeWidth:w,textAlign:k,textBaseline:O,translation:[n,r],backdrop:t}})}return D}_getXAxisLabelAlignment(){let{position:t,ticks:e}=this.options;if(-(0,s.t)(this.labelRotation))return"top"===t?"left":"right";let i="center";return"start"===e.align?i="left":"end"===e.align?i="right":"inner"===e.align&&(i="inner"),i}_getYAxisLabelAlignment(t){let e,i,{position:s,ticks:{crossAlign:a,mirror:n,padding:r}}=this.options,o=this._getLabelSizes(),l=t+r,h=o.widest.width;return"left"===s?n?(i=this.right+r,"near"===a?e="left":"center"===a?(e="center",i+=h/2):(e="right",i+=h)):(i=this.right-l,"near"===a?e="right":"center"===a?(e="center",i-=h/2):(e="left",i=this.left)):"right"===s?n?(i=this.left+r,"near"===a?e="right":"center"===a?(e="center",i-=h/2):(e="left",i-=h)):(i=this.left+l,"near"===a?e="left":"center"===a?(e="center",i+=h/2):(e="right",i=this.right)):e="right",{textAlign:e,x:i}}_computeLabelArea(){if(this.options.ticks.mirror)return;let t=this.chart,e=this.options.position;return"left"===e||"right"===e?{top:0,left:this.left,bottom:t.height,right:this.right}:"top"===e||"bottom"===e?{top:this.top,left:0,bottom:this.bottom,right:t.width}:void 0}drawBackground(){let{ctx:t,options:{backgroundColor:e},left:i,top:s,width:a,height:n}=this;e&&(t.save(),t.fillStyle=e,t.fillRect(i,s,a,n),t.restore())}getLineWidthForValue(t){let e=this.options.grid;if(!this._isVisible()||!e.display)return 0;let i=this.ticks.findIndex(e=>e.value===t);return i>=0?e.setContext(this.getContext(i)).lineWidth:0}drawGrid(t){let e,i,s=this.options.grid,a=this.ctx,n=this._gridLineItems||(this._gridLineItems=this._computeGridLineItems(t)),r=(t,e,i)=>{i.width&&i.color&&(a.save(),a.lineWidth=i.width,a.strokeStyle=i.color,a.setLineDash(i.borderDash||[]),a.lineDashOffset=i.borderDashOffset,a.beginPath(),a.moveTo(t.x,t.y),a.lineTo(e.x,e.y),a.stroke(),a.restore())};if(s.display)for(e=0,i=n.length;e<i;++e){let t=n[e];s.drawOnChartArea&&r({x:t.x1,y:t.y1},{x:t.x2,y:t.y2},t),s.drawTicks&&r({x:t.tx1,y:t.ty1},{x:t.tx2,y:t.ty2},{color:t.tickColor,width:t.tickWidth,borderDash:t.tickBorderDash,borderDashOffset:t.tickBorderDashOffset})}}drawBorder(){let t,e,i,a,{chart:n,ctx:r,options:{border:o,grid:l}}=this,h=o.setContext(this.getContext()),d=o.display?h.width:0;if(!d)return;let c=l.setContext(this.getContext(0)).lineWidth,u=this._borderValue;this.isHorizontal()?(t=(0,s.X)(n,this.left,d)-d/2,e=(0,s.X)(n,this.right,c)+c/2,i=a=u):(i=(0,s.X)(n,this.top,d)-d/2,a=(0,s.X)(n,this.bottom,c)+c/2,t=e=u),r.save(),r.lineWidth=h.width,r.strokeStyle=h.color,r.beginPath(),r.moveTo(t,i),r.lineTo(e,a),r.stroke(),r.restore()}drawLabels(t){if(!this.options.ticks.display)return;let e=this.ctx,i=this._computeLabelArea();for(let a of(i&&(0,s.Y)(e,i),this.getLabelItems(t))){let t=a.options,i=a.font,n=a.label,r=a.textOffset;(0,s.Z)(e,n,0,r,i,t)}i&&(0,s.$)(e)}drawTitle(){let t,{ctx:e,options:{position:i,title:a,reverse:n}}=this;if(!a.display)return;let r=(0,s.a0)(a.font),o=(0,s.E)(a.padding),l=a.align,h=r.lineHeight/2;"bottom"===i||"center"===i||(0,s.i)(i)?(h+=o.bottom,(0,s.b)(a.text)&&(h+=r.lineHeight*(a.text.length-1))):h+=o.top;let{titleX:d,titleY:c,maxWidth:u,rotation:f}=function(t,e,i,a){let n,r,o,{top:l,left:h,bottom:d,right:c,chart:u}=t,{chartArea:f,scales:g}=u,p=0,m=d-l,b=c-h;if(t.isHorizontal()){if(r=(0,s.a2)(a,h,c),(0,s.i)(i)){let t=Object.keys(i)[0],s=i[t];o=g[t].getPixelForValue(s)+m-e}else o="center"===i?(f.bottom+f.top)/2+m-e:tx(t,i,e);n=c-h}else{if((0,s.i)(i)){let t=Object.keys(i)[0],s=i[t];r=g[t].getPixelForValue(s)-b+e}else r="center"===i?(f.left+f.right)/2-b+e:tx(t,i,e);o=(0,s.a2)(a,d,l),p="left"===i?-s.H:s.H}return{titleX:r,titleY:o,maxWidth:n,rotation:p}}(this,h,i,l);(0,s.Z)(e,a.text,0,0,r,{color:a.color,maxWidth:u,rotation:f,textAlign:(t=(0,s.a1)(l),(n&&"right"!==i||!n&&"right"===i)&&(t=tb(t)),t),textBaseline:"middle",translation:[d,c]})}draw(t){this._isVisible()&&(this.drawBackground(),this.drawGrid(t),this.drawBorder(),this.drawTitle(),this.drawLabels(t))}_layers(){let t=this.options,e=t.ticks&&t.ticks.z||0,i=(0,s.v)(t.grid&&t.grid.z,-1),a=(0,s.v)(t.border&&t.border.z,0);return this._isVisible()&&this.draw===tw.prototype.draw?[{z:i,draw:t=>{this.drawBackground(),this.drawGrid(t),this.drawTitle()}},{z:a,draw:()=>{this.drawBorder()}},{z:e,draw:t=>{this.drawLabels(t)}}]:[{z:e,draw:t=>{this.draw(t)}}]}getMatchingVisibleMetas(t){let e,i,s=this.chart.getSortedVisibleDatasetMetas(),a=this.axis+"AxisID",n=[];for(e=0,i=s.length;e<i;++e){let i=s[e];i[a]!==this.id||t&&i.type!==t||n.push(i)}return n}_resolveTickFontOptions(t){let e=this.options.ticks.setContext(this.getContext(t));return(0,s.a0)(e.font)}_maxDigits(){let t=this._resolveTickFontOptions(0).lineHeight;return(this.isHorizontal()?this.width:this.height)/t}}class tk{constructor(t,e,i){this.type=t,this.scope=e,this.override=i,this.items=Object.create(null)}isForType(t){return Object.prototype.isPrototypeOf.call(this.type.prototype,t.prototype)}register(t){var e;let i,a=Object.getPrototypeOf(t);"id"in(e=a)&&"defaults"in e&&(i=this.register(a));let n=this.items,r=t.id,o=this.scope+"."+r;if(!r)throw Error("class does not have id: "+t);return r in n||(n[r]=t,function(t,e,i){var a,n;let r=(0,s.a4)(Object.create(null),[i?s.d.get(i):{},s.d.get(e),t.defaults]);s.d.set(e,r),t.defaultRoutes&&(a=e,Object.keys(n=t.defaultRoutes).forEach(t=>{let e=t.split("."),i=e.pop(),r=[a].concat(e).join("."),o=n[t].split("."),l=o.pop(),h=o.join(".");s.d.route(r,i,h,l)})),t.descriptors&&s.d.describe(e,t.descriptors)}(t,o,i),this.override&&s.d.override(t.id,t.overrides)),o}get(t){return this.items[t]}unregister(t){let e=this.items,i=t.id,a=this.scope;i in e&&delete e[i],a&&i in s.d[a]&&(delete s.d[a][i],this.override&&delete s.a3[i])}}class tP{constructor(){this.controllers=new tk(v,"datasets",!0),this.elements=new tk(tp,"elements"),this.plugins=new tk(Object,"plugins"),this.scales=new tk(tw,"scales"),this._typedRegistries=[this.controllers,this.scales,this.elements]}add(...t){this._each("register",t)}remove(...t){this._each("unregister",t)}addControllers(...t){this._each("register",t,this.controllers)}addElements(...t){this._each("register",t,this.elements)}addPlugins(...t){this._each("register",t,this.plugins)}addScales(...t){this._each("register",t,this.scales)}getController(t){return this._get(t,this.controllers,"controller")}getElement(t){return this._get(t,this.elements,"element")}getPlugin(t){return this._get(t,this.plugins,"plugin")}getScale(t){return this._get(t,this.scales,"scale")}removeControllers(...t){this._each("unregister",t,this.controllers)}removeElements(...t){this._each("unregister",t,this.elements)}removePlugins(...t){this._each("unregister",t,this.plugins)}removeScales(...t){this._each("unregister",t,this.scales)}_each(t,e,i){[...e].forEach(e=>{let a=i||this._getRegistryForType(e);i||a.isForType(e)||a===this.plugins&&e.id?this._exec(t,a,e):(0,s.F)(e,e=>{let s=i||this._getRegistryForType(e);this._exec(t,s,e)})})}_exec(t,e,i){let a=(0,s.a5)(t);(0,s.Q)(i["before"+a],[],i),e[t](i),(0,s.Q)(i["after"+a],[],i)}_getRegistryForType(t){for(let e=0;e<this._typedRegistries.length;e++){let i=this._typedRegistries[e];if(i.isForType(t))return i}return this.plugins}_get(t,e,i){let s=e.get(t);if(void 0===s)throw Error('"'+t+'" is not a registered '+i+".");return s}}var tS=new tP;class tD{constructor(){this._init=[]}notify(t,e,i,s){"beforeInit"===e&&(this._init=this._createDescriptors(t,!0),this._notify(this._init,t,"install"));let a=s?this._descriptors(t).filter(s):this._descriptors(t),n=this._notify(a,t,e,i);return"afterDestroy"===e&&(this._notify(a,t,"stop"),this._notify(this._init,t,"uninstall")),n}_notify(t,e,i,a){for(let n of(a=a||{},t)){let t=n.plugin,r=t[i],o=[e,a,n.options];if(!1===(0,s.Q)(r,o,t)&&a.cancelable)return!1}return!0}invalidate(){(0,s.k)(this._cache)||(this._oldCache=this._cache,this._cache=void 0)}_descriptors(t){if(this._cache)return this._cache;let e=this._cache=this._createDescriptors(t);return this._notifyStateChanges(t),e}_createDescriptors(t,e){let i=t&&t.config,a=(0,s.v)(i.options&&i.options.plugins,{}),n=function(t){let e={},i=[],s=Object.keys(tS.plugins.items);for(let t=0;t<s.length;t++)i.push(tS.getPlugin(s[t]));let a=t.plugins||[];for(let t=0;t<a.length;t++){let s=a[t];-1===i.indexOf(s)&&(i.push(s),e[s.id]=!0)}return{plugins:i,localIds:e}}(i);return!1!==a||e?function(t,{plugins:e,localIds:i},s,a){let n=[],r=t.getContext();for(let l of e){var o;let e=l.id,h=(o=s[e],a||!1!==o?!0===o?{}:o:null);null!==h&&n.push({plugin:l,options:function(t,{plugin:e,local:i},s,a){let n=t.pluginScopeKeys(e),r=t.getOptionScopes(s,n);return i&&e.defaults&&r.push(e.defaults),t.createResolver(r,a,[""],{scriptable:!1,indexable:!1,allKeys:!0})}(t.config,{plugin:l,local:i[e]},h,r)})}return n}(t,n,a,e):[]}_notifyStateChanges(t){let e=this._oldCache||[],i=this._cache,s=(t,e)=>t.filter(t=>!e.some(e=>t.plugin.id===e.plugin.id));this._notify(s(e,i),t,"stop"),this._notify(s(i,e),t,"start")}}function tO(t,e){let i=s.d.datasets[t]||{};return((e.datasets||{})[t]||{}).indexAxis||e.indexAxis||i.indexAxis||"x"}function tC(t){if("x"===t||"y"===t||"r"===t)return t}function tT(t,...e){if(tC(t))return t;for(let s of e){var i;let e=s.axis||("top"===(i=s.position)||"bottom"===i?"x":"left"===i||"right"===i?"y":void 0)||t.length>1&&tC(t[0].toLowerCase());if(e)return e}throw Error(`Cannot determine type of '${t}' axis. Please provide 'axis' or 'position' option.`)}function tA(t,e,i){if(i[e+"AxisID"]===t)return{axis:e}}function tE(t){let e=t.options||(t.options={});e.plugins=(0,s.v)(e.plugins,{}),e.scales=function(t,e){let i=s.a3[t.type]||{scales:{}},a=e.scales||{},n=tO(t.type,e),r=Object.create(null);return Object.keys(a).forEach(e=>{let o=a[e];if(!(0,s.i)(o))return console.error(`Invalid scale configuration for scale: ${e}`);if(o._proxy)return console.warn(`Ignoring resolver passed as options for scale: ${e}`);let l=tT(e,o,function(t,e){if(e.data&&e.data.datasets){let i=e.data.datasets.filter(e=>e.xAxisID===t||e.yAxisID===t);if(i.length)return tA(t,"x",i[0])||tA(t,"y",i[0])}return{}}(e,t),s.d.scales[o.type]),h=l===n?"_index_":"_value_",d=i.scales||{};r[e]=(0,s.ab)(Object.create(null),[{axis:l},o,d[l],d[h]])}),t.data.datasets.forEach(i=>{let n=i.type||t.type,o=i.indexAxis||tO(n,e),l=(s.a3[n]||{}).scales||{};Object.keys(l).forEach(t=>{let e,n=(e=t,"_index_"===t?e=o:"_value_"===t&&(e="x"===o?"y":"x"),e),h=i[n+"AxisID"]||n;r[h]=r[h]||Object.create(null),(0,s.ab)(r[h],[{axis:n},a[h],l[t]])})}),Object.keys(r).forEach(t=>{let e=r[t];(0,s.ab)(e,[s.d.scales[e.type],s.d.scale])}),r}(t,e)}function tL(t){return(t=t||{}).datasets=t.datasets||[],t.labels=t.labels||[],t}let tF=new Map,tR=new Set;function tI(t,e){let i=tF.get(t);return i||(i=e(),tF.set(t,i),tR.add(i)),i}let tz=(t,e,i)=>{let a=(0,s.f)(e,i);void 0!==a&&t.add(a)};class tV{constructor(t){this._config=function(t){return(t=t||{}).data=tL(t.data),tE(t),t}(t),this._scopeCache=new Map,this._resolverCache=new Map}get platform(){return this._config.platform}get type(){return this._config.type}set type(t){this._config.type=t}get data(){return this._config.data}set data(t){this._config.data=tL(t)}get options(){return this._config.options}set options(t){this._config.options=t}get plugins(){return this._config.plugins}update(){let t=this._config;this.clearCache(),tE(t)}clearCache(){this._scopeCache.clear(),this._resolverCache.clear()}datasetScopeKeys(t){return tI(t,()=>[[`datasets.${t}`,""]])}datasetAnimationScopeKeys(t,e){return tI(`${t}.transition.${e}`,()=>[[`datasets.${t}.transitions.${e}`,`transitions.${e}`],[`datasets.${t}`,""]])}datasetElementScopeKeys(t,e){return tI(`${t}-${e}`,()=>[[`datasets.${t}.elements.${e}`,`datasets.${t}`,`elements.${e}`,""]])}pluginScopeKeys(t){let e=t.id,i=this.type;return tI(`${i}-plugin-${e}`,()=>[[`plugins.${e}`,...t.additionalOptionScopes||[]]])}_cachedScopes(t,e){let i=this._scopeCache,s=i.get(t);return(!s||e)&&(s=new Map,i.set(t,s)),s}getOptionScopes(t,e,i){let{options:a,type:n}=this,r=this._cachedScopes(t,i),o=r.get(e);if(o)return o;let l=new Set;e.forEach(e=>{t&&(l.add(t),e.forEach(e=>tz(l,t,e))),e.forEach(t=>tz(l,a,t)),e.forEach(t=>tz(l,s.a3[n]||{},t)),e.forEach(t=>tz(l,s.d,t)),e.forEach(t=>tz(l,s.a6,t))});let h=Array.from(l);return 0===h.length&&h.push(Object.create(null)),tR.has(e)&&r.set(e,h),h}chartOptionScopes(){let{options:t,type:e}=this;return[t,s.a3[e]||{},s.d.datasets[e]||{},{type:e},s.d,s.a6]}resolveNamedOptions(t,e,i,a=[""]){let n={$shared:!0},{resolver:r,subPrefixes:o}=tN(this._resolverCache,t,a),l=r;if(function(t,e){let{isScriptable:i,isIndexable:a}=(0,s.aa)(t);for(let n of e){let e=i(n),r=a(n),o=(r||e)&&t[n];if(e&&((0,s.a7)(o)||tB(o))||r&&(0,s.b)(o))return!0}return!1}(r,e)){n.$shared=!1,i=(0,s.a7)(i)?i():i;let e=this.createResolver(t,i,o);l=(0,s.a8)(r,i,e)}for(let t of e)n[t]=l[t];return n}createResolver(t,e,i=[""],a){let{resolver:n}=tN(this._resolverCache,t,i);return(0,s.i)(e)?(0,s.a8)(n,e,void 0,a):n}}function tN(t,e,i){let a=t.get(e);a||(a=new Map,t.set(e,a));let n=i.join(),r=a.get(n);return r||(r={resolver:(0,s.a9)(e,i),subPrefixes:i.filter(t=>!t.toLowerCase().includes("hover"))},a.set(n,r)),r}let tB=t=>(0,s.i)(t)&&Object.getOwnPropertyNames(t).some(e=>(0,s.a7)(t[e])),tH=["top","bottom","left","right","chartArea"];function tj(t,e){return"top"===t||"bottom"===t||-1===tH.indexOf(t)&&"x"===e}function tW(t,e){return function(i,s){return i[t]===s[t]?i[e]-s[e]:i[t]-s[t]}}function t$(t){let e=t.chart,i=e.options.animation;e.notifyPlugins("afterRender"),(0,s.Q)(i&&i.onComplete,[t],e)}function tY(t){let e=t.chart,i=e.options.animation;(0,s.Q)(i&&i.onProgress,[t],e)}function tU(t){return(0,s.M)()&&"string"==typeof t?t=document.getElementById(t):t&&t.length&&(t=t[0]),t&&t.canvas&&(t=t.canvas),t}let tQ={},tX=t=>{let e=tU(t);return Object.values(tQ).filter(t=>t.canvas===e).pop()};class tq{static defaults=s.d;static instances=tQ;static overrides=s.a3;static registry=tS;static version="4.5.0";static getChart=tX;static register(...t){tS.add(...t),tG()}static unregister(...t){tS.remove(...t),tG()}constructor(t,e){let i=this.config=new tV(e),a=tU(t),r=tX(a);if(r)throw Error("Canvas is already in use. Chart with ID '"+r.id+"' must be destroyed before the canvas with ID '"+r.canvas.id+"' can be reused.");let o=i.createResolver(i.chartOptionScopes(),this.getContext());this.platform=new(i.platform||(!(0,s.M)()||"undefined"!=typeof OffscreenCanvas&&a instanceof OffscreenCanvas?tt:tg)),this.platform.updateConfig(i);let l=this.platform.acquireContext(a,o.aspectRatio),h=l&&l.canvas,d=h&&h.height,c=h&&h.width;if(this.id=(0,s.ac)(),this.ctx=l,this.canvas=h,this.width=c,this.height=d,this._options=o,this._aspectRatio=this.aspectRatio,this._layers=[],this._metasets=[],this._stacks=void 0,this.boxes=[],this.currentDevicePixelRatio=void 0,this.chartArea=void 0,this._active=[],this._lastEvent=void 0,this._listeners={},this._responsiveListeners=void 0,this._sortedMetasets=[],this.scales={},this._plugins=new tD,this.$proxies={},this._hiddenIndices={},this.attached=!1,this._animationsDisabled=void 0,this.$context=void 0,this._doResize=(0,s.ad)(t=>this.update(t),o.resizeDelay||0),this._dataChanges=[],tQ[this.id]=this,!l||!h)return void console.error("Failed to create chart: can't acquire context from the given item");n.listen(this,"complete",t$),n.listen(this,"progress",tY),this._initialize(),this.attached&&this.update()}get aspectRatio(){let{options:{aspectRatio:t,maintainAspectRatio:e},width:i,height:a,_aspectRatio:n}=this;return(0,s.k)(t)?e&&n?n:a?i/a:null:t}get data(){return this.config.data}set data(t){this.config.data=t}get options(){return this._options}set options(t){this.config.options=t}get registry(){return tS}_initialize(){return this.notifyPlugins("beforeInit"),this.options.responsive?this.resize():(0,s.ae)(this,this.options.devicePixelRatio),this.bindEvents(),this.notifyPlugins("afterInit"),this}clear(){return(0,s.af)(this.canvas,this.ctx),this}stop(){return n.stop(this),this}resize(t,e){n.running(this)?this._resizeBeforeDraw={width:t,height:e}:this._resize(t,e)}_resize(t,e){let i=this.options,a=this.canvas,n=i.maintainAspectRatio&&this.aspectRatio,r=this.platform.getMaximumSize(a,t,e,n),o=i.devicePixelRatio||this.platform.getDevicePixelRatio(),l=this.width?"resize":"attach";this.width=r.width,this.height=r.height,this._aspectRatio=this.aspectRatio,(0,s.ae)(this,o,!0)&&(this.notifyPlugins("resize",{size:r}),(0,s.Q)(i.onResize,[this,r],this),this.attached&&this._doResize(l)&&this.render())}ensureScalesHaveIDs(){let t=this.options.scales||{};(0,s.F)(t,(t,e)=>{t.id=e})}buildOrUpdateScales(){let t=this.options,e=t.scales,i=this.scales,a=Object.keys(i).reduce((t,e)=>(t[e]=!1,t),{}),n=[];e&&(n=n.concat(Object.keys(e).map(t=>{let i=e[t],s=tT(t,i),a="r"===s,n="x"===s;return{options:i,dposition:a?"chartArea":n?"bottom":"left",dtype:a?"radialLinear":n?"category":"linear"}}))),(0,s.F)(n,e=>{let n=e.options,r=n.id,o=tT(r,n),l=(0,s.v)(n.type,e.dtype);(void 0===n.position||tj(n.position,o)!==tj(e.dposition))&&(n.position=e.dposition),a[r]=!0;let h=null;r in i&&i[r].type===l?h=i[r]:i[(h=new(tS.getScale(l))({id:r,type:l,ctx:this.ctx,chart:this})).id]=h,h.init(n,t)}),(0,s.F)(a,(t,e)=>{t||delete i[e]}),(0,s.F)(i,t=>{Z.configure(this,t,t.options),Z.addBox(this,t)})}_updateMetasets(){let t=this._metasets,e=this.data.datasets.length,i=t.length;if(t.sort((t,e)=>t.index-e.index),i>e){for(let t=e;t<i;++t)this._destroyDatasetMeta(t);t.splice(e,i-e)}this._sortedMetasets=t.slice(0).sort(tW("order","index"))}_removeUnreferencedMetasets(){let{_metasets:t,data:{datasets:e}}=this;t.length>e.length&&delete this._stacks,t.forEach((t,i)=>{0===e.filter(e=>e===t._dataset).length&&this._destroyDatasetMeta(i)})}buildOrUpdateControllers(){let t,e,i=[],a=this.data.datasets;for(this._removeUnreferencedMetasets(),t=0,e=a.length;t<e;t++){let e=a[t],n=this.getDatasetMeta(t),r=e.type||this.config.type;if(n.type&&n.type!==r&&(this._destroyDatasetMeta(t),n=this.getDatasetMeta(t)),n.type=r,n.indexAxis=e.indexAxis||tO(r,this.options),n.order=e.order||0,n.index=t,n.label=""+e.label,n.visible=this.isDatasetVisible(t),n.controller)n.controller.updateIndex(t),n.controller.linkScales();else{let e=tS.getController(r),{datasetElementType:a,dataElementType:o}=s.d.datasets[r];Object.assign(e,{dataElementType:tS.getElement(o),datasetElementType:a&&tS.getElement(a)}),n.controller=new e(this,t),i.push(n.controller)}}return this._updateMetasets(),i}_resetElements(){(0,s.F)(this.data.datasets,(t,e)=>{this.getDatasetMeta(e).controller.reset()},this)}reset(){this._resetElements(),this.notifyPlugins("reset")}update(t){let e=this.config;e.update();let i=this._options=e.createResolver(e.chartOptionScopes(),this.getContext()),a=this._animationsDisabled=!i.animation;if(this._updateScales(),this._checkEventBindings(),this._updateHiddenIndices(),this._plugins.invalidate(),!1===this.notifyPlugins("beforeUpdate",{mode:t,cancelable:!0}))return;let n=this.buildOrUpdateControllers();this.notifyPlugins("beforeElementsUpdate");let r=0;for(let t=0,e=this.data.datasets.length;t<e;t++){let{controller:e}=this.getDatasetMeta(t),i=!a&&-1===n.indexOf(e);e.buildOrUpdateElements(i),r=Math.max(+e.getMaxOverflow(),r)}r=this._minPadding=i.layout.autoPadding?r:0,this._updateLayout(r),a||(0,s.F)(n,t=>{t.reset()}),this._updateDatasets(t),this.notifyPlugins("afterUpdate",{mode:t}),this._layers.sort(tW("z","_idx"));let{_active:o,_lastEvent:l}=this;l?this._eventHandler(l,!0):o.length&&this._updateHoverStyles(o,o,!0),this.render()}_updateScales(){(0,s.F)(this.scales,t=>{Z.removeBox(this,t)}),this.ensureScalesHaveIDs(),this.buildOrUpdateScales()}_checkEventBindings(){let t=this.options,e=new Set(Object.keys(this._listeners)),i=new Set(t.events);(0,s.ag)(e,i)&&!!this._responsiveListeners===t.responsive||(this.unbindEvents(),this.bindEvents())}_updateHiddenIndices(){let{_hiddenIndices:t}=this;for(let{method:i,start:s,count:a}of this._getUniformDataChanges()||[]){var e="_removeElements"===i?-a:a;for(let i of Object.keys(t)){let a=+i;if(a>=s){let n=t[i];delete t[i],(e>0||a>s)&&(t[a+e]=n)}}}}_getUniformDataChanges(){let t=this._dataChanges;if(!t||!t.length)return;this._dataChanges=[];let e=this.data.datasets.length,i=e=>new Set(t.filter(t=>t[0]===e).map((t,e)=>e+","+t.splice(1).join(","))),a=i(0);for(let t=1;t<e;t++)if(!(0,s.ag)(a,i(t)))return;return Array.from(a).map(t=>t.split(",")).map(t=>({method:t[1],start:+t[2],count:+t[3]}))}_updateLayout(t){if(!1===this.notifyPlugins("beforeLayout",{cancelable:!0}))return;Z.update(this,this.width,this.height,t);let e=this.chartArea,i=e.width<=0||e.height<=0;this._layers=[],(0,s.F)(this.boxes,t=>{i&&"chartArea"===t.position||(t.configure&&t.configure(),this._layers.push(...t._layers()))},this),this._layers.forEach((t,e)=>{t._idx=e}),this.notifyPlugins("afterLayout")}_updateDatasets(t){if(!1!==this.notifyPlugins("beforeDatasetsUpdate",{mode:t,cancelable:!0})){for(let t=0,e=this.data.datasets.length;t<e;++t)this.getDatasetMeta(t).controller.configure();for(let e=0,i=this.data.datasets.length;e<i;++e)this._updateDataset(e,(0,s.a7)(t)?t({datasetIndex:e}):t);this.notifyPlugins("afterDatasetsUpdate",{mode:t})}}_updateDataset(t,e){let i=this.getDatasetMeta(t),s={meta:i,index:t,mode:e,cancelable:!0};!1!==this.notifyPlugins("beforeDatasetUpdate",s)&&(i.controller._update(e),s.cancelable=!1,this.notifyPlugins("afterDatasetUpdate",s))}render(){!1!==this.notifyPlugins("beforeRender",{cancelable:!0})&&(n.has(this)?this.attached&&!n.running(this)&&n.start(this):(this.draw(),t$({chart:this})))}draw(){let t;if(this._resizeBeforeDraw){let{width:t,height:e}=this._resizeBeforeDraw;this._resizeBeforeDraw=null,this._resize(t,e)}if(this.clear(),this.width<=0||this.height<=0||!1===this.notifyPlugins("beforeDraw",{cancelable:!0}))return;let e=this._layers;for(t=0;t<e.length&&e[t].z<=0;++t)e[t].draw(this.chartArea);for(this._drawDatasets();t<e.length;++t)e[t].draw(this.chartArea);this.notifyPlugins("afterDraw")}_getSortedDatasetMetas(t){let e,i,s=this._sortedMetasets,a=[];for(e=0,i=s.length;e<i;++e){let i=s[e];(!t||i.visible)&&a.push(i)}return a}getSortedVisibleDatasetMetas(){return this._getSortedDatasetMetas(!0)}_drawDatasets(){if(!1===this.notifyPlugins("beforeDatasetsDraw",{cancelable:!0}))return;let t=this.getSortedVisibleDatasetMetas();for(let e=t.length-1;e>=0;--e)this._drawDataset(t[e]);this.notifyPlugins("afterDatasetsDraw")}_drawDataset(t){let e=this.ctx,i={meta:t,index:t.index,cancelable:!0},a=(0,s.ah)(this,t);!1!==this.notifyPlugins("beforeDatasetDraw",i)&&(a&&(0,s.Y)(e,a),t.controller.draw(),a&&(0,s.$)(e),i.cancelable=!1,this.notifyPlugins("afterDatasetDraw",i))}isPointInArea(t){return(0,s.C)(t,this.chartArea,this._minPadding)}getElementsAtEventForMode(t,e,i,s){let a=j.modes[e];return"function"==typeof a?a(this,t,i,s):[]}getDatasetMeta(t){let e=this.data.datasets[t],i=this._metasets,s=i.filter(t=>t&&t._dataset===e).pop();return s||(s={type:null,data:[],dataset:null,controller:null,hidden:null,xAxisID:null,yAxisID:null,order:e&&e.order||0,index:t,_dataset:e,_parsed:[],_sorted:!1},i.push(s)),s}getContext(){return this.$context||(this.$context=(0,s.j)(null,{chart:this,type:"chart"}))}getVisibleDatasetCount(){return this.getSortedVisibleDatasetMetas().length}isDatasetVisible(t){let e=this.data.datasets[t];if(!e)return!1;let i=this.getDatasetMeta(t);return"boolean"==typeof i.hidden?!i.hidden:!e.hidden}setDatasetVisibility(t,e){this.getDatasetMeta(t).hidden=!e}toggleDataVisibility(t){this._hiddenIndices[t]=!this._hiddenIndices[t]}getDataVisibility(t){return!this._hiddenIndices[t]}_updateVisibility(t,e,i){let a=i?"show":"hide",n=this.getDatasetMeta(t),r=n.controller._resolveAnimations(void 0,a);(0,s.h)(e)?(n.data[e].hidden=!i,this.update()):(this.setDatasetVisibility(t,i),r.update(n,{visible:i}),this.update(e=>e.datasetIndex===t?a:void 0))}hide(t,e){this._updateVisibility(t,e,!1)}show(t,e){this._updateVisibility(t,e,!0)}_destroyDatasetMeta(t){let e=this._metasets[t];e&&e.controller&&e.controller._destroy(),delete this._metasets[t]}_stop(){let t,e;for(this.stop(),n.remove(this),t=0,e=this.data.datasets.length;t<e;++t)this._destroyDatasetMeta(t)}destroy(){this.notifyPlugins("beforeDestroy");let{canvas:t,ctx:e}=this;this._stop(),this.config.clearCache(),t&&(this.unbindEvents(),(0,s.af)(t,e),this.platform.releaseContext(e),this.canvas=null,this.ctx=null),delete tQ[this.id],this.notifyPlugins("afterDestroy")}toBase64Image(...t){return this.canvas.toDataURL(...t)}bindEvents(){this.bindUserEvents(),this.options.responsive?this.bindResponsiveEvents():this.attached=!0}bindUserEvents(){let t=this._listeners,e=this.platform,i=(i,s)=>{e.addEventListener(this,i,s),t[i]=s},a=(t,e,i)=>{t.offsetX=e,t.offsetY=i,this._eventHandler(t)};(0,s.F)(this.options.events,t=>i(t,a))}bindResponsiveEvents(){let t;this._responsiveListeners||(this._responsiveListeners={});let e=this._responsiveListeners,i=this.platform,s=(t,s)=>{i.addEventListener(this,t,s),e[t]=s},a=(t,s)=>{e[t]&&(i.removeEventListener(this,t,s),delete e[t])},n=(t,e)=>{this.canvas&&this.resize(t,e)},r=()=>{a("attach",r),this.attached=!0,this.resize(),s("resize",n),s("detach",t)};t=()=>{this.attached=!1,a("resize",n),this._stop(),this._resize(0,0),s("attach",r)},i.isAttached(this.canvas)?r():t()}unbindEvents(){(0,s.F)(this._listeners,(t,e)=>{this.platform.removeEventListener(this,e,t)}),this._listeners={},(0,s.F)(this._responsiveListeners,(t,e)=>{this.platform.removeEventListener(this,e,t)}),this._responsiveListeners=void 0}updateHoverStyle(t,e,i){let s,a,n,r=i?"set":"remove";for("dataset"===e&&this.getDatasetMeta(t[0].datasetIndex).controller["_"+r+"DatasetHoverStyle"](),a=0,n=t.length;a<n;++a){let e=(s=t[a])&&this.getDatasetMeta(s.datasetIndex).controller;e&&e[r+"HoverStyle"](s.element,s.datasetIndex,s.index)}}getActiveElements(){return this._active||[]}setActiveElements(t){let e=this._active||[],i=t.map(({datasetIndex:t,index:e})=>{let i=this.getDatasetMeta(t);if(!i)throw Error("No dataset found at index "+t);return{datasetIndex:t,element:i.data[e],index:e}});(0,s.ai)(i,e)||(this._active=i,this._lastEvent=null,this._updateHoverStyles(i,e))}notifyPlugins(t,e,i){return this._plugins.notify(this,t,e,i)}isPluginEnabled(t){return 1===this._plugins._cache.filter(e=>e.plugin.id===t).length}_updateHoverStyles(t,e,i){let s=this.options.hover,a=(t,e)=>t.filter(t=>!e.some(e=>t.datasetIndex===e.datasetIndex&&t.index===e.index)),n=a(e,t),r=i?t:a(t,e);n.length&&this.updateHoverStyle(n,s.mode,!1),r.length&&s.mode&&this.updateHoverStyle(r,s.mode,!0)}_eventHandler(t,e){let i={event:t,replay:e,cancelable:!0,inChartArea:this.isPointInArea(t)},s=e=>(e.options.events||this.options.events).includes(t.native.type);if(!1===this.notifyPlugins("beforeEvent",i,s))return;let a=this._handleEvent(t,e,i.inChartArea);return i.cancelable=!1,this.notifyPlugins("afterEvent",i,s),(a||i.changed)&&this.render(),this}_handleEvent(t,e,i){var a;let{_active:n=[],options:r}=this,o=this._getActiveElements(t,n,i,e),l=(0,s.aj)(t),h=(a=this._lastEvent,i&&"mouseout"!==t.type?l?a:t:null);i&&(this._lastEvent=null,(0,s.Q)(r.onHover,[t,o,this],this),l&&(0,s.Q)(r.onClick,[t,o,this],this));let d=!(0,s.ai)(o,n);return(d||e)&&(this._active=o,this._updateHoverStyles(o,n,e)),this._lastEvent=h,d}_getActiveElements(t,e,i,s){if("mouseout"===t.type)return[];if(!i)return e;let a=this.options.hover;return this.getElementsAtEventForMode(t,a.mode,a,s)}}function tG(){return(0,s.F)(tq.instances,t=>t._plugins.invalidate())}function tK(t,e,i,s){return{x:i+t*Math.cos(e),y:s+t*Math.sin(e)}}function tZ(t,e,i,a,n,r){let{x:o,y:l,startAngle:h,pixelMargin:d,innerRadius:c}=e,u=Math.max(e.outerRadius+a+i-d,0),f=c>0?c+a+i+d:0,g=0,p=n-h;if(a){let t=u>0?u-a:0,e=((c>0?c-a:0)+t)/2;g=(p-(0!==e?p*e/(e+a):p))/2}let m=Math.max(.001,p*u-i/s.P)/u,b=(p-m)/2,x=h+b+g,_=n-b-g,{outerStart:y,outerEnd:v,innerStart:M,innerEnd:w}=function(t,e,i,a){var n;let r=(n=t.options.borderRadius,(0,s.am)(n,["outerStart","outerEnd","innerStart","innerEnd"])),o=(i-e)/2,l=Math.min(o,a*e/2),h=t=>{let e=(i-Math.min(o,t))*a/2;return(0,s.S)(t,0,Math.min(o,e))};return{outerStart:h(r.outerStart),outerEnd:h(r.outerEnd),innerStart:(0,s.S)(r.innerStart,0,l),innerEnd:(0,s.S)(r.innerEnd,0,l)}}(e,f,u,_-x),k=u-y,P=u-v,S=x+y/k,D=_-v/P,O=f+M,C=f+w,T=x+M/O,A=_-w/C;if(t.beginPath(),r){let e=(S+D)/2;if(t.arc(o,l,u,S,e),t.arc(o,l,u,e,D),v>0){let e=tK(P,D,o,l);t.arc(e.x,e.y,v,D,_+s.H)}let i=tK(C,_,o,l);if(t.lineTo(i.x,i.y),w>0){let e=tK(C,A,o,l);t.arc(e.x,e.y,w,_+s.H,A+Math.PI)}let a=(_-w/f+(x+M/f))/2;if(t.arc(o,l,f,_-w/f,a,!0),t.arc(o,l,f,a,x+M/f,!0),M>0){let e=tK(O,T,o,l);t.arc(e.x,e.y,M,T+Math.PI,x-s.H)}let n=tK(k,x,o,l);if(t.lineTo(n.x,n.y),y>0){let e=tK(k,S,o,l);t.arc(e.x,e.y,y,x-s.H,S)}}else{t.moveTo(o,l);let e=Math.cos(S)*u+o,i=Math.sin(S)*u+l;t.lineTo(e,i);let s=Math.cos(D)*u+o,a=Math.sin(D)*u+l;t.lineTo(s,a)}t.closePath()}class tJ extends tp{static id="arc";static defaults={borderAlign:"center",borderColor:"#fff",borderDash:[],borderDashOffset:0,borderJoinStyle:void 0,borderRadius:0,borderWidth:2,offset:0,spacing:0,angle:void 0,circular:!0,selfJoin:!1};static defaultRoutes={backgroundColor:"backgroundColor"};static descriptors={_scriptable:!0,_indexable:t=>"borderDash"!==t};circumference;endAngle;fullCircles;innerRadius;outerRadius;pixelMargin;startAngle;constructor(t){super(),this.options=void 0,this.circumference=void 0,this.startAngle=void 0,this.endAngle=void 0,this.innerRadius=void 0,this.outerRadius=void 0,this.pixelMargin=0,this.fullCircles=0,t&&Object.assign(this,t)}inRange(t,e,i){let a=this.getProps(["x","y"],i),{angle:n,distance:r}=(0,s.D)(a,{x:t,y:e}),{startAngle:o,endAngle:l,innerRadius:h,outerRadius:d,circumference:c}=this.getProps(["startAngle","endAngle","innerRadius","outerRadius","circumference"],i),u=(this.options.spacing+this.options.borderWidth)/2,f=(0,s.v)(c,l-o),g=(0,s.p)(n,o,l)&&o!==l,p=f>=s.T||g,m=(0,s.ak)(r,h+u,d+u);return p&&m}getCenterPoint(t){let{x:e,y:i,startAngle:s,endAngle:a,innerRadius:n,outerRadius:r}=this.getProps(["x","y","startAngle","endAngle","innerRadius","outerRadius"],t),{offset:o,spacing:l}=this.options,h=(s+a)/2,d=(n+r+l+o)/2;return{x:e+Math.cos(h)*d,y:i+Math.sin(h)*d}}tooltipPosition(t){return this.getCenterPoint(t)}draw(t){let{options:e,circumference:i}=this,a=(e.offset||0)/4,n=(e.spacing||0)/2,r=e.circular;if(this.pixelMargin=.33*("inner"===e.borderAlign),this.fullCircles=i>s.T?Math.floor(i/s.T):0,0===i||this.innerRadius<0||this.outerRadius<0)return;t.save();let o=(this.startAngle+this.endAngle)/2;t.translate(Math.cos(o)*a,Math.sin(o)*a);let l=a*(1-Math.sin(Math.min(s.P,i||0)));t.fillStyle=e.backgroundColor,t.strokeStyle=e.borderColor,function(t,e,i,a,n){let{fullCircles:r,startAngle:o,circumference:l}=e,h=e.endAngle;if(r){tZ(t,e,i,a,h,n);for(let e=0;e<r;++e)t.fill();isNaN(l)||(h=o+(l%s.T||s.T))}tZ(t,e,i,a,h,n),t.fill()}(t,this,l,n,r),function(t,e,i,a,n){let{fullCircles:r,startAngle:o,circumference:l,options:h}=e,{borderWidth:d,borderJoinStyle:c,borderDash:u,borderDashOffset:f,borderRadius:g}=h,p="inner"===h.borderAlign;if(!d)return;t.setLineDash(u||[]),t.lineDashOffset=f,p?(t.lineWidth=2*d,t.lineJoin=c||"round"):(t.lineWidth=d,t.lineJoin=c||"bevel");let m=e.endAngle;if(r){tZ(t,e,i,a,m,n);for(let e=0;e<r;++e)t.stroke();isNaN(l)||(m=o+(l%s.T||s.T))}p&&function(t,e,i){let{startAngle:a,pixelMargin:n,x:r,y:o,outerRadius:l,innerRadius:h}=e,d=n/l;t.beginPath(),t.arc(r,o,l,a-d,i+d),h>n?(d=n/h,t.arc(r,o,h,i+d,a-d,!0)):t.arc(r,o,n,i+s.H,a-s.H),t.closePath(),t.clip()}(t,e,m),h.selfJoin&&m-o>=s.P&&0===g&&"miter"!==c&&function(t,e,i){let{startAngle:a,x:n,y:r,outerRadius:o,innerRadius:l,options:h}=e,{borderWidth:d,borderJoinStyle:c}=h,u=Math.min(d/o,(0,s.al)(a-i));if(t.beginPath(),t.arc(n,r,o-d/2,a+u/2,i-u/2),l>0){let e=Math.min(d/l,(0,s.al)(a-i));t.arc(n,r,l+d/2,i-e/2,a+e/2,!0)}else{let e=Math.min(d/2,o*(0,s.al)(a-i));if("round"===c)t.arc(n,r,e,i-s.P/2,a+s.P/2,!0);else if("bevel"===c){let o=2*e*e,l=-o*Math.cos(i+s.P/2)+n,h=-o*Math.sin(i+s.P/2)+r,d=o*Math.cos(a+s.P/2)+n,c=o*Math.sin(a+s.P/2)+r;t.lineTo(l,h),t.lineTo(d,c)}}t.closePath(),t.moveTo(0,0),t.rect(0,0,t.canvas.width,t.canvas.height),t.clip("evenodd")}(t,e,m),r||(tZ(t,e,i,a,m,n),t.stroke())}(t,this,l,n,r),t.restore()}}function t0(t,e,i=e){t.lineCap=(0,s.v)(i.borderCapStyle,e.borderCapStyle),t.setLineDash((0,s.v)(i.borderDash,e.borderDash)),t.lineDashOffset=(0,s.v)(i.borderDashOffset,e.borderDashOffset),t.lineJoin=(0,s.v)(i.borderJoinStyle,e.borderJoinStyle),t.lineWidth=(0,s.v)(i.borderWidth,e.borderWidth),t.strokeStyle=(0,s.v)(i.borderColor,e.borderColor)}function t1(t,e,i){t.lineTo(i.x,i.y)}function t2(t,e,i={}){let s=t.length,{start:a=0,end:n=s-1}=i,{start:r,end:o}=e,l=Math.max(a,r),h=Math.min(n,o);return{count:s,start:l,loop:e.loop,ilen:h<l&&!(a<r&&n<r||a>o&&n>o)?s+h-l:h-l}}function t5(t,e,i,a){let n,r,o,{points:l,options:h}=e,{count:d,start:c,loop:u,ilen:f}=t2(l,i,a),g=h.stepped?s.at:h.tension||"monotone"===h.cubicInterpolationMode?s.au:t1,{move:p=!0,reverse:m}=a||{};for(n=0;n<=f;++n)(r=l[(c+(m?f-n:n))%d]).skip||(p?(t.moveTo(r.x,r.y),p=!1):g(t,o,r,m,h.stepped),o=r);return u&&g(t,o,r=l[(c+(m?f:0))%d],m,h.stepped),!!u}function t3(t,e,i,s){let a,n,r,o,l,h,d=e.points,{count:c,start:u,ilen:f}=t2(d,i,s),{move:g=!0,reverse:p}=s||{},m=0,b=0,x=t=>(u+(p?f-t:t))%c,_=()=>{o!==l&&(t.lineTo(m,l),t.lineTo(m,o),t.lineTo(m,h))};for(g&&(n=d[x(0)],t.moveTo(n.x,n.y)),a=0;a<=f;++a){if((n=d[x(a)]).skip)continue;let e=n.x,i=n.y,s=0|e;s===r?(i<o?o=i:i>l&&(l=i),m=(b*m+e)/++b):(_(),t.lineTo(e,i),r=s,b=0,o=l=i),h=i}_()}function t8(t){let e=t.options,i=e.borderDash&&e.borderDash.length;return t._decimated||t._loop||e.tension||"monotone"===e.cubicInterpolationMode||e.stepped||i?t5:t3}let t4="function"==typeof Path2D;class t6 extends tp{static id="line";static defaults={borderCapStyle:"butt",borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",borderWidth:3,capBezierPoints:!0,cubicInterpolationMode:"default",fill:!1,spanGaps:!1,stepped:!1,tension:0};static defaultRoutes={backgroundColor:"backgroundColor",borderColor:"borderColor"};static descriptors={_scriptable:!0,_indexable:t=>"borderDash"!==t&&"fill"!==t};constructor(t){super(),this.animated=!0,this.options=void 0,this._chart=void 0,this._loop=void 0,this._fullLoop=void 0,this._path=void 0,this._points=void 0,this._segments=void 0,this._decimated=!1,this._pointsUpdated=!1,this._datasetIndex=void 0,t&&Object.assign(this,t)}updateControlPoints(t,e){let i=this.options;if((i.tension||"monotone"===i.cubicInterpolationMode)&&!i.stepped&&!this._pointsUpdated){let a=i.spanGaps?this._loop:this._fullLoop;(0,s.an)(this._points,i,t,a,e),this._pointsUpdated=!0}}set points(t){this._points=t,delete this._segments,delete this._path,this._pointsUpdated=!1}get points(){return this._points}get segments(){return this._segments||(this._segments=(0,s.ao)(this,this.options.segment))}first(){let t=this.segments,e=this.points;return t.length&&e[t[0].start]}last(){let t=this.segments,e=this.points,i=t.length;return i&&e[t[i-1].end]}interpolate(t,e){let i,a,n=this.options,r=t[e],o=this.points,l=(0,s.ap)(this,{property:e,start:r,end:r});if(!l.length)return;let h=[],d=n.stepped?s.aq:n.tension||"monotone"===n.cubicInterpolationMode?s.ar:s.as;for(i=0,a=l.length;i<a;++i){let{start:s,end:a}=l[i],c=o[s],u=o[a];if(c===u){h.push(c);continue}let f=Math.abs((r-c[e])/(u[e]-c[e])),g=d(c,u,f,n.stepped);g[e]=t[e],h.push(g)}return 1===h.length?h[0]:h}pathSegment(t,e,i){return t8(this)(t,this,e,i)}path(t,e,i){let s=this.segments,a=t8(this),n=this._loop;for(let r of(e=e||0,i=i||this.points.length-e,s))n&=a(t,this,r,{start:e,end:e+i-1});return!!n}draw(t,e,i,s){let a=this.options||{};(this.points||[]).length&&a.borderWidth&&(t.save(),function(t,e,i,s){if(t4&&!e.options.segment){let a;(a=e._path)||(a=e._path=new Path2D,e.path(a,i,s)&&a.closePath()),t0(t,e.options),t.stroke(a)}else{let{segments:a,options:n}=e,r=t8(e);for(let o of a)t0(t,n,o.style),t.beginPath(),r(t,e,o,{start:i,end:i+s-1})&&t.closePath(),t.stroke()}}(t,this,i,s),t.restore()),this.animated&&(this._pointsUpdated=!1,this._path=void 0)}}function t7(t,e,i,s){let a=t.options,{[i]:n}=t.getProps([i],s);return Math.abs(e-n)<a.radius+a.hitRadius}class t9 extends tp{static id="point";parsed;skip;stop;static defaults={borderWidth:1,hitRadius:1,hoverBorderWidth:1,hoverRadius:4,pointStyle:"circle",radius:3,rotation:0};static defaultRoutes={backgroundColor:"backgroundColor",borderColor:"borderColor"};constructor(t){super(),this.options=void 0,this.parsed=void 0,this.skip=void 0,this.stop=void 0,t&&Object.assign(this,t)}inRange(t,e,i){let s=this.options,{x:a,y:n}=this.getProps(["x","y"],i);return Math.pow(t-a,2)+Math.pow(e-n,2)<Math.pow(s.hitRadius+s.radius,2)}inXRange(t,e){return t7(this,t,"x",e)}inYRange(t,e){return t7(this,t,"y",e)}getCenterPoint(t){let{x:e,y:i}=this.getProps(["x","y"],t);return{x:e,y:i}}size(t){let e=(t=t||this.options||{}).radius||0,i=(e=Math.max(e,e&&t.hoverRadius||0))&&t.borderWidth||0;return(e+i)*2}draw(t,e){let i=this.options;!this.skip&&!(i.radius<.1)&&(0,s.C)(this,e,this.size(i)/2)&&(t.strokeStyle=i.borderColor,t.lineWidth=i.borderWidth,t.fillStyle=i.backgroundColor,(0,s.av)(t,i,this.x,this.y))}getRange(){let t=this.options||{};return t.radius+t.hitRadius}}function et(t,e){let i,s,a,n,r,{x:o,y:l,base:h,width:d,height:c}=t.getProps(["x","y","base","width","height"],e);return t.horizontal?(r=c/2,i=Math.min(o,h),s=Math.max(o,h),a=l-r,n=l+r):(i=o-(r=d/2),s=o+r,a=Math.min(l,h),n=Math.max(l,h)),{left:i,top:a,right:s,bottom:n}}function ee(t,e,i,a){return t?0:(0,s.S)(e,i,a)}function ei(t,e,i,a){let n=null===e,r=null===i,o=t&&!(n&&r)&&et(t,a);return o&&(n||(0,s.ak)(e,o.left,o.right))&&(r||(0,s.ak)(i,o.top,o.bottom))}function es(t,e){t.rect(e.x,e.y,e.w,e.h)}function ea(t,e,i={}){let s=t.x!==i.x?-e:0,a=t.y!==i.y?-e:0,n=(t.x+t.w!==i.x+i.w?e:0)-s,r=(t.y+t.h!==i.y+i.h?e:0)-a;return{x:t.x+s,y:t.y+a,w:t.w+n,h:t.h+r,radius:t.radius}}class en extends tp{static id="bar";static defaults={borderSkipped:"start",borderWidth:0,borderRadius:0,inflateAmount:"auto",pointStyle:void 0};static defaultRoutes={backgroundColor:"backgroundColor",borderColor:"borderColor"};constructor(t){super(),this.options=void 0,this.horizontal=void 0,this.base=void 0,this.width=void 0,this.height=void 0,this.inflateAmount=void 0,t&&Object.assign(this,t)}draw(t){var e;let{inflateAmount:i,options:{borderColor:a,backgroundColor:n}}=this,{inner:r,outer:o}=function(t){let e=et(t),i=e.right-e.left,a=e.bottom-e.top,n=function(t,e,i){let a=t.options.borderWidth,n=t.borderSkipped,r=(0,s.ax)(a);return{t:ee(n.top,r.top,0,i),r:ee(n.right,r.right,0,e),b:ee(n.bottom,r.bottom,0,i),l:ee(n.left,r.left,0,e)}}(t,i/2,a/2),r=function(t,e,i){let{enableBorderRadius:a}=t.getProps(["enableBorderRadius"]),n=t.options.borderRadius,r=(0,s.ay)(n),o=Math.min(e,i),l=t.borderSkipped,h=a||(0,s.i)(n);return{topLeft:ee(!h||l.top||l.left,r.topLeft,0,o),topRight:ee(!h||l.top||l.right,r.topRight,0,o),bottomLeft:ee(!h||l.bottom||l.left,r.bottomLeft,0,o),bottomRight:ee(!h||l.bottom||l.right,r.bottomRight,0,o)}}(t,i/2,a/2);return{outer:{x:e.left,y:e.top,w:i,h:a,radius:r},inner:{x:e.left+n.l,y:e.top+n.t,w:i-n.l-n.r,h:a-n.t-n.b,radius:{topLeft:Math.max(0,r.topLeft-Math.max(n.t,n.l)),topRight:Math.max(0,r.topRight-Math.max(n.t,n.r)),bottomLeft:Math.max(0,r.bottomLeft-Math.max(n.b,n.l)),bottomRight:Math.max(0,r.bottomRight-Math.max(n.b,n.r))}}}}(this),l=(e=o.radius).topLeft||e.topRight||e.bottomLeft||e.bottomRight?s.aw:es;t.save(),(o.w!==r.w||o.h!==r.h)&&(t.beginPath(),l(t,ea(o,i,r)),t.clip(),l(t,ea(r,-i,o)),t.fillStyle=a,t.fill("evenodd")),t.beginPath(),l(t,ea(r,i)),t.fillStyle=n,t.fill(),t.restore()}inRange(t,e,i){return ei(this,t,e,i)}inXRange(t,e){return ei(this,t,null,e)}inYRange(t,e){return ei(this,null,t,e)}getCenterPoint(t){let{x:e,y:i,base:s,horizontal:a}=this.getProps(["x","y","base","horizontal"],t);return{x:a?(e+s)/2:e,y:a?i:(i+s)/2}}getRange(t){return"x"===t?this.width/2:this.height/2}}let er=["rgb(54, 162, 235)","rgb(255, 99, 132)","rgb(255, 159, 64)","rgb(255, 205, 86)","rgb(75, 192, 192)","rgb(153, 102, 255)","rgb(201, 203, 207)"],eo=er.map(t=>t.replace("rgb(","rgba(").replace(")",", 0.5)"));function el(t,e,i,a){if(a)return;let n=e[t],r=i[t];return"angle"===t&&(n=(0,s.al)(n),r=(0,s.al)(r)),{property:t,start:n,end:r}}function eh(t,e,i){for(;e>t;e--){let t=i[e];if(!isNaN(t.x)&&!isNaN(t.y))break}return e}function ed(t,e,i,s){return t&&e?s(t[i],e[i]):t?t[i]:e?e[i]:0}function ec(t,e){let i=[],a=!1;return(0,s.b)(t)?(a=!0,i=t):i=function(t,e){let{x:i=null,y:s=null}=t||{},a=e.points,n=[];return e.segments.forEach(({start:t,end:e})=>{e=eh(t,e,a);let r=a[t],o=a[e];null!==s?(n.push({x:r.x,y:s}),n.push({x:o.x,y:s})):null!==i&&(n.push({x:i,y:r.y}),n.push({x:i,y:o.y}))}),n}(t,e),i.length?new t6({points:i,options:{tension:0},_loop:a,_fullLoop:a}):null}function eu(t){return t&&!1!==t.fill}class ef{constructor(t){this.x=t.x,this.y=t.y,this.radius=t.radius}pathSegment(t,e,i){let{x:a,y:n,radius:r}=this;return e=e||{start:0,end:s.T},t.arc(a,n,r,e.end,e.start,!0),!i.bounds}interpolate(t){let{x:e,y:i,radius:s}=this,a=t.angle;return{x:e+Math.cos(a)*s,y:i+Math.sin(a)*s,angle:a}}}function eg(t,e,i){let a=function(t){let{chart:e,fill:i,line:a}=t;if((0,s.g)(i)){var n,r=e,o=i;let t=r.getDatasetMeta(o);return t&&r.isDatasetVisible(o)?t.dataset:null}if("stack"===i)return function(t){let{scale:e,index:i,line:a}=t,n=[],r=a.segments,o=a.points,l=function(t,e){let i=[],s=t.getMatchingVisibleMetas("line");for(let t=0;t<s.length;t++){let a=s[t];if(a.index===e)break;a.hidden||i.unshift(a.dataset)}return i}(e,i);l.push(ec({x:null,y:e.bottom},a));for(let t=0;t<r.length;t++){let e=r[t];for(let t=e.start;t<=e.end;t++)!function(t,e,i){let a=[];for(let n=0;n<i.length;n++){let{first:r,last:o,point:l}=function(t,e,i){let a=t.interpolate(e,"x");if(!a)return{};let n=a[i],r=t.segments,o=t.points,l=!1,h=!1;for(let t=0;t<r.length;t++){let e=r[t],a=o[e.start][i],d=o[e.end][i];if((0,s.ak)(n,a,d)){l=n===a,h=n===d;break}}return{first:l,last:h,point:a}}(i[n],e,"x");if(l&&(!r||!o)){if(r)a.unshift(l);else if(t.push(l),!o)break}}t.push(...a)}(n,o[t],l)}return new t6({points:n,options:{}})}(t);if("shape"===i)return!0;let l=((n=t).scale||{}).getPointPositionForValue?function(t){let e,{scale:i,fill:a}=t,n=i.options,r=i.getLabels().length,o=n.reverse?i.max:i.min,l="start"===a?o:"end"===a?i.options.reverse?i.min:i.max:(0,s.i)(a)?a.value:i.getBaseValue(),h=[];if(n.grid.circular){let t=i.getPointPositionForValue(0,o);return new ef({x:t.x,y:t.y,radius:i.getDistanceFromCenterForValue(l)})}for(let t=0;t<r;++t)h.push(i.getPointPositionForValue(t,l));return h}(n):function(t){let e,{scale:i={},fill:a}=t,n=(e=null,"start"===a?e=i.bottom:"end"===a?e=i.top:(0,s.i)(a)?e=i.getPixelForValue(a.value):i.getBasePixel&&(e=i.getBasePixel()),e);if((0,s.g)(n)){let t=i.isHorizontal();return{x:t?n:null,y:t?null:n}}return null}(n);return l instanceof ef?l:ec(l,a)}(e),{chart:n,index:r,line:o,scale:l,axis:h}=e,d=o.options,c=d.fill,u=d.backgroundColor,{above:f=u,below:g=u}=c||{},p=n.getDatasetMeta(r),m=(0,s.ah)(n,p);a&&o.points.length&&((0,s.Y)(t,i),function(t,e){let{line:i,target:s,above:a,below:n,area:r,scale:o,clip:l}=e,h=i._loop?"angle":e.axis;t.save();let d=n;n!==a&&("x"===h?(ep(t,s,r.top),eb(t,{line:i,target:s,color:a,scale:o,property:h,clip:l}),t.restore(),t.save(),ep(t,s,r.bottom)):"y"===h&&(em(t,s,r.left),eb(t,{line:i,target:s,color:n,scale:o,property:h,clip:l}),t.restore(),t.save(),em(t,s,r.right),d=a)),eb(t,{line:i,target:s,color:d,scale:o,property:h,clip:l}),t.restore()}(t,{line:o,target:a,above:f,below:g,area:i,scale:l,axis:h,clip:m}),(0,s.$)(t))}function ep(t,e,i){let{segments:s,points:a}=e,n=!0,r=!1;for(let o of(t.beginPath(),s)){let{start:s,end:l}=o,h=a[s],d=a[eh(s,l,a)];n?(t.moveTo(h.x,h.y),n=!1):(t.lineTo(h.x,i),t.lineTo(h.x,h.y)),(r=!!e.pathSegment(t,o,{move:r}))?t.closePath():t.lineTo(d.x,i)}t.lineTo(e.first().x,i),t.closePath(),t.clip()}function em(t,e,i){let{segments:s,points:a}=e,n=!0,r=!1;for(let o of(t.beginPath(),s)){let{start:s,end:l}=o,h=a[s],d=a[eh(s,l,a)];n?(t.moveTo(h.x,h.y),n=!1):(t.lineTo(i,h.y),t.lineTo(h.x,h.y)),(r=!!e.pathSegment(t,o,{move:r}))?t.closePath():t.lineTo(i,d.y)}t.lineTo(i,e.first().y),t.closePath(),t.clip()}function eb(t,e){let{line:i,target:a,property:n,color:r,scale:o,clip:l}=e;for(let{source:e,target:h,start:d,end:c}of function(t,e,i){let a=t.segments,n=t.points,r=e.points,o=[];for(let t of a){let{start:a,end:l}=t;l=eh(a,l,n);let h=el(i,n[a],n[l],t.loop);if(!e.segments){o.push({source:t,target:h,start:n[a],end:n[l]});continue}for(let a of(0,s.ap)(e,h)){let e=el(i,r[a.start],r[a.end],a.loop);for(let r of(0,s.az)(t,n,e))o.push({source:r,target:a,start:{[i]:ed(h,e,"start",Math.max)},end:{[i]:ed(h,e,"end",Math.min)}})}}return o}(i,a,n)){let s,{style:{backgroundColor:u=r}={}}=e,f=!0!==a;t.save(),t.fillStyle=u,function(t,e,i,s){let a=e.chart.chartArea,{property:n,start:r,end:o}=s||{};if("x"===n||"y"===n){let e,s,l,h;"x"===n?(e=r,s=a.top,l=o,h=a.bottom):(e=a.left,s=r,l=a.right,h=o),t.beginPath(),i&&(e=Math.max(e,i.left),l=Math.min(l,i.right),s=Math.max(s,i.top),h=Math.min(h,i.bottom)),t.rect(e,s,l-e,h-s),t.clip()}}(t,o,l,f&&el(n,d,c)),t.beginPath();let g=!!i.pathSegment(t,e);if(f){g?t.closePath():ex(t,a,c,n);let e=!!a.pathSegment(t,h,{move:g,reverse:!0});(s=g&&e)||ex(t,a,d,n)}t.closePath(),t.fill(s?"evenodd":"nonzero"),t.restore()}}function ex(t,e,i,s){let a=e.interpolate(i,s);a&&t.lineTo(a.x,a.y)}var e_={id:"filler",afterDatasetsUpdate(t,e,i){let a,n,r,o,l=(t.data.datasets||[]).length,h=[];for(n=0;n<l;++n)r=(a=t.getDatasetMeta(n)).dataset,o=null,r&&r.options&&r instanceof t6&&(o={visible:t.isDatasetVisible(n),index:n,fill:function(t,e,i){var a,n,r,o;let l=function(t){let e=t.options,i=e.fill,a=(0,s.v)(i&&i.target,i);return void 0===a&&(a=!!e.backgroundColor),!1!==a&&null!==a&&(!0===a?"origin":a)}(t);if((0,s.i)(l))return!isNaN(l.value)&&l;let h=parseFloat(l);return(0,s.g)(h)&&Math.floor(h)===h?(a=l[0],n=e,r=h,o=i,("-"===a||"+"===a)&&(r=n+r),r!==n&&!(r<0)&&!(r>=o)&&r):["origin","start","end","stack","shape"].indexOf(l)>=0&&l}(r,n,l),chart:t,axis:a.controller.options.indexAxis,scale:a.vScale,line:r}),a.$filler=o,h.push(o);for(n=0;n<l;++n)(o=h[n])&&!1!==o.fill&&(o.fill=function(t,e,i){let a,n=t[e].fill,r=[e];if(!i)return n;for(;!1!==n&&-1===r.indexOf(n);){if(!(0,s.g)(n))return n;if(!(a=t[n]))break;if(a.visible)return n;r.push(n),n=a.fill}return!1}(h,n,i.propagate))},beforeDraw(t,e,i){let s="beforeDraw"===i.drawTime,a=t.getSortedVisibleDatasetMetas(),n=t.chartArea;for(let e=a.length-1;e>=0;--e){let i=a[e].$filler;i&&(i.line.updateControlPoints(n,i.axis),s&&i.fill&&eg(t.ctx,i,n))}},beforeDatasetsDraw(t,e,i){if("beforeDatasetsDraw"!==i.drawTime)return;let s=t.getSortedVisibleDatasetMetas();for(let e=s.length-1;e>=0;--e){let i=s[e].$filler;eu(i)&&eg(t.ctx,i,t.chartArea)}},beforeDatasetDraw(t,e,i){let s=e.meta.$filler;eu(s)&&"beforeDatasetDraw"===i.drawTime&&eg(t.ctx,s,t.chartArea)},defaults:{propagate:!0,drawTime:"beforeDatasetDraw"}};let ey=(t,e)=>{let{boxHeight:i=e,boxWidth:s=e}=t;return t.usePointStyle&&(i=Math.min(i,e),s=t.pointStyleWidth||Math.min(s,e)),{boxWidth:s,boxHeight:i,itemHeight:Math.max(e,i)}},ev=(t,e)=>null!==t&&null!==e&&t.datasetIndex===e.datasetIndex&&t.index===e.index;class eM extends tp{constructor(t){super(),this._added=!1,this.legendHitBoxes=[],this._hoveredItem=null,this.doughnutMode=!1,this.chart=t.chart,this.options=t.options,this.ctx=t.ctx,this.legendItems=void 0,this.columnSizes=void 0,this.lineWidths=void 0,this.maxHeight=void 0,this.maxWidth=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.height=void 0,this.width=void 0,this._margins=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(t,e,i){this.maxWidth=t,this.maxHeight=e,this._margins=i,this.setDimensions(),this.buildLabels(),this.fit()}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=this._margins.left,this.right=this.width):(this.height=this.maxHeight,this.top=this._margins.top,this.bottom=this.height)}buildLabels(){let t=this.options.labels||{},e=(0,s.Q)(t.generateLabels,[this.chart],this)||[];t.filter&&(e=e.filter(e=>t.filter(e,this.chart.data))),t.sort&&(e=e.sort((e,i)=>t.sort(e,i,this.chart.data))),this.options.reverse&&e.reverse(),this.legendItems=e}fit(){let t,e,{options:i,ctx:a}=this;if(!i.display){this.width=this.height=0;return}let n=i.labels,r=(0,s.a0)(n.font),o=r.size,l=this._computeTitleHeight(),{boxWidth:h,itemHeight:d}=ey(n,o);a.font=r.string,this.isHorizontal()?(t=this.maxWidth,e=this._fitRows(l,o,h,d)+10):(e=this.maxHeight,t=this._fitCols(l,r,h,d)+10),this.width=Math.min(t,i.maxWidth||this.maxWidth),this.height=Math.min(e,i.maxHeight||this.maxHeight)}_fitRows(t,e,i,s){let{ctx:a,maxWidth:n,options:{labels:{padding:r}}}=this,o=this.legendHitBoxes=[],l=this.lineWidths=[0],h=s+r,d=t;a.textAlign="left",a.textBaseline="middle";let c=-1,u=-h;return this.legendItems.forEach((t,f)=>{let g=i+e/2+a.measureText(t.text).width;(0===f||l[l.length-1]+g+2*r>n)&&(d+=h,l[l.length-(f>0?0:1)]=0,u+=h,c++),o[f]={left:0,top:u,row:c,width:g,height:s},l[l.length-1]+=g+r}),d}_fitCols(t,e,i,s){let{ctx:a,maxHeight:n,options:{labels:{padding:r}}}=this,o=this.legendHitBoxes=[],l=this.columnSizes=[],h=n-t,d=r,c=0,u=0,f=0,g=0;return this.legendItems.forEach((t,n)=>{var p,m,b,x,_,y,v,M,w,k,P,S;let D,O,{itemWidth:C,itemHeight:T}=(p=i,m=e,b=a,x=t,_=s,{itemWidth:(y=x,v=p,M=m,w=b,(D=y.text)&&"string"!=typeof D&&(D=D.reduce((t,e)=>t.length>e.length?t:e)),v+M.size/2+w.measureText(D).width),itemHeight:(k=_,P=x,S=m.lineHeight,O=k,"string"!=typeof P.text&&(O=ew(P,S)),O)});n>0&&u+T+2*r>h&&(d+=c+r,l.push({width:c,height:u}),f+=c+r,g++,c=u=0),o[n]={left:f,top:u,col:g,width:C,height:T},c=Math.max(c,C),u+=T+r}),d+=c,l.push({width:c,height:u}),d}adjustHitBoxes(){if(!this.options.display)return;let t=this._computeTitleHeight(),{legendHitBoxes:e,options:{align:i,labels:{padding:a},rtl:n}}=this,r=(0,s.aA)(n,this.left,this.width);if(this.isHorizontal()){let n=0,o=(0,s.a2)(i,this.left+a,this.right-this.lineWidths[n]);for(let l of e)n!==l.row&&(n=l.row,o=(0,s.a2)(i,this.left+a,this.right-this.lineWidths[n])),l.top+=this.top+t+a,l.left=r.leftForLtr(r.x(o),l.width),o+=l.width+a}else{let n=0,o=(0,s.a2)(i,this.top+t+a,this.bottom-this.columnSizes[n].height);for(let l of e)l.col!==n&&(n=l.col,o=(0,s.a2)(i,this.top+t+a,this.bottom-this.columnSizes[n].height)),l.top=o,l.left+=this.left+a,l.left=r.leftForLtr(r.x(l.left),l.width),o+=l.height+a}}isHorizontal(){return"top"===this.options.position||"bottom"===this.options.position}draw(){if(this.options.display){let t=this.ctx;(0,s.Y)(t,this),this._draw(),(0,s.$)(t)}}_draw(){let t,{options:e,columnSizes:i,lineWidths:a,ctx:n}=this,{align:r,labels:o}=e,l=s.d.color,h=(0,s.aA)(e.rtl,this.left,this.width),d=(0,s.a0)(o.font),{padding:c}=o,u=d.size,f=u/2;this.drawTitle(),n.textAlign=h.textAlign("left"),n.textBaseline="middle",n.lineWidth=.5,n.font=d.string;let{boxWidth:g,boxHeight:p,itemHeight:m}=ey(o,u),b=function(t,e,i){if(isNaN(g)||g<=0||isNaN(p)||p<0)return;n.save();let a=(0,s.v)(i.lineWidth,1);if(n.fillStyle=(0,s.v)(i.fillStyle,l),n.lineCap=(0,s.v)(i.lineCap,"butt"),n.lineDashOffset=(0,s.v)(i.lineDashOffset,0),n.lineJoin=(0,s.v)(i.lineJoin,"miter"),n.lineWidth=a,n.strokeStyle=(0,s.v)(i.strokeStyle,l),n.setLineDash((0,s.v)(i.lineDash,[])),o.usePointStyle){let r={radius:p*Math.SQRT2/2,pointStyle:i.pointStyle,rotation:i.rotation,borderWidth:a},l=h.xPlus(t,g/2);(0,s.aE)(n,r,l,e+f,o.pointStyleWidth&&g)}else{let r=e+Math.max((u-p)/2,0),o=h.leftForLtr(t,g),l=(0,s.ay)(i.borderRadius);n.beginPath(),Object.values(l).some(t=>0!==t)?(0,s.aw)(n,{x:o,y:r,w:g,h:p,radius:l}):n.rect(o,r,g,p),n.fill(),0!==a&&n.stroke()}n.restore()},x=function(t,e,i){(0,s.Z)(n,i.text,t,e+m/2,d,{strikethrough:i.hidden,textAlign:h.textAlign(i.textAlign)})},_=this.isHorizontal(),y=this._computeTitleHeight();t=_?{x:(0,s.a2)(r,this.left+c,this.right-a[0]),y:this.top+c+y,line:0}:{x:this.left+c,y:(0,s.a2)(r,this.top+y+c,this.bottom-i[0].height),line:0},(0,s.aB)(this.ctx,e.textDirection);let v=m+c;this.legendItems.forEach((l,u)=>{n.strokeStyle=l.fontColor,n.fillStyle=l.fontColor;let p=n.measureText(l.text).width,m=h.textAlign(l.textAlign||(l.textAlign=o.textAlign)),M=g+f+p,w=t.x,k=t.y;if(h.setWidth(this.width),_?u>0&&w+M+c>this.right&&(k=t.y+=v,t.line++,w=t.x=(0,s.a2)(r,this.left+c,this.right-a[t.line])):u>0&&k+v>this.bottom&&(w=t.x=w+i[t.line].width+c,t.line++,k=t.y=(0,s.a2)(r,this.top+y+c,this.bottom-i[t.line].height)),b(h.x(w),k,l),w=(0,s.aC)(m,w+g+f,_?w+M:this.right,e.rtl),x(h.x(w),k,l),_)t.x+=M+c;else if("string"!=typeof l.text){let e=d.lineHeight;t.y+=ew(l,e)+c}else t.y+=v}),(0,s.aD)(this.ctx,e.textDirection)}drawTitle(){let t,e=this.options,i=e.title,a=(0,s.a0)(i.font),n=(0,s.E)(i.padding);if(!i.display)return;let r=(0,s.aA)(e.rtl,this.left,this.width),o=this.ctx,l=i.position,h=a.size/2,d=n.top+h,c=this.left,u=this.width;if(this.isHorizontal())u=Math.max(...this.lineWidths),t=this.top+d,c=(0,s.a2)(e.align,c,this.right-u);else{let i=this.columnSizes.reduce((t,e)=>Math.max(t,e.height),0);t=d+(0,s.a2)(e.align,this.top,this.bottom-i-e.labels.padding-this._computeTitleHeight())}let f=(0,s.a2)(l,c,c+u);o.textAlign=r.textAlign((0,s.a1)(l)),o.textBaseline="middle",o.strokeStyle=i.color,o.fillStyle=i.color,o.font=a.string,(0,s.Z)(o,i.text,f,t,a)}_computeTitleHeight(){let t=this.options.title,e=(0,s.a0)(t.font),i=(0,s.E)(t.padding);return t.display?e.lineHeight+i.height:0}_getLegendItemAt(t,e){let i,a,n;if((0,s.ak)(t,this.left,this.right)&&(0,s.ak)(e,this.top,this.bottom)){for(i=0,n=this.legendHitBoxes;i<n.length;++i)if(a=n[i],(0,s.ak)(t,a.left,a.left+a.width)&&(0,s.ak)(e,a.top,a.top+a.height))return this.legendItems[i]}return null}handleEvent(t){var e,i;let a=this.options;if(e=t.type,i=a,("mousemove"!==e&&"mouseout"!==e||!i.onHover&&!i.onLeave)&&(!i.onClick||"click"!==e&&"mouseup"!==e))return;let n=this._getLegendItemAt(t.x,t.y);if("mousemove"===t.type||"mouseout"===t.type){let e=this._hoveredItem,i=ev(e,n);e&&!i&&(0,s.Q)(a.onLeave,[t,e,this],this),this._hoveredItem=n,n&&!i&&(0,s.Q)(a.onHover,[t,n,this],this)}else n&&(0,s.Q)(a.onClick,[t,n,this],this)}}function ew(t,e){return e*(t.text?t.text.length:0)}var ek={id:"legend",_element:eM,start(t,e,i){let s=t.legend=new eM({ctx:t.ctx,options:i,chart:t});Z.configure(t,s,i),Z.addBox(t,s)},stop(t){Z.removeBox(t,t.legend),delete t.legend},beforeUpdate(t,e,i){let s=t.legend;Z.configure(t,s,i),s.options=i},afterUpdate(t){let e=t.legend;e.buildLabels(),e.adjustHitBoxes()},afterEvent(t,e){e.replay||t.legend.handleEvent(e.event)},defaults:{display:!0,position:"top",align:"center",fullSize:!0,reverse:!1,weight:1e3,onClick(t,e,i){let s=e.datasetIndex,a=i.chart;a.isDatasetVisible(s)?(a.hide(s),e.hidden=!0):(a.show(s),e.hidden=!1)},onHover:null,onLeave:null,labels:{color:t=>t.chart.options.color,boxWidth:40,padding:10,generateLabels(t){let e=t.data.datasets,{labels:{usePointStyle:i,pointStyle:a,textAlign:n,color:r,useBorderRadius:o,borderRadius:l}}=t.legend.options;return t._getSortedDatasetMetas().map(t=>{let h=t.controller.getStyle(i?0:void 0),d=(0,s.E)(h.borderWidth);return{text:e[t.index].label,fillStyle:h.backgroundColor,fontColor:r,hidden:!t.visible,lineCap:h.borderCapStyle,lineDash:h.borderDash,lineDashOffset:h.borderDashOffset,lineJoin:h.borderJoinStyle,lineWidth:(d.width+d.height)/4,strokeStyle:h.borderColor,pointStyle:a||h.pointStyle,rotation:h.rotation,textAlign:n||h.textAlign,borderRadius:o&&(l||h.borderRadius),datasetIndex:t.index}},this)}},title:{color:t=>t.chart.options.color,display:!1,position:"center",text:""}},descriptors:{_scriptable:t=>!t.startsWith("on"),labels:{_scriptable:t=>!["generateLabels","filter","sort"].includes(t)}}};class eP extends tp{constructor(t){super(),this.chart=t.chart,this.options=t.options,this.ctx=t.ctx,this._padding=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(t,e){let i=this.options;if(this.left=0,this.top=0,!i.display){this.width=this.height=this.right=this.bottom=0;return}this.width=this.right=t,this.height=this.bottom=e;let a=(0,s.b)(i.text)?i.text.length:1;this._padding=(0,s.E)(i.padding);let n=a*(0,s.a0)(i.font).lineHeight+this._padding.height;this.isHorizontal()?this.height=n:this.width=n}isHorizontal(){let t=this.options.position;return"top"===t||"bottom"===t}_drawArgs(t){let e,i,a,{top:n,left:r,bottom:o,right:l,options:h}=this,d=h.align,c=0;return this.isHorizontal()?(i=(0,s.a2)(d,r,l),a=n+t,e=l-r):("left"===h.position?(i=r+t,a=(0,s.a2)(d,o,n),c=-.5*s.P):(i=l-t,a=(0,s.a2)(d,n,o),c=.5*s.P),e=o-n),{titleX:i,titleY:a,maxWidth:e,rotation:c}}draw(){let t=this.ctx,e=this.options;if(!e.display)return;let i=(0,s.a0)(e.font),a=i.lineHeight/2+this._padding.top,{titleX:n,titleY:r,maxWidth:o,rotation:l}=this._drawArgs(a);(0,s.Z)(t,e.text,0,0,i,{color:e.color,maxWidth:o,rotation:l,textAlign:(0,s.a1)(e.align),textBaseline:"middle",translation:[n,r]})}}var eS={id:"title",_element:eP,start(t,e,i){let s=new eP({ctx:t.ctx,options:i,chart:t});Z.configure(t,s,i),Z.addBox(t,s),t.titleBlock=s},stop(t){let e=t.titleBlock;Z.removeBox(t,e),delete t.titleBlock},beforeUpdate(t,e,i){let s=t.titleBlock;Z.configure(t,s,i),s.options=i},defaults:{align:"center",display:!1,font:{weight:"bold"},fullSize:!0,padding:10,position:"top",text:"",weight:2e3},defaultRoutes:{color:"color"},descriptors:{_scriptable:!0,_indexable:!1}};new WeakMap;let eD={average(t){let e,i;if(!t.length)return!1;let s=new Set,a=0,n=0;for(e=0,i=t.length;e<i;++e){let i=t[e].element;if(i&&i.hasValue()){let t=i.tooltipPosition();s.add(t.x),a+=t.y,++n}}return 0!==n&&0!==s.size&&{x:[...s].reduce((t,e)=>t+e)/s.size,y:a/n}},nearest(t,e){let i,a,n;if(!t.length)return!1;let r=e.x,o=e.y,l=Number.POSITIVE_INFINITY;for(i=0,a=t.length;i<a;++i){let a=t[i].element;if(a&&a.hasValue()){let t=a.getCenterPoint(),i=(0,s.aF)(e,t);i<l&&(l=i,n=a)}}if(n){let t=n.tooltipPosition();r=t.x,o=t.y}return{x:r,y:o}}};function eO(t,e){return e&&((0,s.b)(e)?Array.prototype.push.apply(t,e):t.push(e)),t}function eC(t){return("string"==typeof t||t instanceof String)&&t.indexOf("\n")>-1?t.split("\n"):t}function eT(t,e){let i=t.chart.ctx,{body:a,footer:n,title:r}=t,{boxWidth:o,boxHeight:l}=e,h=(0,s.a0)(e.bodyFont),d=(0,s.a0)(e.titleFont),c=(0,s.a0)(e.footerFont),u=r.length,f=n.length,g=a.length,p=(0,s.E)(e.padding),m=p.height,b=0,x=a.reduce((t,e)=>t+e.before.length+e.lines.length+e.after.length,0);x+=t.beforeBody.length+t.afterBody.length,u&&(m+=u*d.lineHeight+(u-1)*e.titleSpacing+e.titleMarginBottom),x&&(m+=g*(e.displayColors?Math.max(l,h.lineHeight):h.lineHeight)+(x-g)*h.lineHeight+(x-1)*e.bodySpacing),f&&(m+=e.footerMarginTop+f*c.lineHeight+(f-1)*e.footerSpacing);let _=0,y=function(t){b=Math.max(b,i.measureText(t).width+_)};return i.save(),i.font=d.string,(0,s.F)(t.title,y),i.font=h.string,(0,s.F)(t.beforeBody.concat(t.afterBody),y),_=e.displayColors?o+2+e.boxPadding:0,(0,s.F)(a,t=>{(0,s.F)(t.before,y),(0,s.F)(t.lines,y),(0,s.F)(t.after,y)}),_=0,i.font=c.string,(0,s.F)(t.footer,y),i.restore(),{width:b+=p.width,height:m}}function eA(t,e,i){let s=i.yAlign||e.yAlign||function(t,e){let{y:i,height:s}=e;return i<s/2?"top":i>t.height-s/2?"bottom":"center"}(t,i);return{xAlign:i.xAlign||e.xAlign||function(t,e,i,s){let{x:a,width:n}=i,{width:r,chartArea:{left:o,right:l}}=t,h="center";return"center"===s?h=a<=(o+l)/2?"left":"right":a<=n/2?h="left":a>=r-n/2&&(h="right"),function(t,e,i,s){let{x:a,width:n}=s,r=i.caretSize+i.caretPadding;if("left"===t&&a+n+r>e.width||"right"===t&&a-n-r<0)return!0}(h,t,e,i)&&(h="center"),h}(t,e,i,s),yAlign:s}}function eE(t,e,i,a){let{caretSize:n,caretPadding:r,cornerRadius:o}=t,{xAlign:l,yAlign:h}=i,d=n+r,{topLeft:c,topRight:u,bottomLeft:f,bottomRight:g}=(0,s.ay)(o),p=function(t,e){let{x:i,width:s}=t;return"right"===e?i-=s:"center"===e&&(i-=s/2),i}(e,l),m=function(t,e,i){let{y:s,height:a}=t;return"top"===e?s+=i:"bottom"===e?s-=a+i:s-=a/2,s}(e,h,d);return"center"===h?"left"===l?p+=d:"right"===l&&(p-=d):"left"===l?p-=Math.max(c,f)+n:"right"===l&&(p+=Math.max(u,g)+n),{x:(0,s.S)(p,0,a.width-e.width),y:(0,s.S)(m,0,a.height-e.height)}}function eL(t,e,i){let a=(0,s.E)(i.padding);return"center"===e?t.x+t.width/2:"right"===e?t.x+t.width-a.right:t.x+a.left}function eF(t,e){let i=e&&e.dataset&&e.dataset.tooltip&&e.dataset.tooltip.callbacks;return i?t.override(i):t}let eR={beforeTitle:s.aG,title(t){if(t.length>0){let e=t[0],i=e.chart.data.labels,s=i?i.length:0;if(this&&this.options&&"dataset"===this.options.mode)return e.dataset.label||"";if(e.label)return e.label;if(s>0&&e.dataIndex<s)return i[e.dataIndex]}return""},afterTitle:s.aG,beforeBody:s.aG,beforeLabel:s.aG,label(t){if(this&&this.options&&"dataset"===this.options.mode)return t.label+": "+t.formattedValue||t.formattedValue;let e=t.dataset.label||"";e&&(e+=": ");let i=t.formattedValue;return(0,s.k)(i)||(e+=i),e},labelColor(t){let e=t.chart.getDatasetMeta(t.datasetIndex).controller.getStyle(t.dataIndex);return{borderColor:e.borderColor,backgroundColor:e.backgroundColor,borderWidth:e.borderWidth,borderDash:e.borderDash,borderDashOffset:e.borderDashOffset,borderRadius:0}},labelTextColor(){return this.options.bodyColor},labelPointStyle(t){let e=t.chart.getDatasetMeta(t.datasetIndex).controller.getStyle(t.dataIndex);return{pointStyle:e.pointStyle,rotation:e.rotation}},afterLabel:s.aG,afterBody:s.aG,beforeFooter:s.aG,footer:s.aG,afterFooter:s.aG};function eI(t,e,i,s){let a=t[e].call(i,s);return void 0===a?eR[e].call(i,s):a}class ez extends tp{static positioners=eD;constructor(t){super(),this.opacity=0,this._active=[],this._eventPosition=void 0,this._size=void 0,this._cachedAnimations=void 0,this._tooltipItems=[],this.$animations=void 0,this.$context=void 0,this.chart=t.chart,this.options=t.options,this.dataPoints=void 0,this.title=void 0,this.beforeBody=void 0,this.body=void 0,this.afterBody=void 0,this.footer=void 0,this.xAlign=void 0,this.yAlign=void 0,this.x=void 0,this.y=void 0,this.height=void 0,this.width=void 0,this.caretX=void 0,this.caretY=void 0,this.labelColors=void 0,this.labelPointStyles=void 0,this.labelTextColors=void 0}initialize(t){this.options=t,this._cachedAnimations=void 0,this.$context=void 0}_resolveAnimations(){let t=this._cachedAnimations;if(t)return t;let e=this.chart,i=this.options.setContext(this.getContext()),s=i.enabled&&e.options.animation&&i.animations,a=new h(this.chart,s);return s._cacheable&&(this._cachedAnimations=Object.freeze(a)),a}getContext(){var t,e;return this.$context||(this.$context=(t=this.chart.getContext(),e=this._tooltipItems,(0,s.j)(t,{tooltip:this,tooltipItems:e,type:"tooltip"})))}getTitle(t,e){let{callbacks:i}=e,s=eI(i,"beforeTitle",this,t),a=eI(i,"title",this,t),n=eI(i,"afterTitle",this,t),r=[];return r=eO(r,eC(s)),r=eO(r,eC(a)),r=eO(r,eC(n))}getBeforeBody(t,e){return eO([],eC(eI(e.callbacks,"beforeBody",this,t)))}getBody(t,e){let{callbacks:i}=e,a=[];return(0,s.F)(t,t=>{let e={before:[],lines:[],after:[]},s=eF(i,t);eO(e.before,eC(eI(s,"beforeLabel",this,t))),eO(e.lines,eI(s,"label",this,t)),eO(e.after,eC(eI(s,"afterLabel",this,t))),a.push(e)}),a}getAfterBody(t,e){return eO([],eC(eI(e.callbacks,"afterBody",this,t)))}getFooter(t,e){let{callbacks:i}=e,s=eI(i,"beforeFooter",this,t),a=eI(i,"footer",this,t),n=eI(i,"afterFooter",this,t),r=[];return r=eO(r,eC(s)),r=eO(r,eC(a)),r=eO(r,eC(n))}_createItems(t){let e,i,a=this._active,n=this.chart.data,r=[],o=[],l=[],h=[];for(e=0,i=a.length;e<i;++e)h.push(function(t,e){let{element:i,datasetIndex:s,index:a}=e,n=t.getDatasetMeta(s).controller,{label:r,value:o}=n.getLabelAndValue(a);return{chart:t,label:r,parsed:n.getParsed(a),raw:t.data.datasets[s].data[a],formattedValue:o,dataset:n.getDataset(),dataIndex:a,datasetIndex:s,element:i}}(this.chart,a[e]));return t.filter&&(h=h.filter((e,i,s)=>t.filter(e,i,s,n))),t.itemSort&&(h=h.sort((e,i)=>t.itemSort(e,i,n))),(0,s.F)(h,e=>{let i=eF(t.callbacks,e);r.push(eI(i,"labelColor",this,e)),o.push(eI(i,"labelPointStyle",this,e)),l.push(eI(i,"labelTextColor",this,e))}),this.labelColors=r,this.labelPointStyles=o,this.labelTextColors=l,this.dataPoints=h,h}update(t,e){let i,s=this.options.setContext(this.getContext()),a=this._active,n=[];if(a.length){let t=eD[s.position].call(this,a,this._eventPosition);n=this._createItems(s),this.title=this.getTitle(n,s),this.beforeBody=this.getBeforeBody(n,s),this.body=this.getBody(n,s),this.afterBody=this.getAfterBody(n,s),this.footer=this.getFooter(n,s);let e=this._size=eT(this,s),r=Object.assign({},t,e),o=eA(this.chart,s,r),l=eE(s,r,o,this.chart);this.xAlign=o.xAlign,this.yAlign=o.yAlign,i={opacity:1,x:l.x,y:l.y,width:e.width,height:e.height,caretX:t.x,caretY:t.y}}else 0!==this.opacity&&(i={opacity:0});this._tooltipItems=n,this.$context=void 0,i&&this._resolveAnimations().update(this,i),t&&s.external&&s.external.call(this,{chart:this.chart,tooltip:this,replay:e})}drawCaret(t,e,i,s){let a=this.getCaretPosition(t,i,s);e.lineTo(a.x1,a.y1),e.lineTo(a.x2,a.y2),e.lineTo(a.x3,a.y3)}getCaretPosition(t,e,i){let a,n,r,o,l,h,{xAlign:d,yAlign:c}=this,{caretSize:u,cornerRadius:f}=i,{topLeft:g,topRight:p,bottomLeft:m,bottomRight:b}=(0,s.ay)(f),{x:x,y:_}=t,{width:y,height:v}=e;return"center"===c?(l=_+v/2,"left"===d?(n=(a=x)-u,o=l+u,h=l-u):(n=(a=x+y)+u,o=l-u,h=l+u),r=a):(n="left"===d?x+Math.max(g,m)+u:"right"===d?x+y-Math.max(p,b)-u:this.caretX,"top"===c?(l=(o=_)-u,a=n-u,r=n+u):(l=(o=_+v)+u,a=n+u,r=n-u),h=o),{x1:a,x2:n,x3:r,y1:o,y2:l,y3:h}}drawTitle(t,e,i){let a,n,r,o=this.title,l=o.length;if(l){let h=(0,s.aA)(i.rtl,this.x,this.width);for(r=0,t.x=eL(this,i.titleAlign,i),e.textAlign=h.textAlign(i.titleAlign),e.textBaseline="middle",a=(0,s.a0)(i.titleFont),n=i.titleSpacing,e.fillStyle=i.titleColor,e.font=a.string;r<l;++r)e.fillText(o[r],h.x(t.x),t.y+a.lineHeight/2),t.y+=a.lineHeight+n,r+1===l&&(t.y+=i.titleMarginBottom-n)}}_drawColorBox(t,e,i,a,n){let r=this.labelColors[i],o=this.labelPointStyles[i],{boxHeight:l,boxWidth:h}=n,d=(0,s.a0)(n.bodyFont),c=eL(this,"left",n),u=a.x(c),f=l<d.lineHeight?(d.lineHeight-l)/2:0,g=e.y+f;if(n.usePointStyle){let e={radius:Math.min(h,l)/2,pointStyle:o.pointStyle,rotation:o.rotation,borderWidth:1},i=a.leftForLtr(u,h)+h/2,d=g+l/2;t.strokeStyle=n.multiKeyBackground,t.fillStyle=n.multiKeyBackground,(0,s.av)(t,e,i,d),t.strokeStyle=r.borderColor,t.fillStyle=r.backgroundColor,(0,s.av)(t,e,i,d)}else{t.lineWidth=(0,s.i)(r.borderWidth)?Math.max(...Object.values(r.borderWidth)):r.borderWidth||1,t.strokeStyle=r.borderColor,t.setLineDash(r.borderDash||[]),t.lineDashOffset=r.borderDashOffset||0;let e=a.leftForLtr(u,h),i=a.leftForLtr(a.xPlus(u,1),h-2),o=(0,s.ay)(r.borderRadius);Object.values(o).some(t=>0!==t)?(t.beginPath(),t.fillStyle=n.multiKeyBackground,(0,s.aw)(t,{x:e,y:g,w:h,h:l,radius:o}),t.fill(),t.stroke(),t.fillStyle=r.backgroundColor,t.beginPath(),(0,s.aw)(t,{x:i,y:g+1,w:h-2,h:l-2,radius:o}),t.fill()):(t.fillStyle=n.multiKeyBackground,t.fillRect(e,g,h,l),t.strokeRect(e,g,h,l),t.fillStyle=r.backgroundColor,t.fillRect(i,g+1,h-2,l-2))}t.fillStyle=this.labelTextColors[i]}drawBody(t,e,i){let a,n,r,o,l,h,{body:d}=this,{bodySpacing:c,bodyAlign:u,displayColors:f,boxHeight:g,boxWidth:p,boxPadding:m}=i,b=(0,s.a0)(i.bodyFont),x=b.lineHeight,_=0,y=(0,s.aA)(i.rtl,this.x,this.width),v=function(i){e.fillText(i,y.x(t.x+_),t.y+x/2),t.y+=x+c},M=y.textAlign(u);for(e.textAlign=u,e.textBaseline="middle",e.font=b.string,t.x=eL(this,M,i),e.fillStyle=i.bodyColor,(0,s.F)(this.beforeBody,v),_=f&&"right"!==M?"center"===u?p/2+m:p+2+m:0,r=0,l=d.length;r<l;++r){for(a=d[r],e.fillStyle=this.labelTextColors[r],(0,s.F)(a.before,v),n=a.lines,f&&n.length&&(this._drawColorBox(e,t,r,y,i),x=Math.max(b.lineHeight,g)),o=0,h=n.length;o<h;++o)v(n[o]),x=b.lineHeight;(0,s.F)(a.after,v)}_=0,x=b.lineHeight,(0,s.F)(this.afterBody,v),t.y-=c}drawFooter(t,e,i){let a,n,r=this.footer,o=r.length;if(o){let l=(0,s.aA)(i.rtl,this.x,this.width);for(t.x=eL(this,i.footerAlign,i),t.y+=i.footerMarginTop,e.textAlign=l.textAlign(i.footerAlign),e.textBaseline="middle",a=(0,s.a0)(i.footerFont),e.fillStyle=i.footerColor,e.font=a.string,n=0;n<o;++n)e.fillText(r[n],l.x(t.x),t.y+a.lineHeight/2),t.y+=a.lineHeight+i.footerSpacing}}drawBackground(t,e,i,a){let{xAlign:n,yAlign:r}=this,{x:o,y:l}=t,{width:h,height:d}=i,{topLeft:c,topRight:u,bottomLeft:f,bottomRight:g}=(0,s.ay)(a.cornerRadius);e.fillStyle=a.backgroundColor,e.strokeStyle=a.borderColor,e.lineWidth=a.borderWidth,e.beginPath(),e.moveTo(o+c,l),"top"===r&&this.drawCaret(t,e,i,a),e.lineTo(o+h-u,l),e.quadraticCurveTo(o+h,l,o+h,l+u),"center"===r&&"right"===n&&this.drawCaret(t,e,i,a),e.lineTo(o+h,l+d-g),e.quadraticCurveTo(o+h,l+d,o+h-g,l+d),"bottom"===r&&this.drawCaret(t,e,i,a),e.lineTo(o+f,l+d),e.quadraticCurveTo(o,l+d,o,l+d-f),"center"===r&&"left"===n&&this.drawCaret(t,e,i,a),e.lineTo(o,l+c),e.quadraticCurveTo(o,l,o+c,l),e.closePath(),e.fill(),a.borderWidth>0&&e.stroke()}_updateAnimationTarget(t){let e=this.chart,i=this.$animations,s=i&&i.x,a=i&&i.y;if(s||a){let i=eD[t.position].call(this,this._active,this._eventPosition);if(!i)return;let n=this._size=eT(this,t),r=Object.assign({},i,this._size),o=eA(e,t,r),l=eE(t,r,o,e);(s._to!==l.x||a._to!==l.y)&&(this.xAlign=o.xAlign,this.yAlign=o.yAlign,this.width=n.width,this.height=n.height,this.caretX=i.x,this.caretY=i.y,this._resolveAnimations().update(this,l))}}_willRender(){return!!this.opacity}draw(t){let e=this.options.setContext(this.getContext()),i=this.opacity;if(!i)return;this._updateAnimationTarget(e);let a={width:this.width,height:this.height},n={x:this.x,y:this.y};i=.001>Math.abs(i)?0:i;let r=(0,s.E)(e.padding),o=this.title.length||this.beforeBody.length||this.body.length||this.afterBody.length||this.footer.length;e.enabled&&o&&(t.save(),t.globalAlpha=i,this.drawBackground(n,t,a,e),(0,s.aB)(t,e.textDirection),n.y+=r.top,this.drawTitle(n,t,e),this.drawBody(n,t,e),this.drawFooter(n,t,e),(0,s.aD)(t,e.textDirection),t.restore())}getActiveElements(){return this._active||[]}setActiveElements(t,e){let i=this._active,a=t.map(({datasetIndex:t,index:e})=>{let i=this.chart.getDatasetMeta(t);if(!i)throw Error("Cannot find a dataset at index "+t);return{datasetIndex:t,element:i.data[e],index:e}}),n=!(0,s.ai)(i,a),r=this._positionChanged(a,e);(n||r)&&(this._active=a,this._eventPosition=e,this._ignoreReplayEvents=!0,this.update(!0))}handleEvent(t,e,i=!0){if(e&&this._ignoreReplayEvents)return!1;this._ignoreReplayEvents=!1;let a=this.options,n=this._active||[],r=this._getActiveElements(t,n,e,i),o=this._positionChanged(r,t),l=e||!(0,s.ai)(r,n)||o;return l&&(this._active=r,(a.enabled||a.external)&&(this._eventPosition={x:t.x,y:t.y},this.update(!0,e))),l}_getActiveElements(t,e,i,s){let a=this.options;if("mouseout"===t.type)return[];if(!s)return e.filter(t=>this.chart.data.datasets[t.datasetIndex]&&void 0!==this.chart.getDatasetMeta(t.datasetIndex).controller.getParsed(t.index));let n=this.chart.getElementsAtEventForMode(t,a.mode,a,i);return a.reverse&&n.reverse(),n}_positionChanged(t,e){let{caretX:i,caretY:s,options:a}=this,n=eD[a.position].call(this,t,e);return!1!==n&&(i!==n.x||s!==n.y)}}var eV={id:"tooltip",_element:ez,positioners:eD,afterInit(t,e,i){i&&(t.tooltip=new ez({chart:t,options:i}))},beforeUpdate(t,e,i){t.tooltip&&t.tooltip.initialize(i)},reset(t,e,i){t.tooltip&&t.tooltip.initialize(i)},afterDraw(t){let e=t.tooltip;if(e&&e._willRender()){let i={tooltip:e};if(!1===t.notifyPlugins("beforeTooltipDraw",{...i,cancelable:!0}))return;e.draw(t.ctx),t.notifyPlugins("afterTooltipDraw",i)}},afterEvent(t,e){if(t.tooltip){let i=e.replay;t.tooltip.handleEvent(e.event,i,e.inChartArea)&&(e.changed=!0)}},defaults:{enabled:!0,external:null,position:"average",backgroundColor:"rgba(0,0,0,0.8)",titleColor:"#fff",titleFont:{weight:"bold"},titleSpacing:2,titleMarginBottom:6,titleAlign:"left",bodyColor:"#fff",bodySpacing:2,bodyFont:{},bodyAlign:"left",footerColor:"#fff",footerSpacing:2,footerMarginTop:6,footerFont:{weight:"bold"},footerAlign:"left",padding:6,caretPadding:2,caretSize:5,cornerRadius:6,boxHeight:(t,e)=>e.bodyFont.size,boxWidth:(t,e)=>e.bodyFont.size,multiKeyBackground:"#fff",displayColors:!0,boxPadding:0,borderColor:"rgba(0,0,0,0)",borderWidth:0,animation:{duration:400,easing:"easeOutQuart"},animations:{numbers:{type:"number",properties:["x","y","width","height","caretX","caretY"]},opacity:{easing:"linear",duration:200}},callbacks:eR},defaultRoutes:{bodyFont:"font",footerFont:"font",titleFont:"font"},descriptors:{_scriptable:t=>"filter"!==t&&"itemSort"!==t&&"external"!==t,_indexable:!1,callbacks:{_scriptable:!1,_indexable:!1},animation:{_fallback:!1},animations:{_fallback:"animation"}},additionalOptionScopes:["interaction"]};let eN=(t,e,i,s)=>("string"==typeof e?(i=t.push(e)-1,s.unshift({index:i,label:e})):isNaN(e)&&(i=null),i),eB=(t,e)=>null===t?null:(0,s.S)(Math.round(t),0,e);function eH(t){let e=this.getLabels();return t>=0&&t<e.length?e[t]:t}class ej extends tw{static id="category";static defaults={ticks:{callback:eH}};constructor(t){super(t),this._startValue=void 0,this._valueRange=0,this._addedLabels=[]}init(t){let e=this._addedLabels;if(e.length){let t=this.getLabels();for(let{index:i,label:s}of e)t[i]===s&&t.splice(i,1);this._addedLabels=[]}super.init(t)}parse(t,e){if((0,s.k)(t))return null;let i=this.getLabels();return eB(e=isFinite(e)&&i[e]===t?e:function(t,e,i,s){let a=t.indexOf(e);return -1===a?eN(t,e,i,s):a!==t.lastIndexOf(e)?i:a}(i,t,(0,s.v)(e,t),this._addedLabels),i.length-1)}determineDataLimits(){let{minDefined:t,maxDefined:e}=this.getUserBounds(),{min:i,max:s}=this.getMinMax(!0);"ticks"===this.options.bounds&&(t||(i=0),e||(s=this.getLabels().length-1)),this.min=i,this.max=s}buildTicks(){let t=this.min,e=this.max,i=this.options.offset,s=[],a=this.getLabels();a=0===t&&e===a.length-1?a:a.slice(t,e+1),this._valueRange=Math.max(a.length-!i,1),this._startValue=this.min-.5*!!i;for(let i=t;i<=e;i++)s.push({value:i});return s}getLabelForValue(t){return eH.call(this,t)}configure(){super.configure(),this.isHorizontal()||(this._reversePixels=!this._reversePixels)}getPixelForValue(t){return"number"!=typeof t&&(t=this.parse(t)),null===t?NaN:this.getPixelForDecimal((t-this._startValue)/this._valueRange)}getPixelForTick(t){let e=this.ticks;return t<0||t>e.length-1?null:this.getPixelForValue(e[t].value)}getValueForPixel(t){return Math.round(this._startValue+this.getDecimalForPixel(t)*this._valueRange)}getBasePixel(){return this.bottom}}function eW(t,e,{horizontal:i,minRotation:a}){let n=(0,s.t)(a),r=(i?Math.sin(n):Math.cos(n))||.001,o=.75*e*(""+t).length;return Math.min(e/r,o)}class e$ extends tw{constructor(t){super(t),this.start=void 0,this.end=void 0,this._startValue=void 0,this._endValue=void 0,this._valueRange=0}parse(t,e){return(0,s.k)(t)||("number"==typeof t||t instanceof Number)&&!isFinite(+t)?null:+t}handleTickRangeOptions(){let{beginAtZero:t}=this.options,{minDefined:e,maxDefined:i}=this.getUserBounds(),{min:a,max:n}=this,r=t=>a=e?a:t,o=t=>n=i?n:t;if(t){let t=(0,s.s)(a),e=(0,s.s)(n);t<0&&e<0?o(0):t>0&&e>0&&r(0)}if(a===n){let e=0===n?1:Math.abs(.05*n);o(n+e),t||r(a-e)}this.min=a,this.max=n}getTickLimit(){let t,{maxTicksLimit:e,stepSize:i}=this.options.ticks;return i?(t=Math.ceil(this.max/i)-Math.floor(this.min/i)+1)>1e3&&(console.warn(`scales.${this.id}.ticks.stepSize: ${i} would result generating up to ${t} ticks. Limiting to 1000.`),t=1e3):(t=this.computeTickLimit(),e=e||11),e&&(t=Math.min(e,t)),t}computeTickLimit(){return Number.POSITIVE_INFINITY}buildTicks(){let t=this.options,e=t.ticks,i=this.getTickLimit(),a=function(t,e){let i,a,n,r,o=[],{bounds:l,step:h,min:d,max:c,precision:u,count:f,maxTicks:g,maxDigits:p,includeBounds:m}=t,b=h||1,x=g-1,{min:_,max:y}=e,v=!(0,s.k)(d),M=!(0,s.k)(c),w=!(0,s.k)(f),k=(y-_)/(p+1),P=(0,s.aI)((y-_)/x/b)*b;if(P<1e-14&&!v&&!M)return[{value:_},{value:y}];(r=Math.ceil(y/P)-Math.floor(_/P))>x&&(P=(0,s.aI)(r*P/x/b)*b),(0,s.k)(u)||(P=Math.ceil(P*(i=Math.pow(10,u)))/i),"ticks"===l?(a=Math.floor(_/P)*P,n=Math.ceil(y/P)*P):(a=_,n=y),v&&M&&h&&(0,s.aJ)((c-d)/h,P/1e3)?(r=Math.round(Math.min((c-d)/P,g)),P=(c-d)/r,a=d,n=c):w?(a=v?d:a,P=((n=M?c:n)-a)/(r=f-1)):(r=(n-a)/P,r=(0,s.aK)(r,Math.round(r),P/1e3)?Math.round(r):Math.ceil(r));let S=Math.max((0,s.aL)(P),(0,s.aL)(a));a=Math.round(a*(i=Math.pow(10,(0,s.k)(u)?S:u)))/i,n=Math.round(n*i)/i;let D=0;for(v&&(m&&a!==d?(o.push({value:d}),a<d&&D++,(0,s.aK)(Math.round((a+D*P)*i)/i,d,eW(d,k,t))&&D++):a<d&&D++);D<r;++D){let t=Math.round((a+D*P)*i)/i;if(M&&t>c)break;o.push({value:t})}return M&&m&&n!==c?o.length&&(0,s.aK)(o[o.length-1].value,c,eW(c,k,t))?o[o.length-1].value=c:o.push({value:c}):M&&n!==c||o.push({value:n}),o}({maxTicks:i=Math.max(2,i),bounds:t.bounds,min:t.min,max:t.max,precision:e.precision,step:e.stepSize,count:e.count,maxDigits:this._maxDigits(),horizontal:this.isHorizontal(),minRotation:e.minRotation||0,includeBounds:!1!==e.includeBounds},this._range||this);return"ticks"===t.bounds&&(0,s.aH)(a,this,"value"),t.reverse?(a.reverse(),this.start=this.max,this.end=this.min):(this.start=this.min,this.end=this.max),a}configure(){let t=this.ticks,e=this.min,i=this.max;if(super.configure(),this.options.offset&&t.length){let s=(i-e)/Math.max(t.length-1,1)/2;e-=s,i+=s}this._startValue=e,this._endValue=i,this._valueRange=i-e}getLabelForValue(t){return(0,s.o)(t,this.chart.options.locale,this.options.ticks.format)}}class eY extends e${static id="linear";static defaults={ticks:{callback:s.aM.formatters.numeric}};determineDataLimits(){let{min:t,max:e}=this.getMinMax(!0);this.min=(0,s.g)(t)?t:0,this.max=(0,s.g)(e)?e:1,this.handleTickRangeOptions()}computeTickLimit(){let t=this.isHorizontal(),e=t?this.width:this.height,i=(0,s.t)(this.options.ticks.minRotation),a=(t?Math.sin(i):Math.cos(i))||.001;return Math.ceil(e/Math.min(40,this._resolveTickFontOptions(0).lineHeight/a))}getPixelForValue(t){return null===t?NaN:this.getPixelForDecimal((t-this._startValue)/this._valueRange)}getValueForPixel(t){return this._startValue+this.getDecimalForPixel(t)*this._valueRange}}let eU=t=>Math.floor((0,s.aN)(t)),eQ=(t,e)=>Math.pow(10,eU(t)+e);function eX(t){return 1==t/Math.pow(10,eU(t))}function eq(t,e,i){let s=Math.pow(10,i),a=Math.floor(t/s);return Math.ceil(e/s)-a}class eG extends tw{static id="logarithmic";static defaults={ticks:{callback:s.aM.formatters.logarithmic,major:{enabled:!0}}};constructor(t){super(t),this.start=void 0,this.end=void 0,this._startValue=void 0,this._valueRange=0}parse(t,e){let i=e$.prototype.parse.apply(this,[t,e]);if(0===i){this._zero=!0;return}return(0,s.g)(i)&&i>0?i:null}determineDataLimits(){let{min:t,max:e}=this.getMinMax(!0);this.min=(0,s.g)(t)?Math.max(0,t):null,this.max=(0,s.g)(e)?Math.max(0,e):null,this.options.beginAtZero&&(this._zero=!0),this._zero&&this.min!==this._suggestedMin&&!(0,s.g)(this._userMin)&&(this.min=t===eQ(this.min,0)?eQ(this.min,-1):eQ(this.min,0)),this.handleTickRangeOptions()}handleTickRangeOptions(){let{minDefined:t,maxDefined:e}=this.getUserBounds(),i=this.min,s=this.max,a=e=>i=t?i:e,n=t=>s=e?s:t;i===s&&(i<=0?(a(1),n(10)):(a(eQ(i,-1)),n(eQ(s,1)))),i<=0&&a(eQ(s,-1)),s<=0&&n(eQ(i,1)),this.min=i,this.max=s}buildTicks(){let t=this.options,e=function(t,{min:e,max:i}){e=(0,s.O)(t.min,e);let a=[],n=eU(e),r=function(t,e){let i=eU(e-t);for(;eq(t,e,i)>10;)i++;for(;10>eq(t,e,i);)i--;return Math.min(i,eU(t))}(e,i),o=r<0?Math.pow(10,Math.abs(r)):1,l=Math.pow(10,r),h=n>r?Math.pow(10,n):0,d=Math.round((e-h)*o)/o,c=Math.floor((e-h)/l/10)*l*10,u=Math.floor((d-c)/Math.pow(10,r)),f=(0,s.O)(t.min,Math.round((h+c+u*Math.pow(10,r))*o)/o);for(;f<i;)a.push({value:f,major:eX(f),significand:u}),u>=10?u=u<15?15:20:u++,u>=20&&(u=2,o=++r>=0?1:o),f=Math.round((h+c+u*Math.pow(10,r))*o)/o;let g=(0,s.O)(t.max,f);return a.push({value:g,major:eX(g),significand:u}),a}({min:this._userMin,max:this._userMax},this);return"ticks"===t.bounds&&(0,s.aH)(e,this,"value"),t.reverse?(e.reverse(),this.start=this.max,this.end=this.min):(this.start=this.min,this.end=this.max),e}getLabelForValue(t){return void 0===t?"0":(0,s.o)(t,this.chart.options.locale,this.options.ticks.format)}configure(){let t=this.min;super.configure(),this._startValue=(0,s.aN)(t),this._valueRange=(0,s.aN)(this.max)-(0,s.aN)(t)}getPixelForValue(t){return((void 0===t||0===t)&&(t=this.min),null===t||isNaN(t))?NaN:this.getPixelForDecimal(t===this.min?0:((0,s.aN)(t)-this._startValue)/this._valueRange)}getValueForPixel(t){let e=this.getDecimalForPixel(t);return Math.pow(10,this._startValue+e*this._valueRange)}}function eK(t){let e=t.ticks;if(e.display&&t.display){let t=(0,s.E)(e.backdropPadding);return(0,s.v)(e.font&&e.font.size,s.d.font.size)+t.height}return 0}function eZ(t,e,i,s,a){return t===s||t===a?{start:e-i/2,end:e+i/2}:t<s||t>a?{start:e-i,end:e}:{start:e,end:e+i}}function eJ(t,e,i,a){let{ctx:n}=t;if(i)n.arc(t.xCenter,t.yCenter,e,0,s.T);else{let i=t.getPointPosition(0,e);n.moveTo(i.x,i.y);for(let s=1;s<a;s++)i=t.getPointPosition(s,e),n.lineTo(i.x,i.y)}}class e0 extends e${static id="radialLinear";static defaults={display:!0,animate:!0,position:"chartArea",angleLines:{display:!0,lineWidth:1,borderDash:[],borderDashOffset:0},grid:{circular:!1},startAngle:0,ticks:{showLabelBackdrop:!0,callback:s.aM.formatters.numeric},pointLabels:{backdropColor:void 0,backdropPadding:2,display:!0,font:{size:10},callback:t=>t,padding:5,centerPointLabels:!1}};static defaultRoutes={"angleLines.color":"borderColor","pointLabels.color":"color","ticks.color":"color"};static descriptors={angleLines:{_fallback:"grid"}};constructor(t){super(t),this.xCenter=void 0,this.yCenter=void 0,this.drawingArea=void 0,this._pointLabels=[],this._pointLabelItems=[]}setDimensions(){let t=this._padding=(0,s.E)(eK(this.options)/2),e=this.width=this.maxWidth-t.width,i=this.height=this.maxHeight-t.height;this.xCenter=Math.floor(this.left+e/2+t.left),this.yCenter=Math.floor(this.top+i/2+t.top),this.drawingArea=Math.floor(Math.min(e,i)/2)}determineDataLimits(){let{min:t,max:e}=this.getMinMax(!1);this.min=(0,s.g)(t)&&!isNaN(t)?t:0,this.max=(0,s.g)(e)&&!isNaN(e)?e:0,this.handleTickRangeOptions()}computeTickLimit(){return Math.ceil(this.drawingArea/eK(this.options))}generateTickLabels(t){e$.prototype.generateTickLabels.call(this,t),this._pointLabels=this.getLabels().map((t,e)=>{let i=(0,s.Q)(this.options.pointLabels.callback,[t,e],this);return i||0===i?i:""}).filter((t,e)=>this.chart.getDataVisibility(e))}fit(){let t=this.options;t.display&&t.pointLabels.display?function(t){let e={l:t.left+t._padding.left,r:t.right-t._padding.right,t:t.top+t._padding.top,b:t.bottom-t._padding.bottom},i=Object.assign({},e),a=[],n=[],r=t._pointLabels.length,o=t.options.pointLabels,l=o.centerPointLabels?s.P/r:0;for(let c=0;c<r;c++){var h,d;let r=o.setContext(t.getPointLabelContext(c));n[c]=r.padding;let u=t.getPointPosition(c,t.drawingArea+n[c],l),f=(0,s.a0)(r.font),g=(h=t.ctx,d=t._pointLabels[c],d=(0,s.b)(d)?d:[d],{w:(0,s.aO)(h,f.string,d),h:d.length*f.lineHeight});a[c]=g;let p=(0,s.al)(t.getIndexAngle(c)+l),m=Math.round((0,s.U)(p));!function(t,e,i,s,a){let n=Math.abs(Math.sin(i)),r=Math.abs(Math.cos(i)),o=0,l=0;s.start<e.l?(o=(e.l-s.start)/n,t.l=Math.min(t.l,e.l-o)):s.end>e.r&&(o=(s.end-e.r)/n,t.r=Math.max(t.r,e.r+o)),a.start<e.t?(l=(e.t-a.start)/r,t.t=Math.min(t.t,e.t-l)):a.end>e.b&&(l=(a.end-e.b)/r,t.b=Math.max(t.b,e.b+l))}(i,e,p,eZ(m,u.x,g.w,0,180),eZ(m,u.y,g.h,90,270))}t.setCenterPoint(e.l-i.l,i.r-e.r,e.t-i.t,i.b-e.b),t._pointLabelItems=function(t,e,i){let a,n=[],r=t._pointLabels.length,o=t.options,{centerPointLabels:l,display:h}=o.pointLabels,d={extra:eK(o)/2,additionalAngle:l?s.P/r:0};for(let o=0;o<r;o++){d.padding=i[o],d.size=e[o];let r=function(t,e,i){var a,n,r,o,l,h,d;let c=t.drawingArea,{extra:u,additionalAngle:f,padding:g,size:p}=i,m=t.getPointPosition(e,c+u+g,f),b=Math.round((0,s.U)((0,s.al)(m.angle+s.H))),x=(a=m.y,n=p.h,90===(r=b)||270===r?a-=n/2:(r>270||r<90)&&(a-=n),a),_=0===(o=b)||180===o?"center":o<180?"left":"right",y=(l=m.x,h=p.w,"right"===(d=_)?l-=h:"center"===d&&(l-=h/2),l);return{visible:!0,x:m.x,y:x,textAlign:_,left:y,top:x,right:y+p.w,bottom:x+p.h}}(t,o,d);n.push(r),"auto"===h&&(r.visible=function(t,e){if(!e)return!0;let{left:i,top:a,right:n,bottom:r}=t;return!((0,s.C)({x:i,y:a},e)||(0,s.C)({x:i,y:r},e)||(0,s.C)({x:n,y:a},e)||(0,s.C)({x:n,y:r},e))}(r,a),r.visible&&(a=r))}return n}(t,a,n)}(this):this.setCenterPoint(0,0,0,0)}setCenterPoint(t,e,i,s){this.xCenter+=Math.floor((t-e)/2),this.yCenter+=Math.floor((i-s)/2),this.drawingArea-=Math.min(this.drawingArea/2,Math.max(t,e,i,s))}getIndexAngle(t){let e=s.T/(this._pointLabels.length||1),i=this.options.startAngle||0;return(0,s.al)(t*e+(0,s.t)(i))}getDistanceFromCenterForValue(t){if((0,s.k)(t))return NaN;let e=this.drawingArea/(this.max-this.min);return this.options.reverse?(this.max-t)*e:(t-this.min)*e}getValueForDistanceFromCenter(t){if((0,s.k)(t))return NaN;let e=t/(this.drawingArea/(this.max-this.min));return this.options.reverse?this.max-e:this.min+e}getPointLabelContext(t){let e=this._pointLabels||[];if(t>=0&&t<e.length){var i;let a=e[t];return i=this.getContext(),(0,s.j)(i,{label:a,index:t,type:"pointLabel"})}}getPointPosition(t,e,i=0){let a=this.getIndexAngle(t)-s.H+i;return{x:Math.cos(a)*e+this.xCenter,y:Math.sin(a)*e+this.yCenter,angle:a}}getPointPositionForValue(t,e){return this.getPointPosition(t,this.getDistanceFromCenterForValue(e))}getBasePosition(t){return this.getPointPositionForValue(t||0,this.getBaseValue())}getPointLabelPosition(t){let{left:e,top:i,right:s,bottom:a}=this._pointLabelItems[t];return{left:e,top:i,right:s,bottom:a}}drawBackground(){let{backgroundColor:t,grid:{circular:e}}=this.options;if(t){let i=this.ctx;i.save(),i.beginPath(),eJ(this,this.getDistanceFromCenterForValue(this._endValue),e,this._pointLabels.length),i.closePath(),i.fillStyle=t,i.fill(),i.restore()}}drawGrid(){let t,e,i,a=this.ctx,n=this.options,{angleLines:r,grid:o,border:l}=n,h=this._pointLabels.length;if(n.pointLabels.display&&function(t,e){let{ctx:i,options:{pointLabels:a}}=t;for(let n=e-1;n>=0;n--){let e=t._pointLabelItems[n];if(!e.visible)continue;let r=a.setContext(t.getPointLabelContext(n));!function(t,e,i){let{left:a,top:n,right:r,bottom:o}=i,{backdropColor:l}=e;if(!(0,s.k)(l)){let i=(0,s.ay)(e.borderRadius),h=(0,s.E)(e.backdropPadding);t.fillStyle=l;let d=a-h.left,c=n-h.top,u=r-a+h.width,f=o-n+h.height;Object.values(i).some(t=>0!==t)?(t.beginPath(),(0,s.aw)(t,{x:d,y:c,w:u,h:f,radius:i}),t.fill()):t.fillRect(d,c,u,f)}}(i,r,e);let o=(0,s.a0)(r.font),{x:l,y:h,textAlign:d}=e;(0,s.Z)(i,t._pointLabels[n],l,h+o.lineHeight/2,o,{color:r.color,textAlign:d,textBaseline:"middle"})}}(this,h),o.display&&this.ticks.forEach((t,i)=>{if(0!==i||0===i&&this.min<0){e=this.getDistanceFromCenterForValue(t.value);let s=this.getContext(i),a=o.setContext(s),n=l.setContext(s);!function(t,e,i,s,a){let n=t.ctx,r=e.circular,{color:o,lineWidth:l}=e;(r||s)&&o&&l&&!(i<0)&&(n.save(),n.strokeStyle=o,n.lineWidth=l,n.setLineDash(a.dash||[]),n.lineDashOffset=a.dashOffset,n.beginPath(),eJ(t,i,r,s),n.closePath(),n.stroke(),n.restore())}(this,a,e,h,n)}}),r.display){for(a.save(),t=h-1;t>=0;t--){let s=r.setContext(this.getPointLabelContext(t)),{color:o,lineWidth:l}=s;l&&o&&(a.lineWidth=l,a.strokeStyle=o,a.setLineDash(s.borderDash),a.lineDashOffset=s.borderDashOffset,e=this.getDistanceFromCenterForValue(n.reverse?this.min:this.max),i=this.getPointPosition(t,e),a.beginPath(),a.moveTo(this.xCenter,this.yCenter),a.lineTo(i.x,i.y),a.stroke())}a.restore()}}drawBorder(){}drawLabels(){let t,e,i=this.ctx,a=this.options,n=a.ticks;if(!n.display)return;let r=this.getIndexAngle(0);i.save(),i.translate(this.xCenter,this.yCenter),i.rotate(r),i.textAlign="center",i.textBaseline="middle",this.ticks.forEach((r,o)=>{if(0===o&&this.min>=0&&!a.reverse)return;let l=n.setContext(this.getContext(o)),h=(0,s.a0)(l.font);if(t=this.getDistanceFromCenterForValue(this.ticks[o].value),l.showLabelBackdrop){i.font=h.string,e=i.measureText(r.label).width,i.fillStyle=l.backdropColor;let a=(0,s.E)(l.backdropPadding);i.fillRect(-e/2-a.left,-t-h.size/2-a.top,e+a.width,h.size+a.height)}(0,s.Z)(i,r.label,0,-t,h,{color:l.color,strokeColor:l.textStrokeColor,strokeWidth:l.textStrokeWidth})}),i.restore()}drawTitle(){}}let e1={millisecond:{common:!0,size:1,steps:1e3},second:{common:!0,size:1e3,steps:60},minute:{common:!0,size:6e4,steps:60},hour:{common:!0,size:36e5,steps:24},day:{common:!0,size:864e5,steps:30},week:{common:!1,size:6048e5,steps:4},month:{common:!0,size:2628e6,steps:12},quarter:{common:!1,size:7884e6,steps:4},year:{common:!0,size:3154e7}},e2=Object.keys(e1);function e5(t,e){return t-e}function e3(t,e){if((0,s.k)(e))return null;let i=t._adapter,{parser:a,round:n,isoWeekday:r}=t._parseOpts,o=e;return("function"==typeof a&&(o=a(o)),(0,s.g)(o)||(o="string"==typeof a?i.parse(o,a):i.parse(o)),null===o)?null:(n&&(o="week"===n&&((0,s.x)(r)||!0===r)?i.startOf(o,"isoWeek",r):i.startOf(o,n)),+o)}function e8(t,e,i,s){let a=e2.length;for(let n=e2.indexOf(t);n<a-1;++n){let t=e1[e2[n]],a=t.steps?t.steps:Number.MAX_SAFE_INTEGER;if(t.common&&Math.ceil((i-e)/(a*t.size))<=s)return e2[n]}return e2[a-1]}function e4(t,e,i){if(i){if(i.length){let{lo:a,hi:n}=(0,s.aQ)(i,e);t[i[a]>=e?i[a]:i[n]]=!0}}else t[e]=!0}function e6(t,e,i){let s,a,n=[],r={},o=e.length;for(s=0;s<o;++s)r[a=e[s]]=s,n.push({value:a,major:!1});return 0!==o&&i?function(t,e,i,s){let a,n,r=t._adapter,o=+r.startOf(e[0].value,s),l=e[e.length-1].value;for(a=o;a<=l;a=+r.add(a,1,s))(n=i[a])>=0&&(e[n].major=!0);return e}(t,n,r,i):n}class e7 extends tw{static id="time";static defaults={bounds:"data",adapters:{},time:{parser:!1,unit:!1,round:!1,isoWeekday:!1,minUnit:"millisecond",displayFormats:{}},ticks:{source:"auto",callback:!1,major:{enabled:!1}}};constructor(t){super(t),this._cache={data:[],labels:[],all:[]},this._unit="day",this._majorUnit=void 0,this._offsets={},this._normalized=!1,this._parseOpts=void 0}init(t,e={}){let i=t.time||(t.time={}),a=this._adapter=new z._date(t.adapters.date);a.init(e),(0,s.ab)(i.displayFormats,a.formats()),this._parseOpts={parser:i.parser,round:i.round,isoWeekday:i.isoWeekday},super.init(t),this._normalized=e.normalized}parse(t,e){return void 0===t?null:e3(this,t)}beforeLayout(){super.beforeLayout(),this._cache={data:[],labels:[],all:[]}}determineDataLimits(){let t=this.options,e=this._adapter,i=t.time.unit||"day",{min:a,max:n,minDefined:r,maxDefined:o}=this.getUserBounds();function l(t){r||isNaN(t.min)||(a=Math.min(a,t.min)),o||isNaN(t.max)||(n=Math.max(n,t.max))}r&&o||(l(this._getLabelBounds()),("ticks"!==t.bounds||"labels"!==t.ticks.source)&&l(this.getMinMax(!1))),a=(0,s.g)(a)&&!isNaN(a)?a:+e.startOf(Date.now(),i),n=(0,s.g)(n)&&!isNaN(n)?n:+e.endOf(Date.now(),i)+1,this.min=Math.min(a,n-1),this.max=Math.max(a+1,n)}_getLabelBounds(){let t=this.getLabelTimestamps(),e=Number.POSITIVE_INFINITY,i=Number.NEGATIVE_INFINITY;return t.length&&(e=t[0],i=t[t.length-1]),{min:e,max:i}}buildTicks(){let t=this.options,e=t.time,i=t.ticks,a="labels"===i.source?this.getLabelTimestamps():this._generate();"ticks"===t.bounds&&a.length&&(this.min=this._userMin||a[0],this.max=this._userMax||a[a.length-1]);let n=this.min,r=this.max,o=(0,s.aP)(a,n,r);return this._unit=e.unit||(i.autoSkip?e8(e.minUnit,this.min,this.max,this._getLabelCapacity(n)):function(t,e,i,s,a){for(let n=e2.length-1;n>=e2.indexOf(i);n--){let i=e2[n];if(e1[i].common&&t._adapter.diff(a,s,i)>=e-1)return i}return e2[i?e2.indexOf(i):0]}(this,o.length,e.minUnit,this.min,this.max)),this._majorUnit=i.major.enabled&&"year"!==this._unit?function(t){for(let e=e2.indexOf(t)+1,i=e2.length;e<i;++e)if(e1[e2[e]].common)return e2[e]}(this._unit):void 0,this.initOffsets(a),t.reverse&&o.reverse(),e6(this,o,this._majorUnit)}afterAutoSkip(){this.options.offsetAfterAutoskip&&this.initOffsets(this.ticks.map(t=>+t.value))}initOffsets(t=[]){let e,i,a=0,n=0;this.options.offset&&t.length&&(e=this.getDecimalForValue(t[0]),a=1===t.length?1-e:(this.getDecimalForValue(t[1])-e)/2,i=this.getDecimalForValue(t[t.length-1]),n=1===t.length?i:(i-this.getDecimalForValue(t[t.length-2]))/2);let r=t.length<3?.5:.25;a=(0,s.S)(a,0,r),n=(0,s.S)(n,0,r),this._offsets={start:a,end:n,factor:1/(a+1+n)}}_generate(){let t,e,i=this._adapter,a=this.min,n=this.max,r=this.options,o=r.time,l=o.unit||e8(o.minUnit,a,n,this._getLabelCapacity(a)),h=(0,s.v)(r.ticks.stepSize,1),d="week"===l&&o.isoWeekday,c=(0,s.x)(d)||!0===d,u={},f=a;if(c&&(f=+i.startOf(f,"isoWeek",d)),f=+i.startOf(f,c?"day":l),i.diff(n,a,l)>1e5*h)throw Error(a+" and "+n+" are too far apart with stepSize of "+h+" "+l);let g="data"===r.ticks.source&&this.getDataTimestamps();for(t=f,e=0;t<n;t=+i.add(t,h,l),e++)e4(u,t,g);return(t===n||"ticks"===r.bounds||1===e)&&e4(u,t,g),Object.keys(u).sort(e5).map(t=>+t)}getLabelForValue(t){let e=this._adapter,i=this.options.time;return i.tooltipFormat?e.format(t,i.tooltipFormat):e.format(t,i.displayFormats.datetime)}format(t,e){let i=this.options.time.displayFormats,s=this._unit,a=e||i[s];return this._adapter.format(t,a)}_tickFormatFunction(t,e,i,a){let n=this.options,r=n.ticks.callback;if(r)return(0,s.Q)(r,[t,e,i],this);let o=n.time.displayFormats,l=this._unit,h=this._majorUnit,d=l&&o[l],c=h&&o[h],u=i[e],f=h&&c&&u&&u.major;return this._adapter.format(t,a||(f?c:d))}generateTickLabels(t){let e,i,s;for(e=0,i=t.length;e<i;++e)(s=t[e]).label=this._tickFormatFunction(s.value,e,t)}getDecimalForValue(t){return null===t?NaN:(t-this.min)/(this.max-this.min)}getPixelForValue(t){let e=this._offsets,i=this.getDecimalForValue(t);return this.getPixelForDecimal((e.start+i)*e.factor)}getValueForPixel(t){let e=this._offsets,i=this.getDecimalForPixel(t)/e.factor-e.end;return this.min+i*(this.max-this.min)}_getLabelSize(t){let e=this.options.ticks,i=this.ctx.measureText(t).width,a=(0,s.t)(this.isHorizontal()?e.maxRotation:e.minRotation),n=Math.cos(a),r=Math.sin(a),o=this._resolveTickFontOptions(0).size;return{w:i*n+o*r,h:i*r+o*n}}_getLabelCapacity(t){let e=this.options.time,i=e.displayFormats,s=i[e.unit]||i.millisecond,a=this._tickFormatFunction(t,0,e6(this,[t],this._majorUnit),s),n=this._getLabelSize(a),r=Math.floor(this.isHorizontal()?this.width/n.w:this.height/n.h)-1;return r>0?r:1}getDataTimestamps(){let t,e,i=this._cache.data||[];if(i.length)return i;let s=this.getMatchingVisibleMetas();if(this._normalized&&s.length)return this._cache.data=s[0].controller.getAllParsedValues(this);for(t=0,e=s.length;t<e;++t)i=i.concat(s[t].controller.getAllParsedValues(this));return this._cache.data=this.normalize(i)}getLabelTimestamps(){let t,e,i=this._cache.labels||[];if(i.length)return i;let s=this.getLabels();for(t=0,e=s.length;t<e;++t)i.push(e3(this,s[t]));return this._cache.labels=this._normalized?i:this.normalize(i)}normalize(t){return(0,s._)(t.sort(e5))}}function e9(t,e,i){let a,n,r,o,l=0,h=t.length-1;i?(e>=t[l].pos&&e<=t[h].pos&&({lo:l,hi:h}=(0,s.B)(t,"pos",e)),{pos:a,time:r}=t[l],{pos:n,time:o}=t[h]):(e>=t[l].time&&e<=t[h].time&&({lo:l,hi:h}=(0,s.B)(t,"time",e)),{time:a,pos:r}=t[l],{time:n,pos:o}=t[h]);let d=n-a;return d?r+(o-r)*(e-a)/d:r}class it extends e7{static id="timeseries";static defaults=e7.defaults;constructor(t){super(t),this._table=[],this._minPos=void 0,this._tableRange=void 0}initOffsets(){let t=this._getTimestampsForTable(),e=this._table=this.buildLookupTable(t);this._minPos=e9(e,this.min),this._tableRange=e9(e,this.max)-this._minPos,super.initOffsets(t)}buildLookupTable(t){let e,i,s,{min:a,max:n}=this,r=[],o=[];for(e=0,i=t.length;e<i;++e)(s=t[e])>=a&&s<=n&&r.push(s);if(r.length<2)return[{time:a,pos:0},{time:n,pos:1}];for(e=0,i=r.length;e<i;++e)Math.round((r[e+1]+r[e-1])/2)!==(s=r[e])&&o.push({time:s,pos:e/(i-1)});return o}_generate(){let t=this.min,e=this.max,i=super.getDataTimestamps();return i.includes(t)&&i.length||i.splice(0,0,t),i.includes(e)&&1!==i.length||i.push(e),i.sort((t,e)=>t-e)}_getTimestampsForTable(){let t=this._cache.all||[];if(t.length)return t;let e=this.getDataTimestamps(),i=this.getLabelTimestamps();return t=e.length&&i.length?this.normalize(e.concat(i)):e.length?e:i,t=this._cache.all=t}getDecimalForValue(t){return(e9(this._table,t)-this._minPos)/this._tableRange}getValueForPixel(t){let e=this._offsets,i=this.getDecimalForPixel(t)/e.factor-e.end;return e9(this._table,i*this._tableRange+this._minPos,!0)}}},58022:(t,e,i)=>{let s;function a(t){return t+.5|0}i.d(e,{$:()=>ec,A:()=>tF,B:()=>tL,C:()=>eh,D:()=>tk,E:()=>eM,F:()=>$,G:()=>eZ,H:()=>td,I:()=>e$,J:()=>e1,K:()=>e0,L:()=>tH,M:()=>eW,N:()=>tb,O:()=>N,P:()=>tn,Q:()=>W,R:()=>eP,S:()=>tC,T:()=>tr,U:()=>tM,V:()=>es,W:()=>tT,X:()=>en,Y:()=>ed,Z:()=>eg,_:()=>tN,a:()=>ek,a0:()=>ew,a1:()=>tW,a2:()=>t$,a3:()=>t6,a4:()=>q,a5:()=>tt,a6:()=>t7,a7:()=>ti,a8:()=>function t(e,i,s,a){return new Proxy({_cacheable:!1,_proxy:e,_context:i,_subProxy:s,_stack:new Set,_descriptors:eO(e,a),setContext:i=>t(e,i,s,a),override:n=>t(e.override(n),i,s,a)},{deleteProperty:(t,i)=>(delete t[i],delete e[i],!0),get:(e,i,s)=>eA(e,i,()=>(function(e,i,s){let{_proxy:a,_context:n,_subProxy:r,_descriptors:o}=e,l=a[i];return ti(l)&&o.isScriptable(i)&&(l=function(t,e,i,s){let{_proxy:a,_context:n,_subProxy:r,_stack:o}=i;if(o.has(t))throw Error("Recursion detected: "+Array.from(o).join("->")+"->"+t);o.add(t);let l=e(n,r||s);return o.delete(t),eT(t,l)&&(l=eL(a._scopes,a,t,l)),l}(i,l,e,s)),I(l)&&l.length&&(l=function(e,i,s,a){let{_proxy:n,_context:r,_subProxy:o,_descriptors:l}=s;if(void 0!==r.index&&a(e))return i[r.index%i.length];if(z(i[0])){let s=i,a=n._scopes.filter(t=>t!==s);for(let h of(i=[],s)){let s=eL(a,n,e,h);i.push(t(s,r,o&&o[e],l))}}return i}(i,l,e,o.isIndexable)),eT(i,l)&&(l=t(l,n,r&&r[i],o)),l})(e,i,s)),getOwnPropertyDescriptor:(t,i)=>t._descriptors.allKeys?Reflect.has(e,i)?{enumerable:!0,configurable:!0}:void 0:Reflect.getOwnPropertyDescriptor(e,i),getPrototypeOf:()=>Reflect.getPrototypeOf(e),has:(t,i)=>Reflect.has(e,i),ownKeys:()=>Reflect.ownKeys(e),set:(t,i,s)=>(e[i]=s,delete t[i],!0)})},a9:()=>eD,aA:()=>e8,aB:()=>e4,aC:()=>tY,aD:()=>e6,aE:()=>el,aF:()=>tP,aG:()=>L,aH:()=>ty,aI:()=>tm,aJ:()=>t_,aK:()=>tp,aL:()=>tw,aM:()=>t4,aN:()=>tf,aO:()=>ea,aP:()=>tR,aQ:()=>tE,aa:()=>eO,ab:()=>G,ac:()=>F,ad:()=>tj,ae:()=>eJ,af:()=>er,ag:()=>ts,ah:()=>io,ai:()=>Y,aj:()=>ta,ak:()=>tA,al:()=>tD,am:()=>e_,an:()=>ej,ao:()=>ii,ap:()=>ie,aq:()=>e5,ar:()=>e3,as:()=>e2,at:()=>eu,au:()=>ef,av:()=>eo,aw:()=>ep,ax:()=>ey,ay:()=>ev,az:()=>it,b:()=>I,b4:()=>th,b5:()=>tc,b6:()=>tu,c:()=>tJ,d:()=>ei,e:()=>tK,f:()=>J,g:()=>V,h:()=>te,i:()=>z,j:()=>eS,k:()=>R,l:()=>tz,m:()=>H,n:()=>j,o:()=>t3,p:()=>tO,q:()=>tU,r:()=>tB,s:()=>tg,t:()=>tv,u:()=>tV,v:()=>B,w:()=>tQ,x:()=>tx,y:()=>ez,z:()=>eG});let n=(t,e,i)=>Math.max(Math.min(t,i),e);function r(t){return n(a(2.55*t),0,255)}function o(t){return n(a(255*t),0,255)}function l(t){return n(a(t/2.55)/100,0,1)}function h(t){return n(a(100*t),0,100)}let d={0:0,1:1,2:2,3:3,4:4,5:5,6:6,7:7,8:8,9:9,A:10,B:11,C:12,D:13,E:14,F:15,a:10,b:11,c:12,d:13,e:14,f:15},c=[..."0123456789ABCDEF"],u=t=>c[15&t],f=t=>c[(240&t)>>4]+c[15&t],g=t=>(240&t)>>4==(15&t),p=t=>g(t.r)&&g(t.g)&&g(t.b)&&g(t.a),m=(t,e)=>t<255?e(t):"",b=/^(hsla?|hwb|hsv)\(\s*([-+.e\d]+)(?:deg)?[\s,]+([-+.e\d]+)%[\s,]+([-+.e\d]+)%(?:[\s,]+([-+.e\d]+)(%)?)?\s*\)$/;function x(t,e,i){let s=e*Math.min(i,1-i),a=(e,a=(e+t/30)%12)=>i-s*Math.max(Math.min(a-3,9-a,1),-1);return[a(0),a(8),a(4)]}function _(t,e,i){let s=(s,a=(s+t/60)%6)=>i-i*e*Math.max(Math.min(a,4-a,1),0);return[s(5),s(3),s(1)]}function y(t,e,i){let s,a=x(t,1,.5);for(e+i>1&&(s=1/(e+i),e*=s,i*=s),s=0;s<3;s++)a[s]*=1-e-i,a[s]+=e;return a}function v(t){let e,i,s,a=t.r/255,n=t.g/255,r=t.b/255,o=Math.max(a,n,r),l=Math.min(a,n,r),h=(o+l)/2;o!==l&&(s=o-l,i=h>.5?s/(2-o-l):s/(o+l),e=60*(e=a===o?(n-r)/s+6*(n<r):n===o?(r-a)/s+2:(a-n)/s+4)+.5);return[0|e,i||0,h]}function M(t,e,i,s){return(Array.isArray(e)?t(e[0],e[1],e[2]):t(e,i,s)).map(o)}function w(t){return(t%360+360)%360}let k={x:"dark",Z:"light",Y:"re",X:"blu",W:"gr",V:"medium",U:"slate",A:"ee",T:"ol",S:"or",B:"ra",C:"lateg",D:"ights",R:"in",Q:"turquois",E:"hi",P:"ro",O:"al",N:"le",M:"de",L:"yello",F:"en",K:"ch",G:"arks",H:"ea",I:"ightg",J:"wh"},P={OiceXe:"f0f8ff",antiquewEte:"faebd7",aqua:"ffff",aquamarRe:"7fffd4",azuY:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"0",blanKedOmond:"ffebcd",Xe:"ff",XeviTet:"8a2be2",bPwn:"a52a2a",burlywood:"deb887",caMtXe:"5f9ea0",KartYuse:"7fff00",KocTate:"d2691e",cSO:"ff7f50",cSnflowerXe:"6495ed",cSnsilk:"fff8dc",crimson:"dc143c",cyan:"ffff",xXe:"8b",xcyan:"8b8b",xgTMnPd:"b8860b",xWay:"a9a9a9",xgYF:"6400",xgYy:"a9a9a9",xkhaki:"bdb76b",xmagFta:"8b008b",xTivegYF:"556b2f",xSange:"ff8c00",xScEd:"9932cc",xYd:"8b0000",xsOmon:"e9967a",xsHgYF:"8fbc8f",xUXe:"483d8b",xUWay:"2f4f4f",xUgYy:"2f4f4f",xQe:"ced1",xviTet:"9400d3",dAppRk:"ff1493",dApskyXe:"bfff",dimWay:"696969",dimgYy:"696969",dodgerXe:"1e90ff",fiYbrick:"b22222",flSOwEte:"fffaf0",foYstWAn:"228b22",fuKsia:"ff00ff",gaRsbSo:"dcdcdc",ghostwEte:"f8f8ff",gTd:"ffd700",gTMnPd:"daa520",Way:"808080",gYF:"8000",gYFLw:"adff2f",gYy:"808080",honeyMw:"f0fff0",hotpRk:"ff69b4",RdianYd:"cd5c5c",Rdigo:"4b0082",ivSy:"fffff0",khaki:"f0e68c",lavFMr:"e6e6fa",lavFMrXsh:"fff0f5",lawngYF:"7cfc00",NmoncEffon:"fffacd",ZXe:"add8e6",ZcSO:"f08080",Zcyan:"e0ffff",ZgTMnPdLw:"fafad2",ZWay:"d3d3d3",ZgYF:"90ee90",ZgYy:"d3d3d3",ZpRk:"ffb6c1",ZsOmon:"ffa07a",ZsHgYF:"20b2aa",ZskyXe:"87cefa",ZUWay:"778899",ZUgYy:"778899",ZstAlXe:"b0c4de",ZLw:"ffffe0",lime:"ff00",limegYF:"32cd32",lRF:"faf0e6",magFta:"ff00ff",maPon:"800000",VaquamarRe:"66cdaa",VXe:"cd",VScEd:"ba55d3",VpurpN:"9370db",VsHgYF:"3cb371",VUXe:"7b68ee",VsprRggYF:"fa9a",VQe:"48d1cc",VviTetYd:"c71585",midnightXe:"191970",mRtcYam:"f5fffa",mistyPse:"ffe4e1",moccasR:"ffe4b5",navajowEte:"ffdead",navy:"80",Tdlace:"fdf5e6",Tive:"808000",TivedBb:"6b8e23",Sange:"ffa500",SangeYd:"ff4500",ScEd:"da70d6",pOegTMnPd:"eee8aa",pOegYF:"98fb98",pOeQe:"afeeee",pOeviTetYd:"db7093",papayawEp:"ffefd5",pHKpuff:"ffdab9",peru:"cd853f",pRk:"ffc0cb",plum:"dda0dd",powMrXe:"b0e0e6",purpN:"800080",YbeccapurpN:"663399",Yd:"ff0000",Psybrown:"bc8f8f",PyOXe:"4169e1",saddNbPwn:"8b4513",sOmon:"fa8072",sandybPwn:"f4a460",sHgYF:"2e8b57",sHshell:"fff5ee",siFna:"a0522d",silver:"c0c0c0",skyXe:"87ceeb",UXe:"6a5acd",UWay:"708090",UgYy:"708090",snow:"fffafa",sprRggYF:"ff7f",stAlXe:"4682b4",tan:"d2b48c",teO:"8080",tEstN:"d8bfd8",tomato:"ff6347",Qe:"40e0d0",viTet:"ee82ee",JHt:"f5deb3",wEte:"ffffff",wEtesmoke:"f5f5f5",Lw:"ffff00",LwgYF:"9acd32"},S=/^rgba?\(\s*([-+.\d]+)(%)?[\s,]+([-+.e\d]+)(%)?[\s,]+([-+.e\d]+)(%)?(?:[\s,/]+([-+.e\d]+)(%)?)?\s*\)$/,D=t=>t<=.0031308?12.92*t:1.055*Math.pow(t,1/2.4)-.055,O=t=>t<=.04045?t/12.92:Math.pow((t+.055)/1.055,2.4);function C(t,e,i){if(t){let s=v(t);s[e]=Math.max(0,Math.min(s[e]+s[e]*i,0===e?360:1)),t.r=(s=M(x,s,void 0,void 0))[0],t.g=s[1],t.b=s[2]}}function T(t,e){return t?Object.assign(e||{},t):t}function A(t){var e={r:0,g:0,b:0,a:255};return Array.isArray(t)?t.length>=3&&(e={r:t[0],g:t[1],b:t[2],a:255},t.length>3&&(e.a=o(t[3]))):(e=T(t,{r:0,g:0,b:0,a:1})).a=o(e.a),e}class E{constructor(t){let e;if(t instanceof E)return t;let i=typeof t;"object"===i?e=A(t):"string"===i&&(e=function(t){var e,i=t.length;return"#"===t[0]&&(4===i||5===i?e={r:255&17*d[t[1]],g:255&17*d[t[2]],b:255&17*d[t[3]],a:5===i?17*d[t[4]]:255}:(7===i||9===i)&&(e={r:d[t[1]]<<4|d[t[2]],g:d[t[3]]<<4|d[t[4]],b:d[t[5]]<<4|d[t[6]],a:9===i?d[t[7]]<<4|d[t[8]]:255})),e}(t)||function(t){s||((s=function(){let t,e,i,s,a,n={},r=Object.keys(P),o=Object.keys(k);for(t=0;t<r.length;t++){for(e=0,s=a=r[t];e<o.length;e++)i=o[e],a=a.replace(i,k[i]);i=parseInt(P[s],16),n[a]=[i>>16&255,i>>8&255,255&i]}return n}()).transparent=[0,0,0,0]);let e=s[t.toLowerCase()];return e&&{r:e[0],g:e[1],b:e[2],a:4===e.length?e[3]:255}}(t)||function(t){return"r"===t.charAt(0)?function(t){let e,i,s,a=S.exec(t),o=255;if(a){if(a[7]!==e){let t=+a[7];o=a[8]?r(t):n(255*t,0,255)}return e=+a[1],i=+a[3],s=+a[5],e=255&(a[2]?r(e):n(e,0,255)),{r:e,g:i=255&(a[4]?r(i):n(i,0,255)),b:s=255&(a[6]?r(s):n(s,0,255)),a:o}}}(t):function(t){let e,i=b.exec(t),s=255;if(!i)return;i[5]!==e&&(s=i[6]?r(+i[5]):o(+i[5]));let a=w(+i[2]),n=i[3]/100,l=i[4]/100;return{r:(e="hwb"===i[1]?M(y,a,n,l):"hsv"===i[1]?M(_,a,n,l):M(x,a,n,l))[0],g:e[1],b:e[2],a:s}}(t)}(t)),this._rgb=e,this._valid=!!e}get valid(){return this._valid}get rgb(){var t=T(this._rgb);return t&&(t.a=l(t.a)),t}set rgb(t){this._rgb=A(t)}rgbString(){var t;return this._valid?(t=this._rgb)&&(t.a<255?`rgba(${t.r}, ${t.g}, ${t.b}, ${l(t.a)})`:`rgb(${t.r}, ${t.g}, ${t.b})`):void 0}hexString(){var t,e;return this._valid?(e=p(t=this._rgb)?u:f,t?"#"+e(t.r)+e(t.g)+e(t.b)+m(t.a,e):void 0):void 0}hslString(){return this._valid?function(t){if(!t)return;let e=v(t),i=e[0],s=h(e[1]),a=h(e[2]);return t.a<255?`hsla(${i}, ${s}%, ${a}%, ${l(t.a)})`:`hsl(${i}, ${s}%, ${a}%)`}(this._rgb):void 0}mix(t,e){if(t){let i,s=this.rgb,a=t.rgb,n=e===i?.5:e,r=2*n-1,o=s.a-a.a,l=((r*o==-1?r:(r+o)/(1+r*o))+1)/2;i=1-l,s.r=255&l*s.r+i*a.r+.5,s.g=255&l*s.g+i*a.g+.5,s.b=255&l*s.b+i*a.b+.5,s.a=n*s.a+(1-n)*a.a,this.rgb=s}return this}interpolate(t,e){return t&&(this._rgb=function(t,e,i){let s=O(l(t.r)),a=O(l(t.g)),n=O(l(t.b));return{r:o(D(s+i*(O(l(e.r))-s))),g:o(D(a+i*(O(l(e.g))-a))),b:o(D(n+i*(O(l(e.b))-n))),a:t.a+i*(e.a-t.a)}}(this._rgb,t._rgb,e)),this}clone(){return new E(this.rgb)}alpha(t){return this._rgb.a=o(t),this}clearer(t){let e=this._rgb;return e.a*=1-t,this}greyscale(){let t=this._rgb,e=a(.3*t.r+.59*t.g+.11*t.b);return t.r=t.g=t.b=e,this}opaquer(t){let e=this._rgb;return e.a*=1+t,this}negate(){let t=this._rgb;return t.r=255-t.r,t.g=255-t.g,t.b=255-t.b,this}lighten(t){return C(this._rgb,2,t),this}darken(t){return C(this._rgb,2,-t),this}saturate(t){return C(this._rgb,1,t),this}desaturate(t){return C(this._rgb,1,-t),this}rotate(t){var e,i;return e=this._rgb,(i=v(e))[0]=w(i[0]+t),e.r=(i=M(x,i,void 0,void 0))[0],e.g=i[1],e.b=i[2],this}}function L(){}let F=(()=>{let t=0;return()=>t++})();function R(t){return null==t}function I(t){if(Array.isArray&&Array.isArray(t))return!0;let e=Object.prototype.toString.call(t);return"[object"===e.slice(0,7)&&"Array]"===e.slice(-6)}function z(t){return null!==t&&"[object Object]"===Object.prototype.toString.call(t)}function V(t){return("number"==typeof t||t instanceof Number)&&isFinite(+t)}function N(t,e){return V(t)?t:e}function B(t,e){return void 0===t?e:t}let H=(t,e)=>"string"==typeof t&&t.endsWith("%")?parseFloat(t)/100:t/e,j=(t,e)=>"string"==typeof t&&t.endsWith("%")?parseFloat(t)/100*e:+t;function W(t,e,i){if(t&&"function"==typeof t.call)return t.apply(i,e)}function $(t,e,i,s){let a,n,r;if(I(t))if(n=t.length,s)for(a=n-1;a>=0;a--)e.call(i,t[a],a);else for(a=0;a<n;a++)e.call(i,t[a],a);else if(z(t))for(a=0,n=(r=Object.keys(t)).length;a<n;a++)e.call(i,t[r[a]],r[a])}function Y(t,e){let i,s,a,n;if(!t||!e||t.length!==e.length)return!1;for(i=0,s=t.length;i<s;++i)if(a=t[i],n=e[i],a.datasetIndex!==n.datasetIndex||a.index!==n.index)return!1;return!0}function U(t){if(I(t))return t.map(U);if(z(t)){let e=Object.create(null),i=Object.keys(t),s=i.length,a=0;for(;a<s;++a)e[i[a]]=U(t[i[a]]);return e}return t}function Q(t){return -1===["__proto__","prototype","constructor"].indexOf(t)}function X(t,e,i,s){if(!Q(t))return;let a=e[t],n=i[t];z(a)&&z(n)?q(a,n,s):e[t]=U(n)}function q(t,e,i){let s,a=I(e)?e:[e],n=a.length;if(!z(t))return t;let r=(i=i||{}).merger||X;for(let e=0;e<n;++e){if(!z(s=a[e]))continue;let n=Object.keys(s);for(let e=0,a=n.length;e<a;++e)r(n[e],t,s,i)}return t}function G(t,e){return q(t,e,{merger:K})}function K(t,e,i){if(!Q(t))return;let s=e[t],a=i[t];z(s)&&z(a)?G(s,a):Object.prototype.hasOwnProperty.call(e,t)||(e[t]=U(a))}let Z={"":t=>t,x:t=>t.x,y:t=>t.y};function J(t,e){return(Z[e]||(Z[e]=function(t){let e=function(t){let e=t.split("."),i=[],s="";for(let t of e)(s+=t).endsWith("\\")?s=s.slice(0,-1)+".":(i.push(s),s="");return i}(t);return t=>{for(let i of e){if(""===i)break;t=t&&t[i]}return t}}(e)))(t)}function tt(t){return t.charAt(0).toUpperCase()+t.slice(1)}let te=t=>void 0!==t,ti=t=>"function"==typeof t,ts=(t,e)=>{if(t.size!==e.size)return!1;for(let i of t)if(!e.has(i))return!1;return!0};function ta(t){return"mouseup"===t.type||"click"===t.type||"contextmenu"===t.type}let tn=Math.PI,tr=2*tn,to=tr+tn,tl=Number.POSITIVE_INFINITY,th=tn/180,td=tn/2,tc=tn/4,tu=2*tn/3,tf=Math.log10,tg=Math.sign;function tp(t,e,i){return Math.abs(t-e)<i}function tm(t){let e=Math.round(t),i=Math.pow(10,Math.floor(tf(t=tp(t,e,t/1e3)?e:t))),s=t/i;return(s<=1?1:s<=2?2:s<=5?5:10)*i}function tb(t){let e,i=[],s=Math.sqrt(t);for(e=1;e<s;e++)t%e==0&&(i.push(e),i.push(t/e));return s===(0|s)&&i.push(s),i.sort((t,e)=>t-e).pop(),i}function tx(t){return"symbol"!=typeof t&&("object"!=typeof t||null===t||!!(Symbol.toPrimitive in t||"toString"in t||"valueOf"in t))&&!isNaN(parseFloat(t))&&isFinite(t)}function t_(t,e){let i=Math.round(t);return i-e<=t&&i+e>=t}function ty(t,e,i){let s,a,n;for(s=0,a=t.length;s<a;s++)isNaN(n=t[s][i])||(e.min=Math.min(e.min,n),e.max=Math.max(e.max,n))}function tv(t){return tn/180*t}function tM(t){return 180/tn*t}function tw(t){if(!V(t))return;let e=1,i=0;for(;Math.round(t*e)/e!==t;)e*=10,i++;return i}function tk(t,e){let i=e.x-t.x,s=e.y-t.y,a=Math.sqrt(i*i+s*s),n=Math.atan2(s,i);return n<-.5*tn&&(n+=tr),{angle:n,distance:a}}function tP(t,e){return Math.sqrt(Math.pow(e.x-t.x,2)+Math.pow(e.y-t.y,2))}function tS(t,e){return(t-e+to)%tr-tn}function tD(t){return(t%tr+tr)%tr}function tO(t,e,i,s){let a=tD(t),n=tD(e),r=tD(i),o=tD(n-a),l=tD(r-a),h=tD(a-n),d=tD(a-r);return a===n||a===r||s&&n===r||o>l&&h<d}function tC(t,e,i){return Math.max(e,Math.min(i,t))}function tT(t){return tC(t,-32768,32767)}function tA(t,e,i,s=1e-6){return t>=Math.min(e,i)-s&&t<=Math.max(e,i)+s}function tE(t,e,i){let s;i=i||(i=>t[i]<e);let a=t.length-1,n=0;for(;a-n>1;)i(s=n+a>>1)?n=s:a=s;return{lo:n,hi:a}}let tL=(t,e,i,s)=>tE(t,i,s?s=>{let a=t[s][e];return a<i||a===i&&t[s+1][e]===i}:s=>t[s][e]<i),tF=(t,e,i)=>tE(t,i,s=>t[s][e]>=i);function tR(t,e,i){let s=0,a=t.length;for(;s<a&&t[s]<e;)s++;for(;a>s&&t[a-1]>i;)a--;return s>0||a<t.length?t.slice(s,a):t}let tI=["push","pop","shift","splice","unshift"];function tz(t,e){if(t._chartjs)return void t._chartjs.listeners.push(e);Object.defineProperty(t,"_chartjs",{configurable:!0,enumerable:!1,value:{listeners:[e]}}),tI.forEach(e=>{let i="_onData"+tt(e),s=t[e];Object.defineProperty(t,e,{configurable:!0,enumerable:!1,value(...e){let a=s.apply(this,e);return t._chartjs.listeners.forEach(t=>{"function"==typeof t[i]&&t[i](...e)}),a}})})}function tV(t,e){let i=t._chartjs;if(!i)return;let s=i.listeners,a=s.indexOf(e);-1!==a&&s.splice(a,1),s.length>0||(tI.forEach(e=>{delete t[e]}),delete t._chartjs)}function tN(t){let e=new Set(t);return e.size===t.length?t:Array.from(e)}let tB="undefined"==typeof window?function(t){return t()}:window.requestAnimationFrame;function tH(t,e){let i=[],s=!1;return function(...a){i=a,s||(s=!0,tB.call(window,()=>{s=!1,t.apply(e,i)}))}}function tj(t,e){let i;return function(...s){return e?(clearTimeout(i),i=setTimeout(t,e,s)):t.apply(this,s),e}}let tW=t=>"start"===t?"left":"end"===t?"right":"center",t$=(t,e,i)=>"start"===t?e:"end"===t?i:(e+i)/2,tY=(t,e,i,s)=>t===(s?"left":"right")?i:"center"===t?(e+i)/2:e;function tU(t,e,i){let s=e.length,a=0,n=s;if(t._sorted){let{iScale:r,vScale:o,_parsed:l}=t,h=t.dataset&&t.dataset.options?t.dataset.options.spanGaps:null,d=r.axis,{min:c,max:u,minDefined:f,maxDefined:g}=r.getUserBounds();if(f){if(a=Math.min(tL(l,d,c).lo,i?s:tL(e,d,r.getPixelForValue(c)).lo),h){let t=l.slice(0,a+1).reverse().findIndex(t=>!R(t[o.axis]));a-=Math.max(0,t)}a=tC(a,0,s-1)}if(g){let t=Math.max(tL(l,r.axis,u,!0).hi+1,i?0:tL(e,d,r.getPixelForValue(u),!0).hi+1);if(h){let e=l.slice(t-1).findIndex(t=>!R(t[o.axis]));t+=Math.max(0,e)}n=tC(t,a,s)-a}else n=s-a}return{start:a,count:n}}function tQ(t){let{xScale:e,yScale:i,_scaleRanges:s}=t,a={xmin:e.min,xmax:e.max,ymin:i.min,ymax:i.max};if(!s)return t._scaleRanges=a,!0;let n=s.xmin!==e.min||s.xmax!==e.max||s.ymin!==i.min||s.ymax!==i.max;return Object.assign(s,a),n}let tX=t=>0===t||1===t,tq=(t,e,i)=>-(Math.pow(2,10*(t-=1))*Math.sin((t-e)*tr/i)),tG=(t,e,i)=>Math.pow(2,-10*t)*Math.sin((t-e)*tr/i)+1,tK={linear:t=>t,easeInQuad:t=>t*t,easeOutQuad:t=>-t*(t-2),easeInOutQuad:t=>(t/=.5)<1?.5*t*t:-.5*(--t*(t-2)-1),easeInCubic:t=>t*t*t,easeOutCubic:t=>(t-=1)*t*t+1,easeInOutCubic:t=>(t/=.5)<1?.5*t*t*t:.5*((t-=2)*t*t+2),easeInQuart:t=>t*t*t*t,easeOutQuart:t=>-((t-=1)*t*t*t-1),easeInOutQuart:t=>(t/=.5)<1?.5*t*t*t*t:-.5*((t-=2)*t*t*t-2),easeInQuint:t=>t*t*t*t*t,easeOutQuint:t=>(t-=1)*t*t*t*t+1,easeInOutQuint:t=>(t/=.5)<1?.5*t*t*t*t*t:.5*((t-=2)*t*t*t*t+2),easeInSine:t=>-Math.cos(t*td)+1,easeOutSine:t=>Math.sin(t*td),easeInOutSine:t=>-.5*(Math.cos(tn*t)-1),easeInExpo:t=>0===t?0:Math.pow(2,10*(t-1)),easeOutExpo:t=>1===t?1:-Math.pow(2,-10*t)+1,easeInOutExpo:t=>tX(t)?t:t<.5?.5*Math.pow(2,10*(2*t-1)):.5*(-Math.pow(2,-10*(2*t-1))+2),easeInCirc:t=>t>=1?t:-(Math.sqrt(1-t*t)-1),easeOutCirc:t=>Math.sqrt(1-(t-=1)*t),easeInOutCirc:t=>(t/=.5)<1?-.5*(Math.sqrt(1-t*t)-1):.5*(Math.sqrt(1-(t-=2)*t)+1),easeInElastic:t=>tX(t)?t:tq(t,.075,.3),easeOutElastic:t=>tX(t)?t:tG(t,.075,.3),easeInOutElastic:t=>tX(t)?t:t<.5?.5*tq(2*t,.1125,.45):.5+.5*tG(2*t-1,.1125,.45),easeInBack:t=>t*t*(2.70158*t-1.70158),easeOutBack:t=>(t-=1)*t*(2.70158*t********)+1,easeInOutBack(t){let e=1.70158;return(t/=.5)<1?.5*(t*t*(((e*=1.525)+1)*t-e)):.5*((t-=2)*t*(((e*=1.525)+1)*t+e)+2)},easeInBounce:t=>1-tK.easeOutBounce(1-t),easeOutBounce:t=>t<.36363636363636365?7.5625*t*t:t<.7272727272727273?7.5625*(t-=.5454545454545454)*t+.75:t<.9090909090909091?7.5625*(t-=.8181818181818182)*t+.9375:7.5625*(t-=.9545454545454546)*t+.984375,easeInOutBounce:t=>t<.5?.5*tK.easeInBounce(2*t):.5*tK.easeOutBounce(2*t-1)+.5};function tZ(t){if(t&&"object"==typeof t){let e=t.toString();return"[object CanvasPattern]"===e||"[object CanvasGradient]"===e}return!1}function tJ(t){return tZ(t)?t:new E(t)}function t0(t){return tZ(t)?t:new E(t).saturate(.5).darken(.1).hexString()}let t1=["x","y","borderWidth","radius","tension"],t2=["color","borderColor","backgroundColor"],t5=new Map;function t3(t,e,i){return(function(t,e){let i=t+JSON.stringify(e=e||{}),s=t5.get(i);return s||(s=new Intl.NumberFormat(t,e),t5.set(i,s)),s})(e,i).format(t)}let t8={values:t=>I(t)?t:""+t,numeric(t,e,i){let s;if(0===t)return"0";let a=this.chart.options.locale,n=t;if(i.length>1){var r,o;let e,a=Math.max(Math.abs(i[0].value),Math.abs(i[i.length-1].value));(a<1e-4||a>1e15)&&(s="scientific"),r=t,Math.abs(e=(o=i).length>3?o[2].value-o[1].value:o[1].value-o[0].value)>=1&&r!==Math.floor(r)&&(e=r-Math.floor(r)),n=e}let l=tf(Math.abs(n)),h=isNaN(l)?1:Math.max(Math.min(-1*Math.floor(l),20),0),d={notation:s,minimumFractionDigits:h,maximumFractionDigits:h};return Object.assign(d,this.options.ticks.format),t3(t,a,d)},logarithmic(t,e,i){return 0===t?"0":[1,2,3,5,10,15].includes(i[e].significand||t/Math.pow(10,Math.floor(tf(t))))||e>.8*i.length?t8.numeric.call(this,t,e,i):""}};var t4={formatters:t8};let t6=Object.create(null),t7=Object.create(null);function t9(t,e){if(!e)return t;let i=e.split(".");for(let e=0,s=i.length;e<s;++e){let s=i[e];t=t[s]||(t[s]=Object.create(null))}return t}function et(t,e,i){return"string"==typeof e?q(t9(t,e),i):q(t9(t,""),e)}class ee{constructor(t,e){this.animation=void 0,this.backgroundColor="rgba(0,0,0,0.1)",this.borderColor="rgba(0,0,0,0.1)",this.color="#666",this.datasets={},this.devicePixelRatio=t=>t.chart.platform.getDevicePixelRatio(),this.elements={},this.events=["mousemove","mouseout","click","touchstart","touchmove"],this.font={family:"'Helvetica Neue', 'Helvetica', 'Arial', sans-serif",size:12,style:"normal",lineHeight:1.2,weight:null},this.hover={},this.hoverBackgroundColor=(t,e)=>t0(e.backgroundColor),this.hoverBorderColor=(t,e)=>t0(e.borderColor),this.hoverColor=(t,e)=>t0(e.color),this.indexAxis="x",this.interaction={mode:"nearest",intersect:!0,includeInvisible:!1},this.maintainAspectRatio=!0,this.onHover=null,this.onClick=null,this.parsing=!0,this.plugins={},this.responsive=!0,this.scale=void 0,this.scales={},this.showLine=!0,this.drawActiveElementsOnTop=!0,this.describe(t),this.apply(e)}set(t,e){return et(this,t,e)}get(t){return t9(this,t)}describe(t,e){return et(t7,t,e)}override(t,e){return et(t6,t,e)}route(t,e,i,s){let a=t9(this,t),n=t9(this,i),r="_"+e;Object.defineProperties(a,{[r]:{value:a[e],writable:!0},[e]:{enumerable:!0,get(){let t=this[r],e=n[s];return z(t)?Object.assign({},e,t):B(t,e)},set(t){this[r]=t}}})}apply(t){t.forEach(t=>t(this))}}var ei=new ee({_scriptable:t=>!t.startsWith("on"),_indexable:t=>"events"!==t,hover:{_fallback:"interaction"},interaction:{_scriptable:!1,_indexable:!1}},[function(t){t.set("animation",{delay:void 0,duration:1e3,easing:"easeOutQuart",fn:void 0,from:void 0,loop:void 0,to:void 0,type:void 0}),t.describe("animation",{_fallback:!1,_indexable:!1,_scriptable:t=>"onProgress"!==t&&"onComplete"!==t&&"fn"!==t}),t.set("animations",{colors:{type:"color",properties:t2},numbers:{type:"number",properties:t1}}),t.describe("animations",{_fallback:"animation"}),t.set("transitions",{active:{animation:{duration:400}},resize:{animation:{duration:0}},show:{animations:{colors:{from:"transparent"},visible:{type:"boolean",duration:0}}},hide:{animations:{colors:{to:"transparent"},visible:{type:"boolean",easing:"linear",fn:t=>0|t}}}})},function(t){t.set("layout",{autoPadding:!0,padding:{top:0,right:0,bottom:0,left:0}})},function(t){t.set("scale",{display:!0,offset:!1,reverse:!1,beginAtZero:!1,bounds:"ticks",clip:!0,grace:0,grid:{display:!0,lineWidth:1,drawOnChartArea:!0,drawTicks:!0,tickLength:8,tickWidth:(t,e)=>e.lineWidth,tickColor:(t,e)=>e.color,offset:!1},border:{display:!0,dash:[],dashOffset:0,width:1},title:{display:!1,text:"",padding:{top:4,bottom:4}},ticks:{minRotation:0,maxRotation:50,mirror:!1,textStrokeWidth:0,textStrokeColor:"",padding:3,display:!0,autoSkip:!0,autoSkipPadding:3,labelOffset:0,callback:t4.formatters.values,minor:{},major:{},align:"center",crossAlign:"near",showLabelBackdrop:!1,backdropColor:"rgba(255, 255, 255, 0.75)",backdropPadding:2}}),t.route("scale.ticks","color","","color"),t.route("scale.grid","color","","borderColor"),t.route("scale.border","color","","borderColor"),t.route("scale.title","color","","color"),t.describe("scale",{_fallback:!1,_scriptable:t=>!t.startsWith("before")&&!t.startsWith("after")&&"callback"!==t&&"parser"!==t,_indexable:t=>"borderDash"!==t&&"tickBorderDash"!==t&&"dash"!==t}),t.describe("scales",{_fallback:"scale"}),t.describe("scale.ticks",{_scriptable:t=>"backdropPadding"!==t&&"callback"!==t,_indexable:t=>"backdropPadding"!==t})}]);function es(t,e,i,s,a){let n=e[a];return n||(n=e[a]=t.measureText(a).width,i.push(a)),n>s&&(s=n),s}function ea(t,e,i,s){let a,n,r,o,l,h=(s=s||{}).data=s.data||{},d=s.garbageCollect=s.garbageCollect||[];s.font!==e&&(h=s.data={},d=s.garbageCollect=[],s.font=e),t.save(),t.font=e;let c=0,u=i.length;for(a=0;a<u;a++)if(null==(o=i[a])||I(o)){if(I(o))for(n=0,r=o.length;n<r;n++)null==(l=o[n])||I(l)||(c=es(t,h,d,c,l))}else c=es(t,h,d,c,o);t.restore();let f=d.length/2;if(f>i.length){for(a=0;a<f;a++)delete h[d[a]];d.splice(0,f)}return c}function en(t,e,i){let s=t.currentDevicePixelRatio,a=0!==i?Math.max(i/2,.5):0;return Math.round((e-a)*s)/s+a}function er(t,e){(e||t)&&((e=e||t.getContext("2d")).save(),e.resetTransform(),e.clearRect(0,0,t.width,t.height),e.restore())}function eo(t,e,i,s){el(t,e,i,s,null)}function el(t,e,i,s,a){let n,r,o,l,h,d,c,u,f=e.pointStyle,g=e.rotation,p=e.radius,m=(g||0)*th;if(f&&"object"==typeof f&&("[object HTMLImageElement]"===(n=f.toString())||"[object HTMLCanvasElement]"===n)){t.save(),t.translate(i,s),t.rotate(m),t.drawImage(f,-f.width/2,-f.height/2,f.width,f.height),t.restore();return}if(!isNaN(p)&&!(p<=0)){switch(t.beginPath(),f){default:a?t.ellipse(i,s,a/2,p,0,0,tr):t.arc(i,s,p,0,tr),t.closePath();break;case"triangle":d=a?a/2:p,t.moveTo(i+Math.sin(m)*d,s-Math.cos(m)*p),m+=tu,t.lineTo(i+Math.sin(m)*d,s-Math.cos(m)*p),m+=tu,t.lineTo(i+Math.sin(m)*d,s-Math.cos(m)*p),t.closePath();break;case"rectRounded":h=.516*p,r=Math.cos(m+tc)*(l=p-h),c=Math.cos(m+tc)*(a?a/2-h:l),o=Math.sin(m+tc)*l,u=Math.sin(m+tc)*(a?a/2-h:l),t.arc(i-c,s-o,h,m-tn,m-td),t.arc(i+u,s-r,h,m-td,m),t.arc(i+c,s+o,h,m,m+td),t.arc(i-u,s+r,h,m+td,m+tn),t.closePath();break;case"rect":if(!g){l=Math.SQRT1_2*p,d=a?a/2:l,t.rect(i-d,s-l,2*d,2*l);break}m+=tc;case"rectRot":c=Math.cos(m)*(a?a/2:p),r=Math.cos(m)*p,o=Math.sin(m)*p,u=Math.sin(m)*(a?a/2:p),t.moveTo(i-c,s-o),t.lineTo(i+u,s-r),t.lineTo(i+c,s+o),t.lineTo(i-u,s+r),t.closePath();break;case"crossRot":m+=tc;case"cross":c=Math.cos(m)*(a?a/2:p),r=Math.cos(m)*p,o=Math.sin(m)*p,u=Math.sin(m)*(a?a/2:p),t.moveTo(i-c,s-o),t.lineTo(i+c,s+o),t.moveTo(i+u,s-r),t.lineTo(i-u,s+r);break;case"star":c=Math.cos(m)*(a?a/2:p),r=Math.cos(m)*p,o=Math.sin(m)*p,u=Math.sin(m)*(a?a/2:p),t.moveTo(i-c,s-o),t.lineTo(i+c,s+o),t.moveTo(i+u,s-r),t.lineTo(i-u,s+r),m+=tc,c=Math.cos(m)*(a?a/2:p),r=Math.cos(m)*p,o=Math.sin(m)*p,u=Math.sin(m)*(a?a/2:p),t.moveTo(i-c,s-o),t.lineTo(i+c,s+o),t.moveTo(i+u,s-r),t.lineTo(i-u,s+r);break;case"line":r=a?a/2:Math.cos(m)*p,o=Math.sin(m)*p,t.moveTo(i-r,s-o),t.lineTo(i+r,s+o);break;case"dash":t.moveTo(i,s),t.lineTo(i+Math.cos(m)*(a?a/2:p),s+Math.sin(m)*p);break;case!1:t.closePath()}t.fill(),e.borderWidth>0&&t.stroke()}}function eh(t,e,i){return i=i||.5,!e||t&&t.x>e.left-i&&t.x<e.right+i&&t.y>e.top-i&&t.y<e.bottom+i}function ed(t,e){t.save(),t.beginPath(),t.rect(e.left,e.top,e.right-e.left,e.bottom-e.top),t.clip()}function ec(t){t.restore()}function eu(t,e,i,s,a){if(!e)return t.lineTo(i.x,i.y);if("middle"===a){let s=(e.x+i.x)/2;t.lineTo(s,e.y),t.lineTo(s,i.y)}else"after"===a!=!!s?t.lineTo(e.x,i.y):t.lineTo(i.x,e.y);t.lineTo(i.x,i.y)}function ef(t,e,i,s){if(!e)return t.lineTo(i.x,i.y);t.bezierCurveTo(s?e.cp1x:e.cp2x,s?e.cp1y:e.cp2y,s?i.cp2x:i.cp1x,s?i.cp2y:i.cp1y,i.x,i.y)}function eg(t,e,i,s,a,n={}){let r,o,l=I(e)?e:[e],h=n.strokeWidth>0&&""!==n.strokeColor;for(t.save(),t.font=a.string,n.translation&&t.translate(n.translation[0],n.translation[1]),R(n.rotation)||t.rotate(n.rotation),n.color&&(t.fillStyle=n.color),n.textAlign&&(t.textAlign=n.textAlign),n.textBaseline&&(t.textBaseline=n.textBaseline),r=0;r<l.length;++r)o=l[r],n.backdrop&&function(t,e){let i=t.fillStyle;t.fillStyle=e.color,t.fillRect(e.left,e.top,e.width,e.height),t.fillStyle=i}(t,n.backdrop),h&&(n.strokeColor&&(t.strokeStyle=n.strokeColor),R(n.strokeWidth)||(t.lineWidth=n.strokeWidth),t.strokeText(o,i,s,n.maxWidth)),t.fillText(o,i,s,n.maxWidth),function(t,e,i,s,a){if(a.strikethrough||a.underline){let n=t.measureText(s),r=e-n.actualBoundingBoxLeft,o=e+n.actualBoundingBoxRight,l=i-n.actualBoundingBoxAscent,h=i+n.actualBoundingBoxDescent,d=a.strikethrough?(l+h)/2:h;t.strokeStyle=t.fillStyle,t.beginPath(),t.lineWidth=a.decorationWidth||2,t.moveTo(r,d),t.lineTo(o,d),t.stroke()}}(t,i,s,o,n),s+=Number(a.lineHeight);t.restore()}function ep(t,e){let{x:i,y:s,w:a,h:n,radius:r}=e;t.arc(i+r.topLeft,s+r.topLeft,r.topLeft,1.5*tn,tn,!0),t.lineTo(i,s+n-r.bottomLeft),t.arc(i+r.bottomLeft,s+n-r.bottomLeft,r.bottomLeft,tn,td,!0),t.lineTo(i+a-r.bottomRight,s+n),t.arc(i+a-r.bottomRight,s+n-r.bottomRight,r.bottomRight,td,0,!0),t.lineTo(i+a,s+r.topRight),t.arc(i+a-r.topRight,s+r.topRight,r.topRight,0,-td,!0),t.lineTo(i+r.topLeft,s)}let em=/^(normal|(\d+(?:\.\d+)?)(px|em|%)?)$/,eb=/^(normal|italic|initial|inherit|unset|(oblique( -?[0-9]?[0-9]deg)?))$/,ex=t=>+t||0;function e_(t,e){let i={},s=z(e),a=s?Object.keys(e):e,n=z(t)?s?i=>B(t[i],t[e[i]]):e=>t[e]:()=>t;for(let t of a)i[t]=ex(n(t));return i}function ey(t){return e_(t,{top:"y",right:"x",bottom:"y",left:"x"})}function ev(t){return e_(t,["topLeft","topRight","bottomLeft","bottomRight"])}function eM(t){let e=ey(t);return e.width=e.left+e.right,e.height=e.top+e.bottom,e}function ew(t,e){t=t||{},e=e||ei.font;let i=B(t.size,e.size);"string"==typeof i&&(i=parseInt(i,10));let s=B(t.style,e.style);s&&!(""+s).match(eb)&&(console.warn('Invalid font style specified: "'+s+'"'),s=void 0);let a={family:B(t.family,e.family),lineHeight:function(t,e){let i=(""+t).match(em);if(!i||"normal"===i[1])return 1.2*e;switch(t=+i[2],i[3]){case"px":return t;case"%":t/=100}return e*t}(B(t.lineHeight,e.lineHeight),i),size:i,style:s,weight:B(t.weight,e.weight),string:""};return a.string=!a||R(a.size)||R(a.family)?null:(a.style?a.style+" ":"")+(a.weight?a.weight+" ":"")+a.size+"px "+a.family,a}function ek(t,e,i,s){let a,n,r,o=!0;for(a=0,n=t.length;a<n;++a)if(void 0!==(r=t[a])&&(void 0!==e&&"function"==typeof r&&(r=r(e),o=!1),void 0!==i&&I(r)&&(r=r[i%r.length],o=!1),void 0!==r))return s&&!o&&(s.cacheable=!1),r}function eP(t,e,i){let{min:s,max:a}=t,n=j(e,(a-s)/2),r=(t,e)=>i&&0===t?0:t+e;return{min:r(s,-Math.abs(n)),max:r(a,n)}}function eS(t,e){return Object.assign(Object.create(t),e)}function eD(t,e=[""],i,s,a=()=>t[0]){let n=i||t;return void 0===s&&(s=eR("_fallback",t)),new Proxy({[Symbol.toStringTag]:"Object",_cacheable:!0,_scopes:t,_rootScopes:n,_fallback:s,_getTarget:a,override:i=>eD([i,...t],e,n,s)},{deleteProperty:(e,i)=>(delete e[i],delete e._keys,delete t[0][i],!0),get:(i,s)=>eA(i,s,()=>(function(t,e,i,s){let a;for(let n of e)if(void 0!==(a=eR(eC(n,t),i)))return eT(t,a)?eL(i,s,t,a):a})(s,e,t,i)),getOwnPropertyDescriptor:(t,e)=>Reflect.getOwnPropertyDescriptor(t._scopes[0],e),getPrototypeOf:()=>Reflect.getPrototypeOf(t[0]),has:(t,e)=>eI(t).includes(e),ownKeys:t=>eI(t),set(t,e,i){let s=t._storage||(t._storage=a());return t[e]=s[e]=i,delete t._keys,!0}})}function eO(t,e={scriptable:!0,indexable:!0}){let{_scriptable:i=e.scriptable,_indexable:s=e.indexable,_allKeys:a=e.allKeys}=t;return{allKeys:a,scriptable:i,indexable:s,isScriptable:ti(i)?i:()=>i,isIndexable:ti(s)?s:()=>s}}let eC=(t,e)=>t?t+tt(e):e,eT=(t,e)=>z(e)&&"adapters"!==t&&(null===Object.getPrototypeOf(e)||e.constructor===Object);function eA(t,e,i){if(Object.prototype.hasOwnProperty.call(t,e)||"constructor"===e)return t[e];let s=i();return t[e]=s,s}let eE=(t,e)=>!0===t?e:"string"==typeof t?J(e,t):void 0;function eL(t,e,i,s){var a;let n=e._rootScopes,r=(a=e._fallback,ti(a)?a(i,s):a),o=[...t,...n],l=new Set;l.add(s);let h=eF(l,o,i,r||i,s);return null!==h&&(void 0===r||r===i||null!==(h=eF(l,o,r,h,s)))&&eD(Array.from(l),[""],n,r,()=>(function(t,e,i){let s=t._getTarget();e in s||(s[e]={});let a=s[e];return I(a)&&z(i)?i:a||{}})(e,i,s))}function eF(t,e,i,s,a){for(;i;)i=function(t,e,i,s,a){for(let r of e){let e=eE(i,r);if(e){var n;t.add(e);let r=(n=e._fallback,ti(n)?n(i,a):n);if(void 0!==r&&r!==i&&r!==s)return r}else if(!1===e&&void 0!==s&&i!==s)return null}return!1}(t,e,i,s,a);return i}function eR(t,e){for(let i of e){if(!i)continue;let e=i[t];if(void 0!==e)return e}}function eI(t){let e=t._keys;return e||(e=t._keys=function(t){let e=new Set;for(let i of t)for(let t of Object.keys(i).filter(t=>!t.startsWith("_")))e.add(t);return Array.from(e)}(t._scopes)),e}function ez(t,e,i,s){let a,n,r,{iScale:o}=t,{key:l="r"}=this._parsing,h=Array(s);for(a=0;a<s;++a)r=e[n=a+i],h[a]={r:o.parse(J(r,l),n)};return h}let eV=Number.EPSILON||1e-14,eN=(t,e)=>e<t.length&&!t[e].skip&&t[e],eB=t=>"x"===t?"y":"x";function eH(t,e,i){return Math.max(Math.min(t,i),e)}function ej(t,e,i,s,a){let n,r,o,l;if(e.spanGaps&&(t=t.filter(t=>!t.skip)),"monotone"===e.cubicInterpolationMode)!function(t,e="x"){let i,s,a,n=eB(e),r=t.length,o=Array(r).fill(0),l=Array(r),h=eN(t,0);for(i=0;i<r;++i)if(s=a,a=h,h=eN(t,i+1),a){if(h){let t=h[e]-a[e];o[i]=0!==t?(h[n]-a[n])/t:0}l[i]=s?h?tg(o[i-1])!==tg(o[i])?0:(o[i-1]+o[i])/2:o[i-1]:o[i]}!function(t,e,i){let s,a,n,r,o,l=t.length,h=eN(t,0);for(let d=0;d<l-1;++d)if(o=h,h=eN(t,d+1),o&&h){if(tp(e[d],0,eV)){i[d]=i[d+1]=0;continue}(r=Math.pow(s=i[d]/e[d],2)+Math.pow(a=i[d+1]/e[d],2))<=9||(n=3/Math.sqrt(r),i[d]=s*n*e[d],i[d+1]=a*n*e[d])}}(t,o,l),function(t,e,i="x"){let s,a,n,r=eB(i),o=t.length,l=eN(t,0);for(let h=0;h<o;++h){if(a=n,n=l,l=eN(t,h+1),!n)continue;let o=n[i],d=n[r];a&&(s=(o-a[i])/3,n[`cp1${i}`]=o-s,n[`cp1${r}`]=d-s*e[h]),l&&(s=(l[i]-o)/3,n[`cp2${i}`]=o+s,n[`cp2${r}`]=d+s*e[h])}}(t,l,e)}(t,a);else{let i=s?t[t.length-1]:t[0];for(n=0,r=t.length;n<r;++n)l=function(t,e,i,s){let a=t.skip?e:t,n=i.skip?e:i,r=tP(e,a),o=tP(n,e),l=r/(r+o),h=o/(r+o);l=isNaN(l)?0:l,h=isNaN(h)?0:h;let d=s*l,c=s*h;return{previous:{x:e.x-d*(n.x-a.x),y:e.y-d*(n.y-a.y)},next:{x:e.x+c*(n.x-a.x),y:e.y+c*(n.y-a.y)}}}(i,o=t[n],t[Math.min(n+1,r-!s)%r],e.tension),o.cp1x=l.previous.x,o.cp1y=l.previous.y,o.cp2x=l.next.x,o.cp2y=l.next.y,i=o}e.capBezierPoints&&function(t,e){let i,s,a,n,r,o=eh(t[0],e);for(i=0,s=t.length;i<s;++i)r=n,n=o,o=i<s-1&&eh(t[i+1],e),n&&(a=t[i],r&&(a.cp1x=eH(a.cp1x,e.left,e.right),a.cp1y=eH(a.cp1y,e.top,e.bottom)),o&&(a.cp2x=eH(a.cp2x,e.left,e.right),a.cp2y=eH(a.cp2y,e.top,e.bottom)))}(t,i)}function eW(){return"undefined"!=typeof window&&"undefined"!=typeof document}function e$(t){let e=t.parentNode;return e&&"[object ShadowRoot]"===e.toString()&&(e=e.host),e}function eY(t,e,i){let s;return"string"==typeof t?(s=parseInt(t,10),-1!==t.indexOf("%")&&(s=s/100*e.parentNode[i])):s=t,s}let eU=t=>t.ownerDocument.defaultView.getComputedStyle(t,null),eQ=["top","right","bottom","left"];function eX(t,e,i){let s={};i=i?"-"+i:"";for(let a=0;a<4;a++){let n=eQ[a];s[n]=parseFloat(t[e+"-"+n+i])||0}return s.width=s.left+s.right,s.height=s.top+s.bottom,s}let eq=(t,e,i)=>(t>0||e>0)&&(!i||!i.shadowRoot);function eG(t,e){if("native"in t)return t;let{canvas:i,currentDevicePixelRatio:s}=e,a=eU(i),n="border-box"===a.boxSizing,r=eX(a,"padding"),o=eX(a,"border","width"),{x:l,y:h,box:d}=function(t,e){let i,s,a=t.touches,n=a&&a.length?a[0]:t,{offsetX:r,offsetY:o}=n,l=!1;if(eq(r,o,t.target))i=r,s=o;else{let t=e.getBoundingClientRect();i=n.clientX-t.left,s=n.clientY-t.top,l=!0}return{x:i,y:s,box:l}}(t,i),c=r.left+(d&&o.left),u=r.top+(d&&o.top),{width:f,height:g}=e;return n&&(f-=r.width+o.width,g-=r.height+o.height),{x:Math.round((l-c)/f*i.width/s),y:Math.round((h-u)/g*i.height/s)}}let eK=t=>Math.round(10*t)/10;function eZ(t,e,i,s){let a=eU(t),n=eX(a,"margin"),r=eY(a.maxWidth,t,"clientWidth")||tl,o=eY(a.maxHeight,t,"clientHeight")||tl,l=function(t,e,i){let s,a;if(void 0===e||void 0===i){let n=t&&e$(t);if(n){let t=n.getBoundingClientRect(),r=eU(n),o=eX(r,"border","width"),l=eX(r,"padding");e=t.width-l.width-o.width,i=t.height-l.height-o.height,s=eY(r.maxWidth,n,"clientWidth"),a=eY(r.maxHeight,n,"clientHeight")}else e=t.clientWidth,i=t.clientHeight}return{width:e,height:i,maxWidth:s||tl,maxHeight:a||tl}}(t,e,i),{width:h,height:d}=l;if("content-box"===a.boxSizing){let t=eX(a,"border","width"),e=eX(a,"padding");h-=e.width+t.width,d-=e.height+t.height}return h=Math.max(0,h-n.width),d=Math.max(0,s?h/s:d-n.height),h=eK(Math.min(h,r,l.maxWidth)),d=eK(Math.min(d,o,l.maxHeight)),h&&!d&&(d=eK(h/2)),(void 0!==e||void 0!==i)&&s&&l.height&&d>l.height&&(h=eK(Math.floor((d=l.height)*s))),{width:h,height:d}}function eJ(t,e,i){let s=e||1,a=Math.floor(t.height*s),n=Math.floor(t.width*s);t.height=Math.floor(t.height),t.width=Math.floor(t.width);let r=t.canvas;return r.style&&(i||!r.style.height&&!r.style.width)&&(r.style.height=`${t.height}px`,r.style.width=`${t.width}px`),(t.currentDevicePixelRatio!==s||r.height!==a||r.width!==n)&&(t.currentDevicePixelRatio=s,r.height=a,r.width=n,t.ctx.setTransform(s,0,0,s,0,0),!0)}let e0=function(){let t=!1;try{let e={get passive(){return t=!0,!1}};eW()&&(window.addEventListener("test",null,e),window.removeEventListener("test",null,e))}catch(t){}return t}();function e1(t,e){let i=eU(t).getPropertyValue(e),s=i&&i.match(/^(\d+)(\.\d+)?px$/);return s?+s[1]:void 0}function e2(t,e,i,s){return{x:t.x+i*(e.x-t.x),y:t.y+i*(e.y-t.y)}}function e5(t,e,i,s){return{x:t.x+i*(e.x-t.x),y:"middle"===s?i<.5?t.y:e.y:"after"===s?i<1?t.y:e.y:i>0?e.y:t.y}}function e3(t,e,i,s){let a={x:t.cp2x,y:t.cp2y},n={x:e.cp1x,y:e.cp1y},r=e2(t,a,i),o=e2(a,n,i),l=e2(n,e,i),h=e2(r,o,i),d=e2(o,l,i);return e2(h,d,i)}function e8(t,e,i){var s;return t?(s=i,{x:t=>e+e+s-t,setWidth(t){s=t},textAlign:t=>"center"===t?t:"right"===t?"left":"right",xPlus:(t,e)=>t-e,leftForLtr:(t,e)=>t-e}):{x:t=>t,setWidth(t){},textAlign:t=>t,xPlus:(t,e)=>t+e,leftForLtr:(t,e)=>t}}function e4(t,e){let i,s;("ltr"===e||"rtl"===e)&&(s=[(i=t.canvas.style).getPropertyValue("direction"),i.getPropertyPriority("direction")],i.setProperty("direction",e,"important"),t.prevTextDirection=s)}function e6(t,e){void 0!==e&&(delete t.prevTextDirection,t.canvas.style.setProperty("direction",e[0],e[1]))}function e7(t){return"angle"===t?{between:tO,compare:tS,normalize:tD}:{between:tA,compare:(t,e)=>t-e,normalize:t=>t}}function e9({start:t,end:e,count:i,loop:s,style:a}){return{start:t%i,end:e%i,loop:s&&(e-t+1)%i==0,style:a}}function it(t,e,i){let s,a,n;if(!i)return[t];let{property:r,start:o,end:l}=i,h=e.length,{compare:d,between:c,normalize:u}=e7(r),{start:f,end:g,loop:p,style:m}=function(t,e,i){let s,{property:a,start:n,end:r}=i,{between:o,normalize:l}=e7(a),h=e.length,{start:d,end:c,loop:u}=t;if(u){for(d+=h,c+=h,s=0;s<h&&o(l(e[d%h][a]),n,r);++s)d--,c--;d%=h,c%=h}return c<d&&(c+=h),{start:d,end:c,loop:u,style:t.style}}(t,e,i),b=[],x=!1,_=null,y=()=>c(o,n,s)&&0!==d(o,n),v=()=>0===d(l,s)||c(l,n,s),M=()=>x||y(),w=()=>!x||v();for(let t=f,i=f;t<=g;++t)(a=e[t%h]).skip||(s=u(a[r]))!==n&&(x=c(s,o,l),null===_&&M()&&(_=0===d(s,o)?t:i),null!==_&&w()&&(b.push(e9({start:_,end:t,loop:p,count:h,style:m})),_=null),i=t,n=s);return null!==_&&b.push(e9({start:_,end:g,loop:p,count:h,style:m})),b}function ie(t,e){let i=[],s=t.segments;for(let a=0;a<s.length;a++){let n=it(s[a],t.points,e);n.length&&i.push(...n)}return i}function ii(t,e){let i=t.points,s=t.options.spanGaps,a=i.length;if(!a)return[];let n=!!t._loop,{start:r,end:o}=function(t,e,i,s){let a=0,n=e-1;if(i&&!s)for(;a<e&&!t[a].skip;)a++;for(;a<e&&t[a].skip;)a++;for(a%=e,i&&(n+=a);n>a&&t[n%e].skip;)n--;return{start:a,end:n%=e}}(i,a,n,s);if(!0===s)return is(t,[{start:r,end:o,loop:n}],i,e);let l=o<r?o+a:o,h=!!t._fullLoop&&0===r&&o===a-1;return is(t,function(t,e,i,s){let a,n=t.length,r=[],o=e,l=t[e];for(a=e+1;a<=i;++a){let i=t[a%n];i.skip||i.stop?l.skip||(s=!1,r.push({start:e%n,end:(a-1)%n,loop:s}),e=o=i.stop?a:null):(o=a,l.skip&&(e=a)),l=i}return null!==o&&r.push({start:e%n,end:o%n,loop:s}),r}(i,r,l,h),i,e)}function is(t,e,i,s){return s&&s.setContext&&i?function(t,e,i,s){let a=t._chart.getContext(),n=ia(t.options),{_datasetIndex:r,options:{spanGaps:o}}=t,l=i.length,h=[],d=n,c=e[0].start,u=c;function f(t,e,s,a){let n=o?-1:1;if(t!==e){for(t+=l;i[t%l].skip;)t-=n;for(;i[e%l].skip;)e+=n;t%l!=e%l&&(h.push({start:t%l,end:e%l,loop:s,style:a}),d=a,c=e%l)}}for(let t of e){let e,n=i[(c=o?c:t.start)%l];for(u=c+1;u<=t.end;u++){let o=i[u%l];(function(t,e){if(!e)return!1;let i=[],s=function(t,e){return tZ(e)?(i.includes(e)||i.push(e),i.indexOf(e)):e};return JSON.stringify(t,s)!==JSON.stringify(e,s)})(e=ia(s.setContext(eS(a,{type:"segment",p0:n,p1:o,p0DataIndex:(u-1)%l,p1DataIndex:u%l,datasetIndex:r}))),d)&&f(c,u-1,t.loop,d),n=o,d=e}c<u-1&&f(c,u-1,t.loop,d)}return h}(t,e,i,s):e}function ia(t){return{backgroundColor:t.backgroundColor,borderCapStyle:t.borderCapStyle,borderDash:t.borderDash,borderDashOffset:t.borderDashOffset,borderJoinStyle:t.borderJoinStyle,borderWidth:t.borderWidth,borderColor:t.borderColor}}function ir(t,e,i){return t.options.clip?t[i]:e[i]}function io(t,e){let i=e._clip;if(i.disabled)return!1;let s=function(t,e){let{xScale:i,yScale:s}=t;return i&&s?{left:ir(i,e,"left"),right:ir(i,e,"right"),top:ir(s,e,"top"),bottom:ir(s,e,"bottom")}:e}(e,t.chartArea);return{left:!1===i.left?0:s.left-(!0===i.left?0:i.left),right:!1===i.right?t.width:s.right+(!0===i.right?0:i.right),top:!1===i.top?0:s.top-(!0===i.top?0:i.top),bottom:!1===i.bottom?t.height:s.bottom+(!0===i.bottom?0:i.bottom)}}}};