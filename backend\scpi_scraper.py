import requests
from bs4 import BeautifulSoup
import re
from dataclasses import dataclass
from typing import Optional

@dataclass
class ScpiData:
    """
    Classe pour stocker les données d'une SCPI
    """
    nom: Optional[str] = None
    type_de_capital: Optional[str] = None
    capitalisation: Optional[str] = None
    collecte_nette_2024: Optional[str] = None
    collecte_brute_2024: Optional[str] = None
    parts_en_attente: Optional[str] = None
    parts_echange: Optional[str] = None
    versement_des_dividendes: Optional[str] = None
    frais_reel_de_souscription: Optional[str] = None
    frais_de_gestion: Optional[str] = None
    delai_de_jouissance: Optional[str] = None
    minimum_1ere_souscription: Optional[str] = None
    prix_retrait_recent: Optional[str] = None
    prix_souscription_recent: Optional[str] = None
    date_prix_recent: Optional[str] = None

    def __str__(self):
        """Affichage formaté des données"""
        result = f"=== {self.nom or 'SCPI'} ===\n"
        for field, value in self.__dict__.items():
            if value is not None and field != 'nom':
                field_name = field.replace('_', ' ').title()
                result += f"{field_name}: {value}\n"
        return result

def get_scpi_data(url: str) -> Optional[ScpiData]:
    """
    Récupère les données d'une SCPI depuis le site Primaliance

    Args:
        url (str): URL de la page SCPI

    Returns:
        ScpiData: Objet contenant les données de la SCPI ou None si erreur
    """
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }

    try:
        # Récupération de la page
        response = requests.get(url, headers=headers)
        response.raise_for_status()

        # Parsing HTML
        soup = BeautifulSoup(response.content, 'html.parser')

        # Création de l'objet ScpiData
        scpi = ScpiData()

        # Récupération du nom de la SCPI
        title = soup.find('h1')
        if title:
            scpi.nom = title.get_text(strip=True)

        # Mapping des champs trouvés vers les attributs de la dataclass
        field_mapping = {
            'Type de capital': 'type_de_capital',
            'Capitalisation': 'capitalisation',
            'Collecte nette 2024': 'collecte_nette_2024',
            'Collecte brute 2024': 'collecte_brute_2024',
            '% Parts en attente au 31/12/2024': 'parts_en_attente',
            '% Parts échange 2021': 'parts_echange',
            'Versement des dividendes': 'versement_des_dividendes',
            'Frais réel de souscription': 'frais_reel_de_souscription',
            'Frais de gestion': 'frais_de_gestion',
            'Délai de jouissance': 'delai_de_jouissance',
            'Minimum 1ère souscription': 'minimum_1ere_souscription'
        }

        # Recherche des tableaux d'informations
        tables = soup.find_all('table')
        for table in tables:
            rows = table.find_all('tr')
            for row in rows:
                cells = row.find_all(['td', 'th'])
                if len(cells) >= 2:
                    key = cells[0].get_text(strip=True)
                    value = cells[1].get_text(strip=True)

                    # Utiliser le mapping pour assigner aux bons attributs
                    if key in field_mapping:
                        setattr(scpi, field_mapping[key], value)

        # Récupération du prix de retrait le plus récent depuis "Evolution du prix sur 1 an glissant"
        evolution_prix_section = soup.find('h2', string=re.compile(r'Evolution du prix.*glissant', re.IGNORECASE))
        if not evolution_prix_section:
            evolution_prix_section = soup.find(string=re.compile(r'Evolution du prix.*glissant', re.IGNORECASE))
            if evolution_prix_section:
                evolution_prix_section = evolution_prix_section.parent

        if evolution_prix_section:
            # Trouver le tableau suivant cette section
            next_element = evolution_prix_section.find_next_sibling()
            table = None

            # Chercher le tableau dans les éléments suivants
            while next_element and not table:
                if next_element.name == 'table':
                    table = next_element
                elif next_element.find('table'):
                    table = next_element.find('table')
                else:
                    next_element = next_element.find_next_sibling()

            if table:
                # Récupérer la première ligne de données (la plus récente)
                rows = table.find_all('tr')
                if len(rows) >= 2:  # Header + au moins une ligne de données
                    # Analyser les headers pour identifier les colonnes
                    headers = [th.get_text(strip=True).lower() for th in rows[0].find_all(['th', 'td'])]

                    # Trouver les indices des colonnes importantes
                    prix_souscription_idx = None
                    prix_retrait_idx = None
                    date_idx = None

                    for i, header in enumerate(headers):
                        if 'prix' in header and 'souscription' in header:
                            prix_souscription_idx = i
                        elif 'prix' in header and 'retrait' in header:
                            prix_retrait_idx = i
                        elif 'date' in header:
                            date_idx = i

                    # Récupérer la première ligne de données (la plus récente)
                    if len(rows) >= 2:
                        first_data_row = rows[1]
                        cells = first_data_row.find_all(['td', 'th'])

                        if date_idx is not None and date_idx < len(cells):
                            scpi.date_prix_recent = cells[date_idx].get_text(strip=True)

                        if prix_souscription_idx is not None and prix_souscription_idx < len(cells):
                            scpi.prix_souscription_recent = cells[prix_souscription_idx].get_text(strip=True)

                        if prix_retrait_idx is not None and prix_retrait_idx < len(cells):
                            scpi.prix_retrait_recent = cells[prix_retrait_idx].get_text(strip=True)

        return scpi

    except requests.RequestException as e:
        print(f"Erreur lors de la requête : {e}")
        return None
    except Exception as e:
        print(f"Erreur lors du parsing : {e}")
        return None

# Exemple d'utilisation
if __name__ == "__main__":
    url = "https://www.primaliance.com/scpi-de-rendement/49-scpi-epargne-fonciere"

    print("Récupération des informations SCPI...")
    scpi_data = get_scpi_data(url)

    if scpi_data:
        print(scpi_data)

        # Accès direct aux attributs
        print(f"\nPrix de retrait récent: {scpi_data.prix_retrait_recent}")
        print(f"Capitalisation: {scpi_data.capitalisation}")
        print(f"Frais de gestion: {scpi_data.frais_de_gestion}")
    else:
        print("Échec de la récupération des données")
