(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[990],{2365:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let a=n(3464).A.create({baseURL:"http://localhost:8000/api",headers:{"Content-Type":"application/json"},timeout:1e4});a.interceptors.response.use(e=>e,e=>{var t,n;return console.error("API Error:",null==(t=e.response)?void 0:t.status,null==(n=e.config)?void 0:n.url,e.message),Promise.reject(e)});let r=a},2497:(e,t,n)=>{Promise.resolve().then(n.bind(n,7688))},7688:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>c});var a=n(5155),r=n(2115),l=n(2365),i=n(8827),s=n(902),d=n(9986);let o=e=>{let{liability:t,onSave:n,onCancel:d}=e,[o,c]=(0,r.useState)({name:(null==t?void 0:t.name)||"",initial_amount:(null==t?void 0:t.initial_amount)||0,remaining_capital:(null==t?void 0:t.remaining_capital)||0,interest_rate:(null==t?void 0:t.interest_rate)||0,monthly_payment:(null==t?void 0:t.monthly_payment)||0,end_date:(null==t?void 0:t.end_date)?new Date(t.end_date).toISOString().split("T")[0]:new Date().toISOString().split("T")[0]});(0,r.useEffect)(()=>{c({name:(null==t?void 0:t.name)||"",initial_amount:(null==t?void 0:t.initial_amount)||0,remaining_capital:(null==t?void 0:t.remaining_capital)||0,interest_rate:(null==t?void 0:t.interest_rate)||0,monthly_payment:(null==t?void 0:t.monthly_payment)||0,end_date:(null==t?void 0:t.end_date)?new Date(t.end_date).toISOString().split("T")[0]:new Date().toISOString().split("T")[0]})},[t]);let u=async e=>{e.preventDefault();let a=(null==t?void 0:t.id)?"put":"post",r=(null==t?void 0:t.id)?"/liabilities/".concat(t.id):"/liabilities";try{await l.A[a](r,o),n()}catch(e){console.error("Erreur lors de la sauvegarde de l'emprunt",e)}};return(0,a.jsxs)(i.A,{onSubmit:u,children:[(0,a.jsxs)(i.A.Group,{className:"mb-3",children:[(0,a.jsx)(i.A.Label,{children:"Nom"}),(0,a.jsx)(i.A.Control,{type:"text",value:o.name,onChange:e=>c({...o,name:e.target.value}),required:!0})]}),(0,a.jsxs)(i.A.Group,{className:"mb-3",children:[(0,a.jsx)(i.A.Label,{children:"Capital Restant D\xfb"}),(0,a.jsx)(i.A.Control,{type:"number",value:o.remaining_capital,onChange:e=>c({...o,remaining_capital:parseFloat(e.target.value)}),required:!0})]}),(0,a.jsxs)(i.A.Group,{className:"mb-3",children:[(0,a.jsx)(i.A.Label,{children:"Mensualit\xe9"}),(0,a.jsx)(i.A.Control,{type:"number",value:o.monthly_payment,onChange:e=>c({...o,monthly_payment:parseFloat(e.target.value)}),required:!0})]}),(0,a.jsxs)(i.A.Group,{className:"mb-3",children:[(0,a.jsx)(i.A.Label,{children:"Taux d'int\xe9r\xeat (%)"}),(0,a.jsx)(i.A.Control,{type:"number",step:"0.01",value:o.interest_rate,onChange:e=>c({...o,interest_rate:parseFloat(e.target.value)}),required:!0})]}),(0,a.jsxs)(i.A.Group,{className:"mb-3",children:[(0,a.jsx)(i.A.Label,{children:"Date de fin"}),(0,a.jsx)(i.A.Control,{type:"date",value:o.end_date,onChange:e=>c({...o,end_date:e.target.value}),required:!0})]}),!(null==t?void 0:t.id)&&(0,a.jsxs)(i.A.Group,{className:"mb-3",children:[(0,a.jsx)(i.A.Label,{children:"Montant Initial (optionnel si non pertinent)"}),(0,a.jsx)(i.A.Control,{type:"number",value:o.initial_amount,onChange:e=>c({...o,initial_amount:parseFloat(e.target.value)})})]}),(0,a.jsx)(s.A,{variant:"primary",type:"submit",children:"Sauvegarder"}),(0,a.jsx)(s.A,{variant:"secondary",onClick:d,className:"ms-2",children:"Annuler"})]})},c=()=>{let[e,t]=(0,r.useState)([]),[n,i]=(0,r.useState)(!0),[c,u]=(0,r.useState)(null),[m,h]=(0,r.useState)(!1),[p,x]=(0,r.useState)(null),[j,v]=(0,r.useState)(0),y=()=>{i(!0),u(null),l.A.get("/liabilities").then(e=>{t(e.data),i(!1),v(0)}).catch(e=>{console.error("Liabilities API error:",e),j<2?(v(e=>e+1),setTimeout(()=>y(),1e3)):(u("Erreur lors de la r\xe9cup\xe9ration des emprunts."),i(!1))})};(0,r.useEffect)(()=>{y()},[]);let _=async e=>{if(window.confirm("\xcates-vous s\xfbr de vouloir supprimer cet emprunt ?"))try{await l.A.delete("/liabilities/".concat(e)),y()}catch(e){console.error("Erreur lors de la suppression de l'emprunt",e)}};return n&&!m&&0===j?(0,a.jsx)("p",{children:"Chargement des emprunts..."}):c&&j>=2?(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-danger",children:c}),(0,a.jsx)("button",{className:"btn btn-primary",onClick:()=>{v(0),y()},children:"R\xe9essayer"})]}):n||e.length||c?n&&!m?(0,a.jsxs)("p",{children:["Chargement des emprunts, tentative ",j+1,"..."]}):(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"d-flex justify-content-between align-items-center mb-4",children:[(0,a.jsx)("h1",{children:"Mes Emprunts (Passifs)"}),(0,a.jsx)(s.A,{variant:"primary",onClick:()=>{x({}),h(!0)},children:"Ajouter un Emprunt"})]}),e.length>0&&(0,a.jsxs)("table",{className:"table table-striped table-hover",children:[(0,a.jsx)("thead",{className:"table-dark",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{children:"Nom"}),(0,a.jsx)("th",{className:"text-end",children:"Capital Restant D\xfb"}),(0,a.jsx)("th",{className:"text-end",children:"Mensualit\xe9"}),(0,a.jsx)("th",{className:"text-center",children:"Taux"}),(0,a.jsx)("th",{className:"text-center",children:"Date de Fin"}),(0,a.jsx)("th",{className:"text-center",children:"Actions"})]})}),(0,a.jsx)("tbody",{children:e.map(e=>(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{children:e.name}),(0,a.jsx)("td",{className:"text-end",children:new Intl.NumberFormat("fr-FR",{style:"currency",currency:"EUR"}).format(e.remaining_capital)}),(0,a.jsx)("td",{className:"text-end",children:new Intl.NumberFormat("fr-FR",{style:"currency",currency:"EUR"}).format(e.monthly_payment)}),(0,a.jsxs)("td",{className:"text-center",children:[e.interest_rate,"%"]}),(0,a.jsx)("td",{className:"text-center",children:new Date(e.end_date).toLocaleDateString("fr-FR")}),(0,a.jsxs)("td",{className:"text-center",children:[(0,a.jsx)(s.A,{variant:"outline-primary",size:"sm",onClick:()=>{x(e),h(!0)},children:"Modifier"})," ",(0,a.jsx)(s.A,{variant:"outline-danger",size:"sm",onClick:()=>_(e.id),children:"Supprimer"})]})]},e.id))})]}),(0,a.jsxs)(d.A,{show:m,onHide:()=>h(!1),children:[(0,a.jsx)(d.A.Header,{closeButton:!0,children:(0,a.jsxs)(d.A.Title,{children:[(null==p?void 0:p.id)?"Modifier":"Ajouter"," un Emprunt"]})}),(0,a.jsx)(d.A.Body,{children:(0,a.jsx)(o,{liability:p,onSave:()=>{h(!1),x(null),y()},onCancel:()=>h(!1)})})]})]}):(0,a.jsx)("p",{children:"Aucun emprunt trouv\xe9."})}}},e=>{var t=t=>e(e.s=t);e.O(0,[464,160,405,441,684,358],()=>t(2497)),_N_E=e.O()}]);