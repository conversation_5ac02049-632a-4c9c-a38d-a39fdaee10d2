{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/AI-Code/fire_UI/frontnextjs/node_modules/chartjs-plugin-annotation/dist/chartjs-plugin-annotation.esm.js"], "sourcesContent": ["/*!\n* chartjs-plugin-annotation v3.1.0\n* https://www.chartjs.org/chartjs-plugin-annotation/index\n * (c) 2024 chartjs-plugin-annotation Contributors\n * Released under the MIT License\n */\nimport { Element, DoughnutController, defaults, Animations, Chart } from 'chart.js';\nimport { distanceBetweenPoints, toRadians, isObject, valueOrDefault, defined, isFunction, callback, isArray, toFont, addRoundedRectPath, toTRBLCorners, QUARTER_PI, PI, HALF_PI, TWO_THIRDS_PI, TAU, isNumber, RAD_PER_DEG, toPadding, isFinite, getAngleFromPoint, toDegrees, clipArea, unclipArea } from 'chart.js/helpers';\n\n/**\n * @typedef { import(\"chart.js\").ChartEvent } ChartEvent\n * @typedef { import('../../types/element').AnnotationElement } AnnotationElement\n */\n\nconst interaction = {\n  modes: {\n    /**\n     * Point mode returns all elements that hit test based on the event position\n     * @param {AnnotationElement[]} visibleElements - annotation elements which are visible\n     * @param {ChartEvent} event - the event we are find things at\n     * @return {AnnotationElement[]} - elements that are found\n     */\n    point(visibleElements, event) {\n      return filterElements(visibleElements, event, {intersect: true});\n    },\n\n    /**\n     * Nearest mode returns the element closest to the event position\n     * @param {AnnotationElement[]} visibleElements - annotation elements which are visible\n     * @param {ChartEvent} event - the event we are find things at\n     * @param {Object} options - interaction options to use\n     * @return {AnnotationElement[]} - elements that are found (only 1 element)\n     */\n    nearest(visibleElements, event, options) {\n      return getNearestItem(visibleElements, event, options);\n    },\n    /**\n     * x mode returns the elements that hit-test at the current x coordinate\n     * @param {AnnotationElement[]} visibleElements - annotation elements which are visible\n     * @param {ChartEvent} event - the event we are find things at\n     * @param {Object} options - interaction options to use\n     * @return {AnnotationElement[]} - elements that are found\n     */\n    x(visibleElements, event, options) {\n      return filterElements(visibleElements, event, {intersect: options.intersect, axis: 'x'});\n    },\n\n    /**\n     * y mode returns the elements that hit-test at the current y coordinate\n     * @param {AnnotationElement[]} visibleElements - annotation elements which are visible\n     * @param {ChartEvent} event - the event we are find things at\n     * @param {Object} options - interaction options to use\n     * @return {AnnotationElement[]} - elements that are found\n     */\n    y(visibleElements, event, options) {\n      return filterElements(visibleElements, event, {intersect: options.intersect, axis: 'y'});\n    }\n  }\n};\n\n/**\n * Returns all elements that hit test based on the event position\n * @param {AnnotationElement[]} visibleElements - annotation elements which are visible\n * @param {ChartEvent} event - the event we are find things at\n * @param {Object} options - interaction options to use\n * @return {AnnotationElement[]} - elements that are found\n */\nfunction getElements(visibleElements, event, options) {\n  const mode = interaction.modes[options.mode] || interaction.modes.nearest;\n  return mode(visibleElements, event, options);\n}\n\nfunction inRangeByAxis(element, event, axis) {\n  if (axis !== 'x' && axis !== 'y') {\n    return element.inRange(event.x, event.y, 'x', true) || element.inRange(event.x, event.y, 'y', true);\n  }\n  return element.inRange(event.x, event.y, axis, true);\n}\n\nfunction getPointByAxis(event, center, axis) {\n  if (axis === 'x') {\n    return {x: event.x, y: center.y};\n  } else if (axis === 'y') {\n    return {x: center.x, y: event.y};\n  }\n  return center;\n}\n\nfunction filterElements(visibleElements, event, options) {\n  return visibleElements.filter((element) => options.intersect ? element.inRange(event.x, event.y) : inRangeByAxis(element, event, options.axis));\n}\n\nfunction getNearestItem(visibleElements, event, options) {\n  let minDistance = Number.POSITIVE_INFINITY;\n\n  return filterElements(visibleElements, event, options)\n    .reduce((nearestItems, element) => {\n      const center = element.getCenterPoint();\n      const evenPoint = getPointByAxis(event, center, options.axis);\n      const distance = distanceBetweenPoints(event, evenPoint);\n      if (distance < minDistance) {\n        nearestItems = [element];\n        minDistance = distance;\n      } else if (distance === minDistance) {\n        // Can have multiple items at the same distance in which case we sort by size\n        nearestItems.push(element);\n      }\n\n      return nearestItems;\n    }, [])\n    .sort((a, b) => a._index - b._index)\n    .slice(0, 1); // return only the top item;\n}\n\n/**\n * @typedef {import('chart.js').Point} Point\n */\n\n/**\n * Rotate a `point` relative to `center` point by `angle`\n * @param {Point} point - the point to rotate\n * @param {Point} center - center point for rotation\n * @param {number} angle - angle for rotation, in radians\n * @returns {Point} rotated point\n */\nfunction rotated(point, center, angle) {\n  const cos = Math.cos(angle);\n  const sin = Math.sin(angle);\n  const cx = center.x;\n  const cy = center.y;\n\n  return {\n    x: cx + cos * (point.x - cx) - sin * (point.y - cy),\n    y: cy + sin * (point.x - cx) + cos * (point.y - cy)\n  };\n}\n\nconst isOlderPart = (act, req) => req > act || (act.length > req.length && act.slice(0, req.length) === req);\n\n/**\n * @typedef { import('chart.js').Point } Point\n * @typedef { import('chart.js').InteractionAxis } InteractionAxis\n * @typedef { import('../../types/element').AnnotationElement } AnnotationElement\n */\n\nconst EPSILON = 0.001;\nconst clamp = (x, from, to) => Math.min(to, Math.max(from, x));\n\n/**\n * @param {{value: number, start: number, end: number}} limit\n * @param {number} hitSize\n * @returns {boolean}\n */\nconst inLimit = (limit, hitSize) => limit.value >= limit.start - hitSize && limit.value <= limit.end + hitSize;\n\n/**\n * @param {Object} obj\n * @param {number} from\n * @param {number} to\n * @returns {Object}\n */\nfunction clampAll(obj, from, to) {\n  for (const key of Object.keys(obj)) {\n    obj[key] = clamp(obj[key], from, to);\n  }\n  return obj;\n}\n\n/**\n * @param {Point} point\n * @param {Point} center\n * @param {number} radius\n * @param {number} hitSize\n * @returns {boolean}\n */\nfunction inPointRange(point, center, radius, hitSize) {\n  if (!point || !center || radius <= 0) {\n    return false;\n  }\n  return (Math.pow(point.x - center.x, 2) + Math.pow(point.y - center.y, 2)) <= Math.pow(radius + hitSize, 2);\n}\n\n/**\n * @param {Point} point\n * @param {{x: number, y: number, x2: number, y2: number}} rect\n * @param {InteractionAxis} axis\n * @param {{borderWidth: number, hitTolerance: number}} hitsize\n * @returns {boolean}\n */\nfunction inBoxRange(point, {x, y, x2, y2}, axis, {borderWidth, hitTolerance}) {\n  const hitSize = (borderWidth + hitTolerance) / 2;\n  const inRangeX = point.x >= x - hitSize - EPSILON && point.x <= x2 + hitSize + EPSILON;\n  const inRangeY = point.y >= y - hitSize - EPSILON && point.y <= y2 + hitSize + EPSILON;\n  if (axis === 'x') {\n    return inRangeX;\n  } else if (axis === 'y') {\n    return inRangeY;\n  }\n  return inRangeX && inRangeY;\n}\n\n/**\n * @param {Point} point\n * @param {rect: {x: number, y: number, x2: number, y2: number}, center: {x: number, y: number}} element\n * @param {InteractionAxis} axis\n * @param {{rotation: number, borderWidth: number, hitTolerance: number}}\n * @returns {boolean}\n */\nfunction inLabelRange(point, {rect, center}, axis, {rotation, borderWidth, hitTolerance}) {\n  const rotPoint = rotated(point, center, toRadians(-rotation));\n  return inBoxRange(rotPoint, rect, axis, {borderWidth, hitTolerance});\n}\n\n/**\n * @param {AnnotationElement} element\n * @param {boolean} useFinalPosition\n * @returns {Point}\n */\nfunction getElementCenterPoint(element, useFinalPosition) {\n  const {centerX, centerY} = element.getProps(['centerX', 'centerY'], useFinalPosition);\n  return {x: centerX, y: centerY};\n}\n\n/**\n * @param {string} pkg\n * @param {string} min\n * @param {string} ver\n * @param {boolean} [strict=true]\n * @returns {boolean}\n */\nfunction requireVersion(pkg, min, ver, strict = true) {\n  const parts = ver.split('.');\n  let i = 0;\n  for (const req of min.split('.')) {\n    const act = parts[i++];\n    if (parseInt(req, 10) < parseInt(act, 10)) {\n      break;\n    }\n    if (isOlderPart(act, req)) {\n      if (strict) {\n        throw new Error(`${pkg} v${ver} is not supported. v${min} or newer is required.`);\n      } else {\n        return false;\n      }\n    }\n  }\n  return true;\n}\n\nconst isPercentString = (s) => typeof s === 'string' && s.endsWith('%');\nconst toPercent = (s) => parseFloat(s) / 100;\nconst toPositivePercent = (s) => clamp(toPercent(s), 0, 1);\n\nconst boxAppering = (x, y) => ({x, y, x2: x, y2: y, width: 0, height: 0});\nconst defaultInitAnimation = {\n  box: (properties) => boxAppering(properties.centerX, properties.centerY),\n  doughnutLabel: (properties) => boxAppering(properties.centerX, properties.centerY),\n  ellipse: (properties) => ({centerX: properties.centerX, centerY: properties.centerX, radius: 0, width: 0, height: 0}),\n  label: (properties) => boxAppering(properties.centerX, properties.centerY),\n  line: (properties) => boxAppering(properties.x, properties.y),\n  point: (properties) => ({centerX: properties.centerX, centerY: properties.centerY, radius: 0, width: 0, height: 0}),\n  polygon: (properties) => boxAppering(properties.centerX, properties.centerY)\n};\n\n/**\n * @typedef { import('chart.js').FontSpec } FontSpec\n * @typedef { import('chart.js').Point } Point\n * @typedef { import('chart.js').Padding } Padding\n * @typedef { import('../../types/element').AnnotationBoxModel } AnnotationBoxModel\n * @typedef { import('../../types/element').AnnotationElement } AnnotationElement\n * @typedef { import('../../types/options').AnnotationPointCoordinates } AnnotationPointCoordinates\n * @typedef { import('../../types/label').CoreLabelOptions } CoreLabelOptions\n * @typedef { import('../../types/label').LabelPositionObject } LabelPositionObject\n */\n\n/**\n * @param {number} size\n * @param {number|string} position\n * @returns {number}\n */\nfunction getRelativePosition(size, position) {\n  if (position === 'start') {\n    return 0;\n  }\n  if (position === 'end') {\n    return size;\n  }\n  if (isPercentString(position)) {\n    return toPositivePercent(position) * size;\n  }\n  return size / 2;\n}\n\n/**\n * @param {number} size\n * @param {number|string} value\n * @param {boolean} [positivePercent=true]\n * @returns {number}\n */\nfunction getSize(size, value, positivePercent = true) {\n  if (typeof value === 'number') {\n    return value;\n  } else if (isPercentString(value)) {\n    return (positivePercent ? toPositivePercent(value) : toPercent(value)) * size;\n  }\n  return size;\n}\n\n/**\n * @param {{x: number, width: number}} size\n * @param {CoreLabelOptions} options\n * @returns {number}\n */\nfunction calculateTextAlignment(size, options) {\n  const {x, width} = size;\n  const textAlign = options.textAlign;\n  if (textAlign === 'center') {\n    return x + width / 2;\n  } else if (textAlign === 'end' || textAlign === 'right') {\n    return x + width;\n  }\n  return x;\n}\n\n/**\n * @param {Point} point\n * @param {{height: number, width: number}} labelSize\n * @param {{borderWidth: number, position: {LabelPositionObject|string}, xAdjust: number, yAdjust: number}} options\n * @param {Padding|undefined} padding\n * @returns {{x: number, y: number, x2: number, y2: number, height: number, width: number, centerX: number, centerY: number}}\n */\nfunction measureLabelRectangle(point, labelSize, {borderWidth, position, xAdjust, yAdjust}, padding) {\n  const hasPadding = isObject(padding);\n  const width = labelSize.width + (hasPadding ? padding.width : 0) + borderWidth;\n  const height = labelSize.height + (hasPadding ? padding.height : 0) + borderWidth;\n  const positionObj = toPosition(position);\n  const x = calculateLabelPosition$1(point.x, width, xAdjust, positionObj.x);\n  const y = calculateLabelPosition$1(point.y, height, yAdjust, positionObj.y);\n\n  return {\n    x,\n    y,\n    x2: x + width,\n    y2: y + height,\n    width,\n    height,\n    centerX: x + width / 2,\n    centerY: y + height / 2\n  };\n}\n\n/**\n * @param {LabelPositionObject|string} value\n * @param {string|number} defaultValue\n * @returns {LabelPositionObject}\n */\nfunction toPosition(value, defaultValue = 'center') {\n  if (isObject(value)) {\n    return {\n      x: valueOrDefault(value.x, defaultValue),\n      y: valueOrDefault(value.y, defaultValue),\n    };\n  }\n  value = valueOrDefault(value, defaultValue);\n  return {\n    x: value,\n    y: value\n  };\n}\n\n/**\n * @param {CoreLabelOptions} options\n * @param {number} fitRatio\n * @returns {boolean}\n */\nconst shouldFit = (options, fitRatio) => options && options.autoFit && fitRatio < 1;\n\n/**\n * @param {CoreLabelOptions} options\n * @param {number} fitRatio\n * @returns {FontSpec[]}\n */\nfunction toFonts(options, fitRatio) {\n  const optFont = options.font;\n  const fonts = isArray(optFont) ? optFont : [optFont];\n  if (shouldFit(options, fitRatio)) {\n    return fonts.map(function(f) {\n      const font = toFont(f);\n      font.size = Math.floor(f.size * fitRatio);\n      font.lineHeight = f.lineHeight;\n      return toFont(font);\n    });\n  }\n  return fonts.map(f => toFont(f));\n}\n\n/**\n * @param {AnnotationPointCoordinates} options\n * @returns {boolean}\n */\nfunction isBoundToPoint(options) {\n  return options && (defined(options.xValue) || defined(options.yValue));\n}\n\nfunction calculateLabelPosition$1(start, size, adjust = 0, position) {\n  return start - getRelativePosition(size, position) + adjust;\n}\n\n/**\n * @param {Chart} chart\n * @param {AnnotationBoxModel} properties\n * @param {CoreAnnotationOptions} options\n * @returns {AnnotationElement}\n */\nfunction initAnimationProperties(chart, properties, options) {\n  const initAnim = options.init;\n  if (!initAnim) {\n    return;\n  } else if (initAnim === true) {\n    return applyDefault(properties, options);\n  }\n  return execCallback(chart, properties, options);\n}\n\n/**\n * @param {Object} options\n * @param {Array} hooks\n * @param {Object} hooksContainer\n * @returns {boolean}\n */\nfunction loadHooks(options, hooks, hooksContainer) {\n  let activated = false;\n  hooks.forEach(hook => {\n    if (isFunction(options[hook])) {\n      activated = true;\n      hooksContainer[hook] = options[hook];\n    } else if (defined(hooksContainer[hook])) {\n      delete hooksContainer[hook];\n    }\n  });\n  return activated;\n}\n\nfunction applyDefault(properties, options) {\n  const type = options.type || 'line';\n  return defaultInitAnimation[type](properties);\n}\n\nfunction execCallback(chart, properties, options) {\n  const result = callback(options.init, [{chart, properties, options}]);\n  if (result === true) {\n    return applyDefault(properties, options);\n  } else if (isObject(result)) {\n    return result;\n  }\n}\n\nconst widthCache = new Map();\nconst notRadius = (radius) => isNaN(radius) || radius <= 0;\nconst fontsKey = (fonts) => fonts.reduce(function(prev, item) {\n  prev += item.string;\n  return prev;\n}, '');\n\n/**\n * @typedef { import('chart.js').Point } Point\n * @typedef { import('../../types/label').CoreLabelOptions } CoreLabelOptions\n * @typedef { import('../../types/options').PointAnnotationOptions } PointAnnotationOptions\n */\n\n/**\n * Determine if content is an image or a canvas.\n * @param {*} content\n * @returns boolean|undefined\n * @todo move this function to chart.js helpers\n */\nfunction isImageOrCanvas(content) {\n  if (content && typeof content === 'object') {\n    const type = content.toString();\n    return (type === '[object HTMLImageElement]' || type === '[object HTMLCanvasElement]');\n  }\n}\n\n/**\n * Set the translation on the canvas if the rotation must be applied.\n * @param {CanvasRenderingContext2D} ctx - chart canvas context\n * @param {Point} point - the point of translation\n * @param {number} rotation - rotation (in degrees) to apply\n */\nfunction translate(ctx, {x, y}, rotation) {\n  if (rotation) {\n    ctx.translate(x, y);\n    ctx.rotate(toRadians(rotation));\n    ctx.translate(-x, -y);\n  }\n}\n\n/**\n * @param {CanvasRenderingContext2D} ctx\n * @param {Object} options\n * @returns {boolean|undefined}\n */\nfunction setBorderStyle(ctx, options) {\n  if (options && options.borderWidth) {\n    ctx.lineCap = options.borderCapStyle || 'butt';\n    ctx.setLineDash(options.borderDash);\n    ctx.lineDashOffset = options.borderDashOffset;\n    ctx.lineJoin = options.borderJoinStyle || 'miter';\n    ctx.lineWidth = options.borderWidth;\n    ctx.strokeStyle = options.borderColor;\n    return true;\n  }\n}\n\n/**\n * @param {CanvasRenderingContext2D} ctx\n * @param {Object} options\n */\nfunction setShadowStyle(ctx, options) {\n  ctx.shadowColor = options.backgroundShadowColor;\n  ctx.shadowBlur = options.shadowBlur;\n  ctx.shadowOffsetX = options.shadowOffsetX;\n  ctx.shadowOffsetY = options.shadowOffsetY;\n}\n\n/**\n * @param {CanvasRenderingContext2D} ctx\n * @param {CoreLabelOptions} options\n * @returns {{width: number, height: number}}\n */\nfunction measureLabelSize(ctx, options) {\n  const content = options.content;\n  if (isImageOrCanvas(content)) {\n    const size = {\n      width: getSize(content.width, options.width),\n      height: getSize(content.height, options.height)\n    };\n    return size;\n  }\n  const fonts = toFonts(options);\n  const strokeWidth = options.textStrokeWidth;\n  const lines = isArray(content) ? content : [content];\n  const mapKey = lines.join() + fontsKey(fonts) + strokeWidth + (ctx._measureText ? '-spriting' : '');\n  if (!widthCache.has(mapKey)) {\n    widthCache.set(mapKey, calculateLabelSize(ctx, lines, fonts, strokeWidth));\n  }\n  return widthCache.get(mapKey);\n}\n\n/**\n * @param {CanvasRenderingContext2D} ctx\n * @param {{x: number, y: number, width: number, height: number}} rect\n * @param {Object} options\n */\nfunction drawBox(ctx, rect, options) {\n  const {x, y, width, height} = rect;\n  ctx.save();\n  setShadowStyle(ctx, options);\n  const stroke = setBorderStyle(ctx, options);\n  ctx.fillStyle = options.backgroundColor;\n  ctx.beginPath();\n  addRoundedRectPath(ctx, {\n    x, y, w: width, h: height,\n    radius: clampAll(toTRBLCorners(options.borderRadius), 0, Math.min(width, height) / 2)\n  });\n  ctx.closePath();\n  ctx.fill();\n  if (stroke) {\n    ctx.shadowColor = options.borderShadowColor;\n    ctx.stroke();\n  }\n  ctx.restore();\n}\n\n/**\n * @param {CanvasRenderingContext2D} ctx\n * @param {{x: number, y: number, width: number, height: number}} rect\n * @param {CoreLabelOptions} options\n * @param {number} fitRatio\n */\nfunction drawLabel(ctx, rect, options, fitRatio) {\n  const content = options.content;\n  if (isImageOrCanvas(content)) {\n    ctx.save();\n    ctx.globalAlpha = getOpacity(options.opacity, content.style.opacity);\n    ctx.drawImage(content, rect.x, rect.y, rect.width, rect.height);\n    ctx.restore();\n    return;\n  }\n  const labels = isArray(content) ? content : [content];\n  const fonts = toFonts(options, fitRatio);\n  const optColor = options.color;\n  const colors = isArray(optColor) ? optColor : [optColor];\n  const x = calculateTextAlignment(rect, options);\n  const y = rect.y + options.textStrokeWidth / 2;\n  ctx.save();\n  ctx.textBaseline = 'middle';\n  ctx.textAlign = options.textAlign;\n  if (setTextStrokeStyle(ctx, options)) {\n    applyLabelDecoration(ctx, {x, y}, labels, fonts);\n  }\n  applyLabelContent(ctx, {x, y}, labels, {fonts, colors});\n  ctx.restore();\n}\n\nfunction setTextStrokeStyle(ctx, options) {\n  if (options.textStrokeWidth > 0) {\n    // https://stackoverflow.com/questions/13627111/drawing-text-with-an-outer-stroke-with-html5s-canvas\n    ctx.lineJoin = 'round';\n    ctx.miterLimit = 2;\n    ctx.lineWidth = options.textStrokeWidth;\n    ctx.strokeStyle = options.textStrokeColor;\n    return true;\n  }\n}\n\n/**\n * @param {CanvasRenderingContext2D} ctx\n * @param {{radius: number, options: PointAnnotationOptions}} element\n * @param {number} x\n * @param {number} y\n */\nfunction drawPoint(ctx, element, x, y) {\n  const {radius, options} = element;\n  const style = options.pointStyle;\n  const rotation = options.rotation;\n  let rad = (rotation || 0) * RAD_PER_DEG;\n\n  if (isImageOrCanvas(style)) {\n    ctx.save();\n    ctx.translate(x, y);\n    ctx.rotate(rad);\n    ctx.drawImage(style, -style.width / 2, -style.height / 2, style.width, style.height);\n    ctx.restore();\n    return;\n  }\n  if (notRadius(radius)) {\n    return;\n  }\n  drawPointStyle(ctx, {x, y, radius, rotation, style, rad});\n}\n\nfunction drawPointStyle(ctx, {x, y, radius, rotation, style, rad}) {\n  let xOffset, yOffset, size, cornerRadius;\n  ctx.beginPath();\n\n  switch (style) {\n  // Default includes circle\n  default:\n    ctx.arc(x, y, radius, 0, TAU);\n    ctx.closePath();\n    break;\n  case 'triangle':\n    ctx.moveTo(x + Math.sin(rad) * radius, y - Math.cos(rad) * radius);\n    rad += TWO_THIRDS_PI;\n    ctx.lineTo(x + Math.sin(rad) * radius, y - Math.cos(rad) * radius);\n    rad += TWO_THIRDS_PI;\n    ctx.lineTo(x + Math.sin(rad) * radius, y - Math.cos(rad) * radius);\n    ctx.closePath();\n    break;\n  case 'rectRounded':\n    // NOTE: the rounded rect implementation changed to use `arc` instead of\n    // `quadraticCurveTo` since it generates better results when rect is\n    // almost a circle. 0.516 (instead of 0.5) produces results with visually\n    // closer proportion to the previous impl and it is inscribed in the\n    // circle with `radius`. For more details, see the following PRs:\n    // https://github.com/chartjs/Chart.js/issues/5597\n    // https://github.com/chartjs/Chart.js/issues/5858\n    cornerRadius = radius * 0.516;\n    size = radius - cornerRadius;\n    xOffset = Math.cos(rad + QUARTER_PI) * size;\n    yOffset = Math.sin(rad + QUARTER_PI) * size;\n    ctx.arc(x - xOffset, y - yOffset, cornerRadius, rad - PI, rad - HALF_PI);\n    ctx.arc(x + yOffset, y - xOffset, cornerRadius, rad - HALF_PI, rad);\n    ctx.arc(x + xOffset, y + yOffset, cornerRadius, rad, rad + HALF_PI);\n    ctx.arc(x - yOffset, y + xOffset, cornerRadius, rad + HALF_PI, rad + PI);\n    ctx.closePath();\n    break;\n  case 'rect':\n    if (!rotation) {\n      size = Math.SQRT1_2 * radius;\n      ctx.rect(x - size, y - size, 2 * size, 2 * size);\n      break;\n    }\n    rad += QUARTER_PI;\n    /* falls through */\n  case 'rectRot':\n    xOffset = Math.cos(rad) * radius;\n    yOffset = Math.sin(rad) * radius;\n    ctx.moveTo(x - xOffset, y - yOffset);\n    ctx.lineTo(x + yOffset, y - xOffset);\n    ctx.lineTo(x + xOffset, y + yOffset);\n    ctx.lineTo(x - yOffset, y + xOffset);\n    ctx.closePath();\n    break;\n  case 'crossRot':\n    rad += QUARTER_PI;\n    /* falls through */\n  case 'cross':\n    xOffset = Math.cos(rad) * radius;\n    yOffset = Math.sin(rad) * radius;\n    ctx.moveTo(x - xOffset, y - yOffset);\n    ctx.lineTo(x + xOffset, y + yOffset);\n    ctx.moveTo(x + yOffset, y - xOffset);\n    ctx.lineTo(x - yOffset, y + xOffset);\n    break;\n  case 'star':\n    xOffset = Math.cos(rad) * radius;\n    yOffset = Math.sin(rad) * radius;\n    ctx.moveTo(x - xOffset, y - yOffset);\n    ctx.lineTo(x + xOffset, y + yOffset);\n    ctx.moveTo(x + yOffset, y - xOffset);\n    ctx.lineTo(x - yOffset, y + xOffset);\n    rad += QUARTER_PI;\n    xOffset = Math.cos(rad) * radius;\n    yOffset = Math.sin(rad) * radius;\n    ctx.moveTo(x - xOffset, y - yOffset);\n    ctx.lineTo(x + xOffset, y + yOffset);\n    ctx.moveTo(x + yOffset, y - xOffset);\n    ctx.lineTo(x - yOffset, y + xOffset);\n    break;\n  case 'line':\n    xOffset = Math.cos(rad) * radius;\n    yOffset = Math.sin(rad) * radius;\n    ctx.moveTo(x - xOffset, y - yOffset);\n    ctx.lineTo(x + xOffset, y + yOffset);\n    break;\n  case 'dash':\n    ctx.moveTo(x, y);\n    ctx.lineTo(x + Math.cos(rad) * radius, y + Math.sin(rad) * radius);\n    break;\n  }\n\n  ctx.fill();\n}\n\nfunction calculateLabelSize(ctx, lines, fonts, strokeWidth) {\n  ctx.save();\n  const count = lines.length;\n  let width = 0;\n  let height = strokeWidth;\n  for (let i = 0; i < count; i++) {\n    const font = fonts[Math.min(i, fonts.length - 1)];\n    ctx.font = font.string;\n    const text = lines[i];\n    width = Math.max(width, ctx.measureText(text).width + strokeWidth);\n    height += font.lineHeight;\n  }\n  ctx.restore();\n  return {width, height};\n}\n\nfunction applyLabelDecoration(ctx, {x, y}, labels, fonts) {\n  ctx.beginPath();\n  let lhs = 0;\n  labels.forEach(function(l, i) {\n    const f = fonts[Math.min(i, fonts.length - 1)];\n    const lh = f.lineHeight;\n    ctx.font = f.string;\n    ctx.strokeText(l, x, y + lh / 2 + lhs);\n    lhs += lh;\n  });\n  ctx.stroke();\n}\n\nfunction applyLabelContent(ctx, {x, y}, labels, {fonts, colors}) {\n  let lhs = 0;\n  labels.forEach(function(l, i) {\n    const c = colors[Math.min(i, colors.length - 1)];\n    const f = fonts[Math.min(i, fonts.length - 1)];\n    const lh = f.lineHeight;\n    ctx.beginPath();\n    ctx.font = f.string;\n    ctx.fillStyle = c;\n    ctx.fillText(l, x, y + lh / 2 + lhs);\n    lhs += lh;\n    ctx.fill();\n  });\n}\n\nfunction getOpacity(value, elementValue) {\n  const opacity = isNumber(value) ? value : elementValue;\n  return isNumber(opacity) ? clamp(opacity, 0, 1) : 1;\n}\n\nconst positions = ['left', 'bottom', 'top', 'right'];\n\n/**\n * @typedef { import('../../types/element').AnnotationElement } AnnotationElement\n */\n\n/**\n * Drawa the callout component for labels.\n * @param {CanvasRenderingContext2D} ctx - chart canvas context\n * @param {AnnotationElement} element - the label element\n */\nfunction drawCallout(ctx, element) {\n  const {pointX, pointY, options} = element;\n  const callout = options.callout;\n  const calloutPosition = callout && callout.display && resolveCalloutPosition(element, callout);\n  if (!calloutPosition || isPointInRange(element, callout, calloutPosition)) {\n    return;\n  }\n\n  ctx.save();\n  ctx.beginPath();\n  const stroke = setBorderStyle(ctx, callout);\n  if (!stroke) {\n    return ctx.restore();\n  }\n  const {separatorStart, separatorEnd} = getCalloutSeparatorCoord(element, calloutPosition);\n  const {sideStart, sideEnd} = getCalloutSideCoord(element, calloutPosition, separatorStart);\n  if (callout.margin > 0 || options.borderWidth === 0) {\n    ctx.moveTo(separatorStart.x, separatorStart.y);\n    ctx.lineTo(separatorEnd.x, separatorEnd.y);\n  }\n  ctx.moveTo(sideStart.x, sideStart.y);\n  ctx.lineTo(sideEnd.x, sideEnd.y);\n  const rotatedPoint = rotated({x: pointX, y: pointY}, element.getCenterPoint(), toRadians(-element.rotation));\n  ctx.lineTo(rotatedPoint.x, rotatedPoint.y);\n  ctx.stroke();\n  ctx.restore();\n}\n\nfunction getCalloutSeparatorCoord(element, position) {\n  const {x, y, x2, y2} = element;\n  const adjust = getCalloutSeparatorAdjust(element, position);\n  let separatorStart, separatorEnd;\n  if (position === 'left' || position === 'right') {\n    separatorStart = {x: x + adjust, y};\n    separatorEnd = {x: separatorStart.x, y: y2};\n  } else {\n    //  position 'top' or 'bottom'\n    separatorStart = {x, y: y + adjust};\n    separatorEnd = {x: x2, y: separatorStart.y};\n  }\n  return {separatorStart, separatorEnd};\n}\n\nfunction getCalloutSeparatorAdjust(element, position) {\n  const {width, height, options} = element;\n  const adjust = options.callout.margin + options.borderWidth / 2;\n  if (position === 'right') {\n    return width + adjust;\n  } else if (position === 'bottom') {\n    return height + adjust;\n  }\n  return -adjust;\n}\n\nfunction getCalloutSideCoord(element, position, separatorStart) {\n  const {y, width, height, options} = element;\n  const start = options.callout.start;\n  const side = getCalloutSideAdjust(position, options.callout);\n  let sideStart, sideEnd;\n  if (position === 'left' || position === 'right') {\n    sideStart = {x: separatorStart.x, y: y + getSize(height, start)};\n    sideEnd = {x: sideStart.x + side, y: sideStart.y};\n  } else {\n    //  position 'top' or 'bottom'\n    sideStart = {x: separatorStart.x + getSize(width, start), y: separatorStart.y};\n    sideEnd = {x: sideStart.x, y: sideStart.y + side};\n  }\n  return {sideStart, sideEnd};\n}\n\nfunction getCalloutSideAdjust(position, options) {\n  const side = options.side;\n  if (position === 'left' || position === 'top') {\n    return -side;\n  }\n  return side;\n}\n\nfunction resolveCalloutPosition(element, options) {\n  const position = options.position;\n  if (positions.includes(position)) {\n    return position;\n  }\n  return resolveCalloutAutoPosition(element, options);\n}\n\nfunction resolveCalloutAutoPosition(element, options) {\n  const {x, y, x2, y2, width, height, pointX, pointY, centerX, centerY, rotation} = element;\n  const center = {x: centerX, y: centerY};\n  const start = options.start;\n  const xAdjust = getSize(width, start);\n  const yAdjust = getSize(height, start);\n  const xPoints = [x, x + xAdjust, x + xAdjust, x2];\n  const yPoints = [y + yAdjust, y2, y, y2];\n  const result = [];\n  for (let index = 0; index < 4; index++) {\n    const rotatedPoint = rotated({x: xPoints[index], y: yPoints[index]}, center, toRadians(rotation));\n    result.push({\n      position: positions[index],\n      distance: distanceBetweenPoints(rotatedPoint, {x: pointX, y: pointY})\n    });\n  }\n  return result.sort((a, b) => a.distance - b.distance)[0].position;\n}\n\nfunction isPointInRange(element, callout, position) {\n  const {pointX, pointY} = element;\n  const margin = callout.margin;\n  let x = pointX;\n  let y = pointY;\n  if (position === 'left') {\n    x += margin;\n  } else if (position === 'right') {\n    x -= margin;\n  } else if (position === 'top') {\n    y += margin;\n  } else if (position === 'bottom') {\n    y -= margin;\n  }\n  return element.inRange(x, y);\n}\n\nconst limitedLineScale = {\n  xScaleID: {min: 'xMin', max: 'xMax', start: 'left', end: 'right', startProp: 'x', endProp: 'x2'},\n  yScaleID: {min: 'yMin', max: 'yMax', start: 'bottom', end: 'top', startProp: 'y', endProp: 'y2'}\n};\n\n/**\n * @typedef { import(\"chart.js\").Chart } Chart\n * @typedef { import(\"chart.js\").Scale } Scale\n * @typedef { import(\"chart.js\").Point } Point\n * @typedef { import('../../types/element').AnnotationBoxModel } AnnotationBoxModel\n * @typedef { import('../../types/options').CoreAnnotationOptions } CoreAnnotationOptions\n * @typedef { import('../../types/options').LineAnnotationOptions } LineAnnotationOptions\n * @typedef { import('../../types/options').PointAnnotationOptions } PointAnnotationOptions\n * @typedef { import('../../types/options').PolygonAnnotationOptions } PolygonAnnotationOptions\n */\n\n/**\n * @param {Scale} scale\n * @param {number|string} value\n * @param {number} fallback\n * @returns {number}\n */\nfunction scaleValue(scale, value, fallback) {\n  value = typeof value === 'number' ? value : scale.parse(value);\n  return isFinite(value) ? scale.getPixelForValue(value) : fallback;\n}\n\n/**\n * Search the scale defined in chartjs by the axis related to the annotation options key.\n * @param {{ [key: string]: Scale }} scales\n * @param {CoreAnnotationOptions} options\n * @param {string} key\n * @returns {string}\n */\nfunction retrieveScaleID(scales, options, key) {\n  const scaleID = options[key];\n  if (scaleID || key === 'scaleID') {\n    return scaleID;\n  }\n  const axis = key.charAt(0);\n  const axes = Object.values(scales).filter((scale) => scale.axis && scale.axis === axis);\n  if (axes.length) {\n    return axes[0].id;\n  }\n  return axis;\n}\n\n/**\n * @param {Scale} scale\n * @param {{min: number, max: number, start: number, end: number}} options\n * @returns {{start: number, end: number}|undefined}\n */\nfunction getDimensionByScale(scale, options) {\n  if (scale) {\n    const reverse = scale.options.reverse;\n    const start = scaleValue(scale, options.min, reverse ? options.end : options.start);\n    const end = scaleValue(scale, options.max, reverse ? options.start : options.end);\n    return {\n      start,\n      end\n    };\n  }\n}\n\n/**\n * @param {Chart} chart\n * @param {CoreAnnotationOptions} options\n * @returns {Point}\n */\nfunction getChartPoint(chart, options) {\n  const {chartArea, scales} = chart;\n  const xScale = scales[retrieveScaleID(scales, options, 'xScaleID')];\n  const yScale = scales[retrieveScaleID(scales, options, 'yScaleID')];\n  let x = chartArea.width / 2;\n  let y = chartArea.height / 2;\n\n  if (xScale) {\n    x = scaleValue(xScale, options.xValue, xScale.left + xScale.width / 2);\n  }\n\n  if (yScale) {\n    y = scaleValue(yScale, options.yValue, yScale.top + yScale.height / 2);\n  }\n  return {x, y};\n}\n\n/**\n * @param {Chart} chart\n * @param {CoreAnnotationOptions} options\n * @returns {AnnotationBoxModel}\n */\nfunction resolveBoxProperties(chart, options) {\n  const scales = chart.scales;\n  const xScale = scales[retrieveScaleID(scales, options, 'xScaleID')];\n  const yScale = scales[retrieveScaleID(scales, options, 'yScaleID')];\n\n  if (!xScale && !yScale) {\n    return {};\n  }\n\n  let {left: x, right: x2} = xScale || chart.chartArea;\n  let {top: y, bottom: y2} = yScale || chart.chartArea;\n  const xDim = getChartDimensionByScale(xScale, {min: options.xMin, max: options.xMax, start: x, end: x2});\n  x = xDim.start;\n  x2 = xDim.end;\n  const yDim = getChartDimensionByScale(yScale, {min: options.yMin, max: options.yMax, start: y2, end: y});\n  y = yDim.start;\n  y2 = yDim.end;\n\n  return {\n    x,\n    y,\n    x2,\n    y2,\n    width: x2 - x,\n    height: y2 - y,\n    centerX: x + (x2 - x) / 2,\n    centerY: y + (y2 - y) / 2\n  };\n}\n\n/**\n * @param {Chart} chart\n * @param {PointAnnotationOptions|PolygonAnnotationOptions} options\n * @returns {AnnotationBoxModel}\n */\nfunction resolvePointProperties(chart, options) {\n  if (!isBoundToPoint(options)) {\n    const box = resolveBoxProperties(chart, options);\n    let radius = options.radius;\n    if (!radius || isNaN(radius)) {\n      radius = Math.min(box.width, box.height) / 2;\n      options.radius = radius;\n    }\n    const size = radius * 2;\n    const adjustCenterX = box.centerX + options.xAdjust;\n    const adjustCenterY = box.centerY + options.yAdjust;\n    return {\n      x: adjustCenterX - radius,\n      y: adjustCenterY - radius,\n      x2: adjustCenterX + radius,\n      y2: adjustCenterY + radius,\n      centerX: adjustCenterX,\n      centerY: adjustCenterY,\n      width: size,\n      height: size,\n      radius\n    };\n  }\n  return getChartCircle(chart, options);\n}\n/**\n * @param {Chart} chart\n * @param {LineAnnotationOptions} options\n * @returns {AnnotationBoxModel}\n */\nfunction resolveLineProperties(chart, options) {\n  const {scales, chartArea} = chart;\n  const scale = scales[options.scaleID];\n  const area = {x: chartArea.left, y: chartArea.top, x2: chartArea.right, y2: chartArea.bottom};\n\n  if (scale) {\n    resolveFullLineProperties(scale, area, options);\n  } else {\n    resolveLimitedLineProperties(scales, area, options);\n  }\n  return area;\n}\n\n/**\n * @param {Chart} chart\n * @param {CoreAnnotationOptions} options\n * @param {boolean} [centerBased=false]\n * @returns {AnnotationBoxModel}\n */\nfunction resolveBoxAndLabelProperties(chart, options) {\n  const properties = resolveBoxProperties(chart, options);\n  properties.initProperties = initAnimationProperties(chart, properties, options);\n  properties.elements = [{\n    type: 'label',\n    optionScope: 'label',\n    properties: resolveLabelElementProperties$1(chart, properties, options),\n    initProperties: properties.initProperties\n  }];\n  return properties;\n}\n\nfunction getChartCircle(chart, options) {\n  const point = getChartPoint(chart, options);\n  const size = options.radius * 2;\n  return {\n    x: point.x - options.radius + options.xAdjust,\n    y: point.y - options.radius + options.yAdjust,\n    x2: point.x + options.radius + options.xAdjust,\n    y2: point.y + options.radius + options.yAdjust,\n    centerX: point.x + options.xAdjust,\n    centerY: point.y + options.yAdjust,\n    radius: options.radius,\n    width: size,\n    height: size\n  };\n}\n\nfunction getChartDimensionByScale(scale, options) {\n  const result = getDimensionByScale(scale, options) || options;\n  return {\n    start: Math.min(result.start, result.end),\n    end: Math.max(result.start, result.end)\n  };\n}\n\nfunction resolveFullLineProperties(scale, area, options) {\n  const min = scaleValue(scale, options.value, NaN);\n  const max = scaleValue(scale, options.endValue, min);\n  if (scale.isHorizontal()) {\n    area.x = min;\n    area.x2 = max;\n  } else {\n    area.y = min;\n    area.y2 = max;\n  }\n}\n\nfunction resolveLimitedLineProperties(scales, area, options) {\n  for (const scaleId of Object.keys(limitedLineScale)) {\n    const scale = scales[retrieveScaleID(scales, options, scaleId)];\n    if (scale) {\n      const {min, max, start, end, startProp, endProp} = limitedLineScale[scaleId];\n      const dim = getDimensionByScale(scale, {min: options[min], max: options[max], start: scale[start], end: scale[end]});\n      area[startProp] = dim.start;\n      area[endProp] = dim.end;\n    }\n  }\n}\n\nfunction calculateX({properties, options}, labelSize, position, padding) {\n  const {x: start, x2: end, width: size} = properties;\n  return calculatePosition({start, end, size, borderWidth: options.borderWidth}, {\n    position: position.x,\n    padding: {start: padding.left, end: padding.right},\n    adjust: options.label.xAdjust,\n    size: labelSize.width\n  });\n}\n\nfunction calculateY({properties, options}, labelSize, position, padding) {\n  const {y: start, y2: end, height: size} = properties;\n  return calculatePosition({start, end, size, borderWidth: options.borderWidth}, {\n    position: position.y,\n    padding: {start: padding.top, end: padding.bottom},\n    adjust: options.label.yAdjust,\n    size: labelSize.height\n  });\n}\n\nfunction calculatePosition(boxOpts, labelOpts) {\n  const {start, end, borderWidth} = boxOpts;\n  const {position, padding: {start: padStart, end: padEnd}, adjust} = labelOpts;\n  const availableSize = end - borderWidth - start - padStart - padEnd - labelOpts.size;\n  return start + borderWidth / 2 + adjust + getRelativePosition(availableSize, position);\n}\n\nfunction resolveLabelElementProperties$1(chart, properties, options) {\n  const label = options.label;\n  label.backgroundColor = 'transparent';\n  label.callout.display = false;\n  const position = toPosition(label.position);\n  const padding = toPadding(label.padding);\n  const labelSize = measureLabelSize(chart.ctx, label);\n  const x = calculateX({properties, options}, labelSize, position, padding);\n  const y = calculateY({properties, options}, labelSize, position, padding);\n  const width = labelSize.width + padding.width;\n  const height = labelSize.height + padding.height;\n  return {\n    x,\n    y,\n    x2: x + width,\n    y2: y + height,\n    width,\n    height,\n    centerX: x + width / 2,\n    centerY: y + height / 2,\n    rotation: label.rotation\n  };\n\n}\n\nconst moveHooks = ['enter', 'leave'];\n\n/**\n * @typedef { import(\"chart.js\").Chart } Chart\n * @typedef { import('../../types/options').AnnotationPluginOptions } AnnotationPluginOptions\n */\n\nconst eventHooks = moveHooks.concat('click');\n\n/**\n * @param {Chart} chart\n * @param {Object} state\n * @param {AnnotationPluginOptions} options\n */\nfunction updateListeners(chart, state, options) {\n  state.listened = loadHooks(options, eventHooks, state.listeners);\n  state.moveListened = false;\n\n  moveHooks.forEach(hook => {\n    if (isFunction(options[hook])) {\n      state.moveListened = true;\n    }\n  });\n\n  if (!state.listened || !state.moveListened) {\n    state.annotations.forEach(scope => {\n      if (!state.listened && isFunction(scope.click)) {\n        state.listened = true;\n      }\n      if (!state.moveListened) {\n        moveHooks.forEach(hook => {\n          if (isFunction(scope[hook])) {\n            state.listened = true;\n            state.moveListened = true;\n          }\n        });\n      }\n    });\n  }\n}\n\n/**\n * @param {Object} state\n * @param {ChartEvent} event\n * @param {AnnotationPluginOptions} options\n * @return {boolean|undefined}\n */\nfunction handleEvent(state, event, options) {\n  if (state.listened) {\n    switch (event.type) {\n    case 'mousemove':\n    case 'mouseout':\n      return handleMoveEvents(state, event, options);\n    case 'click':\n      return handleClickEvents(state, event, options);\n    }\n  }\n}\n\nfunction handleMoveEvents(state, event, options) {\n  if (!state.moveListened) {\n    return;\n  }\n\n  let elements;\n\n  if (event.type === 'mousemove') {\n    elements = getElements(state.visibleElements, event, options.interaction);\n  } else {\n    elements = [];\n  }\n\n  const previous = state.hovered;\n  state.hovered = elements;\n\n  const context = {state, event};\n  let changed = dispatchMoveEvents(context, 'leave', previous, elements);\n  return dispatchMoveEvents(context, 'enter', elements, previous) || changed;\n}\n\nfunction dispatchMoveEvents({state, event}, hook, elements, checkElements) {\n  let changed;\n  for (const element of elements) {\n    if (checkElements.indexOf(element) < 0) {\n      changed = dispatchEvent(element.options[hook] || state.listeners[hook], element, event) || changed;\n    }\n  }\n  return changed;\n}\n\nfunction handleClickEvents(state, event, options) {\n  const listeners = state.listeners;\n  const elements = getElements(state.visibleElements, event, options.interaction);\n  let changed;\n  for (const element of elements) {\n    changed = dispatchEvent(element.options.click || listeners.click, element, event) || changed;\n  }\n  return changed;\n}\n\nfunction dispatchEvent(handler, element, event) {\n  return callback(handler, [element.$context, event]) === true;\n}\n\n/**\n * @typedef { import(\"chart.js\").Chart } Chart\n * @typedef { import('../../types/options').AnnotationPluginOptions } AnnotationPluginOptions\n * @typedef { import('../../types/element').AnnotationElement } AnnotationElement\n */\n\nconst elementHooks = ['afterDraw', 'beforeDraw'];\n\n/**\n * @param {Chart} chart\n * @param {Object} state\n * @param {AnnotationPluginOptions} options\n */\nfunction updateHooks(chart, state, options) {\n  const visibleElements = state.visibleElements;\n  state.hooked = loadHooks(options, elementHooks, state.hooks);\n\n  if (!state.hooked) {\n    visibleElements.forEach(scope => {\n      if (!state.hooked) {\n        elementHooks.forEach(hook => {\n          if (isFunction(scope.options[hook])) {\n            state.hooked = true;\n          }\n        });\n      }\n    });\n  }\n}\n\n/**\n * @param {Object} state\n * @param {AnnotationElement} element\n * @param {string} hook\n */\nfunction invokeHook(state, element, hook) {\n  if (state.hooked) {\n    const callbackHook = element.options[hook] || state.hooks[hook];\n    return callback(callbackHook, [element.$context]);\n  }\n}\n\n/**\n * @typedef { import(\"chart.js\").Chart } Chart\n * @typedef { import(\"chart.js\").Scale } Scale\n * @typedef { import('../../types/options').CoreAnnotationOptions } CoreAnnotationOptions\n */\n\n/**\n * @param {Chart} chart\n * @param {Scale} scale\n * @param {CoreAnnotationOptions[]} annotations\n */\nfunction adjustScaleRange(chart, scale, annotations) {\n  const range = getScaleLimits(chart.scales, scale, annotations);\n  let changed = changeScaleLimit(scale, range, 'min', 'suggestedMin');\n  changed = changeScaleLimit(scale, range, 'max', 'suggestedMax') || changed;\n  if (changed && isFunction(scale.handleTickRangeOptions)) {\n    scale.handleTickRangeOptions();\n  }\n}\n\n/**\n * @param {CoreAnnotationOptions[]} annotations\n * @param {{ [key: string]: Scale }} scales\n */\nfunction verifyScaleOptions(annotations, scales) {\n  for (const annotation of annotations) {\n    verifyScaleIDs(annotation, scales);\n  }\n}\n\nfunction changeScaleLimit(scale, range, limit, suggestedLimit) {\n  if (isFinite(range[limit]) && !scaleLimitDefined(scale.options, limit, suggestedLimit)) {\n    const changed = scale[limit] !== range[limit];\n    scale[limit] = range[limit];\n    return changed;\n  }\n}\n\nfunction scaleLimitDefined(scaleOptions, limit, suggestedLimit) {\n  return defined(scaleOptions[limit]) || defined(scaleOptions[suggestedLimit]);\n}\n\nfunction verifyScaleIDs(annotation, scales) {\n  for (const key of ['scaleID', 'xScaleID', 'yScaleID']) {\n    const scaleID = retrieveScaleID(scales, annotation, key);\n    if (scaleID && !scales[scaleID] && verifyProperties(annotation, key)) {\n      console.warn(`No scale found with id '${scaleID}' for annotation '${annotation.id}'`);\n    }\n  }\n}\n\nfunction verifyProperties(annotation, key) {\n  if (key === 'scaleID') {\n    return true;\n  }\n  const axis = key.charAt(0);\n  for (const prop of ['Min', 'Max', 'Value']) {\n    if (defined(annotation[axis + prop])) {\n      return true;\n    }\n  }\n  return false;\n}\n\nfunction getScaleLimits(scales, scale, annotations) {\n  const axis = scale.axis;\n  const scaleID = scale.id;\n  const scaleIDOption = axis + 'ScaleID';\n  const limits = {\n    min: valueOrDefault(scale.min, Number.NEGATIVE_INFINITY),\n    max: valueOrDefault(scale.max, Number.POSITIVE_INFINITY)\n  };\n  for (const annotation of annotations) {\n    if (annotation.scaleID === scaleID) {\n      updateLimits(annotation, scale, ['value', 'endValue'], limits);\n    } else if (retrieveScaleID(scales, annotation, scaleIDOption) === scaleID) {\n      updateLimits(annotation, scale, [axis + 'Min', axis + 'Max', axis + 'Value'], limits);\n    }\n  }\n  return limits;\n}\n\nfunction updateLimits(annotation, scale, props, limits) {\n  for (const prop of props) {\n    const raw = annotation[prop];\n    if (defined(raw)) {\n      const value = scale.parse(raw);\n      limits.min = Math.min(limits.min, value);\n      limits.max = Math.max(limits.max, value);\n    }\n  }\n}\n\nclass BoxAnnotation extends Element {\n\n  inRange(mouseX, mouseY, axis, useFinalPosition) {\n    const {x, y} = rotated({x: mouseX, y: mouseY}, this.getCenterPoint(useFinalPosition), toRadians(-this.options.rotation));\n    return inBoxRange({x, y}, this.getProps(['x', 'y', 'x2', 'y2'], useFinalPosition), axis, this.options);\n  }\n\n  getCenterPoint(useFinalPosition) {\n    return getElementCenterPoint(this, useFinalPosition);\n  }\n\n  draw(ctx) {\n    ctx.save();\n    translate(ctx, this.getCenterPoint(), this.options.rotation);\n    drawBox(ctx, this, this.options);\n    ctx.restore();\n  }\n\n  get label() {\n    return this.elements && this.elements[0];\n  }\n\n  resolveElementProperties(chart, options) {\n    return resolveBoxAndLabelProperties(chart, options);\n  }\n}\n\nBoxAnnotation.id = 'boxAnnotation';\n\nBoxAnnotation.defaults = {\n  adjustScaleRange: true,\n  backgroundShadowColor: 'transparent',\n  borderCapStyle: 'butt',\n  borderDash: [],\n  borderDashOffset: 0,\n  borderJoinStyle: 'miter',\n  borderRadius: 0,\n  borderShadowColor: 'transparent',\n  borderWidth: 1,\n  display: true,\n  init: undefined,\n  hitTolerance: 0,\n  label: {\n    backgroundColor: 'transparent',\n    borderWidth: 0,\n    callout: {\n      display: false\n    },\n    color: 'black',\n    content: null,\n    display: false,\n    drawTime: undefined,\n    font: {\n      family: undefined,\n      lineHeight: undefined,\n      size: undefined,\n      style: undefined,\n      weight: 'bold'\n    },\n    height: undefined,\n    hitTolerance: undefined,\n    opacity: undefined,\n    padding: 6,\n    position: 'center',\n    rotation: undefined,\n    textAlign: 'start',\n    textStrokeColor: undefined,\n    textStrokeWidth: 0,\n    width: undefined,\n    xAdjust: 0,\n    yAdjust: 0,\n    z: undefined\n  },\n  rotation: 0,\n  shadowBlur: 0,\n  shadowOffsetX: 0,\n  shadowOffsetY: 0,\n  xMax: undefined,\n  xMin: undefined,\n  xScaleID: undefined,\n  yMax: undefined,\n  yMin: undefined,\n  yScaleID: undefined,\n  z: 0\n};\n\nBoxAnnotation.defaultRoutes = {\n  borderColor: 'color',\n  backgroundColor: 'color'\n};\n\nBoxAnnotation.descriptors = {\n  label: {\n    _fallback: true\n  }\n};\n\nclass DoughnutLabelAnnotation extends Element {\n\n  inRange(mouseX, mouseY, axis, useFinalPosition) {\n    return inLabelRange(\n      {x: mouseX, y: mouseY},\n      {rect: this.getProps(['x', 'y', 'x2', 'y2'], useFinalPosition), center: this.getCenterPoint(useFinalPosition)},\n      axis,\n      {rotation: this.rotation, borderWidth: 0, hitTolerance: this.options.hitTolerance}\n    );\n  }\n\n  getCenterPoint(useFinalPosition) {\n    return getElementCenterPoint(this, useFinalPosition);\n  }\n\n  draw(ctx) {\n    const options = this.options;\n    if (!options.display || !options.content) {\n      return;\n    }\n    drawBackground(ctx, this);\n    ctx.save();\n    translate(ctx, this.getCenterPoint(), this.rotation);\n    drawLabel(ctx, this, options, this._fitRatio);\n    ctx.restore();\n  }\n\n  resolveElementProperties(chart, options) {\n    const meta = getDatasetMeta(chart, options);\n    if (!meta) {\n      return {};\n    }\n    const {controllerMeta, point, radius} = getControllerMeta(chart, options, meta);\n    let labelSize = measureLabelSize(chart.ctx, options);\n    const _fitRatio = getFitRatio(labelSize, radius);\n    if (shouldFit(options, _fitRatio)) {\n      labelSize = {width: labelSize.width * _fitRatio, height: labelSize.height * _fitRatio};\n    }\n    const {position, xAdjust, yAdjust} = options;\n    const boxSize = measureLabelRectangle(point, labelSize, {borderWidth: 0, position, xAdjust, yAdjust});\n    return {\n      initProperties: initAnimationProperties(chart, boxSize, options),\n      ...boxSize,\n      ...controllerMeta,\n      rotation: options.rotation,\n      _fitRatio\n    };\n  }\n}\n\nDoughnutLabelAnnotation.id = 'doughnutLabelAnnotation';\n\nDoughnutLabelAnnotation.defaults = {\n  autoFit: true,\n  autoHide: true,\n  backgroundColor: 'transparent',\n  backgroundShadowColor: 'transparent',\n  borderColor: 'transparent',\n  borderDash: [],\n  borderDashOffset: 0,\n  borderJoinStyle: 'miter',\n  borderShadowColor: 'transparent',\n  borderWidth: 0,\n  color: 'black',\n  content: null,\n  display: true,\n  font: {\n    family: undefined,\n    lineHeight: undefined,\n    size: undefined,\n    style: undefined,\n    weight: undefined\n  },\n  height: undefined,\n  hitTolerance: 0,\n  init: undefined,\n  opacity: undefined,\n  position: 'center',\n  rotation: 0,\n  shadowBlur: 0,\n  shadowOffsetX: 0,\n  shadowOffsetY: 0,\n  spacing: 1,\n  textAlign: 'center',\n  textStrokeColor: undefined,\n  textStrokeWidth: 0,\n  width: undefined,\n  xAdjust: 0,\n  yAdjust: 0\n};\n\nDoughnutLabelAnnotation.defaultRoutes = {\n};\n\nfunction getDatasetMeta(chart, options) {\n  return chart.getSortedVisibleDatasetMetas().reduce(function(result, value) {\n    const controller = value.controller;\n    if (controller instanceof DoughnutController &&\n      isControllerVisible(chart, options, value.data) &&\n      (!result || controller.innerRadius < result.controller.innerRadius) &&\n      controller.options.circumference >= 90) {\n      return value;\n    }\n    return result;\n  }, undefined);\n}\n\nfunction isControllerVisible(chart, options, elements) {\n  if (!options.autoHide) {\n    return true;\n  }\n  for (let i = 0; i < elements.length; i++) {\n    if (!elements[i].hidden && chart.getDataVisibility(i)) {\n      return true;\n    }\n  }\n}\n\nfunction getControllerMeta({chartArea}, options, meta) {\n  const {left, top, right, bottom} = chartArea;\n  const {innerRadius, offsetX, offsetY} = meta.controller;\n  const x = (left + right) / 2 + offsetX;\n  const y = (top + bottom) / 2 + offsetY;\n  const square = {\n    left: Math.max(x - innerRadius, left),\n    right: Math.min(x + innerRadius, right),\n    top: Math.max(y - innerRadius, top),\n    bottom: Math.min(y + innerRadius, bottom)\n  };\n  const point = {\n    x: (square.left + square.right) / 2,\n    y: (square.top + square.bottom) / 2\n  };\n  const space = options.spacing + options.borderWidth / 2;\n  const _radius = innerRadius - space;\n  const _counterclockwise = point.y > y;\n  const side = _counterclockwise ? top + space : bottom - space;\n  const angles = getAngles(side, x, y, _radius);\n  const controllerMeta = {\n    _centerX: x,\n    _centerY: y,\n    _radius,\n    _counterclockwise,\n    ...angles\n  };\n  return {\n    controllerMeta,\n    point,\n    radius: Math.min(innerRadius, Math.min(square.right - square.left, square.bottom - square.top) / 2)\n  };\n}\n\nfunction getFitRatio({width, height}, radius) {\n  const hypo = Math.sqrt(Math.pow(width, 2) + Math.pow(height, 2));\n  return (radius * 2) / hypo;\n}\n\nfunction getAngles(y, centerX, centerY, radius) {\n  const yk2 = Math.pow(centerY - y, 2);\n  const r2 = Math.pow(radius, 2);\n  const b = centerX * -2;\n  const c = Math.pow(centerX, 2) + yk2 - r2;\n  const delta = Math.pow(b, 2) - (4 * c);\n  if (delta <= 0) {\n    return {\n      _startAngle: 0,\n      _endAngle: TAU\n    };\n  }\n  const start = (-b - Math.sqrt(delta)) / 2;\n  const end = (-b + Math.sqrt(delta)) / 2;\n  return {\n    _startAngle: getAngleFromPoint({x: centerX, y: centerY}, {x: start, y}).angle,\n    _endAngle: getAngleFromPoint({x: centerX, y: centerY}, {x: end, y}).angle\n  };\n}\n\nfunction drawBackground(ctx, element) {\n  const {_centerX, _centerY, _radius, _startAngle, _endAngle, _counterclockwise, options} = element;\n  ctx.save();\n  const stroke = setBorderStyle(ctx, options);\n  ctx.fillStyle = options.backgroundColor;\n  ctx.beginPath();\n  ctx.arc(_centerX, _centerY, _radius, _startAngle, _endAngle, _counterclockwise);\n  ctx.closePath();\n  ctx.fill();\n  if (stroke) {\n    ctx.stroke();\n  }\n  ctx.restore();\n}\n\nclass LabelAnnotation extends Element {\n\n  inRange(mouseX, mouseY, axis, useFinalPosition) {\n    return inLabelRange(\n      {x: mouseX, y: mouseY},\n      {rect: this.getProps(['x', 'y', 'x2', 'y2'], useFinalPosition), center: this.getCenterPoint(useFinalPosition)},\n      axis,\n      {rotation: this.rotation, borderWidth: this.options.borderWidth, hitTolerance: this.options.hitTolerance}\n    );\n  }\n\n  getCenterPoint(useFinalPosition) {\n    return getElementCenterPoint(this, useFinalPosition);\n  }\n\n  draw(ctx) {\n    const options = this.options;\n    const visible = !defined(this._visible) || this._visible;\n    if (!options.display || !options.content || !visible) {\n      return;\n    }\n    ctx.save();\n    translate(ctx, this.getCenterPoint(), this.rotation);\n    drawCallout(ctx, this);\n    drawBox(ctx, this, options);\n    drawLabel(ctx, getLabelSize(this), options);\n    ctx.restore();\n  }\n\n  resolveElementProperties(chart, options) {\n    let point;\n    if (!isBoundToPoint(options)) {\n      const {centerX, centerY} = resolveBoxProperties(chart, options);\n      point = {x: centerX, y: centerY};\n    } else {\n      point = getChartPoint(chart, options);\n    }\n    const padding = toPadding(options.padding);\n    const labelSize = measureLabelSize(chart.ctx, options);\n    const boxSize = measureLabelRectangle(point, labelSize, options, padding);\n    return {\n      initProperties: initAnimationProperties(chart, boxSize, options),\n      pointX: point.x,\n      pointY: point.y,\n      ...boxSize,\n      rotation: options.rotation\n    };\n  }\n}\n\nLabelAnnotation.id = 'labelAnnotation';\n\nLabelAnnotation.defaults = {\n  adjustScaleRange: true,\n  backgroundColor: 'transparent',\n  backgroundShadowColor: 'transparent',\n  borderCapStyle: 'butt',\n  borderDash: [],\n  borderDashOffset: 0,\n  borderJoinStyle: 'miter',\n  borderRadius: 0,\n  borderShadowColor: 'transparent',\n  borderWidth: 0,\n  callout: {\n    borderCapStyle: 'butt',\n    borderColor: undefined,\n    borderDash: [],\n    borderDashOffset: 0,\n    borderJoinStyle: 'miter',\n    borderWidth: 1,\n    display: false,\n    margin: 5,\n    position: 'auto',\n    side: 5,\n    start: '50%',\n  },\n  color: 'black',\n  content: null,\n  display: true,\n  font: {\n    family: undefined,\n    lineHeight: undefined,\n    size: undefined,\n    style: undefined,\n    weight: undefined\n  },\n  height: undefined,\n  hitTolerance: 0,\n  init: undefined,\n  opacity: undefined,\n  padding: 6,\n  position: 'center',\n  rotation: 0,\n  shadowBlur: 0,\n  shadowOffsetX: 0,\n  shadowOffsetY: 0,\n  textAlign: 'center',\n  textStrokeColor: undefined,\n  textStrokeWidth: 0,\n  width: undefined,\n  xAdjust: 0,\n  xMax: undefined,\n  xMin: undefined,\n  xScaleID: undefined,\n  xValue: undefined,\n  yAdjust: 0,\n  yMax: undefined,\n  yMin: undefined,\n  yScaleID: undefined,\n  yValue: undefined,\n  z: 0\n};\n\nLabelAnnotation.defaultRoutes = {\n  borderColor: 'color'\n};\n\nfunction getLabelSize({x, y, width, height, options}) {\n  const hBorderWidth = options.borderWidth / 2;\n  const padding = toPadding(options.padding);\n  return {\n    x: x + padding.left + hBorderWidth,\n    y: y + padding.top + hBorderWidth,\n    width: width - padding.left - padding.right - options.borderWidth,\n    height: height - padding.top - padding.bottom - options.borderWidth\n  };\n}\n\nconst pointInLine = (p1, p2, t) => ({x: p1.x + t * (p2.x - p1.x), y: p1.y + t * (p2.y - p1.y)});\nconst interpolateX = (y, p1, p2) => pointInLine(p1, p2, Math.abs((y - p1.y) / (p2.y - p1.y))).x;\nconst interpolateY = (x, p1, p2) => pointInLine(p1, p2, Math.abs((x - p1.x) / (p2.x - p1.x))).y;\nconst sqr = v => v * v;\nconst rangeLimit = (mouseX, mouseY, {x, y, x2, y2}, axis) => axis === 'y' ? {start: Math.min(y, y2), end: Math.max(y, y2), value: mouseY} : {start: Math.min(x, x2), end: Math.max(x, x2), value: mouseX};\n// http://www.independent-software.com/determining-coordinates-on-a-html-canvas-bezier-curve.html\nconst coordInCurve = (start, cp, end, t) => (1 - t) * (1 - t) * start + 2 * (1 - t) * t * cp + t * t * end;\nconst pointInCurve = (start, cp, end, t) => ({x: coordInCurve(start.x, cp.x, end.x, t), y: coordInCurve(start.y, cp.y, end.y, t)});\nconst coordAngleInCurve = (start, cp, end, t) => 2 * (1 - t) * (cp - start) + 2 * t * (end - cp);\nconst angleInCurve = (start, cp, end, t) => -Math.atan2(coordAngleInCurve(start.x, cp.x, end.x, t), coordAngleInCurve(start.y, cp.y, end.y, t)) + 0.5 * PI;\n\nclass LineAnnotation extends Element {\n\n  inRange(mouseX, mouseY, axis, useFinalPosition) {\n    const hitSize = (this.options.borderWidth + this.options.hitTolerance) / 2;\n    if (axis !== 'x' && axis !== 'y') {\n      const point = {mouseX, mouseY};\n      const {path, ctx} = this;\n      if (path) {\n        setBorderStyle(ctx, this.options);\n        ctx.lineWidth += this.options.hitTolerance;\n        const {chart} = this.$context;\n        const mx = mouseX * chart.currentDevicePixelRatio;\n        const my = mouseY * chart.currentDevicePixelRatio;\n        const result = ctx.isPointInStroke(path, mx, my) || isOnLabel(this, point, useFinalPosition);\n        ctx.restore();\n        return result;\n      }\n      const epsilon = sqr(hitSize);\n      return intersects(this, point, epsilon, useFinalPosition) || isOnLabel(this, point, useFinalPosition);\n    }\n    return inAxisRange(this, {mouseX, mouseY}, axis, {hitSize, useFinalPosition});\n  }\n\n  getCenterPoint(useFinalPosition) {\n    return getElementCenterPoint(this, useFinalPosition);\n  }\n\n  draw(ctx) {\n    const {x, y, x2, y2, cp, options} = this;\n\n    ctx.save();\n    if (!setBorderStyle(ctx, options)) {\n      // no border width, then line is not drawn\n      return ctx.restore();\n    }\n    setShadowStyle(ctx, options);\n\n    const length = Math.sqrt(Math.pow(x2 - x, 2) + Math.pow(y2 - y, 2));\n    if (options.curve && cp) {\n      drawCurve(ctx, this, cp, length);\n      return ctx.restore();\n    }\n    const {startOpts, endOpts, startAdjust, endAdjust} = getArrowHeads(this);\n    const angle = Math.atan2(y2 - y, x2 - x);\n    ctx.translate(x, y);\n    ctx.rotate(angle);\n    ctx.beginPath();\n    ctx.moveTo(0 + startAdjust, 0);\n    ctx.lineTo(length - endAdjust, 0);\n    ctx.shadowColor = options.borderShadowColor;\n    ctx.stroke();\n    drawArrowHead(ctx, 0, startAdjust, startOpts);\n    drawArrowHead(ctx, length, -endAdjust, endOpts);\n    ctx.restore();\n  }\n\n  get label() {\n    return this.elements && this.elements[0];\n  }\n\n  resolveElementProperties(chart, options) {\n    const area = resolveLineProperties(chart, options);\n    const {x, y, x2, y2} = area;\n    const inside = isLineInArea(area, chart.chartArea);\n    const properties = inside\n      ? limitLineToArea({x, y}, {x: x2, y: y2}, chart.chartArea)\n      : {x, y, x2, y2, width: Math.abs(x2 - x), height: Math.abs(y2 - y)};\n    properties.centerX = (x2 + x) / 2;\n    properties.centerY = (y2 + y) / 2;\n    properties.initProperties = initAnimationProperties(chart, properties, options);\n    if (options.curve) {\n      const p1 = {x: properties.x, y: properties.y};\n      const p2 = {x: properties.x2, y: properties.y2};\n      properties.cp = getControlPoint(properties, options, distanceBetweenPoints(p1, p2));\n    }\n    const labelProperties = resolveLabelElementProperties(chart, properties, options.label);\n    // additonal prop to manage zoom/pan\n    labelProperties._visible = inside;\n\n    properties.elements = [{\n      type: 'label',\n      optionScope: 'label',\n      properties: labelProperties,\n      initProperties: properties.initProperties\n    }];\n    return properties;\n  }\n}\n\nLineAnnotation.id = 'lineAnnotation';\n\nconst arrowHeadsDefaults = {\n  backgroundColor: undefined,\n  backgroundShadowColor: undefined,\n  borderColor: undefined,\n  borderDash: undefined,\n  borderDashOffset: undefined,\n  borderShadowColor: undefined,\n  borderWidth: undefined,\n  display: undefined,\n  fill: undefined,\n  length: undefined,\n  shadowBlur: undefined,\n  shadowOffsetX: undefined,\n  shadowOffsetY: undefined,\n  width: undefined\n};\n\nLineAnnotation.defaults = {\n  adjustScaleRange: true,\n  arrowHeads: {\n    display: false,\n    end: Object.assign({}, arrowHeadsDefaults),\n    fill: false,\n    length: 12,\n    start: Object.assign({}, arrowHeadsDefaults),\n    width: 6\n  },\n  borderDash: [],\n  borderDashOffset: 0,\n  borderShadowColor: 'transparent',\n  borderWidth: 2,\n  curve: false,\n  controlPoint: {\n    y: '-50%'\n  },\n  display: true,\n  endValue: undefined,\n  init: undefined,\n  hitTolerance: 0,\n  label: {\n    backgroundColor: 'rgba(0,0,0,0.8)',\n    backgroundShadowColor: 'transparent',\n    borderCapStyle: 'butt',\n    borderColor: 'black',\n    borderDash: [],\n    borderDashOffset: 0,\n    borderJoinStyle: 'miter',\n    borderRadius: 6,\n    borderShadowColor: 'transparent',\n    borderWidth: 0,\n    callout: Object.assign({}, LabelAnnotation.defaults.callout),\n    color: '#fff',\n    content: null,\n    display: false,\n    drawTime: undefined,\n    font: {\n      family: undefined,\n      lineHeight: undefined,\n      size: undefined,\n      style: undefined,\n      weight: 'bold'\n    },\n    height: undefined,\n    hitTolerance: undefined,\n    opacity: undefined,\n    padding: 6,\n    position: 'center',\n    rotation: 0,\n    shadowBlur: 0,\n    shadowOffsetX: 0,\n    shadowOffsetY: 0,\n    textAlign: 'center',\n    textStrokeColor: undefined,\n    textStrokeWidth: 0,\n    width: undefined,\n    xAdjust: 0,\n    yAdjust: 0,\n    z: undefined\n  },\n  scaleID: undefined,\n  shadowBlur: 0,\n  shadowOffsetX: 0,\n  shadowOffsetY: 0,\n  value: undefined,\n  xMax: undefined,\n  xMin: undefined,\n  xScaleID: undefined,\n  yMax: undefined,\n  yMin: undefined,\n  yScaleID: undefined,\n  z: 0\n};\n\nLineAnnotation.descriptors = {\n  arrowHeads: {\n    start: {\n      _fallback: true\n    },\n    end: {\n      _fallback: true\n    },\n    _fallback: true\n  }\n};\n\nLineAnnotation.defaultRoutes = {\n  borderColor: 'color'\n};\n\nfunction inAxisRange(element, {mouseX, mouseY}, axis, {hitSize, useFinalPosition}) {\n  const limit = rangeLimit(mouseX, mouseY, element.getProps(['x', 'y', 'x2', 'y2'], useFinalPosition), axis);\n  return inLimit(limit, hitSize) || isOnLabel(element, {mouseX, mouseY}, useFinalPosition, axis);\n}\n\nfunction isLineInArea({x, y, x2, y2}, {top, right, bottom, left}) {\n  return !(\n    (x < left && x2 < left) ||\n    (x > right && x2 > right) ||\n    (y < top && y2 < top) ||\n    (y > bottom && y2 > bottom)\n  );\n}\n\nfunction limitPointToArea({x, y}, p2, {top, right, bottom, left}) {\n  if (x < left) {\n    y = interpolateY(left, {x, y}, p2);\n    x = left;\n  }\n  if (x > right) {\n    y = interpolateY(right, {x, y}, p2);\n    x = right;\n  }\n  if (y < top) {\n    x = interpolateX(top, {x, y}, p2);\n    y = top;\n  }\n  if (y > bottom) {\n    x = interpolateX(bottom, {x, y}, p2);\n    y = bottom;\n  }\n  return {x, y};\n}\n\nfunction limitLineToArea(p1, p2, area) {\n  const {x, y} = limitPointToArea(p1, p2, area);\n  const {x: x2, y: y2} = limitPointToArea(p2, p1, area);\n  return {x, y, x2, y2, width: Math.abs(x2 - x), height: Math.abs(y2 - y)};\n}\n\nfunction intersects(element, {mouseX, mouseY}, epsilon = EPSILON, useFinalPosition) {\n  // Adapted from https://stackoverflow.com/a/6853926/25507\n  const {x: x1, y: y1, x2, y2} = element.getProps(['x', 'y', 'x2', 'y2'], useFinalPosition);\n  const dx = x2 - x1;\n  const dy = y2 - y1;\n  const lenSq = sqr(dx) + sqr(dy);\n  const t = lenSq === 0 ? -1 : ((mouseX - x1) * dx + (mouseY - y1) * dy) / lenSq;\n\n  let xx, yy;\n  if (t < 0) {\n    xx = x1;\n    yy = y1;\n  } else if (t > 1) {\n    xx = x2;\n    yy = y2;\n  } else {\n    xx = x1 + t * dx;\n    yy = y1 + t * dy;\n  }\n  return (sqr(mouseX - xx) + sqr(mouseY - yy)) <= epsilon;\n}\n\nfunction isOnLabel(element, {mouseX, mouseY}, useFinalPosition, axis) {\n  const label = element.label;\n  return label.options.display && label.inRange(mouseX, mouseY, axis, useFinalPosition);\n}\n\nfunction resolveLabelElementProperties(chart, properties, options) {\n  const borderWidth = options.borderWidth;\n  const padding = toPadding(options.padding);\n  const textSize = measureLabelSize(chart.ctx, options);\n  const width = textSize.width + padding.width + borderWidth;\n  const height = textSize.height + padding.height + borderWidth;\n  return calculateLabelPosition(properties, options, {width, height, padding}, chart.chartArea);\n}\n\nfunction calculateAutoRotation(properties) {\n  const {x, y, x2, y2} = properties;\n  const rotation = Math.atan2(y2 - y, x2 - x);\n  // Flip the rotation if it goes > PI/2 or < -PI/2, so label stays upright\n  return rotation > PI / 2 ? rotation - PI : rotation < PI / -2 ? rotation + PI : rotation;\n}\n\nfunction calculateLabelPosition(properties, label, sizes, chartArea) {\n  const {width, height, padding} = sizes;\n  const {xAdjust, yAdjust} = label;\n  const p1 = {x: properties.x, y: properties.y};\n  const p2 = {x: properties.x2, y: properties.y2};\n  const rotation = label.rotation === 'auto' ? calculateAutoRotation(properties) : toRadians(label.rotation);\n  const size = rotatedSize(width, height, rotation);\n  const t = calculateT(properties, label, {labelSize: size, padding}, chartArea);\n  const pt = properties.cp ? pointInCurve(p1, properties.cp, p2, t) : pointInLine(p1, p2, t);\n  const xCoordinateSizes = {size: size.w, min: chartArea.left, max: chartArea.right, padding: padding.left};\n  const yCoordinateSizes = {size: size.h, min: chartArea.top, max: chartArea.bottom, padding: padding.top};\n  const centerX = adjustLabelCoordinate(pt.x, xCoordinateSizes) + xAdjust;\n  const centerY = adjustLabelCoordinate(pt.y, yCoordinateSizes) + yAdjust;\n  return {\n    x: centerX - (width / 2),\n    y: centerY - (height / 2),\n    x2: centerX + (width / 2),\n    y2: centerY + (height / 2),\n    centerX,\n    centerY,\n    pointX: pt.x,\n    pointY: pt.y,\n    width,\n    height,\n    rotation: toDegrees(rotation)\n  };\n}\n\nfunction rotatedSize(width, height, rotation) {\n  const cos = Math.cos(rotation);\n  const sin = Math.sin(rotation);\n  return {\n    w: Math.abs(width * cos) + Math.abs(height * sin),\n    h: Math.abs(width * sin) + Math.abs(height * cos)\n  };\n}\n\nfunction calculateT(properties, label, sizes, chartArea) {\n  let t;\n  const space = spaceAround(properties, chartArea);\n  if (label.position === 'start') {\n    t = calculateTAdjust({w: properties.x2 - properties.x, h: properties.y2 - properties.y}, sizes, label, space);\n  } else if (label.position === 'end') {\n    t = 1 - calculateTAdjust({w: properties.x - properties.x2, h: properties.y - properties.y2}, sizes, label, space);\n  } else {\n    t = getRelativePosition(1, label.position);\n  }\n  return t;\n}\n\nfunction calculateTAdjust(lineSize, sizes, label, space) {\n  const {labelSize, padding} = sizes;\n  const lineW = lineSize.w * space.dx;\n  const lineH = lineSize.h * space.dy;\n  const x = (lineW > 0) && ((labelSize.w / 2 + padding.left - space.x) / lineW);\n  const y = (lineH > 0) && ((labelSize.h / 2 + padding.top - space.y) / lineH);\n  return clamp(Math.max(x, y), 0, 0.25);\n}\n\nfunction spaceAround(properties, chartArea) {\n  const {x, x2, y, y2} = properties;\n  const t = Math.min(y, y2) - chartArea.top;\n  const l = Math.min(x, x2) - chartArea.left;\n  const b = chartArea.bottom - Math.max(y, y2);\n  const r = chartArea.right - Math.max(x, x2);\n  return {\n    x: Math.min(l, r),\n    y: Math.min(t, b),\n    dx: l <= r ? 1 : -1,\n    dy: t <= b ? 1 : -1\n  };\n}\n\nfunction adjustLabelCoordinate(coordinate, labelSizes) {\n  const {size, min, max, padding} = labelSizes;\n  const halfSize = size / 2;\n  if (size > max - min) {\n    // if it does not fit, display as much as possible\n    return (max + min) / 2;\n  }\n  if (min >= (coordinate - padding - halfSize)) {\n    coordinate = min + padding + halfSize;\n  }\n  if (max <= (coordinate + padding + halfSize)) {\n    coordinate = max - padding - halfSize;\n  }\n  return coordinate;\n}\n\nfunction getArrowHeads(line) {\n  const options = line.options;\n  const arrowStartOpts = options.arrowHeads && options.arrowHeads.start;\n  const arrowEndOpts = options.arrowHeads && options.arrowHeads.end;\n  return {\n    startOpts: arrowStartOpts,\n    endOpts: arrowEndOpts,\n    startAdjust: getLineAdjust(line, arrowStartOpts),\n    endAdjust: getLineAdjust(line, arrowEndOpts)\n  };\n}\n\nfunction getLineAdjust(line, arrowOpts) {\n  if (!arrowOpts || !arrowOpts.display) {\n    return 0;\n  }\n  const {length, width} = arrowOpts;\n  const adjust = line.options.borderWidth / 2;\n  const p1 = {x: length, y: width + adjust};\n  const p2 = {x: 0, y: adjust};\n  return Math.abs(interpolateX(0, p1, p2));\n}\n\nfunction drawArrowHead(ctx, offset, adjust, arrowOpts) {\n  if (!arrowOpts || !arrowOpts.display) {\n    return;\n  }\n  const {length, width, fill, backgroundColor, borderColor} = arrowOpts;\n  const arrowOffsetX = Math.abs(offset - length) + adjust;\n  ctx.beginPath();\n  setShadowStyle(ctx, arrowOpts);\n  setBorderStyle(ctx, arrowOpts);\n  ctx.moveTo(arrowOffsetX, -width);\n  ctx.lineTo(offset + adjust, 0);\n  ctx.lineTo(arrowOffsetX, width);\n  if (fill === true) {\n    ctx.fillStyle = backgroundColor || borderColor;\n    ctx.closePath();\n    ctx.fill();\n    ctx.shadowColor = 'transparent';\n  } else {\n    ctx.shadowColor = arrowOpts.borderShadowColor;\n  }\n  ctx.stroke();\n}\n\nfunction getControlPoint(properties, options, distance) {\n  const {x, y, x2, y2, centerX, centerY} = properties;\n  const angle = Math.atan2(y2 - y, x2 - x);\n  const cp = toPosition(options.controlPoint, 0);\n  const point = {\n    x: centerX + getSize(distance, cp.x, false),\n    y: centerY + getSize(distance, cp.y, false)\n  };\n  return rotated(point, {x: centerX, y: centerY}, angle);\n}\n\nfunction drawArrowHeadOnCurve(ctx, {x, y}, {angle, adjust}, arrowOpts) {\n  if (!arrowOpts || !arrowOpts.display) {\n    return;\n  }\n  ctx.save();\n  ctx.translate(x, y);\n  ctx.rotate(angle);\n  drawArrowHead(ctx, 0, -adjust, arrowOpts);\n  ctx.restore();\n}\n\nfunction drawCurve(ctx, element, cp, length) {\n  const {x, y, x2, y2, options} = element;\n  const {startOpts, endOpts, startAdjust, endAdjust} = getArrowHeads(element);\n  const p1 = {x, y};\n  const p2 = {x: x2, y: y2};\n  const startAngle = angleInCurve(p1, cp, p2, 0);\n  const endAngle = angleInCurve(p1, cp, p2, 1) - PI;\n  const ps = pointInCurve(p1, cp, p2, startAdjust / length);\n  const pe = pointInCurve(p1, cp, p2, 1 - endAdjust / length);\n\n  const path = new Path2D();\n  ctx.beginPath();\n  path.moveTo(ps.x, ps.y);\n  path.quadraticCurveTo(cp.x, cp.y, pe.x, pe.y);\n  ctx.shadowColor = options.borderShadowColor;\n  ctx.stroke(path);\n  element.path = path;\n  element.ctx = ctx;\n  drawArrowHeadOnCurve(ctx, ps, {angle: startAngle, adjust: startAdjust}, startOpts);\n  drawArrowHeadOnCurve(ctx, pe, {angle: endAngle, adjust: endAdjust}, endOpts);\n}\n\nclass EllipseAnnotation extends Element {\n\n  inRange(mouseX, mouseY, axis, useFinalPosition) {\n    const rotation = this.options.rotation;\n    const hitSize = (this.options.borderWidth + this.options.hitTolerance) / 2;\n    if (axis !== 'x' && axis !== 'y') {\n      return pointInEllipse({x: mouseX, y: mouseY}, this.getProps(['width', 'height', 'centerX', 'centerY'], useFinalPosition), rotation, hitSize);\n    }\n    const {x, y, x2, y2} = this.getProps(['x', 'y', 'x2', 'y2'], useFinalPosition);\n    const limit = axis === 'y' ? {start: y, end: y2} : {start: x, end: x2};\n    const rotatedPoint = rotated({x: mouseX, y: mouseY}, this.getCenterPoint(useFinalPosition), toRadians(-rotation));\n    return rotatedPoint[axis] >= limit.start - hitSize - EPSILON && rotatedPoint[axis] <= limit.end + hitSize + EPSILON;\n  }\n\n  getCenterPoint(useFinalPosition) {\n    return getElementCenterPoint(this, useFinalPosition);\n  }\n\n  draw(ctx) {\n    const {width, height, centerX, centerY, options} = this;\n    ctx.save();\n    translate(ctx, this.getCenterPoint(), options.rotation);\n    setShadowStyle(ctx, this.options);\n    ctx.beginPath();\n    ctx.fillStyle = options.backgroundColor;\n    const stroke = setBorderStyle(ctx, options);\n    ctx.ellipse(centerX, centerY, height / 2, width / 2, PI / 2, 0, 2 * PI);\n    ctx.fill();\n    if (stroke) {\n      ctx.shadowColor = options.borderShadowColor;\n      ctx.stroke();\n    }\n    ctx.restore();\n  }\n\n  get label() {\n    return this.elements && this.elements[0];\n  }\n\n  resolveElementProperties(chart, options) {\n    return resolveBoxAndLabelProperties(chart, options);\n  }\n\n}\n\nEllipseAnnotation.id = 'ellipseAnnotation';\n\nEllipseAnnotation.defaults = {\n  adjustScaleRange: true,\n  backgroundShadowColor: 'transparent',\n  borderDash: [],\n  borderDashOffset: 0,\n  borderShadowColor: 'transparent',\n  borderWidth: 1,\n  display: true,\n  hitTolerance: 0,\n  init: undefined,\n  label: Object.assign({}, BoxAnnotation.defaults.label),\n  rotation: 0,\n  shadowBlur: 0,\n  shadowOffsetX: 0,\n  shadowOffsetY: 0,\n  xMax: undefined,\n  xMin: undefined,\n  xScaleID: undefined,\n  yMax: undefined,\n  yMin: undefined,\n  yScaleID: undefined,\n  z: 0\n};\n\nEllipseAnnotation.defaultRoutes = {\n  borderColor: 'color',\n  backgroundColor: 'color'\n};\n\nEllipseAnnotation.descriptors = {\n  label: {\n    _fallback: true\n  }\n};\n\nfunction pointInEllipse(p, ellipse, rotation, hitSize) {\n  const {width, height, centerX, centerY} = ellipse;\n  const xRadius = width / 2;\n  const yRadius = height / 2;\n\n  if (xRadius <= 0 || yRadius <= 0) {\n    return false;\n  }\n  // https://stackoverflow.com/questions/7946187/point-and-ellipse-rotated-position-test-algorithm\n  const angle = toRadians(rotation || 0);\n  const cosAngle = Math.cos(angle);\n  const sinAngle = Math.sin(angle);\n  const a = Math.pow(cosAngle * (p.x - centerX) + sinAngle * (p.y - centerY), 2);\n  const b = Math.pow(sinAngle * (p.x - centerX) - cosAngle * (p.y - centerY), 2);\n  return (a / Math.pow(xRadius + hitSize, 2)) + (b / Math.pow(yRadius + hitSize, 2)) <= 1.0001;\n}\n\nclass PointAnnotation extends Element {\n\n  inRange(mouseX, mouseY, axis, useFinalPosition) {\n    const {x, y, x2, y2, width} = this.getProps(['x', 'y', 'x2', 'y2', 'width'], useFinalPosition);\n    const hitSize = (this.options.borderWidth + this.options.hitTolerance) / 2;\n    if (axis !== 'x' && axis !== 'y') {\n      return inPointRange({x: mouseX, y: mouseY}, this.getCenterPoint(useFinalPosition), width / 2, hitSize);\n    }\n    const limit = axis === 'y' ? {start: y, end: y2, value: mouseY} : {start: x, end: x2, value: mouseX};\n    return inLimit(limit, hitSize);\n  }\n\n  getCenterPoint(useFinalPosition) {\n    return getElementCenterPoint(this, useFinalPosition);\n  }\n\n  draw(ctx) {\n    const options = this.options;\n    const borderWidth = options.borderWidth;\n    if (options.radius < 0.1) {\n      return;\n    }\n    ctx.save();\n    ctx.fillStyle = options.backgroundColor;\n    setShadowStyle(ctx, options);\n    const stroke = setBorderStyle(ctx, options);\n    drawPoint(ctx, this, this.centerX, this.centerY);\n    if (stroke && !isImageOrCanvas(options.pointStyle)) {\n      ctx.shadowColor = options.borderShadowColor;\n      ctx.stroke();\n    }\n    ctx.restore();\n    options.borderWidth = borderWidth;\n  }\n\n  resolveElementProperties(chart, options) {\n    const properties = resolvePointProperties(chart, options);\n    properties.initProperties = initAnimationProperties(chart, properties, options);\n    return properties;\n  }\n}\n\nPointAnnotation.id = 'pointAnnotation';\n\nPointAnnotation.defaults = {\n  adjustScaleRange: true,\n  backgroundShadowColor: 'transparent',\n  borderDash: [],\n  borderDashOffset: 0,\n  borderShadowColor: 'transparent',\n  borderWidth: 1,\n  display: true,\n  hitTolerance: 0,\n  init: undefined,\n  pointStyle: 'circle',\n  radius: 10,\n  rotation: 0,\n  shadowBlur: 0,\n  shadowOffsetX: 0,\n  shadowOffsetY: 0,\n  xAdjust: 0,\n  xMax: undefined,\n  xMin: undefined,\n  xScaleID: undefined,\n  xValue: undefined,\n  yAdjust: 0,\n  yMax: undefined,\n  yMin: undefined,\n  yScaleID: undefined,\n  yValue: undefined,\n  z: 0\n};\n\nPointAnnotation.defaultRoutes = {\n  borderColor: 'color',\n  backgroundColor: 'color'\n};\n\nclass PolygonAnnotation extends Element {\n\n  inRange(mouseX, mouseY, axis, useFinalPosition) {\n    if (axis !== 'x' && axis !== 'y') {\n      return this.options.radius >= 0.1 && this.elements.length > 1 && pointIsInPolygon(this.elements, mouseX, mouseY, useFinalPosition);\n    }\n    const rotatedPoint = rotated({x: mouseX, y: mouseY}, this.getCenterPoint(useFinalPosition), toRadians(-this.options.rotation));\n    const axisPoints = this.elements.map((point) => axis === 'y' ? point.bY : point.bX);\n    const start = Math.min(...axisPoints);\n    const end = Math.max(...axisPoints);\n    return rotatedPoint[axis] >= start && rotatedPoint[axis] <= end;\n  }\n\n  getCenterPoint(useFinalPosition) {\n    return getElementCenterPoint(this, useFinalPosition);\n  }\n\n  draw(ctx) {\n    const {elements, options} = this;\n    ctx.save();\n    ctx.beginPath();\n    ctx.fillStyle = options.backgroundColor;\n    setShadowStyle(ctx, options);\n    const stroke = setBorderStyle(ctx, options);\n    let first = true;\n    for (const el of elements) {\n      if (first) {\n        ctx.moveTo(el.x, el.y);\n        first = false;\n      } else {\n        ctx.lineTo(el.x, el.y);\n      }\n    }\n    ctx.closePath();\n    ctx.fill();\n    // If no border, don't draw it\n    if (stroke) {\n      ctx.shadowColor = options.borderShadowColor;\n      ctx.stroke();\n    }\n    ctx.restore();\n  }\n\n  resolveElementProperties(chart, options) {\n    const properties = resolvePointProperties(chart, options);\n    const {sides, rotation} = options;\n    const elements = [];\n    const angle = (2 * PI) / sides;\n    let rad = rotation * RAD_PER_DEG;\n    for (let i = 0; i < sides; i++, rad += angle) {\n      const elProps = buildPointElement(properties, options, rad);\n      elProps.initProperties = initAnimationProperties(chart, properties, options);\n      elements.push(elProps);\n    }\n    properties.elements = elements;\n    return properties;\n  }\n}\n\nPolygonAnnotation.id = 'polygonAnnotation';\n\nPolygonAnnotation.defaults = {\n  adjustScaleRange: true,\n  backgroundShadowColor: 'transparent',\n  borderCapStyle: 'butt',\n  borderDash: [],\n  borderDashOffset: 0,\n  borderJoinStyle: 'miter',\n  borderShadowColor: 'transparent',\n  borderWidth: 1,\n  display: true,\n  hitTolerance: 0,\n  init: undefined,\n  point: {\n    radius: 0\n  },\n  radius: 10,\n  rotation: 0,\n  shadowBlur: 0,\n  shadowOffsetX: 0,\n  shadowOffsetY: 0,\n  sides: 3,\n  xAdjust: 0,\n  xMax: undefined,\n  xMin: undefined,\n  xScaleID: undefined,\n  xValue: undefined,\n  yAdjust: 0,\n  yMax: undefined,\n  yMin: undefined,\n  yScaleID: undefined,\n  yValue: undefined,\n  z: 0\n};\n\nPolygonAnnotation.defaultRoutes = {\n  borderColor: 'color',\n  backgroundColor: 'color'\n};\n\nfunction buildPointElement({centerX, centerY}, {radius, borderWidth, hitTolerance}, rad) {\n  const hitSize = (borderWidth + hitTolerance) / 2;\n  const sin = Math.sin(rad);\n  const cos = Math.cos(rad);\n  const point = {x: centerX + sin * radius, y: centerY - cos * radius};\n  return {\n    type: 'point',\n    optionScope: 'point',\n    properties: {\n      x: point.x,\n      y: point.y,\n      centerX: point.x,\n      centerY: point.y,\n      bX: centerX + sin * (radius + hitSize),\n      bY: centerY - cos * (radius + hitSize)\n    }\n  };\n}\n\nfunction pointIsInPolygon(points, x, y, useFinalPosition) {\n  let isInside = false;\n  let A = points[points.length - 1].getProps(['bX', 'bY'], useFinalPosition);\n  for (const point of points) {\n    const B = point.getProps(['bX', 'bY'], useFinalPosition);\n    if ((B.bY > y) !== (A.bY > y) && x < (A.bX - B.bX) * (y - B.bY) / (A.bY - B.bY) + B.bX) {\n      isInside = !isInside;\n    }\n    A = B;\n  }\n  return isInside;\n}\n\nconst annotationTypes = {\n  box: BoxAnnotation,\n  doughnutLabel: DoughnutLabelAnnotation,\n  ellipse: EllipseAnnotation,\n  label: LabelAnnotation,\n  line: LineAnnotation,\n  point: PointAnnotation,\n  polygon: PolygonAnnotation\n};\n\n/**\n * Register fallback for annotation elements\n * For example lineAnnotation options would be looked through:\n * - the annotation object (options.plugins.annotation.annotations[id])\n * - element options (options.elements.lineAnnotation)\n * - element defaults (defaults.elements.lineAnnotation)\n * - annotation plugin defaults (defaults.plugins.annotation, this is what we are registering here)\n */\nObject.keys(annotationTypes).forEach(key => {\n  defaults.describe(`elements.${annotationTypes[key].id}`, {\n    _fallback: 'plugins.annotation.common'\n  });\n});\n\nconst directUpdater = {\n  update: Object.assign\n};\n\nconst hooks$1 = eventHooks.concat(elementHooks);\nconst resolve = (value, optDefs) => isObject(optDefs) ? resolveObj(value, optDefs) : value;\n\n\n/**\n * @typedef { import(\"chart.js\").Chart } Chart\n * @typedef { import(\"chart.js\").UpdateMode } UpdateMode\n * @typedef { import('../../types/options').AnnotationPluginOptions } AnnotationPluginOptions\n */\n\n/**\n * @param {string} prop\n * @returns {boolean}\n */\nconst isIndexable = (prop) => prop === 'color' || prop === 'font';\n\n/**\n * Resolve the annotation type, checking if is supported.\n * @param {string} [type=line] - annotation type\n * @returns {string} resolved annotation type\n */\nfunction resolveType(type = 'line') {\n  if (annotationTypes[type]) {\n    return type;\n  }\n  console.warn(`Unknown annotation type: '${type}', defaulting to 'line'`);\n  return 'line';\n}\n\n/**\n * @param {Chart} chart\n * @param {Object} state\n * @param {AnnotationPluginOptions} options\n * @param {UpdateMode} mode\n */\nfunction updateElements(chart, state, options, mode) {\n  const animations = resolveAnimations(chart, options.animations, mode);\n\n  const annotations = state.annotations;\n  const elements = resyncElements(state.elements, annotations);\n\n  for (let i = 0; i < annotations.length; i++) {\n    const annotationOptions = annotations[i];\n    const element = getOrCreateElement(elements, i, annotationOptions.type);\n    const resolver = annotationOptions.setContext(getContext(chart, element, elements, annotationOptions));\n    const properties = element.resolveElementProperties(chart, resolver);\n\n    properties.skip = toSkip(properties);\n\n    if ('elements' in properties) {\n      updateSubElements(element, properties.elements, resolver, animations);\n      // Remove the sub-element definitions from properties, so the actual elements\n      // are not overwritten by their definitions\n      delete properties.elements;\n    }\n\n    if (!defined(element.x)) {\n      // If the element is newly created, assing the properties directly - to\n      // make them readily awailable to any scriptable options. If we do not do this,\n      // the properties retruned by `resolveElementProperties` are available only\n      // after options resolution.\n      Object.assign(element, properties);\n    }\n\n    Object.assign(element, properties.initProperties);\n    properties.options = resolveAnnotationOptions(resolver);\n\n    animations.update(element, properties);\n  }\n}\n\nfunction toSkip(properties) {\n  return isNaN(properties.x) || isNaN(properties.y);\n}\n\nfunction resolveAnimations(chart, animOpts, mode) {\n  if (mode === 'reset' || mode === 'none' || mode === 'resize') {\n    return directUpdater;\n  }\n  return new Animations(chart, animOpts);\n}\n\nfunction updateSubElements(mainElement, elements, resolver, animations) {\n  const subElements = mainElement.elements || (mainElement.elements = []);\n  subElements.length = elements.length;\n  for (let i = 0; i < elements.length; i++) {\n    const definition = elements[i];\n    const properties = definition.properties;\n    const subElement = getOrCreateElement(subElements, i, definition.type, definition.initProperties);\n    const subResolver = resolver[definition.optionScope].override(definition);\n    properties.options = resolveAnnotationOptions(subResolver);\n    animations.update(subElement, properties);\n  }\n}\n\nfunction getOrCreateElement(elements, index, type, initProperties) {\n  const elementClass = annotationTypes[resolveType(type)];\n  let element = elements[index];\n  if (!element || !(element instanceof elementClass)) {\n    element = elements[index] = new elementClass();\n    Object.assign(element, initProperties);\n  }\n  return element;\n}\n\nfunction resolveAnnotationOptions(resolver) {\n  const elementClass = annotationTypes[resolveType(resolver.type)];\n  const result = {};\n  result.id = resolver.id;\n  result.type = resolver.type;\n  result.drawTime = resolver.drawTime;\n  Object.assign(result,\n    resolveObj(resolver, elementClass.defaults),\n    resolveObj(resolver, elementClass.defaultRoutes));\n  for (const hook of hooks$1) {\n    result[hook] = resolver[hook];\n  }\n  return result;\n}\n\nfunction resolveObj(resolver, defs) {\n  const result = {};\n  for (const prop of Object.keys(defs)) {\n    const optDefs = defs[prop];\n    const value = resolver[prop];\n    if (isIndexable(prop) && isArray(value)) {\n      result[prop] = value.map((item) => resolve(item, optDefs));\n    } else {\n      result[prop] = resolve(value, optDefs);\n    }\n  }\n  return result;\n}\n\nfunction getContext(chart, element, elements, annotation) {\n  return element.$context || (element.$context = Object.assign(Object.create(chart.getContext()), {\n    element,\n    get elements() {\n      return elements.filter((el) => el && el.options);\n    },\n    id: annotation.id,\n    type: 'annotation'\n  }));\n}\n\nfunction resyncElements(elements, annotations) {\n  const count = annotations.length;\n  const start = elements.length;\n\n  if (start < count) {\n    const add = count - start;\n    elements.splice(start, 0, ...new Array(add));\n  } else if (start > count) {\n    elements.splice(count, start - count);\n  }\n  return elements;\n}\n\nvar version = \"3.1.0\";\n\nconst chartStates = new Map();\nconst isNotDoughnutLabel = annotation => annotation.type !== 'doughnutLabel';\nconst hooks = eventHooks.concat(elementHooks);\n\nvar annotation = {\n  id: 'annotation',\n\n  version,\n\n  beforeRegister() {\n    requireVersion('chart.js', '4.0', Chart.version);\n  },\n\n  afterRegister() {\n    Chart.register(annotationTypes);\n  },\n\n  afterUnregister() {\n    Chart.unregister(annotationTypes);\n  },\n\n  beforeInit(chart) {\n    chartStates.set(chart, {\n      annotations: [],\n      elements: [],\n      visibleElements: [],\n      listeners: {},\n      listened: false,\n      moveListened: false,\n      hooks: {},\n      hooked: false,\n      hovered: []\n    });\n  },\n\n  beforeUpdate(chart, args, options) {\n    const state = chartStates.get(chart);\n    const annotations = state.annotations = [];\n\n    let annotationOptions = options.annotations;\n    if (isObject(annotationOptions)) {\n      Object.keys(annotationOptions).forEach(key => {\n        const value = annotationOptions[key];\n        if (isObject(value)) {\n          value.id = key;\n          annotations.push(value);\n        }\n      });\n    } else if (isArray(annotationOptions)) {\n      annotations.push(...annotationOptions);\n    }\n    verifyScaleOptions(annotations.filter(isNotDoughnutLabel), chart.scales);\n  },\n\n  afterDataLimits(chart, args) {\n    const state = chartStates.get(chart);\n    adjustScaleRange(chart, args.scale, state.annotations.filter(isNotDoughnutLabel).filter(a => a.display && a.adjustScaleRange));\n  },\n\n  afterUpdate(chart, args, options) {\n    const state = chartStates.get(chart);\n    updateListeners(chart, state, options);\n    updateElements(chart, state, options, args.mode);\n    state.visibleElements = state.elements.filter(el => !el.skip && el.options.display);\n    updateHooks(chart, state, options);\n  },\n\n  beforeDatasetsDraw(chart, _args, options) {\n    draw(chart, 'beforeDatasetsDraw', options.clip);\n  },\n\n  afterDatasetsDraw(chart, _args, options) {\n    draw(chart, 'afterDatasetsDraw', options.clip);\n  },\n\n  beforeDatasetDraw(chart, _args, options) {\n    draw(chart, _args.index, options.clip);\n  },\n\n  beforeDraw(chart, _args, options) {\n    draw(chart, 'beforeDraw', options.clip);\n  },\n\n  afterDraw(chart, _args, options) {\n    draw(chart, 'afterDraw', options.clip);\n  },\n\n  beforeEvent(chart, args, options) {\n    const state = chartStates.get(chart);\n    if (handleEvent(state, args.event, options)) {\n      args.changed = true;\n    }\n  },\n\n  afterDestroy(chart) {\n    chartStates.delete(chart);\n  },\n\n  getAnnotations(chart) {\n    const state = chartStates.get(chart);\n    return state ? state.elements : [];\n  },\n\n  // only for testing\n  _getAnnotationElementsAtEventForMode(visibleElements, event, options) {\n    return getElements(visibleElements, event, options);\n  },\n\n  defaults: {\n    animations: {\n      numbers: {\n        properties: ['x', 'y', 'x2', 'y2', 'width', 'height', 'centerX', 'centerY', 'pointX', 'pointY', 'radius'],\n        type: 'number'\n      },\n      colors: {\n        properties: ['backgroundColor', 'borderColor'],\n        type: 'color'\n      }\n    },\n    clip: true,\n    interaction: {\n      mode: undefined,\n      axis: undefined,\n      intersect: undefined\n    },\n    common: {\n      drawTime: 'afterDatasetsDraw',\n      init: false,\n      label: {\n      }\n    }\n  },\n\n  descriptors: {\n    _indexable: false,\n    _scriptable: (prop) => !hooks.includes(prop) && prop !== 'init',\n    annotations: {\n      _allKeys: false,\n      _fallback: (prop, opts) => `elements.${annotationTypes[resolveType(opts.type)].id}`\n    },\n    interaction: {\n      _fallback: true\n    },\n    common: {\n      label: {\n        _indexable: isIndexable,\n        _fallback: true\n      },\n      _indexable: isIndexable\n    }\n  },\n\n  additionalOptionScopes: ['']\n};\n\nfunction draw(chart, caller, clip) {\n  const {ctx, chartArea} = chart;\n  const state = chartStates.get(chart);\n\n  if (clip) {\n    clipArea(ctx, chartArea);\n  }\n\n  const drawableElements = getDrawableElements(state.visibleElements, caller).sort((a, b) => a.element.options.z - b.element.options.z);\n  for (const item of drawableElements) {\n    drawElement(ctx, chartArea, state, item);\n  }\n\n  if (clip) {\n    unclipArea(ctx);\n  }\n}\n\nfunction getDrawableElements(elements, caller) {\n  const drawableElements = [];\n  for (const el of elements) {\n    if (el.options.drawTime === caller) {\n      drawableElements.push({element: el, main: true});\n    }\n    if (el.elements && el.elements.length) {\n      for (const sub of el.elements) {\n        if (sub.options.display && sub.options.drawTime === caller) {\n          drawableElements.push({element: sub});\n        }\n      }\n    }\n  }\n  return drawableElements;\n}\n\nfunction drawElement(ctx, chartArea, state, item) {\n  const el = item.element;\n  if (item.main) {\n    invokeHook(state, el, 'beforeDraw');\n    el.draw(ctx, chartArea);\n    invokeHook(state, el, 'afterDraw');\n  } else {\n    el.draw(ctx, chartArea);\n  }\n}\n\nexport { annotation as default };\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;AACD;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAEA;;;CAGC,GAED,MAAM,cAAc;IAClB,OAAO;QACL;;;;;KAKC,GACD,OAAM,eAAe,EAAE,KAAK;YAC1B,OAAO,eAAe,iBAAiB,OAAO;gBAAC,WAAW;YAAI;QAChE;QAEA;;;;;;KAMC,GACD,SAAQ,eAAe,EAAE,KAAK,EAAE,OAAO;YACrC,OAAO,eAAe,iBAAiB,OAAO;QAChD;QACA;;;;;;KAMC,GACD,GAAE,eAAe,EAAE,KAAK,EAAE,OAAO;YAC/B,OAAO,eAAe,iBAAiB,OAAO;gBAAC,WAAW,QAAQ,SAAS;gBAAE,MAAM;YAAG;QACxF;QAEA;;;;;;KAMC,GACD,GAAE,eAAe,EAAE,KAAK,EAAE,OAAO;YAC/B,OAAO,eAAe,iBAAiB,OAAO;gBAAC,WAAW,QAAQ,SAAS;gBAAE,MAAM;YAAG;QACxF;IACF;AACF;AAEA;;;;;;CAMC,GACD,SAAS,YAAY,eAAe,EAAE,KAAK,EAAE,OAAO;IAClD,MAAM,OAAO,YAAY,KAAK,CAAC,QAAQ,IAAI,CAAC,IAAI,YAAY,KAAK,CAAC,OAAO;IACzE,OAAO,KAAK,iBAAiB,OAAO;AACtC;AAEA,SAAS,cAAc,OAAO,EAAE,KAAK,EAAE,IAAI;IACzC,IAAI,SAAS,OAAO,SAAS,KAAK;QAChC,OAAO,QAAQ,OAAO,CAAC,MAAM,CAAC,EAAE,MAAM,CAAC,EAAE,KAAK,SAAS,QAAQ,OAAO,CAAC,MAAM,CAAC,EAAE,MAAM,CAAC,EAAE,KAAK;IAChG;IACA,OAAO,QAAQ,OAAO,CAAC,MAAM,CAAC,EAAE,MAAM,CAAC,EAAE,MAAM;AACjD;AAEA,SAAS,eAAe,KAAK,EAAE,MAAM,EAAE,IAAI;IACzC,IAAI,SAAS,KAAK;QAChB,OAAO;YAAC,GAAG,MAAM,CAAC;YAAE,GAAG,OAAO,CAAC;QAAA;IACjC,OAAO,IAAI,SAAS,KAAK;QACvB,OAAO;YAAC,GAAG,OAAO,CAAC;YAAE,GAAG,MAAM,CAAC;QAAA;IACjC;IACA,OAAO;AACT;AAEA,SAAS,eAAe,eAAe,EAAE,KAAK,EAAE,OAAO;IACrD,OAAO,gBAAgB,MAAM,CAAC,CAAC,UAAY,QAAQ,SAAS,GAAG,QAAQ,OAAO,CAAC,MAAM,CAAC,EAAE,MAAM,CAAC,IAAI,cAAc,SAAS,OAAO,QAAQ,IAAI;AAC/I;AAEA,SAAS,eAAe,eAAe,EAAE,KAAK,EAAE,OAAO;IACrD,IAAI,cAAc,OAAO,iBAAiB;IAE1C,OAAO,eAAe,iBAAiB,OAAO,SAC3C,MAAM,CAAC,CAAC,cAAc;QACrB,MAAM,SAAS,QAAQ,cAAc;QACrC,MAAM,YAAY,eAAe,OAAO,QAAQ,QAAQ,IAAI;QAC5D,MAAM,WAAW,CAAA,GAAA,qNAAA,CAAA,wBAAqB,AAAD,EAAE,OAAO;QAC9C,IAAI,WAAW,aAAa;YAC1B,eAAe;gBAAC;aAAQ;YACxB,cAAc;QAChB,OAAO,IAAI,aAAa,aAAa;YACnC,6EAA6E;YAC7E,aAAa,IAAI,CAAC;QACpB;QAEA,OAAO;IACT,GAAG,EAAE,EACJ,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,MAAM,GAAG,EAAE,MAAM,EAClC,KAAK,CAAC,GAAG,IAAI,4BAA4B;AAC9C;AAEA;;CAEC,GAED;;;;;;CAMC,GACD,SAAS,QAAQ,KAAK,EAAE,MAAM,EAAE,KAAK;IACnC,MAAM,MAAM,KAAK,GAAG,CAAC;IACrB,MAAM,MAAM,KAAK,GAAG,CAAC;IACrB,MAAM,KAAK,OAAO,CAAC;IACnB,MAAM,KAAK,OAAO,CAAC;IAEnB,OAAO;QACL,GAAG,KAAK,MAAM,CAAC,MAAM,CAAC,GAAG,EAAE,IAAI,MAAM,CAAC,MAAM,CAAC,GAAG,EAAE;QAClD,GAAG,KAAK,MAAM,CAAC,MAAM,CAAC,GAAG,EAAE,IAAI,MAAM,CAAC,MAAM,CAAC,GAAG,EAAE;IACpD;AACF;AAEA,MAAM,cAAc,CAAC,KAAK,MAAQ,MAAM,OAAQ,IAAI,MAAM,GAAG,IAAI,MAAM,IAAI,IAAI,KAAK,CAAC,GAAG,IAAI,MAAM,MAAM;AAExG;;;;CAIC,GAED,MAAM,UAAU;AAChB,MAAM,QAAQ,CAAC,GAAG,MAAM,KAAO,KAAK,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,MAAM;AAE3D;;;;CAIC,GACD,MAAM,UAAU,CAAC,OAAO,UAAY,MAAM,KAAK,IAAI,MAAM,KAAK,GAAG,WAAW,MAAM,KAAK,IAAI,MAAM,GAAG,GAAG;AAEvG;;;;;CAKC,GACD,SAAS,SAAS,GAAG,EAAE,IAAI,EAAE,EAAE;IAC7B,KAAK,MAAM,OAAO,OAAO,IAAI,CAAC,KAAM;QAClC,GAAG,CAAC,IAAI,GAAG,MAAM,GAAG,CAAC,IAAI,EAAE,MAAM;IACnC;IACA,OAAO;AACT;AAEA;;;;;;CAMC,GACD,SAAS,aAAa,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO;IAClD,IAAI,CAAC,SAAS,CAAC,UAAU,UAAU,GAAG;QACpC,OAAO;IACT;IACA,OAAO,AAAC,KAAK,GAAG,CAAC,MAAM,CAAC,GAAG,OAAO,CAAC,EAAE,KAAK,KAAK,GAAG,CAAC,MAAM,CAAC,GAAG,OAAO,CAAC,EAAE,MAAO,KAAK,GAAG,CAAC,SAAS,SAAS;AAC3G;AAEA;;;;;;CAMC,GACD,SAAS,WAAW,KAAK,EAAE,EAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAC,EAAE,IAAI,EAAE,EAAC,WAAW,EAAE,YAAY,EAAC;IAC1E,MAAM,UAAU,CAAC,cAAc,YAAY,IAAI;IAC/C,MAAM,WAAW,MAAM,CAAC,IAAI,IAAI,UAAU,WAAW,MAAM,CAAC,IAAI,KAAK,UAAU;IAC/E,MAAM,WAAW,MAAM,CAAC,IAAI,IAAI,UAAU,WAAW,MAAM,CAAC,IAAI,KAAK,UAAU;IAC/E,IAAI,SAAS,KAAK;QAChB,OAAO;IACT,OAAO,IAAI,SAAS,KAAK;QACvB,OAAO;IACT;IACA,OAAO,YAAY;AACrB;AAEA;;;;;;CAMC,GACD,SAAS,aAAa,KAAK,EAAE,EAAC,IAAI,EAAE,MAAM,EAAC,EAAE,IAAI,EAAE,EAAC,QAAQ,EAAE,WAAW,EAAE,YAAY,EAAC;IACtF,MAAM,WAAW,QAAQ,OAAO,QAAQ,CAAA,GAAA,wMAAA,CAAA,YAAS,AAAD,EAAE,CAAC;IACnD,OAAO,WAAW,UAAU,MAAM,MAAM;QAAC;QAAa;IAAY;AACpE;AAEA;;;;CAIC,GACD,SAAS,sBAAsB,OAAO,EAAE,gBAAgB;IACtD,MAAM,EAAC,OAAO,EAAE,OAAO,EAAC,GAAG,QAAQ,QAAQ,CAAC;QAAC;QAAW;KAAU,EAAE;IACpE,OAAO;QAAC,GAAG;QAAS,GAAG;IAAO;AAChC;AAEA;;;;;;CAMC,GACD,SAAS,eAAe,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,SAAS,IAAI;IAClD,MAAM,QAAQ,IAAI,KAAK,CAAC;IACxB,IAAI,IAAI;IACR,KAAK,MAAM,OAAO,IAAI,KAAK,CAAC,KAAM;QAChC,MAAM,MAAM,KAAK,CAAC,IAAI;QACtB,IAAI,SAAS,KAAK,MAAM,SAAS,KAAK,KAAK;YACzC;QACF;QACA,IAAI,YAAY,KAAK,MAAM;YACzB,IAAI,QAAQ;gBACV,MAAM,IAAI,MAAM,GAAG,IAAI,EAAE,EAAE,IAAI,oBAAoB,EAAE,IAAI,sBAAsB,CAAC;YAClF,OAAO;gBACL,OAAO;YACT;QACF;IACF;IACA,OAAO;AACT;AAEA,MAAM,kBAAkB,CAAC,IAAM,OAAO,MAAM,YAAY,EAAE,QAAQ,CAAC;AACnE,MAAM,YAAY,CAAC,IAAM,WAAW,KAAK;AACzC,MAAM,oBAAoB,CAAC,IAAM,MAAM,UAAU,IAAI,GAAG;AAExD,MAAM,cAAc,CAAC,GAAG,IAAM,CAAC;QAAC;QAAG;QAAG,IAAI;QAAG,IAAI;QAAG,OAAO;QAAG,QAAQ;IAAC,CAAC;AACxE,MAAM,uBAAuB;IAC3B,KAAK,CAAC,aAAe,YAAY,WAAW,OAAO,EAAE,WAAW,OAAO;IACvE,eAAe,CAAC,aAAe,YAAY,WAAW,OAAO,EAAE,WAAW,OAAO;IACjF,SAAS,CAAC,aAAe,CAAC;YAAC,SAAS,WAAW,OAAO;YAAE,SAAS,WAAW,OAAO;YAAE,QAAQ;YAAG,OAAO;YAAG,QAAQ;QAAC,CAAC;IACpH,OAAO,CAAC,aAAe,YAAY,WAAW,OAAO,EAAE,WAAW,OAAO;IACzE,MAAM,CAAC,aAAe,YAAY,WAAW,CAAC,EAAE,WAAW,CAAC;IAC5D,OAAO,CAAC,aAAe,CAAC;YAAC,SAAS,WAAW,OAAO;YAAE,SAAS,WAAW,OAAO;YAAE,QAAQ;YAAG,OAAO;YAAG,QAAQ;QAAC,CAAC;IAClH,SAAS,CAAC,aAAe,YAAY,WAAW,OAAO,EAAE,WAAW,OAAO;AAC7E;AAEA;;;;;;;;;CASC,GAED;;;;CAIC,GACD,SAAS,oBAAoB,IAAI,EAAE,QAAQ;IACzC,IAAI,aAAa,SAAS;QACxB,OAAO;IACT;IACA,IAAI,aAAa,OAAO;QACtB,OAAO;IACT;IACA,IAAI,gBAAgB,WAAW;QAC7B,OAAO,kBAAkB,YAAY;IACvC;IACA,OAAO,OAAO;AAChB;AAEA;;;;;CAKC,GACD,SAAS,QAAQ,IAAI,EAAE,KAAK,EAAE,kBAAkB,IAAI;IAClD,IAAI,OAAO,UAAU,UAAU;QAC7B,OAAO;IACT,OAAO,IAAI,gBAAgB,QAAQ;QACjC,OAAO,CAAC,kBAAkB,kBAAkB,SAAS,UAAU,MAAM,IAAI;IAC3E;IACA,OAAO;AACT;AAEA;;;;CAIC,GACD,SAAS,uBAAuB,IAAI,EAAE,OAAO;IAC3C,MAAM,EAAC,CAAC,EAAE,KAAK,EAAC,GAAG;IACnB,MAAM,YAAY,QAAQ,SAAS;IACnC,IAAI,cAAc,UAAU;QAC1B,OAAO,IAAI,QAAQ;IACrB,OAAO,IAAI,cAAc,SAAS,cAAc,SAAS;QACvD,OAAO,IAAI;IACb;IACA,OAAO;AACT;AAEA;;;;;;CAMC,GACD,SAAS,sBAAsB,KAAK,EAAE,SAAS,EAAE,EAAC,WAAW,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAC,EAAE,OAAO;IACjG,MAAM,aAAa,CAAA,GAAA,uMAAA,CAAA,WAAQ,AAAD,EAAE;IAC5B,MAAM,QAAQ,UAAU,KAAK,GAAG,CAAC,aAAa,QAAQ,KAAK,GAAG,CAAC,IAAI;IACnE,MAAM,SAAS,UAAU,MAAM,GAAG,CAAC,aAAa,QAAQ,MAAM,GAAG,CAAC,IAAI;IACtE,MAAM,cAAc,WAAW;IAC/B,MAAM,IAAI,yBAAyB,MAAM,CAAC,EAAE,OAAO,SAAS,YAAY,CAAC;IACzE,MAAM,IAAI,yBAAyB,MAAM,CAAC,EAAE,QAAQ,SAAS,YAAY,CAAC;IAE1E,OAAO;QACL;QACA;QACA,IAAI,IAAI;QACR,IAAI,IAAI;QACR;QACA;QACA,SAAS,IAAI,QAAQ;QACrB,SAAS,IAAI,SAAS;IACxB;AACF;AAEA;;;;CAIC,GACD,SAAS,WAAW,KAAK,EAAE,eAAe,QAAQ;IAChD,IAAI,CAAA,GAAA,uMAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ;QACnB,OAAO;YACL,GAAG,CAAA,GAAA,6MAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,CAAC,EAAE;YAC3B,GAAG,CAAA,GAAA,6MAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,CAAC,EAAE;QAC7B;IACF;IACA,QAAQ,CAAA,GAAA,6MAAA,CAAA,iBAAc,AAAD,EAAE,OAAO;IAC9B,OAAO;QACL,GAAG;QACH,GAAG;IACL;AACF;AAEA;;;;CAIC,GACD,MAAM,YAAY,CAAC,SAAS,WAAa,WAAW,QAAQ,OAAO,IAAI,WAAW;AAElF;;;;CAIC,GACD,SAAS,QAAQ,OAAO,EAAE,QAAQ;IAChC,MAAM,UAAU,QAAQ,IAAI;IAC5B,MAAM,QAAQ,CAAA,GAAA,sMAAA,CAAA,UAAO,AAAD,EAAE,WAAW,UAAU;QAAC;KAAQ;IACpD,IAAI,UAAU,SAAS,WAAW;QAChC,OAAO,MAAM,GAAG,CAAC,SAAS,CAAC;YACzB,MAAM,OAAO,CAAA,GAAA,sMAAA,CAAA,SAAM,AAAD,EAAE;YACpB,KAAK,IAAI,GAAG,KAAK,KAAK,CAAC,EAAE,IAAI,GAAG;YAChC,KAAK,UAAU,GAAG,EAAE,UAAU;YAC9B,OAAO,CAAA,GAAA,sMAAA,CAAA,SAAM,AAAD,EAAE;QAChB;IACF;IACA,OAAO,MAAM,GAAG,CAAC,CAAA,IAAK,CAAA,GAAA,sMAAA,CAAA,SAAM,AAAD,EAAE;AAC/B;AAEA;;;CAGC,GACD,SAAS,eAAe,OAAO;IAC7B,OAAO,WAAW,CAAC,CAAA,GAAA,sMAAA,CAAA,UAAO,AAAD,EAAE,QAAQ,MAAM,KAAK,CAAA,GAAA,sMAAA,CAAA,UAAO,AAAD,EAAE,QAAQ,MAAM,CAAC;AACvE;AAEA,SAAS,yBAAyB,KAAK,EAAE,IAAI,EAAE,SAAS,CAAC,EAAE,QAAQ;IACjE,OAAO,QAAQ,oBAAoB,MAAM,YAAY;AACvD;AAEA;;;;;CAKC,GACD,SAAS,wBAAwB,KAAK,EAAE,UAAU,EAAE,OAAO;IACzD,MAAM,WAAW,QAAQ,IAAI;IAC7B,IAAI,CAAC,UAAU;QACb;IACF,OAAO,IAAI,aAAa,MAAM;QAC5B,OAAO,aAAa,YAAY;IAClC;IACA,OAAO,aAAa,OAAO,YAAY;AACzC;AAEA;;;;;CAKC,GACD,SAAS,UAAU,OAAO,EAAE,KAAK,EAAE,cAAc;IAC/C,IAAI,YAAY;IAChB,MAAM,OAAO,CAAC,CAAA;QACZ,IAAI,CAAA,GAAA,0MAAA,CAAA,aAAU,AAAD,EAAE,OAAO,CAAC,KAAK,GAAG;YAC7B,YAAY;YACZ,cAAc,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK;QACtC,OAAO,IAAI,CAAA,GAAA,sMAAA,CAAA,UAAO,AAAD,EAAE,cAAc,CAAC,KAAK,GAAG;YACxC,OAAO,cAAc,CAAC,KAAK;QAC7B;IACF;IACA,OAAO;AACT;AAEA,SAAS,aAAa,UAAU,EAAE,OAAO;IACvC,MAAM,OAAO,QAAQ,IAAI,IAAI;IAC7B,OAAO,oBAAoB,CAAC,KAAK,CAAC;AACpC;AAEA,SAAS,aAAa,KAAK,EAAE,UAAU,EAAE,OAAO;IAC9C,MAAM,SAAS,CAAA,GAAA,uMAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ,IAAI,EAAE;QAAC;YAAC;YAAO;YAAY;QAAO;KAAE;IACpE,IAAI,WAAW,MAAM;QACnB,OAAO,aAAa,YAAY;IAClC,OAAO,IAAI,CAAA,GAAA,uMAAA,CAAA,WAAQ,AAAD,EAAE,SAAS;QAC3B,OAAO;IACT;AACF;AAEA,MAAM,aAAa,IAAI;AACvB,MAAM,YAAY,CAAC,SAAW,MAAM,WAAW,UAAU;AACzD,MAAM,WAAW,CAAC,QAAU,MAAM,MAAM,CAAC,SAAS,IAAI,EAAE,IAAI;QAC1D,QAAQ,KAAK,MAAM;QACnB,OAAO;IACT,GAAG;AAEH;;;;CAIC,GAED;;;;;CAKC,GACD,SAAS,gBAAgB,OAAO;IAC9B,IAAI,WAAW,OAAO,YAAY,UAAU;QAC1C,MAAM,OAAO,QAAQ,QAAQ;QAC7B,OAAQ,SAAS,+BAA+B,SAAS;IAC3D;AACF;AAEA;;;;;CAKC,GACD,SAAS,UAAU,GAAG,EAAE,EAAC,CAAC,EAAE,CAAC,EAAC,EAAE,QAAQ;IACtC,IAAI,UAAU;QACZ,IAAI,SAAS,CAAC,GAAG;QACjB,IAAI,MAAM,CAAC,CAAA,GAAA,wMAAA,CAAA,YAAS,AAAD,EAAE;QACrB,IAAI,SAAS,CAAC,CAAC,GAAG,CAAC;IACrB;AACF;AAEA;;;;CAIC,GACD,SAAS,eAAe,GAAG,EAAE,OAAO;IAClC,IAAI,WAAW,QAAQ,WAAW,EAAE;QAClC,IAAI,OAAO,GAAG,QAAQ,cAAc,IAAI;QACxC,IAAI,WAAW,CAAC,QAAQ,UAAU;QAClC,IAAI,cAAc,GAAG,QAAQ,gBAAgB;QAC7C,IAAI,QAAQ,GAAG,QAAQ,eAAe,IAAI;QAC1C,IAAI,SAAS,GAAG,QAAQ,WAAW;QACnC,IAAI,WAAW,GAAG,QAAQ,WAAW;QACrC,OAAO;IACT;AACF;AAEA;;;CAGC,GACD,SAAS,eAAe,GAAG,EAAE,OAAO;IAClC,IAAI,WAAW,GAAG,QAAQ,qBAAqB;IAC/C,IAAI,UAAU,GAAG,QAAQ,UAAU;IACnC,IAAI,aAAa,GAAG,QAAQ,aAAa;IACzC,IAAI,aAAa,GAAG,QAAQ,aAAa;AAC3C;AAEA;;;;CAIC,GACD,SAAS,iBAAiB,GAAG,EAAE,OAAO;IACpC,MAAM,UAAU,QAAQ,OAAO;IAC/B,IAAI,gBAAgB,UAAU;QAC5B,MAAM,OAAO;YACX,OAAO,QAAQ,QAAQ,KAAK,EAAE,QAAQ,KAAK;YAC3C,QAAQ,QAAQ,QAAQ,MAAM,EAAE,QAAQ,MAAM;QAChD;QACA,OAAO;IACT;IACA,MAAM,QAAQ,QAAQ;IACtB,MAAM,cAAc,QAAQ,eAAe;IAC3C,MAAM,QAAQ,CAAA,GAAA,sMAAA,CAAA,UAAO,AAAD,EAAE,WAAW,UAAU;QAAC;KAAQ;IACpD,MAAM,SAAS,MAAM,IAAI,KAAK,SAAS,SAAS,cAAc,CAAC,IAAI,YAAY,GAAG,cAAc,EAAE;IAClG,IAAI,CAAC,WAAW,GAAG,CAAC,SAAS;QAC3B,WAAW,GAAG,CAAC,QAAQ,mBAAmB,KAAK,OAAO,OAAO;IAC/D;IACA,OAAO,WAAW,GAAG,CAAC;AACxB;AAEA;;;;CAIC,GACD,SAAS,QAAQ,GAAG,EAAE,IAAI,EAAE,OAAO;IACjC,MAAM,EAAC,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,EAAC,GAAG;IAC9B,IAAI,IAAI;IACR,eAAe,KAAK;IACpB,MAAM,SAAS,eAAe,KAAK;IACnC,IAAI,SAAS,GAAG,QAAQ,eAAe;IACvC,IAAI,SAAS;IACb,CAAA,GAAA,kNAAA,CAAA,qBAAkB,AAAD,EAAE,KAAK;QACtB;QAAG;QAAG,GAAG;QAAO,GAAG;QACnB,QAAQ,SAAS,CAAA,GAAA,6MAAA,CAAA,gBAAa,AAAD,EAAE,QAAQ,YAAY,GAAG,GAAG,KAAK,GAAG,CAAC,OAAO,UAAU;IACrF;IACA,IAAI,SAAS;IACb,IAAI,IAAI;IACR,IAAI,QAAQ;QACV,IAAI,WAAW,GAAG,QAAQ,iBAAiB;QAC3C,IAAI,MAAM;IACZ;IACA,IAAI,OAAO;AACb;AAEA;;;;;CAKC,GACD,SAAS,UAAU,GAAG,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ;IAC7C,MAAM,UAAU,QAAQ,OAAO;IAC/B,IAAI,gBAAgB,UAAU;QAC5B,IAAI,IAAI;QACR,IAAI,WAAW,GAAG,WAAW,QAAQ,OAAO,EAAE,QAAQ,KAAK,CAAC,OAAO;QACnE,IAAI,SAAS,CAAC,SAAS,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,KAAK,KAAK,EAAE,KAAK,MAAM;QAC9D,IAAI,OAAO;QACX;IACF;IACA,MAAM,SAAS,CAAA,GAAA,sMAAA,CAAA,UAAO,AAAD,EAAE,WAAW,UAAU;QAAC;KAAQ;IACrD,MAAM,QAAQ,QAAQ,SAAS;IAC/B,MAAM,WAAW,QAAQ,KAAK;IAC9B,MAAM,SAAS,CAAA,GAAA,sMAAA,CAAA,UAAO,AAAD,EAAE,YAAY,WAAW;QAAC;KAAS;IACxD,MAAM,IAAI,uBAAuB,MAAM;IACvC,MAAM,IAAI,KAAK,CAAC,GAAG,QAAQ,eAAe,GAAG;IAC7C,IAAI,IAAI;IACR,IAAI,YAAY,GAAG;IACnB,IAAI,SAAS,GAAG,QAAQ,SAAS;IACjC,IAAI,mBAAmB,KAAK,UAAU;QACpC,qBAAqB,KAAK;YAAC;YAAG;QAAC,GAAG,QAAQ;IAC5C;IACA,kBAAkB,KAAK;QAAC;QAAG;IAAC,GAAG,QAAQ;QAAC;QAAO;IAAM;IACrD,IAAI,OAAO;AACb;AAEA,SAAS,mBAAmB,GAAG,EAAE,OAAO;IACtC,IAAI,QAAQ,eAAe,GAAG,GAAG;QAC/B,oGAAoG;QACpG,IAAI,QAAQ,GAAG;QACf,IAAI,UAAU,GAAG;QACjB,IAAI,SAAS,GAAG,QAAQ,eAAe;QACvC,IAAI,WAAW,GAAG,QAAQ,eAAe;QACzC,OAAO;IACT;AACF;AAEA;;;;;CAKC,GACD,SAAS,UAAU,GAAG,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;IACnC,MAAM,EAAC,MAAM,EAAE,OAAO,EAAC,GAAG;IAC1B,MAAM,QAAQ,QAAQ,UAAU;IAChC,MAAM,WAAW,QAAQ,QAAQ;IACjC,IAAI,MAAM,CAAC,YAAY,CAAC,IAAI,2MAAA,CAAA,cAAW;IAEvC,IAAI,gBAAgB,QAAQ;QAC1B,IAAI,IAAI;QACR,IAAI,SAAS,CAAC,GAAG;QACjB,IAAI,MAAM,CAAC;QACX,IAAI,SAAS,CAAC,OAAO,CAAC,MAAM,KAAK,GAAG,GAAG,CAAC,MAAM,MAAM,GAAG,GAAG,MAAM,KAAK,EAAE,MAAM,MAAM;QACnF,IAAI,OAAO;QACX;IACF;IACA,IAAI,UAAU,SAAS;QACrB;IACF;IACA,eAAe,KAAK;QAAC;QAAG;QAAG;QAAQ;QAAU;QAAO;IAAG;AACzD;AAEA,SAAS,eAAe,GAAG,EAAE,EAAC,CAAC,EAAE,CAAC,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG,EAAC;IAC/D,IAAI,SAAS,SAAS,MAAM;IAC5B,IAAI,SAAS;IAEb,OAAQ;QACR,0BAA0B;QAC1B;YACE,IAAI,GAAG,CAAC,GAAG,GAAG,QAAQ,GAAG,kMAAA,CAAA,MAAG;YAC5B,IAAI,SAAS;YACb;QACF,KAAK;YACH,IAAI,MAAM,CAAC,IAAI,KAAK,GAAG,CAAC,OAAO,QAAQ,IAAI,KAAK,GAAG,CAAC,OAAO;YAC3D,OAAO,6MAAA,CAAA,gBAAa;YACpB,IAAI,MAAM,CAAC,IAAI,KAAK,GAAG,CAAC,OAAO,QAAQ,IAAI,KAAK,GAAG,CAAC,OAAO;YAC3D,OAAO,6MAAA,CAAA,gBAAa;YACpB,IAAI,MAAM,CAAC,IAAI,KAAK,GAAG,CAAC,OAAO,QAAQ,IAAI,KAAK,GAAG,CAAC,OAAO;YAC3D,IAAI,SAAS;YACb;QACF,KAAK;YACH,wEAAwE;YACxE,oEAAoE;YACpE,yEAAyE;YACzE,oEAAoE;YACpE,iEAAiE;YACjE,kDAAkD;YAClD,kDAAkD;YAClD,eAAe,SAAS;YACxB,OAAO,SAAS;YAChB,UAAU,KAAK,GAAG,CAAC,MAAM,0MAAA,CAAA,aAAU,IAAI;YACvC,UAAU,KAAK,GAAG,CAAC,MAAM,0MAAA,CAAA,aAAU,IAAI;YACvC,IAAI,GAAG,CAAC,IAAI,SAAS,IAAI,SAAS,cAAc,MAAM,iMAAA,CAAA,KAAE,EAAE,MAAM,sMAAA,CAAA,UAAO;YACvE,IAAI,GAAG,CAAC,IAAI,SAAS,IAAI,SAAS,cAAc,MAAM,sMAAA,CAAA,UAAO,EAAE;YAC/D,IAAI,GAAG,CAAC,IAAI,SAAS,IAAI,SAAS,cAAc,KAAK,MAAM,sMAAA,CAAA,UAAO;YAClE,IAAI,GAAG,CAAC,IAAI,SAAS,IAAI,SAAS,cAAc,MAAM,sMAAA,CAAA,UAAO,EAAE,MAAM,iMAAA,CAAA,KAAE;YACvE,IAAI,SAAS;YACb;QACF,KAAK;YACH,IAAI,CAAC,UAAU;gBACb,OAAO,KAAK,OAAO,GAAG;gBACtB,IAAI,IAAI,CAAC,IAAI,MAAM,IAAI,MAAM,IAAI,MAAM,IAAI;gBAC3C;YACF;YACA,OAAO,0MAAA,CAAA,aAAU;QACjB,iBAAiB,GACnB,KAAK;YACH,UAAU,KAAK,GAAG,CAAC,OAAO;YAC1B,UAAU,KAAK,GAAG,CAAC,OAAO;YAC1B,IAAI,MAAM,CAAC,IAAI,SAAS,IAAI;YAC5B,IAAI,MAAM,CAAC,IAAI,SAAS,IAAI;YAC5B,IAAI,MAAM,CAAC,IAAI,SAAS,IAAI;YAC5B,IAAI,MAAM,CAAC,IAAI,SAAS,IAAI;YAC5B,IAAI,SAAS;YACb;QACF,KAAK;YACH,OAAO,0MAAA,CAAA,aAAU;QACjB,iBAAiB,GACnB,KAAK;YACH,UAAU,KAAK,GAAG,CAAC,OAAO;YAC1B,UAAU,KAAK,GAAG,CAAC,OAAO;YAC1B,IAAI,MAAM,CAAC,IAAI,SAAS,IAAI;YAC5B,IAAI,MAAM,CAAC,IAAI,SAAS,IAAI;YAC5B,IAAI,MAAM,CAAC,IAAI,SAAS,IAAI;YAC5B,IAAI,MAAM,CAAC,IAAI,SAAS,IAAI;YAC5B;QACF,KAAK;YACH,UAAU,KAAK,GAAG,CAAC,OAAO;YAC1B,UAAU,KAAK,GAAG,CAAC,OAAO;YAC1B,IAAI,MAAM,CAAC,IAAI,SAAS,IAAI;YAC5B,IAAI,MAAM,CAAC,IAAI,SAAS,IAAI;YAC5B,IAAI,MAAM,CAAC,IAAI,SAAS,IAAI;YAC5B,IAAI,MAAM,CAAC,IAAI,SAAS,IAAI;YAC5B,OAAO,0MAAA,CAAA,aAAU;YACjB,UAAU,KAAK,GAAG,CAAC,OAAO;YAC1B,UAAU,KAAK,GAAG,CAAC,OAAO;YAC1B,IAAI,MAAM,CAAC,IAAI,SAAS,IAAI;YAC5B,IAAI,MAAM,CAAC,IAAI,SAAS,IAAI;YAC5B,IAAI,MAAM,CAAC,IAAI,SAAS,IAAI;YAC5B,IAAI,MAAM,CAAC,IAAI,SAAS,IAAI;YAC5B;QACF,KAAK;YACH,UAAU,KAAK,GAAG,CAAC,OAAO;YAC1B,UAAU,KAAK,GAAG,CAAC,OAAO;YAC1B,IAAI,MAAM,CAAC,IAAI,SAAS,IAAI;YAC5B,IAAI,MAAM,CAAC,IAAI,SAAS,IAAI;YAC5B;QACF,KAAK;YACH,IAAI,MAAM,CAAC,GAAG;YACd,IAAI,MAAM,CAAC,IAAI,KAAK,GAAG,CAAC,OAAO,QAAQ,IAAI,KAAK,GAAG,CAAC,OAAO;YAC3D;IACF;IAEA,IAAI,IAAI;AACV;AAEA,SAAS,mBAAmB,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,WAAW;IACxD,IAAI,IAAI;IACR,MAAM,QAAQ,MAAM,MAAM;IAC1B,IAAI,QAAQ;IACZ,IAAI,SAAS;IACb,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,IAAK;QAC9B,MAAM,OAAO,KAAK,CAAC,KAAK,GAAG,CAAC,GAAG,MAAM,MAAM,GAAG,GAAG;QACjD,IAAI,IAAI,GAAG,KAAK,MAAM;QACtB,MAAM,OAAO,KAAK,CAAC,EAAE;QACrB,QAAQ,KAAK,GAAG,CAAC,OAAO,IAAI,WAAW,CAAC,MAAM,KAAK,GAAG;QACtD,UAAU,KAAK,UAAU;IAC3B;IACA,IAAI,OAAO;IACX,OAAO;QAAC;QAAO;IAAM;AACvB;AAEA,SAAS,qBAAqB,GAAG,EAAE,EAAC,CAAC,EAAE,CAAC,EAAC,EAAE,MAAM,EAAE,KAAK;IACtD,IAAI,SAAS;IACb,IAAI,MAAM;IACV,OAAO,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC;QAC1B,MAAM,IAAI,KAAK,CAAC,KAAK,GAAG,CAAC,GAAG,MAAM,MAAM,GAAG,GAAG;QAC9C,MAAM,KAAK,EAAE,UAAU;QACvB,IAAI,IAAI,GAAG,EAAE,MAAM;QACnB,IAAI,UAAU,CAAC,GAAG,GAAG,IAAI,KAAK,IAAI;QAClC,OAAO;IACT;IACA,IAAI,MAAM;AACZ;AAEA,SAAS,kBAAkB,GAAG,EAAE,EAAC,CAAC,EAAE,CAAC,EAAC,EAAE,MAAM,EAAE,EAAC,KAAK,EAAE,MAAM,EAAC;IAC7D,IAAI,MAAM;IACV,OAAO,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC;QAC1B,MAAM,IAAI,MAAM,CAAC,KAAK,GAAG,CAAC,GAAG,OAAO,MAAM,GAAG,GAAG;QAChD,MAAM,IAAI,KAAK,CAAC,KAAK,GAAG,CAAC,GAAG,MAAM,MAAM,GAAG,GAAG;QAC9C,MAAM,KAAK,EAAE,UAAU;QACvB,IAAI,SAAS;QACb,IAAI,IAAI,GAAG,EAAE,MAAM;QACnB,IAAI,SAAS,GAAG;QAChB,IAAI,QAAQ,CAAC,GAAG,GAAG,IAAI,KAAK,IAAI;QAChC,OAAO;QACP,IAAI,IAAI;IACV;AACF;AAEA,SAAS,WAAW,KAAK,EAAE,YAAY;IACrC,MAAM,UAAU,CAAA,GAAA,uMAAA,CAAA,WAAQ,AAAD,EAAE,SAAS,QAAQ;IAC1C,OAAO,CAAA,GAAA,uMAAA,CAAA,WAAQ,AAAD,EAAE,WAAW,MAAM,SAAS,GAAG,KAAK;AACpD;AAEA,MAAM,YAAY;IAAC;IAAQ;IAAU;IAAO;CAAQ;AAEpD;;CAEC,GAED;;;;CAIC,GACD,SAAS,YAAY,GAAG,EAAE,OAAO;IAC/B,MAAM,EAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAC,GAAG;IAClC,MAAM,UAAU,QAAQ,OAAO;IAC/B,MAAM,kBAAkB,WAAW,QAAQ,OAAO,IAAI,uBAAuB,SAAS;IACtF,IAAI,CAAC,mBAAmB,eAAe,SAAS,SAAS,kBAAkB;QACzE;IACF;IAEA,IAAI,IAAI;IACR,IAAI,SAAS;IACb,MAAM,SAAS,eAAe,KAAK;IACnC,IAAI,CAAC,QAAQ;QACX,OAAO,IAAI,OAAO;IACpB;IACA,MAAM,EAAC,cAAc,EAAE,YAAY,EAAC,GAAG,yBAAyB,SAAS;IACzE,MAAM,EAAC,SAAS,EAAE,OAAO,EAAC,GAAG,oBAAoB,SAAS,iBAAiB;IAC3E,IAAI,QAAQ,MAAM,GAAG,KAAK,QAAQ,WAAW,KAAK,GAAG;QACnD,IAAI,MAAM,CAAC,eAAe,CAAC,EAAE,eAAe,CAAC;QAC7C,IAAI,MAAM,CAAC,aAAa,CAAC,EAAE,aAAa,CAAC;IAC3C;IACA,IAAI,MAAM,CAAC,UAAU,CAAC,EAAE,UAAU,CAAC;IACnC,IAAI,MAAM,CAAC,QAAQ,CAAC,EAAE,QAAQ,CAAC;IAC/B,MAAM,eAAe,QAAQ;QAAC,GAAG;QAAQ,GAAG;IAAM,GAAG,QAAQ,cAAc,IAAI,CAAA,GAAA,wMAAA,CAAA,YAAS,AAAD,EAAE,CAAC,QAAQ,QAAQ;IAC1G,IAAI,MAAM,CAAC,aAAa,CAAC,EAAE,aAAa,CAAC;IACzC,IAAI,MAAM;IACV,IAAI,OAAO;AACb;AAEA,SAAS,yBAAyB,OAAO,EAAE,QAAQ;IACjD,MAAM,EAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAC,GAAG;IACvB,MAAM,SAAS,0BAA0B,SAAS;IAClD,IAAI,gBAAgB;IACpB,IAAI,aAAa,UAAU,aAAa,SAAS;QAC/C,iBAAiB;YAAC,GAAG,IAAI;YAAQ;QAAC;QAClC,eAAe;YAAC,GAAG,eAAe,CAAC;YAAE,GAAG;QAAE;IAC5C,OAAO;QACL,8BAA8B;QAC9B,iBAAiB;YAAC;YAAG,GAAG,IAAI;QAAM;QAClC,eAAe;YAAC,GAAG;YAAI,GAAG,eAAe,CAAC;QAAA;IAC5C;IACA,OAAO;QAAC;QAAgB;IAAY;AACtC;AAEA,SAAS,0BAA0B,OAAO,EAAE,QAAQ;IAClD,MAAM,EAAC,KAAK,EAAE,MAAM,EAAE,OAAO,EAAC,GAAG;IACjC,MAAM,SAAS,QAAQ,OAAO,CAAC,MAAM,GAAG,QAAQ,WAAW,GAAG;IAC9D,IAAI,aAAa,SAAS;QACxB,OAAO,QAAQ;IACjB,OAAO,IAAI,aAAa,UAAU;QAChC,OAAO,SAAS;IAClB;IACA,OAAO,CAAC;AACV;AAEA,SAAS,oBAAoB,OAAO,EAAE,QAAQ,EAAE,cAAc;IAC5D,MAAM,EAAC,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAC,GAAG;IACpC,MAAM,QAAQ,QAAQ,OAAO,CAAC,KAAK;IACnC,MAAM,OAAO,qBAAqB,UAAU,QAAQ,OAAO;IAC3D,IAAI,WAAW;IACf,IAAI,aAAa,UAAU,aAAa,SAAS;QAC/C,YAAY;YAAC,GAAG,eAAe,CAAC;YAAE,GAAG,IAAI,QAAQ,QAAQ;QAAM;QAC/D,UAAU;YAAC,GAAG,UAAU,CAAC,GAAG;YAAM,GAAG,UAAU,CAAC;QAAA;IAClD,OAAO;QACL,8BAA8B;QAC9B,YAAY;YAAC,GAAG,eAAe,CAAC,GAAG,QAAQ,OAAO;YAAQ,GAAG,eAAe,CAAC;QAAA;QAC7E,UAAU;YAAC,GAAG,UAAU,CAAC;YAAE,GAAG,UAAU,CAAC,GAAG;QAAI;IAClD;IACA,OAAO;QAAC;QAAW;IAAO;AAC5B;AAEA,SAAS,qBAAqB,QAAQ,EAAE,OAAO;IAC7C,MAAM,OAAO,QAAQ,IAAI;IACzB,IAAI,aAAa,UAAU,aAAa,OAAO;QAC7C,OAAO,CAAC;IACV;IACA,OAAO;AACT;AAEA,SAAS,uBAAuB,OAAO,EAAE,OAAO;IAC9C,MAAM,WAAW,QAAQ,QAAQ;IACjC,IAAI,UAAU,QAAQ,CAAC,WAAW;QAChC,OAAO;IACT;IACA,OAAO,2BAA2B,SAAS;AAC7C;AAEA,SAAS,2BAA2B,OAAO,EAAE,OAAO;IAClD,MAAM,EAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAC,GAAG;IAClF,MAAM,SAAS;QAAC,GAAG;QAAS,GAAG;IAAO;IACtC,MAAM,QAAQ,QAAQ,KAAK;IAC3B,MAAM,UAAU,QAAQ,OAAO;IAC/B,MAAM,UAAU,QAAQ,QAAQ;IAChC,MAAM,UAAU;QAAC;QAAG,IAAI;QAAS,IAAI;QAAS;KAAG;IACjD,MAAM,UAAU;QAAC,IAAI;QAAS;QAAI;QAAG;KAAG;IACxC,MAAM,SAAS,EAAE;IACjB,IAAK,IAAI,QAAQ,GAAG,QAAQ,GAAG,QAAS;QACtC,MAAM,eAAe,QAAQ;YAAC,GAAG,OAAO,CAAC,MAAM;YAAE,GAAG,OAAO,CAAC,MAAM;QAAA,GAAG,QAAQ,CAAA,GAAA,wMAAA,CAAA,YAAS,AAAD,EAAE;QACvF,OAAO,IAAI,CAAC;YACV,UAAU,SAAS,CAAC,MAAM;YAC1B,UAAU,CAAA,GAAA,qNAAA,CAAA,wBAAqB,AAAD,EAAE,cAAc;gBAAC,GAAG;gBAAQ,GAAG;YAAM;QACrE;IACF;IACA,OAAO,OAAO,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,QAAQ,GAAG,EAAE,QAAQ,CAAC,CAAC,EAAE,CAAC,QAAQ;AACnE;AAEA,SAAS,eAAe,OAAO,EAAE,OAAO,EAAE,QAAQ;IAChD,MAAM,EAAC,MAAM,EAAE,MAAM,EAAC,GAAG;IACzB,MAAM,SAAS,QAAQ,MAAM;IAC7B,IAAI,IAAI;IACR,IAAI,IAAI;IACR,IAAI,aAAa,QAAQ;QACvB,KAAK;IACP,OAAO,IAAI,aAAa,SAAS;QAC/B,KAAK;IACP,OAAO,IAAI,aAAa,OAAO;QAC7B,KAAK;IACP,OAAO,IAAI,aAAa,UAAU;QAChC,KAAK;IACP;IACA,OAAO,QAAQ,OAAO,CAAC,GAAG;AAC5B;AAEA,MAAM,mBAAmB;IACvB,UAAU;QAAC,KAAK;QAAQ,KAAK;QAAQ,OAAO;QAAQ,KAAK;QAAS,WAAW;QAAK,SAAS;IAAI;IAC/F,UAAU;QAAC,KAAK;QAAQ,KAAK;QAAQ,OAAO;QAAU,KAAK;QAAO,WAAW;QAAK,SAAS;IAAI;AACjG;AAEA;;;;;;;;;CASC,GAED;;;;;CAKC,GACD,SAAS,WAAW,KAAK,EAAE,KAAK,EAAE,QAAQ;IACxC,QAAQ,OAAO,UAAU,WAAW,QAAQ,MAAM,KAAK,CAAC;IACxD,OAAO,CAAA,GAAA,uMAAA,CAAA,WAAQ,AAAD,EAAE,SAAS,MAAM,gBAAgB,CAAC,SAAS;AAC3D;AAEA;;;;;;CAMC,GACD,SAAS,gBAAgB,MAAM,EAAE,OAAO,EAAE,GAAG;IAC3C,MAAM,UAAU,OAAO,CAAC,IAAI;IAC5B,IAAI,WAAW,QAAQ,WAAW;QAChC,OAAO;IACT;IACA,MAAM,OAAO,IAAI,MAAM,CAAC;IACxB,MAAM,OAAO,OAAO,MAAM,CAAC,QAAQ,MAAM,CAAC,CAAC,QAAU,MAAM,IAAI,IAAI,MAAM,IAAI,KAAK;IAClF,IAAI,KAAK,MAAM,EAAE;QACf,OAAO,IAAI,CAAC,EAAE,CAAC,EAAE;IACnB;IACA,OAAO;AACT;AAEA;;;;CAIC,GACD,SAAS,oBAAoB,KAAK,EAAE,OAAO;IACzC,IAAI,OAAO;QACT,MAAM,UAAU,MAAM,OAAO,CAAC,OAAO;QACrC,MAAM,QAAQ,WAAW,OAAO,QAAQ,GAAG,EAAE,UAAU,QAAQ,GAAG,GAAG,QAAQ,KAAK;QAClF,MAAM,MAAM,WAAW,OAAO,QAAQ,GAAG,EAAE,UAAU,QAAQ,KAAK,GAAG,QAAQ,GAAG;QAChF,OAAO;YACL;YACA;QACF;IACF;AACF;AAEA;;;;CAIC,GACD,SAAS,cAAc,KAAK,EAAE,OAAO;IACnC,MAAM,EAAC,SAAS,EAAE,MAAM,EAAC,GAAG;IAC5B,MAAM,SAAS,MAAM,CAAC,gBAAgB,QAAQ,SAAS,YAAY;IACnE,MAAM,SAAS,MAAM,CAAC,gBAAgB,QAAQ,SAAS,YAAY;IACnE,IAAI,IAAI,UAAU,KAAK,GAAG;IAC1B,IAAI,IAAI,UAAU,MAAM,GAAG;IAE3B,IAAI,QAAQ;QACV,IAAI,WAAW,QAAQ,QAAQ,MAAM,EAAE,OAAO,IAAI,GAAG,OAAO,KAAK,GAAG;IACtE;IAEA,IAAI,QAAQ;QACV,IAAI,WAAW,QAAQ,QAAQ,MAAM,EAAE,OAAO,GAAG,GAAG,OAAO,MAAM,GAAG;IACtE;IACA,OAAO;QAAC;QAAG;IAAC;AACd;AAEA;;;;CAIC,GACD,SAAS,qBAAqB,KAAK,EAAE,OAAO;IAC1C,MAAM,SAAS,MAAM,MAAM;IAC3B,MAAM,SAAS,MAAM,CAAC,gBAAgB,QAAQ,SAAS,YAAY;IACnE,MAAM,SAAS,MAAM,CAAC,gBAAgB,QAAQ,SAAS,YAAY;IAEnE,IAAI,CAAC,UAAU,CAAC,QAAQ;QACtB,OAAO,CAAC;IACV;IAEA,IAAI,EAAC,MAAM,CAAC,EAAE,OAAO,EAAE,EAAC,GAAG,UAAU,MAAM,SAAS;IACpD,IAAI,EAAC,KAAK,CAAC,EAAE,QAAQ,EAAE,EAAC,GAAG,UAAU,MAAM,SAAS;IACpD,MAAM,OAAO,yBAAyB,QAAQ;QAAC,KAAK,QAAQ,IAAI;QAAE,KAAK,QAAQ,IAAI;QAAE,OAAO;QAAG,KAAK;IAAE;IACtG,IAAI,KAAK,KAAK;IACd,KAAK,KAAK,GAAG;IACb,MAAM,OAAO,yBAAyB,QAAQ;QAAC,KAAK,QAAQ,IAAI;QAAE,KAAK,QAAQ,IAAI;QAAE,OAAO;QAAI,KAAK;IAAC;IACtG,IAAI,KAAK,KAAK;IACd,KAAK,KAAK,GAAG;IAEb,OAAO;QACL;QACA;QACA;QACA;QACA,OAAO,KAAK;QACZ,QAAQ,KAAK;QACb,SAAS,IAAI,CAAC,KAAK,CAAC,IAAI;QACxB,SAAS,IAAI,CAAC,KAAK,CAAC,IAAI;IAC1B;AACF;AAEA;;;;CAIC,GACD,SAAS,uBAAuB,KAAK,EAAE,OAAO;IAC5C,IAAI,CAAC,eAAe,UAAU;QAC5B,MAAM,MAAM,qBAAqB,OAAO;QACxC,IAAI,SAAS,QAAQ,MAAM;QAC3B,IAAI,CAAC,UAAU,MAAM,SAAS;YAC5B,SAAS,KAAK,GAAG,CAAC,IAAI,KAAK,EAAE,IAAI,MAAM,IAAI;YAC3C,QAAQ,MAAM,GAAG;QACnB;QACA,MAAM,OAAO,SAAS;QACtB,MAAM,gBAAgB,IAAI,OAAO,GAAG,QAAQ,OAAO;QACnD,MAAM,gBAAgB,IAAI,OAAO,GAAG,QAAQ,OAAO;QACnD,OAAO;YACL,GAAG,gBAAgB;YACnB,GAAG,gBAAgB;YACnB,IAAI,gBAAgB;YACpB,IAAI,gBAAgB;YACpB,SAAS;YACT,SAAS;YACT,OAAO;YACP,QAAQ;YACR;QACF;IACF;IACA,OAAO,eAAe,OAAO;AAC/B;AACA;;;;CAIC,GACD,SAAS,sBAAsB,KAAK,EAAE,OAAO;IAC3C,MAAM,EAAC,MAAM,EAAE,SAAS,EAAC,GAAG;IAC5B,MAAM,QAAQ,MAAM,CAAC,QAAQ,OAAO,CAAC;IACrC,MAAM,OAAO;QAAC,GAAG,UAAU,IAAI;QAAE,GAAG,UAAU,GAAG;QAAE,IAAI,UAAU,KAAK;QAAE,IAAI,UAAU,MAAM;IAAA;IAE5F,IAAI,OAAO;QACT,0BAA0B,OAAO,MAAM;IACzC,OAAO;QACL,6BAA6B,QAAQ,MAAM;IAC7C;IACA,OAAO;AACT;AAEA;;;;;CAKC,GACD,SAAS,6BAA6B,KAAK,EAAE,OAAO;IAClD,MAAM,aAAa,qBAAqB,OAAO;IAC/C,WAAW,cAAc,GAAG,wBAAwB,OAAO,YAAY;IACvE,WAAW,QAAQ,GAAG;QAAC;YACrB,MAAM;YACN,aAAa;YACb,YAAY,gCAAgC,OAAO,YAAY;YAC/D,gBAAgB,WAAW,cAAc;QAC3C;KAAE;IACF,OAAO;AACT;AAEA,SAAS,eAAe,KAAK,EAAE,OAAO;IACpC,MAAM,QAAQ,cAAc,OAAO;IACnC,MAAM,OAAO,QAAQ,MAAM,GAAG;IAC9B,OAAO;QACL,GAAG,MAAM,CAAC,GAAG,QAAQ,MAAM,GAAG,QAAQ,OAAO;QAC7C,GAAG,MAAM,CAAC,GAAG,QAAQ,MAAM,GAAG,QAAQ,OAAO;QAC7C,IAAI,MAAM,CAAC,GAAG,QAAQ,MAAM,GAAG,QAAQ,OAAO;QAC9C,IAAI,MAAM,CAAC,GAAG,QAAQ,MAAM,GAAG,QAAQ,OAAO;QAC9C,SAAS,MAAM,CAAC,GAAG,QAAQ,OAAO;QAClC,SAAS,MAAM,CAAC,GAAG,QAAQ,OAAO;QAClC,QAAQ,QAAQ,MAAM;QACtB,OAAO;QACP,QAAQ;IACV;AACF;AAEA,SAAS,yBAAyB,KAAK,EAAE,OAAO;IAC9C,MAAM,SAAS,oBAAoB,OAAO,YAAY;IACtD,OAAO;QACL,OAAO,KAAK,GAAG,CAAC,OAAO,KAAK,EAAE,OAAO,GAAG;QACxC,KAAK,KAAK,GAAG,CAAC,OAAO,KAAK,EAAE,OAAO,GAAG;IACxC;AACF;AAEA,SAAS,0BAA0B,KAAK,EAAE,IAAI,EAAE,OAAO;IACrD,MAAM,MAAM,WAAW,OAAO,QAAQ,KAAK,EAAE;IAC7C,MAAM,MAAM,WAAW,OAAO,QAAQ,QAAQ,EAAE;IAChD,IAAI,MAAM,YAAY,IAAI;QACxB,KAAK,CAAC,GAAG;QACT,KAAK,EAAE,GAAG;IACZ,OAAO;QACL,KAAK,CAAC,GAAG;QACT,KAAK,EAAE,GAAG;IACZ;AACF;AAEA,SAAS,6BAA6B,MAAM,EAAE,IAAI,EAAE,OAAO;IACzD,KAAK,MAAM,WAAW,OAAO,IAAI,CAAC,kBAAmB;QACnD,MAAM,QAAQ,MAAM,CAAC,gBAAgB,QAAQ,SAAS,SAAS;QAC/D,IAAI,OAAO;YACT,MAAM,EAAC,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,SAAS,EAAE,OAAO,EAAC,GAAG,gBAAgB,CAAC,QAAQ;YAC5E,MAAM,MAAM,oBAAoB,OAAO;gBAAC,KAAK,OAAO,CAAC,IAAI;gBAAE,KAAK,OAAO,CAAC,IAAI;gBAAE,OAAO,KAAK,CAAC,MAAM;gBAAE,KAAK,KAAK,CAAC,IAAI;YAAA;YAClH,IAAI,CAAC,UAAU,GAAG,IAAI,KAAK;YAC3B,IAAI,CAAC,QAAQ,GAAG,IAAI,GAAG;QACzB;IACF;AACF;AAEA,SAAS,WAAW,EAAC,UAAU,EAAE,OAAO,EAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO;IACrE,MAAM,EAAC,GAAG,KAAK,EAAE,IAAI,GAAG,EAAE,OAAO,IAAI,EAAC,GAAG;IACzC,OAAO,kBAAkB;QAAC;QAAO;QAAK;QAAM,aAAa,QAAQ,WAAW;IAAA,GAAG;QAC7E,UAAU,SAAS,CAAC;QACpB,SAAS;YAAC,OAAO,QAAQ,IAAI;YAAE,KAAK,QAAQ,KAAK;QAAA;QACjD,QAAQ,QAAQ,KAAK,CAAC,OAAO;QAC7B,MAAM,UAAU,KAAK;IACvB;AACF;AAEA,SAAS,WAAW,EAAC,UAAU,EAAE,OAAO,EAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO;IACrE,MAAM,EAAC,GAAG,KAAK,EAAE,IAAI,GAAG,EAAE,QAAQ,IAAI,EAAC,GAAG;IAC1C,OAAO,kBAAkB;QAAC;QAAO;QAAK;QAAM,aAAa,QAAQ,WAAW;IAAA,GAAG;QAC7E,UAAU,SAAS,CAAC;QACpB,SAAS;YAAC,OAAO,QAAQ,GAAG;YAAE,KAAK,QAAQ,MAAM;QAAA;QACjD,QAAQ,QAAQ,KAAK,CAAC,OAAO;QAC7B,MAAM,UAAU,MAAM;IACxB;AACF;AAEA,SAAS,kBAAkB,OAAO,EAAE,SAAS;IAC3C,MAAM,EAAC,KAAK,EAAE,GAAG,EAAE,WAAW,EAAC,GAAG;IAClC,MAAM,EAAC,QAAQ,EAAE,SAAS,EAAC,OAAO,QAAQ,EAAE,KAAK,MAAM,EAAC,EAAE,MAAM,EAAC,GAAG;IACpE,MAAM,gBAAgB,MAAM,cAAc,QAAQ,WAAW,SAAS,UAAU,IAAI;IACpF,OAAO,QAAQ,cAAc,IAAI,SAAS,oBAAoB,eAAe;AAC/E;AAEA,SAAS,gCAAgC,KAAK,EAAE,UAAU,EAAE,OAAO;IACjE,MAAM,QAAQ,QAAQ,KAAK;IAC3B,MAAM,eAAe,GAAG;IACxB,MAAM,OAAO,CAAC,OAAO,GAAG;IACxB,MAAM,WAAW,WAAW,MAAM,QAAQ;IAC1C,MAAM,UAAU,CAAA,GAAA,wMAAA,CAAA,YAAS,AAAD,EAAE,MAAM,OAAO;IACvC,MAAM,YAAY,iBAAiB,MAAM,GAAG,EAAE;IAC9C,MAAM,IAAI,WAAW;QAAC;QAAY;IAAO,GAAG,WAAW,UAAU;IACjE,MAAM,IAAI,WAAW;QAAC;QAAY;IAAO,GAAG,WAAW,UAAU;IACjE,MAAM,QAAQ,UAAU,KAAK,GAAG,QAAQ,KAAK;IAC7C,MAAM,SAAS,UAAU,MAAM,GAAG,QAAQ,MAAM;IAChD,OAAO;QACL;QACA;QACA,IAAI,IAAI;QACR,IAAI,IAAI;QACR;QACA;QACA,SAAS,IAAI,QAAQ;QACrB,SAAS,IAAI,SAAS;QACtB,UAAU,MAAM,QAAQ;IAC1B;AAEF;AAEA,MAAM,YAAY;IAAC;IAAS;CAAQ;AAEpC;;;CAGC,GAED,MAAM,aAAa,UAAU,MAAM,CAAC;AAEpC;;;;CAIC,GACD,SAAS,gBAAgB,KAAK,EAAE,KAAK,EAAE,OAAO;IAC5C,MAAM,QAAQ,GAAG,UAAU,SAAS,YAAY,MAAM,SAAS;IAC/D,MAAM,YAAY,GAAG;IAErB,UAAU,OAAO,CAAC,CAAA;QAChB,IAAI,CAAA,GAAA,0MAAA,CAAA,aAAU,AAAD,EAAE,OAAO,CAAC,KAAK,GAAG;YAC7B,MAAM,YAAY,GAAG;QACvB;IACF;IAEA,IAAI,CAAC,MAAM,QAAQ,IAAI,CAAC,MAAM,YAAY,EAAE;QAC1C,MAAM,WAAW,CAAC,OAAO,CAAC,CAAA;YACxB,IAAI,CAAC,MAAM,QAAQ,IAAI,CAAA,GAAA,0MAAA,CAAA,aAAU,AAAD,EAAE,MAAM,KAAK,GAAG;gBAC9C,MAAM,QAAQ,GAAG;YACnB;YACA,IAAI,CAAC,MAAM,YAAY,EAAE;gBACvB,UAAU,OAAO,CAAC,CAAA;oBAChB,IAAI,CAAA,GAAA,0MAAA,CAAA,aAAU,AAAD,EAAE,KAAK,CAAC,KAAK,GAAG;wBAC3B,MAAM,QAAQ,GAAG;wBACjB,MAAM,YAAY,GAAG;oBACvB;gBACF;YACF;QACF;IACF;AACF;AAEA;;;;;CAKC,GACD,SAAS,YAAY,KAAK,EAAE,KAAK,EAAE,OAAO;IACxC,IAAI,MAAM,QAAQ,EAAE;QAClB,OAAQ,MAAM,IAAI;YAClB,KAAK;YACL,KAAK;gBACH,OAAO,iBAAiB,OAAO,OAAO;YACxC,KAAK;gBACH,OAAO,kBAAkB,OAAO,OAAO;QACzC;IACF;AACF;AAEA,SAAS,iBAAiB,KAAK,EAAE,KAAK,EAAE,OAAO;IAC7C,IAAI,CAAC,MAAM,YAAY,EAAE;QACvB;IACF;IAEA,IAAI;IAEJ,IAAI,MAAM,IAAI,KAAK,aAAa;QAC9B,WAAW,YAAY,MAAM,eAAe,EAAE,OAAO,QAAQ,WAAW;IAC1E,OAAO;QACL,WAAW,EAAE;IACf;IAEA,MAAM,WAAW,MAAM,OAAO;IAC9B,MAAM,OAAO,GAAG;IAEhB,MAAM,UAAU;QAAC;QAAO;IAAK;IAC7B,IAAI,UAAU,mBAAmB,SAAS,SAAS,UAAU;IAC7D,OAAO,mBAAmB,SAAS,SAAS,UAAU,aAAa;AACrE;AAEA,SAAS,mBAAmB,EAAC,KAAK,EAAE,KAAK,EAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,aAAa;IACvE,IAAI;IACJ,KAAK,MAAM,WAAW,SAAU;QAC9B,IAAI,cAAc,OAAO,CAAC,WAAW,GAAG;YACtC,UAAU,cAAc,QAAQ,OAAO,CAAC,KAAK,IAAI,MAAM,SAAS,CAAC,KAAK,EAAE,SAAS,UAAU;QAC7F;IACF;IACA,OAAO;AACT;AAEA,SAAS,kBAAkB,KAAK,EAAE,KAAK,EAAE,OAAO;IAC9C,MAAM,YAAY,MAAM,SAAS;IACjC,MAAM,WAAW,YAAY,MAAM,eAAe,EAAE,OAAO,QAAQ,WAAW;IAC9E,IAAI;IACJ,KAAK,MAAM,WAAW,SAAU;QAC9B,UAAU,cAAc,QAAQ,OAAO,CAAC,KAAK,IAAI,UAAU,KAAK,EAAE,SAAS,UAAU;IACvF;IACA,OAAO;AACT;AAEA,SAAS,cAAc,OAAO,EAAE,OAAO,EAAE,KAAK;IAC5C,OAAO,CAAA,GAAA,uMAAA,CAAA,WAAQ,AAAD,EAAE,SAAS;QAAC,QAAQ,QAAQ;QAAE;KAAM,MAAM;AAC1D;AAEA;;;;CAIC,GAED,MAAM,eAAe;IAAC;IAAa;CAAa;AAEhD;;;;CAIC,GACD,SAAS,YAAY,KAAK,EAAE,KAAK,EAAE,OAAO;IACxC,MAAM,kBAAkB,MAAM,eAAe;IAC7C,MAAM,MAAM,GAAG,UAAU,SAAS,cAAc,MAAM,KAAK;IAE3D,IAAI,CAAC,MAAM,MAAM,EAAE;QACjB,gBAAgB,OAAO,CAAC,CAAA;YACtB,IAAI,CAAC,MAAM,MAAM,EAAE;gBACjB,aAAa,OAAO,CAAC,CAAA;oBACnB,IAAI,CAAA,GAAA,0MAAA,CAAA,aAAU,AAAD,EAAE,MAAM,OAAO,CAAC,KAAK,GAAG;wBACnC,MAAM,MAAM,GAAG;oBACjB;gBACF;YACF;QACF;IACF;AACF;AAEA;;;;CAIC,GACD,SAAS,WAAW,KAAK,EAAE,OAAO,EAAE,IAAI;IACtC,IAAI,MAAM,MAAM,EAAE;QAChB,MAAM,eAAe,QAAQ,OAAO,CAAC,KAAK,IAAI,MAAM,KAAK,CAAC,KAAK;QAC/D,OAAO,CAAA,GAAA,uMAAA,CAAA,WAAQ,AAAD,EAAE,cAAc;YAAC,QAAQ,QAAQ;SAAC;IAClD;AACF;AAEA;;;;CAIC,GAED;;;;CAIC,GACD,SAAS,iBAAiB,KAAK,EAAE,KAAK,EAAE,WAAW;IACjD,MAAM,QAAQ,eAAe,MAAM,MAAM,EAAE,OAAO;IAClD,IAAI,UAAU,iBAAiB,OAAO,OAAO,OAAO;IACpD,UAAU,iBAAiB,OAAO,OAAO,OAAO,mBAAmB;IACnE,IAAI,WAAW,CAAA,GAAA,0MAAA,CAAA,aAAU,AAAD,EAAE,MAAM,sBAAsB,GAAG;QACvD,MAAM,sBAAsB;IAC9B;AACF;AAEA;;;CAGC,GACD,SAAS,mBAAmB,WAAW,EAAE,MAAM;IAC7C,KAAK,MAAM,cAAc,YAAa;QACpC,eAAe,YAAY;IAC7B;AACF;AAEA,SAAS,iBAAiB,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,cAAc;IAC3D,IAAI,CAAA,GAAA,uMAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,CAAC,MAAM,KAAK,CAAC,kBAAkB,MAAM,OAAO,EAAE,OAAO,iBAAiB;QACtF,MAAM,UAAU,KAAK,CAAC,MAAM,KAAK,KAAK,CAAC,MAAM;QAC7C,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM;QAC3B,OAAO;IACT;AACF;AAEA,SAAS,kBAAkB,YAAY,EAAE,KAAK,EAAE,cAAc;IAC5D,OAAO,CAAA,GAAA,sMAAA,CAAA,UAAO,AAAD,EAAE,YAAY,CAAC,MAAM,KAAK,CAAA,GAAA,sMAAA,CAAA,UAAO,AAAD,EAAE,YAAY,CAAC,eAAe;AAC7E;AAEA,SAAS,eAAe,UAAU,EAAE,MAAM;IACxC,KAAK,MAAM,OAAO;QAAC;QAAW;QAAY;KAAW,CAAE;QACrD,MAAM,UAAU,gBAAgB,QAAQ,YAAY;QACpD,IAAI,WAAW,CAAC,MAAM,CAAC,QAAQ,IAAI,iBAAiB,YAAY,MAAM;YACpE,QAAQ,IAAI,CAAC,CAAC,wBAAwB,EAAE,QAAQ,kBAAkB,EAAE,WAAW,EAAE,CAAC,CAAC,CAAC;QACtF;IACF;AACF;AAEA,SAAS,iBAAiB,UAAU,EAAE,GAAG;IACvC,IAAI,QAAQ,WAAW;QACrB,OAAO;IACT;IACA,MAAM,OAAO,IAAI,MAAM,CAAC;IACxB,KAAK,MAAM,QAAQ;QAAC;QAAO;QAAO;KAAQ,CAAE;QAC1C,IAAI,CAAA,GAAA,sMAAA,CAAA,UAAO,AAAD,EAAE,UAAU,CAAC,OAAO,KAAK,GAAG;YACpC,OAAO;QACT;IACF;IACA,OAAO;AACT;AAEA,SAAS,eAAe,MAAM,EAAE,KAAK,EAAE,WAAW;IAChD,MAAM,OAAO,MAAM,IAAI;IACvB,MAAM,UAAU,MAAM,EAAE;IACxB,MAAM,gBAAgB,OAAO;IAC7B,MAAM,SAAS;QACb,KAAK,CAAA,GAAA,6MAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,GAAG,EAAE,OAAO,iBAAiB;QACvD,KAAK,CAAA,GAAA,6MAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,GAAG,EAAE,OAAO,iBAAiB;IACzD;IACA,KAAK,MAAM,cAAc,YAAa;QACpC,IAAI,WAAW,OAAO,KAAK,SAAS;YAClC,aAAa,YAAY,OAAO;gBAAC;gBAAS;aAAW,EAAE;QACzD,OAAO,IAAI,gBAAgB,QAAQ,YAAY,mBAAmB,SAAS;YACzE,aAAa,YAAY,OAAO;gBAAC,OAAO;gBAAO,OAAO;gBAAO,OAAO;aAAQ,EAAE;QAChF;IACF;IACA,OAAO;AACT;AAEA,SAAS,aAAa,UAAU,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM;IACpD,KAAK,MAAM,QAAQ,MAAO;QACxB,MAAM,MAAM,UAAU,CAAC,KAAK;QAC5B,IAAI,CAAA,GAAA,sMAAA,CAAA,UAAO,AAAD,EAAE,MAAM;YAChB,MAAM,QAAQ,MAAM,KAAK,CAAC;YAC1B,OAAO,GAAG,GAAG,KAAK,GAAG,CAAC,OAAO,GAAG,EAAE;YAClC,OAAO,GAAG,GAAG,KAAK,GAAG,CAAC,OAAO,GAAG,EAAE;QACpC;IACF;AACF;AAEA,MAAM,sBAAsB,+JAAA,CAAA,UAAO;IAEjC,QAAQ,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,gBAAgB,EAAE;QAC9C,MAAM,EAAC,CAAC,EAAE,CAAC,EAAC,GAAG,QAAQ;YAAC,GAAG;YAAQ,GAAG;QAAM,GAAG,IAAI,CAAC,cAAc,CAAC,mBAAmB,CAAA,GAAA,wMAAA,CAAA,YAAS,AAAD,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ;QACtH,OAAO,WAAW;YAAC;YAAG;QAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;YAAC;YAAK;YAAK;YAAM;SAAK,EAAE,mBAAmB,MAAM,IAAI,CAAC,OAAO;IACvG;IAEA,eAAe,gBAAgB,EAAE;QAC/B,OAAO,sBAAsB,IAAI,EAAE;IACrC;IAEA,KAAK,GAAG,EAAE;QACR,IAAI,IAAI;QACR,UAAU,KAAK,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ;QAC3D,QAAQ,KAAK,IAAI,EAAE,IAAI,CAAC,OAAO;QAC/B,IAAI,OAAO;IACb;IAEA,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,EAAE;IAC1C;IAEA,yBAAyB,KAAK,EAAE,OAAO,EAAE;QACvC,OAAO,6BAA6B,OAAO;IAC7C;AACF;AAEA,cAAc,EAAE,GAAG;AAEnB,cAAc,QAAQ,GAAG;IACvB,kBAAkB;IAClB,uBAAuB;IACvB,gBAAgB;IAChB,YAAY,EAAE;IACd,kBAAkB;IAClB,iBAAiB;IACjB,cAAc;IACd,mBAAmB;IACnB,aAAa;IACb,SAAS;IACT,MAAM;IACN,cAAc;IACd,OAAO;QACL,iBAAiB;QACjB,aAAa;QACb,SAAS;YACP,SAAS;QACX;QACA,OAAO;QACP,SAAS;QACT,SAAS;QACT,UAAU;QACV,MAAM;YACJ,QAAQ;YACR,YAAY;YACZ,MAAM;YACN,OAAO;YACP,QAAQ;QACV;QACA,QAAQ;QACR,cAAc;QACd,SAAS;QACT,SAAS;QACT,UAAU;QACV,UAAU;QACV,WAAW;QACX,iBAAiB;QACjB,iBAAiB;QACjB,OAAO;QACP,SAAS;QACT,SAAS;QACT,GAAG;IACL;IACA,UAAU;IACV,YAAY;IACZ,eAAe;IACf,eAAe;IACf,MAAM;IACN,MAAM;IACN,UAAU;IACV,MAAM;IACN,MAAM;IACN,UAAU;IACV,GAAG;AACL;AAEA,cAAc,aAAa,GAAG;IAC5B,aAAa;IACb,iBAAiB;AACnB;AAEA,cAAc,WAAW,GAAG;IAC1B,OAAO;QACL,WAAW;IACb;AACF;AAEA,MAAM,gCAAgC,+JAAA,CAAA,UAAO;IAE3C,QAAQ,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,gBAAgB,EAAE;QAC9C,OAAO,aACL;YAAC,GAAG;YAAQ,GAAG;QAAM,GACrB;YAAC,MAAM,IAAI,CAAC,QAAQ,CAAC;gBAAC;gBAAK;gBAAK;gBAAM;aAAK,EAAE;YAAmB,QAAQ,IAAI,CAAC,cAAc,CAAC;QAAiB,GAC7G,MACA;YAAC,UAAU,IAAI,CAAC,QAAQ;YAAE,aAAa;YAAG,cAAc,IAAI,CAAC,OAAO,CAAC,YAAY;QAAA;IAErF;IAEA,eAAe,gBAAgB,EAAE;QAC/B,OAAO,sBAAsB,IAAI,EAAE;IACrC;IAEA,KAAK,GAAG,EAAE;QACR,MAAM,UAAU,IAAI,CAAC,OAAO;QAC5B,IAAI,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ,OAAO,EAAE;YACxC;QACF;QACA,eAAe,KAAK,IAAI;QACxB,IAAI,IAAI;QACR,UAAU,KAAK,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,QAAQ;QACnD,UAAU,KAAK,IAAI,EAAE,SAAS,IAAI,CAAC,SAAS;QAC5C,IAAI,OAAO;IACb;IAEA,yBAAyB,KAAK,EAAE,OAAO,EAAE;QACvC,MAAM,OAAO,eAAe,OAAO;QACnC,IAAI,CAAC,MAAM;YACT,OAAO,CAAC;QACV;QACA,MAAM,EAAC,cAAc,EAAE,KAAK,EAAE,MAAM,EAAC,GAAG,kBAAkB,OAAO,SAAS;QAC1E,IAAI,YAAY,iBAAiB,MAAM,GAAG,EAAE;QAC5C,MAAM,YAAY,YAAY,WAAW;QACzC,IAAI,UAAU,SAAS,YAAY;YACjC,YAAY;gBAAC,OAAO,UAAU,KAAK,GAAG;gBAAW,QAAQ,UAAU,MAAM,GAAG;YAAS;QACvF;QACA,MAAM,EAAC,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAC,GAAG;QACrC,MAAM,UAAU,sBAAsB,OAAO,WAAW;YAAC,aAAa;YAAG;YAAU;YAAS;QAAO;QACnG,OAAO;YACL,gBAAgB,wBAAwB,OAAO,SAAS;YACxD,GAAG,OAAO;YACV,GAAG,cAAc;YACjB,UAAU,QAAQ,QAAQ;YAC1B;QACF;IACF;AACF;AAEA,wBAAwB,EAAE,GAAG;AAE7B,wBAAwB,QAAQ,GAAG;IACjC,SAAS;IACT,UAAU;IACV,iBAAiB;IACjB,uBAAuB;IACvB,aAAa;IACb,YAAY,EAAE;IACd,kBAAkB;IAClB,iBAAiB;IACjB,mBAAmB;IACnB,aAAa;IACb,OAAO;IACP,SAAS;IACT,SAAS;IACT,MAAM;QACJ,QAAQ;QACR,YAAY;QACZ,MAAM;QACN,OAAO;QACP,QAAQ;IACV;IACA,QAAQ;IACR,cAAc;IACd,MAAM;IACN,SAAS;IACT,UAAU;IACV,UAAU;IACV,YAAY;IACZ,eAAe;IACf,eAAe;IACf,SAAS;IACT,WAAW;IACX,iBAAiB;IACjB,iBAAiB;IACjB,OAAO;IACP,SAAS;IACT,SAAS;AACX;AAEA,wBAAwB,aAAa,GAAG,CACxC;AAEA,SAAS,eAAe,KAAK,EAAE,OAAO;IACpC,OAAO,MAAM,4BAA4B,GAAG,MAAM,CAAC,SAAS,MAAM,EAAE,KAAK;QACvE,MAAM,aAAa,MAAM,UAAU;QACnC,IAAI,sBAAsB,+JAAA,CAAA,qBAAkB,IAC1C,oBAAoB,OAAO,SAAS,MAAM,IAAI,KAC9C,CAAC,CAAC,UAAU,WAAW,WAAW,GAAG,OAAO,UAAU,CAAC,WAAW,KAClE,WAAW,OAAO,CAAC,aAAa,IAAI,IAAI;YACxC,OAAO;QACT;QACA,OAAO;IACT,GAAG;AACL;AAEA,SAAS,oBAAoB,KAAK,EAAE,OAAO,EAAE,QAAQ;IACnD,IAAI,CAAC,QAAQ,QAAQ,EAAE;QACrB,OAAO;IACT;IACA,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,MAAM,EAAE,IAAK;QACxC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,MAAM,IAAI,MAAM,iBAAiB,CAAC,IAAI;YACrD,OAAO;QACT;IACF;AACF;AAEA,SAAS,kBAAkB,EAAC,SAAS,EAAC,EAAE,OAAO,EAAE,IAAI;IACnD,MAAM,EAAC,IAAI,EAAE,GAAG,EAAE,KAAK,EAAE,MAAM,EAAC,GAAG;IACnC,MAAM,EAAC,WAAW,EAAE,OAAO,EAAE,OAAO,EAAC,GAAG,KAAK,UAAU;IACvD,MAAM,IAAI,CAAC,OAAO,KAAK,IAAI,IAAI;IAC/B,MAAM,IAAI,CAAC,MAAM,MAAM,IAAI,IAAI;IAC/B,MAAM,SAAS;QACb,MAAM,KAAK,GAAG,CAAC,IAAI,aAAa;QAChC,OAAO,KAAK,GAAG,CAAC,IAAI,aAAa;QACjC,KAAK,KAAK,GAAG,CAAC,IAAI,aAAa;QAC/B,QAAQ,KAAK,GAAG,CAAC,IAAI,aAAa;IACpC;IACA,MAAM,QAAQ;QACZ,GAAG,CAAC,OAAO,IAAI,GAAG,OAAO,KAAK,IAAI;QAClC,GAAG,CAAC,OAAO,GAAG,GAAG,OAAO,MAAM,IAAI;IACpC;IACA,MAAM,QAAQ,QAAQ,OAAO,GAAG,QAAQ,WAAW,GAAG;IACtD,MAAM,UAAU,cAAc;IAC9B,MAAM,oBAAoB,MAAM,CAAC,GAAG;IACpC,MAAM,OAAO,oBAAoB,MAAM,QAAQ,SAAS;IACxD,MAAM,SAAS,UAAU,MAAM,GAAG,GAAG;IACrC,MAAM,iBAAiB;QACrB,UAAU;QACV,UAAU;QACV;QACA;QACA,GAAG,MAAM;IACX;IACA,OAAO;QACL;QACA;QACA,QAAQ,KAAK,GAAG,CAAC,aAAa,KAAK,GAAG,CAAC,OAAO,KAAK,GAAG,OAAO,IAAI,EAAE,OAAO,MAAM,GAAG,OAAO,GAAG,IAAI;IACnG;AACF;AAEA,SAAS,YAAY,EAAC,KAAK,EAAE,MAAM,EAAC,EAAE,MAAM;IAC1C,MAAM,OAAO,KAAK,IAAI,CAAC,KAAK,GAAG,CAAC,OAAO,KAAK,KAAK,GAAG,CAAC,QAAQ;IAC7D,OAAO,AAAC,SAAS,IAAK;AACxB;AAEA,SAAS,UAAU,CAAC,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM;IAC5C,MAAM,MAAM,KAAK,GAAG,CAAC,UAAU,GAAG;IAClC,MAAM,KAAK,KAAK,GAAG,CAAC,QAAQ;IAC5B,MAAM,IAAI,UAAU,CAAC;IACrB,MAAM,IAAI,KAAK,GAAG,CAAC,SAAS,KAAK,MAAM;IACvC,MAAM,QAAQ,KAAK,GAAG,CAAC,GAAG,KAAM,IAAI;IACpC,IAAI,SAAS,GAAG;QACd,OAAO;YACL,aAAa;YACb,WAAW,kMAAA,CAAA,MAAG;QAChB;IACF;IACA,MAAM,QAAQ,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,MAAM,IAAI;IACxC,MAAM,MAAM,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,MAAM,IAAI;IACtC,OAAO;QACL,aAAa,CAAA,GAAA,gNAAA,CAAA,oBAAiB,AAAD,EAAE;YAAC,GAAG;YAAS,GAAG;QAAO,GAAG;YAAC,GAAG;YAAO;QAAC,GAAG,KAAK;QAC7E,WAAW,CAAA,GAAA,gNAAA,CAAA,oBAAiB,AAAD,EAAE;YAAC,GAAG;YAAS,GAAG;QAAO,GAAG;YAAC,GAAG;YAAK;QAAC,GAAG,KAAK;IAC3E;AACF;AAEA,SAAS,eAAe,GAAG,EAAE,OAAO;IAClC,MAAM,EAAC,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,WAAW,EAAE,SAAS,EAAE,iBAAiB,EAAE,OAAO,EAAC,GAAG;IAC1F,IAAI,IAAI;IACR,MAAM,SAAS,eAAe,KAAK;IACnC,IAAI,SAAS,GAAG,QAAQ,eAAe;IACvC,IAAI,SAAS;IACb,IAAI,GAAG,CAAC,UAAU,UAAU,SAAS,aAAa,WAAW;IAC7D,IAAI,SAAS;IACb,IAAI,IAAI;IACR,IAAI,QAAQ;QACV,IAAI,MAAM;IACZ;IACA,IAAI,OAAO;AACb;AAEA,MAAM,wBAAwB,+JAAA,CAAA,UAAO;IAEnC,QAAQ,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,gBAAgB,EAAE;QAC9C,OAAO,aACL;YAAC,GAAG;YAAQ,GAAG;QAAM,GACrB;YAAC,MAAM,IAAI,CAAC,QAAQ,CAAC;gBAAC;gBAAK;gBAAK;gBAAM;aAAK,EAAE;YAAmB,QAAQ,IAAI,CAAC,cAAc,CAAC;QAAiB,GAC7G,MACA;YAAC,UAAU,IAAI,CAAC,QAAQ;YAAE,aAAa,IAAI,CAAC,OAAO,CAAC,WAAW;YAAE,cAAc,IAAI,CAAC,OAAO,CAAC,YAAY;QAAA;IAE5G;IAEA,eAAe,gBAAgB,EAAE;QAC/B,OAAO,sBAAsB,IAAI,EAAE;IACrC;IAEA,KAAK,GAAG,EAAE;QACR,MAAM,UAAU,IAAI,CAAC,OAAO;QAC5B,MAAM,UAAU,CAAC,CAAA,GAAA,sMAAA,CAAA,UAAO,AAAD,EAAE,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,QAAQ;QACxD,IAAI,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ,OAAO,IAAI,CAAC,SAAS;YACpD;QACF;QACA,IAAI,IAAI;QACR,UAAU,KAAK,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,QAAQ;QACnD,YAAY,KAAK,IAAI;QACrB,QAAQ,KAAK,IAAI,EAAE;QACnB,UAAU,KAAK,aAAa,IAAI,GAAG;QACnC,IAAI,OAAO;IACb;IAEA,yBAAyB,KAAK,EAAE,OAAO,EAAE;QACvC,IAAI;QACJ,IAAI,CAAC,eAAe,UAAU;YAC5B,MAAM,EAAC,OAAO,EAAE,OAAO,EAAC,GAAG,qBAAqB,OAAO;YACvD,QAAQ;gBAAC,GAAG;gBAAS,GAAG;YAAO;QACjC,OAAO;YACL,QAAQ,cAAc,OAAO;QAC/B;QACA,MAAM,UAAU,CAAA,GAAA,wMAAA,CAAA,YAAS,AAAD,EAAE,QAAQ,OAAO;QACzC,MAAM,YAAY,iBAAiB,MAAM,GAAG,EAAE;QAC9C,MAAM,UAAU,sBAAsB,OAAO,WAAW,SAAS;QACjE,OAAO;YACL,gBAAgB,wBAAwB,OAAO,SAAS;YACxD,QAAQ,MAAM,CAAC;YACf,QAAQ,MAAM,CAAC;YACf,GAAG,OAAO;YACV,UAAU,QAAQ,QAAQ;QAC5B;IACF;AACF;AAEA,gBAAgB,EAAE,GAAG;AAErB,gBAAgB,QAAQ,GAAG;IACzB,kBAAkB;IAClB,iBAAiB;IACjB,uBAAuB;IACvB,gBAAgB;IAChB,YAAY,EAAE;IACd,kBAAkB;IAClB,iBAAiB;IACjB,cAAc;IACd,mBAAmB;IACnB,aAAa;IACb,SAAS;QACP,gBAAgB;QAChB,aAAa;QACb,YAAY,EAAE;QACd,kBAAkB;QAClB,iBAAiB;QACjB,aAAa;QACb,SAAS;QACT,QAAQ;QACR,UAAU;QACV,MAAM;QACN,OAAO;IACT;IACA,OAAO;IACP,SAAS;IACT,SAAS;IACT,MAAM;QACJ,QAAQ;QACR,YAAY;QACZ,MAAM;QACN,OAAO;QACP,QAAQ;IACV;IACA,QAAQ;IACR,cAAc;IACd,MAAM;IACN,SAAS;IACT,SAAS;IACT,UAAU;IACV,UAAU;IACV,YAAY;IACZ,eAAe;IACf,eAAe;IACf,WAAW;IACX,iBAAiB;IACjB,iBAAiB;IACjB,OAAO;IACP,SAAS;IACT,MAAM;IACN,MAAM;IACN,UAAU;IACV,QAAQ;IACR,SAAS;IACT,MAAM;IACN,MAAM;IACN,UAAU;IACV,QAAQ;IACR,GAAG;AACL;AAEA,gBAAgB,aAAa,GAAG;IAC9B,aAAa;AACf;AAEA,SAAS,aAAa,EAAC,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAC;IAClD,MAAM,eAAe,QAAQ,WAAW,GAAG;IAC3C,MAAM,UAAU,CAAA,GAAA,wMAAA,CAAA,YAAS,AAAD,EAAE,QAAQ,OAAO;IACzC,OAAO;QACL,GAAG,IAAI,QAAQ,IAAI,GAAG;QACtB,GAAG,IAAI,QAAQ,GAAG,GAAG;QACrB,OAAO,QAAQ,QAAQ,IAAI,GAAG,QAAQ,KAAK,GAAG,QAAQ,WAAW;QACjE,QAAQ,SAAS,QAAQ,GAAG,GAAG,QAAQ,MAAM,GAAG,QAAQ,WAAW;IACrE;AACF;AAEA,MAAM,cAAc,CAAC,IAAI,IAAI,IAAM,CAAC;QAAC,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC;QAAG,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC;IAAC,CAAC;AAC9F,MAAM,eAAe,CAAC,GAAG,IAAI,KAAO,YAAY,IAAI,IAAI,KAAK,GAAG,CAAC,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC;AAC/F,MAAM,eAAe,CAAC,GAAG,IAAI,KAAO,YAAY,IAAI,IAAI,KAAK,GAAG,CAAC,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC;AAC/F,MAAM,MAAM,CAAA,IAAK,IAAI;AACrB,MAAM,aAAa,CAAC,QAAQ,QAAQ,EAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAC,EAAE,OAAS,SAAS,MAAM;QAAC,OAAO,KAAK,GAAG,CAAC,GAAG;QAAK,KAAK,KAAK,GAAG,CAAC,GAAG;QAAK,OAAO;IAAM,IAAI;QAAC,OAAO,KAAK,GAAG,CAAC,GAAG;QAAK,KAAK,KAAK,GAAG,CAAC,GAAG;QAAK,OAAO;IAAM;AACxM,iGAAiG;AACjG,MAAM,eAAe,CAAC,OAAO,IAAI,KAAK,IAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,QAAQ,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,KAAK,IAAI,IAAI;AACvG,MAAM,eAAe,CAAC,OAAO,IAAI,KAAK,IAAM,CAAC;QAAC,GAAG,aAAa,MAAM,CAAC,EAAE,GAAG,CAAC,EAAE,IAAI,CAAC,EAAE;QAAI,GAAG,aAAa,MAAM,CAAC,EAAE,GAAG,CAAC,EAAE,IAAI,CAAC,EAAE;IAAE,CAAC;AACjI,MAAM,oBAAoB,CAAC,OAAO,IAAI,KAAK,IAAM,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK,IAAI,IAAI,IAAI,CAAC,MAAM,EAAE;AAC/F,MAAM,eAAe,CAAC,OAAO,IAAI,KAAK,IAAM,CAAC,KAAK,KAAK,CAAC,kBAAkB,MAAM,CAAC,EAAE,GAAG,CAAC,EAAE,IAAI,CAAC,EAAE,IAAI,kBAAkB,MAAM,CAAC,EAAE,GAAG,CAAC,EAAE,IAAI,CAAC,EAAE,MAAM,MAAM,iMAAA,CAAA,KAAE;AAE1J,MAAM,uBAAuB,+JAAA,CAAA,UAAO;IAElC,QAAQ,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,gBAAgB,EAAE;QAC9C,MAAM,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,IAAI;QACzE,IAAI,SAAS,OAAO,SAAS,KAAK;YAChC,MAAM,QAAQ;gBAAC;gBAAQ;YAAM;YAC7B,MAAM,EAAC,IAAI,EAAE,GAAG,EAAC,GAAG,IAAI;YACxB,IAAI,MAAM;gBACR,eAAe,KAAK,IAAI,CAAC,OAAO;gBAChC,IAAI,SAAS,IAAI,IAAI,CAAC,OAAO,CAAC,YAAY;gBAC1C,MAAM,EAAC,KAAK,EAAC,GAAG,IAAI,CAAC,QAAQ;gBAC7B,MAAM,KAAK,SAAS,MAAM,uBAAuB;gBACjD,MAAM,KAAK,SAAS,MAAM,uBAAuB;gBACjD,MAAM,SAAS,IAAI,eAAe,CAAC,MAAM,IAAI,OAAO,UAAU,IAAI,EAAE,OAAO;gBAC3E,IAAI,OAAO;gBACX,OAAO;YACT;YACA,MAAM,UAAU,IAAI;YACpB,OAAO,WAAW,IAAI,EAAE,OAAO,SAAS,qBAAqB,UAAU,IAAI,EAAE,OAAO;QACtF;QACA,OAAO,YAAY,IAAI,EAAE;YAAC;YAAQ;QAAM,GAAG,MAAM;YAAC;YAAS;QAAgB;IAC7E;IAEA,eAAe,gBAAgB,EAAE;QAC/B,OAAO,sBAAsB,IAAI,EAAE;IACrC;IAEA,KAAK,GAAG,EAAE;QACR,MAAM,EAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,OAAO,EAAC,GAAG,IAAI;QAExC,IAAI,IAAI;QACR,IAAI,CAAC,eAAe,KAAK,UAAU;YACjC,0CAA0C;YAC1C,OAAO,IAAI,OAAO;QACpB;QACA,eAAe,KAAK;QAEpB,MAAM,SAAS,KAAK,IAAI,CAAC,KAAK,GAAG,CAAC,KAAK,GAAG,KAAK,KAAK,GAAG,CAAC,KAAK,GAAG;QAChE,IAAI,QAAQ,KAAK,IAAI,IAAI;YACvB,UAAU,KAAK,IAAI,EAAE,IAAI;YACzB,OAAO,IAAI,OAAO;QACpB;QACA,MAAM,EAAC,SAAS,EAAE,OAAO,EAAE,WAAW,EAAE,SAAS,EAAC,GAAG,cAAc,IAAI;QACvE,MAAM,QAAQ,KAAK,KAAK,CAAC,KAAK,GAAG,KAAK;QACtC,IAAI,SAAS,CAAC,GAAG;QACjB,IAAI,MAAM,CAAC;QACX,IAAI,SAAS;QACb,IAAI,MAAM,CAAC,IAAI,aAAa;QAC5B,IAAI,MAAM,CAAC,SAAS,WAAW;QAC/B,IAAI,WAAW,GAAG,QAAQ,iBAAiB;QAC3C,IAAI,MAAM;QACV,cAAc,KAAK,GAAG,aAAa;QACnC,cAAc,KAAK,QAAQ,CAAC,WAAW;QACvC,IAAI,OAAO;IACb;IAEA,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,EAAE;IAC1C;IAEA,yBAAyB,KAAK,EAAE,OAAO,EAAE;QACvC,MAAM,OAAO,sBAAsB,OAAO;QAC1C,MAAM,EAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAC,GAAG;QACvB,MAAM,SAAS,aAAa,MAAM,MAAM,SAAS;QACjD,MAAM,aAAa,SACf,gBAAgB;YAAC;YAAG;QAAC,GAAG;YAAC,GAAG;YAAI,GAAG;QAAE,GAAG,MAAM,SAAS,IACvD;YAAC;YAAG;YAAG;YAAI;YAAI,OAAO,KAAK,GAAG,CAAC,KAAK;YAAI,QAAQ,KAAK,GAAG,CAAC,KAAK;QAAE;QACpE,WAAW,OAAO,GAAG,CAAC,KAAK,CAAC,IAAI;QAChC,WAAW,OAAO,GAAG,CAAC,KAAK,CAAC,IAAI;QAChC,WAAW,cAAc,GAAG,wBAAwB,OAAO,YAAY;QACvE,IAAI,QAAQ,KAAK,EAAE;YACjB,MAAM,KAAK;gBAAC,GAAG,WAAW,CAAC;gBAAE,GAAG,WAAW,CAAC;YAAA;YAC5C,MAAM,KAAK;gBAAC,GAAG,WAAW,EAAE;gBAAE,GAAG,WAAW,EAAE;YAAA;YAC9C,WAAW,EAAE,GAAG,gBAAgB,YAAY,SAAS,CAAA,GAAA,qNAAA,CAAA,wBAAqB,AAAD,EAAE,IAAI;QACjF;QACA,MAAM,kBAAkB,8BAA8B,OAAO,YAAY,QAAQ,KAAK;QACtF,oCAAoC;QACpC,gBAAgB,QAAQ,GAAG;QAE3B,WAAW,QAAQ,GAAG;YAAC;gBACrB,MAAM;gBACN,aAAa;gBACb,YAAY;gBACZ,gBAAgB,WAAW,cAAc;YAC3C;SAAE;QACF,OAAO;IACT;AACF;AAEA,eAAe,EAAE,GAAG;AAEpB,MAAM,qBAAqB;IACzB,iBAAiB;IACjB,uBAAuB;IACvB,aAAa;IACb,YAAY;IACZ,kBAAkB;IAClB,mBAAmB;IACnB,aAAa;IACb,SAAS;IACT,MAAM;IACN,QAAQ;IACR,YAAY;IACZ,eAAe;IACf,eAAe;IACf,OAAO;AACT;AAEA,eAAe,QAAQ,GAAG;IACxB,kBAAkB;IAClB,YAAY;QACV,SAAS;QACT,KAAK,OAAO,MAAM,CAAC,CAAC,GAAG;QACvB,MAAM;QACN,QAAQ;QACR,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG;QACzB,OAAO;IACT;IACA,YAAY,EAAE;IACd,kBAAkB;IAClB,mBAAmB;IACnB,aAAa;IACb,OAAO;IACP,cAAc;QACZ,GAAG;IACL;IACA,SAAS;IACT,UAAU;IACV,MAAM;IACN,cAAc;IACd,OAAO;QACL,iBAAiB;QACjB,uBAAuB;QACvB,gBAAgB;QAChB,aAAa;QACb,YAAY,EAAE;QACd,kBAAkB;QAClB,iBAAiB;QACjB,cAAc;QACd,mBAAmB;QACnB,aAAa;QACb,SAAS,OAAO,MAAM,CAAC,CAAC,GAAG,gBAAgB,QAAQ,CAAC,OAAO;QAC3D,OAAO;QACP,SAAS;QACT,SAAS;QACT,UAAU;QACV,MAAM;YACJ,QAAQ;YACR,YAAY;YACZ,MAAM;YACN,OAAO;YACP,QAAQ;QACV;QACA,QAAQ;QACR,cAAc;QACd,SAAS;QACT,SAAS;QACT,UAAU;QACV,UAAU;QACV,YAAY;QACZ,eAAe;QACf,eAAe;QACf,WAAW;QACX,iBAAiB;QACjB,iBAAiB;QACjB,OAAO;QACP,SAAS;QACT,SAAS;QACT,GAAG;IACL;IACA,SAAS;IACT,YAAY;IACZ,eAAe;IACf,eAAe;IACf,OAAO;IACP,MAAM;IACN,MAAM;IACN,UAAU;IACV,MAAM;IACN,MAAM;IACN,UAAU;IACV,GAAG;AACL;AAEA,eAAe,WAAW,GAAG;IAC3B,YAAY;QACV,OAAO;YACL,WAAW;QACb;QACA,KAAK;YACH,WAAW;QACb;QACA,WAAW;IACb;AACF;AAEA,eAAe,aAAa,GAAG;IAC7B,aAAa;AACf;AAEA,SAAS,YAAY,OAAO,EAAE,EAAC,MAAM,EAAE,MAAM,EAAC,EAAE,IAAI,EAAE,EAAC,OAAO,EAAE,gBAAgB,EAAC;IAC/E,MAAM,QAAQ,WAAW,QAAQ,QAAQ,QAAQ,QAAQ,CAAC;QAAC;QAAK;QAAK;QAAM;KAAK,EAAE,mBAAmB;IACrG,OAAO,QAAQ,OAAO,YAAY,UAAU,SAAS;QAAC;QAAQ;IAAM,GAAG,kBAAkB;AAC3F;AAEA,SAAS,aAAa,EAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAC,EAAE,EAAC,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,EAAC;IAC9D,OAAO,CAAC,CACN,AAAC,IAAI,QAAQ,KAAK,QACjB,IAAI,SAAS,KAAK,SAClB,IAAI,OAAO,KAAK,OAChB,IAAI,UAAU,KAAK,MACtB;AACF;AAEA,SAAS,iBAAiB,EAAC,CAAC,EAAE,CAAC,EAAC,EAAE,EAAE,EAAE,EAAC,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,EAAC;IAC9D,IAAI,IAAI,MAAM;QACZ,IAAI,aAAa,MAAM;YAAC;YAAG;QAAC,GAAG;QAC/B,IAAI;IACN;IACA,IAAI,IAAI,OAAO;QACb,IAAI,aAAa,OAAO;YAAC;YAAG;QAAC,GAAG;QAChC,IAAI;IACN;IACA,IAAI,IAAI,KAAK;QACX,IAAI,aAAa,KAAK;YAAC;YAAG;QAAC,GAAG;QAC9B,IAAI;IACN;IACA,IAAI,IAAI,QAAQ;QACd,IAAI,aAAa,QAAQ;YAAC;YAAG;QAAC,GAAG;QACjC,IAAI;IACN;IACA,OAAO;QAAC;QAAG;IAAC;AACd;AAEA,SAAS,gBAAgB,EAAE,EAAE,EAAE,EAAE,IAAI;IACnC,MAAM,EAAC,CAAC,EAAE,CAAC,EAAC,GAAG,iBAAiB,IAAI,IAAI;IACxC,MAAM,EAAC,GAAG,EAAE,EAAE,GAAG,EAAE,EAAC,GAAG,iBAAiB,IAAI,IAAI;IAChD,OAAO;QAAC;QAAG;QAAG;QAAI;QAAI,OAAO,KAAK,GAAG,CAAC,KAAK;QAAI,QAAQ,KAAK,GAAG,CAAC,KAAK;IAAE;AACzE;AAEA,SAAS,WAAW,OAAO,EAAE,EAAC,MAAM,EAAE,MAAM,EAAC,EAAE,UAAU,OAAO,EAAE,gBAAgB;IAChF,yDAAyD;IACzD,MAAM,EAAC,GAAG,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAC,GAAG,QAAQ,QAAQ,CAAC;QAAC;QAAK;QAAK;QAAM;KAAK,EAAE;IACxE,MAAM,KAAK,KAAK;IAChB,MAAM,KAAK,KAAK;IAChB,MAAM,QAAQ,IAAI,MAAM,IAAI;IAC5B,MAAM,IAAI,UAAU,IAAI,CAAC,IAAI,CAAC,CAAC,SAAS,EAAE,IAAI,KAAK,CAAC,SAAS,EAAE,IAAI,EAAE,IAAI;IAEzE,IAAI,IAAI;IACR,IAAI,IAAI,GAAG;QACT,KAAK;QACL,KAAK;IACP,OAAO,IAAI,IAAI,GAAG;QAChB,KAAK;QACL,KAAK;IACP,OAAO;QACL,KAAK,KAAK,IAAI;QACd,KAAK,KAAK,IAAI;IAChB;IACA,OAAO,AAAC,IAAI,SAAS,MAAM,IAAI,SAAS,OAAQ;AAClD;AAEA,SAAS,UAAU,OAAO,EAAE,EAAC,MAAM,EAAE,MAAM,EAAC,EAAE,gBAAgB,EAAE,IAAI;IAClE,MAAM,QAAQ,QAAQ,KAAK;IAC3B,OAAO,MAAM,OAAO,CAAC,OAAO,IAAI,MAAM,OAAO,CAAC,QAAQ,QAAQ,MAAM;AACtE;AAEA,SAAS,8BAA8B,KAAK,EAAE,UAAU,EAAE,OAAO;IAC/D,MAAM,cAAc,QAAQ,WAAW;IACvC,MAAM,UAAU,CAAA,GAAA,wMAAA,CAAA,YAAS,AAAD,EAAE,QAAQ,OAAO;IACzC,MAAM,WAAW,iBAAiB,MAAM,GAAG,EAAE;IAC7C,MAAM,QAAQ,SAAS,KAAK,GAAG,QAAQ,KAAK,GAAG;IAC/C,MAAM,SAAS,SAAS,MAAM,GAAG,QAAQ,MAAM,GAAG;IAClD,OAAO,uBAAuB,YAAY,SAAS;QAAC;QAAO;QAAQ;IAAO,GAAG,MAAM,SAAS;AAC9F;AAEA,SAAS,sBAAsB,UAAU;IACvC,MAAM,EAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAC,GAAG;IACvB,MAAM,WAAW,KAAK,KAAK,CAAC,KAAK,GAAG,KAAK;IACzC,yEAAyE;IACzE,OAAO,WAAW,iMAAA,CAAA,KAAE,GAAG,IAAI,WAAW,iMAAA,CAAA,KAAE,GAAG,WAAW,iMAAA,CAAA,KAAE,GAAG,CAAC,IAAI,WAAW,iMAAA,CAAA,KAAE,GAAG;AAClF;AAEA,SAAS,uBAAuB,UAAU,EAAE,KAAK,EAAE,KAAK,EAAE,SAAS;IACjE,MAAM,EAAC,KAAK,EAAE,MAAM,EAAE,OAAO,EAAC,GAAG;IACjC,MAAM,EAAC,OAAO,EAAE,OAAO,EAAC,GAAG;IAC3B,MAAM,KAAK;QAAC,GAAG,WAAW,CAAC;QAAE,GAAG,WAAW,CAAC;IAAA;IAC5C,MAAM,KAAK;QAAC,GAAG,WAAW,EAAE;QAAE,GAAG,WAAW,EAAE;IAAA;IAC9C,MAAM,WAAW,MAAM,QAAQ,KAAK,SAAS,sBAAsB,cAAc,CAAA,GAAA,wMAAA,CAAA,YAAS,AAAD,EAAE,MAAM,QAAQ;IACzG,MAAM,OAAO,YAAY,OAAO,QAAQ;IACxC,MAAM,IAAI,WAAW,YAAY,OAAO;QAAC,WAAW;QAAM;IAAO,GAAG;IACpE,MAAM,KAAK,WAAW,EAAE,GAAG,aAAa,IAAI,WAAW,EAAE,EAAE,IAAI,KAAK,YAAY,IAAI,IAAI;IACxF,MAAM,mBAAmB;QAAC,MAAM,KAAK,CAAC;QAAE,KAAK,UAAU,IAAI;QAAE,KAAK,UAAU,KAAK;QAAE,SAAS,QAAQ,IAAI;IAAA;IACxG,MAAM,mBAAmB;QAAC,MAAM,KAAK,CAAC;QAAE,KAAK,UAAU,GAAG;QAAE,KAAK,UAAU,MAAM;QAAE,SAAS,QAAQ,GAAG;IAAA;IACvG,MAAM,UAAU,sBAAsB,GAAG,CAAC,EAAE,oBAAoB;IAChE,MAAM,UAAU,sBAAsB,GAAG,CAAC,EAAE,oBAAoB;IAChE,OAAO;QACL,GAAG,UAAW,QAAQ;QACtB,GAAG,UAAW,SAAS;QACvB,IAAI,UAAW,QAAQ;QACvB,IAAI,UAAW,SAAS;QACxB;QACA;QACA,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC;QACZ;QACA;QACA,UAAU,CAAA,GAAA,wMAAA,CAAA,YAAS,AAAD,EAAE;IACtB;AACF;AAEA,SAAS,YAAY,KAAK,EAAE,MAAM,EAAE,QAAQ;IAC1C,MAAM,MAAM,KAAK,GAAG,CAAC;IACrB,MAAM,MAAM,KAAK,GAAG,CAAC;IACrB,OAAO;QACL,GAAG,KAAK,GAAG,CAAC,QAAQ,OAAO,KAAK,GAAG,CAAC,SAAS;QAC7C,GAAG,KAAK,GAAG,CAAC,QAAQ,OAAO,KAAK,GAAG,CAAC,SAAS;IAC/C;AACF;AAEA,SAAS,WAAW,UAAU,EAAE,KAAK,EAAE,KAAK,EAAE,SAAS;IACrD,IAAI;IACJ,MAAM,QAAQ,YAAY,YAAY;IACtC,IAAI,MAAM,QAAQ,KAAK,SAAS;QAC9B,IAAI,iBAAiB;YAAC,GAAG,WAAW,EAAE,GAAG,WAAW,CAAC;YAAE,GAAG,WAAW,EAAE,GAAG,WAAW,CAAC;QAAA,GAAG,OAAO,OAAO;IACzG,OAAO,IAAI,MAAM,QAAQ,KAAK,OAAO;QACnC,IAAI,IAAI,iBAAiB;YAAC,GAAG,WAAW,CAAC,GAAG,WAAW,EAAE;YAAE,GAAG,WAAW,CAAC,GAAG,WAAW,EAAE;QAAA,GAAG,OAAO,OAAO;IAC7G,OAAO;QACL,IAAI,oBAAoB,GAAG,MAAM,QAAQ;IAC3C;IACA,OAAO;AACT;AAEA,SAAS,iBAAiB,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK;IACrD,MAAM,EAAC,SAAS,EAAE,OAAO,EAAC,GAAG;IAC7B,MAAM,QAAQ,SAAS,CAAC,GAAG,MAAM,EAAE;IACnC,MAAM,QAAQ,SAAS,CAAC,GAAG,MAAM,EAAE;IACnC,MAAM,IAAI,AAAC,QAAQ,KAAO,CAAC,UAAU,CAAC,GAAG,IAAI,QAAQ,IAAI,GAAG,MAAM,CAAC,IAAI;IACvE,MAAM,IAAI,AAAC,QAAQ,KAAO,CAAC,UAAU,CAAC,GAAG,IAAI,QAAQ,GAAG,GAAG,MAAM,CAAC,IAAI;IACtE,OAAO,MAAM,KAAK,GAAG,CAAC,GAAG,IAAI,GAAG;AAClC;AAEA,SAAS,YAAY,UAAU,EAAE,SAAS;IACxC,MAAM,EAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAC,GAAG;IACvB,MAAM,IAAI,KAAK,GAAG,CAAC,GAAG,MAAM,UAAU,GAAG;IACzC,MAAM,IAAI,KAAK,GAAG,CAAC,GAAG,MAAM,UAAU,IAAI;IAC1C,MAAM,IAAI,UAAU,MAAM,GAAG,KAAK,GAAG,CAAC,GAAG;IACzC,MAAM,IAAI,UAAU,KAAK,GAAG,KAAK,GAAG,CAAC,GAAG;IACxC,OAAO;QACL,GAAG,KAAK,GAAG,CAAC,GAAG;QACf,GAAG,KAAK,GAAG,CAAC,GAAG;QACf,IAAI,KAAK,IAAI,IAAI,CAAC;QAClB,IAAI,KAAK,IAAI,IAAI,CAAC;IACpB;AACF;AAEA,SAAS,sBAAsB,UAAU,EAAE,UAAU;IACnD,MAAM,EAAC,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,OAAO,EAAC,GAAG;IAClC,MAAM,WAAW,OAAO;IACxB,IAAI,OAAO,MAAM,KAAK;QACpB,kDAAkD;QAClD,OAAO,CAAC,MAAM,GAAG,IAAI;IACvB;IACA,IAAI,OAAQ,aAAa,UAAU,UAAW;QAC5C,aAAa,MAAM,UAAU;IAC/B;IACA,IAAI,OAAQ,aAAa,UAAU,UAAW;QAC5C,aAAa,MAAM,UAAU;IAC/B;IACA,OAAO;AACT;AAEA,SAAS,cAAc,IAAI;IACzB,MAAM,UAAU,KAAK,OAAO;IAC5B,MAAM,iBAAiB,QAAQ,UAAU,IAAI,QAAQ,UAAU,CAAC,KAAK;IACrE,MAAM,eAAe,QAAQ,UAAU,IAAI,QAAQ,UAAU,CAAC,GAAG;IACjE,OAAO;QACL,WAAW;QACX,SAAS;QACT,aAAa,cAAc,MAAM;QACjC,WAAW,cAAc,MAAM;IACjC;AACF;AAEA,SAAS,cAAc,IAAI,EAAE,SAAS;IACpC,IAAI,CAAC,aAAa,CAAC,UAAU,OAAO,EAAE;QACpC,OAAO;IACT;IACA,MAAM,EAAC,MAAM,EAAE,KAAK,EAAC,GAAG;IACxB,MAAM,SAAS,KAAK,OAAO,CAAC,WAAW,GAAG;IAC1C,MAAM,KAAK;QAAC,GAAG;QAAQ,GAAG,QAAQ;IAAM;IACxC,MAAM,KAAK;QAAC,GAAG;QAAG,GAAG;IAAM;IAC3B,OAAO,KAAK,GAAG,CAAC,aAAa,GAAG,IAAI;AACtC;AAEA,SAAS,cAAc,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,SAAS;IACnD,IAAI,CAAC,aAAa,CAAC,UAAU,OAAO,EAAE;QACpC;IACF;IACA,MAAM,EAAC,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,eAAe,EAAE,WAAW,EAAC,GAAG;IAC5D,MAAM,eAAe,KAAK,GAAG,CAAC,SAAS,UAAU;IACjD,IAAI,SAAS;IACb,eAAe,KAAK;IACpB,eAAe,KAAK;IACpB,IAAI,MAAM,CAAC,cAAc,CAAC;IAC1B,IAAI,MAAM,CAAC,SAAS,QAAQ;IAC5B,IAAI,MAAM,CAAC,cAAc;IACzB,IAAI,SAAS,MAAM;QACjB,IAAI,SAAS,GAAG,mBAAmB;QACnC,IAAI,SAAS;QACb,IAAI,IAAI;QACR,IAAI,WAAW,GAAG;IACpB,OAAO;QACL,IAAI,WAAW,GAAG,UAAU,iBAAiB;IAC/C;IACA,IAAI,MAAM;AACZ;AAEA,SAAS,gBAAgB,UAAU,EAAE,OAAO,EAAE,QAAQ;IACpD,MAAM,EAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,OAAO,EAAE,OAAO,EAAC,GAAG;IACzC,MAAM,QAAQ,KAAK,KAAK,CAAC,KAAK,GAAG,KAAK;IACtC,MAAM,KAAK,WAAW,QAAQ,YAAY,EAAE;IAC5C,MAAM,QAAQ;QACZ,GAAG,UAAU,QAAQ,UAAU,GAAG,CAAC,EAAE;QACrC,GAAG,UAAU,QAAQ,UAAU,GAAG,CAAC,EAAE;IACvC;IACA,OAAO,QAAQ,OAAO;QAAC,GAAG;QAAS,GAAG;IAAO,GAAG;AAClD;AAEA,SAAS,qBAAqB,GAAG,EAAE,EAAC,CAAC,EAAE,CAAC,EAAC,EAAE,EAAC,KAAK,EAAE,MAAM,EAAC,EAAE,SAAS;IACnE,IAAI,CAAC,aAAa,CAAC,UAAU,OAAO,EAAE;QACpC;IACF;IACA,IAAI,IAAI;IACR,IAAI,SAAS,CAAC,GAAG;IACjB,IAAI,MAAM,CAAC;IACX,cAAc,KAAK,GAAG,CAAC,QAAQ;IAC/B,IAAI,OAAO;AACb;AAEA,SAAS,UAAU,GAAG,EAAE,OAAO,EAAE,EAAE,EAAE,MAAM;IACzC,MAAM,EAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,OAAO,EAAC,GAAG;IAChC,MAAM,EAAC,SAAS,EAAE,OAAO,EAAE,WAAW,EAAE,SAAS,EAAC,GAAG,cAAc;IACnE,MAAM,KAAK;QAAC;QAAG;IAAC;IAChB,MAAM,KAAK;QAAC,GAAG;QAAI,GAAG;IAAE;IACxB,MAAM,aAAa,aAAa,IAAI,IAAI,IAAI;IAC5C,MAAM,WAAW,aAAa,IAAI,IAAI,IAAI,KAAK,iMAAA,CAAA,KAAE;IACjD,MAAM,KAAK,aAAa,IAAI,IAAI,IAAI,cAAc;IAClD,MAAM,KAAK,aAAa,IAAI,IAAI,IAAI,IAAI,YAAY;IAEpD,MAAM,OAAO,IAAI;IACjB,IAAI,SAAS;IACb,KAAK,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC;IACtB,KAAK,gBAAgB,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,CAAC;IAC5C,IAAI,WAAW,GAAG,QAAQ,iBAAiB;IAC3C,IAAI,MAAM,CAAC;IACX,QAAQ,IAAI,GAAG;IACf,QAAQ,GAAG,GAAG;IACd,qBAAqB,KAAK,IAAI;QAAC,OAAO;QAAY,QAAQ;IAAW,GAAG;IACxE,qBAAqB,KAAK,IAAI;QAAC,OAAO;QAAU,QAAQ;IAAS,GAAG;AACtE;AAEA,MAAM,0BAA0B,+JAAA,CAAA,UAAO;IAErC,QAAQ,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,gBAAgB,EAAE;QAC9C,MAAM,WAAW,IAAI,CAAC,OAAO,CAAC,QAAQ;QACtC,MAAM,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,IAAI;QACzE,IAAI,SAAS,OAAO,SAAS,KAAK;YAChC,OAAO,eAAe;gBAAC,GAAG;gBAAQ,GAAG;YAAM,GAAG,IAAI,CAAC,QAAQ,CAAC;gBAAC;gBAAS;gBAAU;gBAAW;aAAU,EAAE,mBAAmB,UAAU;QACtI;QACA,MAAM,EAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;YAAC;YAAK;YAAK;YAAM;SAAK,EAAE;QAC7D,MAAM,QAAQ,SAAS,MAAM;YAAC,OAAO;YAAG,KAAK;QAAE,IAAI;YAAC,OAAO;YAAG,KAAK;QAAE;QACrE,MAAM,eAAe,QAAQ;YAAC,GAAG;YAAQ,GAAG;QAAM,GAAG,IAAI,CAAC,cAAc,CAAC,mBAAmB,CAAA,GAAA,wMAAA,CAAA,YAAS,AAAD,EAAE,CAAC;QACvG,OAAO,YAAY,CAAC,KAAK,IAAI,MAAM,KAAK,GAAG,UAAU,WAAW,YAAY,CAAC,KAAK,IAAI,MAAM,GAAG,GAAG,UAAU;IAC9G;IAEA,eAAe,gBAAgB,EAAE;QAC/B,OAAO,sBAAsB,IAAI,EAAE;IACrC;IAEA,KAAK,GAAG,EAAE;QACR,MAAM,EAAC,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAC,GAAG,IAAI;QACvD,IAAI,IAAI;QACR,UAAU,KAAK,IAAI,CAAC,cAAc,IAAI,QAAQ,QAAQ;QACtD,eAAe,KAAK,IAAI,CAAC,OAAO;QAChC,IAAI,SAAS;QACb,IAAI,SAAS,GAAG,QAAQ,eAAe;QACvC,MAAM,SAAS,eAAe,KAAK;QACnC,IAAI,OAAO,CAAC,SAAS,SAAS,SAAS,GAAG,QAAQ,GAAG,iMAAA,CAAA,KAAE,GAAG,GAAG,GAAG,IAAI,iMAAA,CAAA,KAAE;QACtE,IAAI,IAAI;QACR,IAAI,QAAQ;YACV,IAAI,WAAW,GAAG,QAAQ,iBAAiB;YAC3C,IAAI,MAAM;QACZ;QACA,IAAI,OAAO;IACb;IAEA,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,EAAE;IAC1C;IAEA,yBAAyB,KAAK,EAAE,OAAO,EAAE;QACvC,OAAO,6BAA6B,OAAO;IAC7C;AAEF;AAEA,kBAAkB,EAAE,GAAG;AAEvB,kBAAkB,QAAQ,GAAG;IAC3B,kBAAkB;IAClB,uBAAuB;IACvB,YAAY,EAAE;IACd,kBAAkB;IAClB,mBAAmB;IACnB,aAAa;IACb,SAAS;IACT,cAAc;IACd,MAAM;IACN,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,cAAc,QAAQ,CAAC,KAAK;IACrD,UAAU;IACV,YAAY;IACZ,eAAe;IACf,eAAe;IACf,MAAM;IACN,MAAM;IACN,UAAU;IACV,MAAM;IACN,MAAM;IACN,UAAU;IACV,GAAG;AACL;AAEA,kBAAkB,aAAa,GAAG;IAChC,aAAa;IACb,iBAAiB;AACnB;AAEA,kBAAkB,WAAW,GAAG;IAC9B,OAAO;QACL,WAAW;IACb;AACF;AAEA,SAAS,eAAe,CAAC,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAO;IACnD,MAAM,EAAC,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAC,GAAG;IAC1C,MAAM,UAAU,QAAQ;IACxB,MAAM,UAAU,SAAS;IAEzB,IAAI,WAAW,KAAK,WAAW,GAAG;QAChC,OAAO;IACT;IACA,gGAAgG;IAChG,MAAM,QAAQ,CAAA,GAAA,wMAAA,CAAA,YAAS,AAAD,EAAE,YAAY;IACpC,MAAM,WAAW,KAAK,GAAG,CAAC;IAC1B,MAAM,WAAW,KAAK,GAAG,CAAC;IAC1B,MAAM,IAAI,KAAK,GAAG,CAAC,WAAW,CAAC,EAAE,CAAC,GAAG,OAAO,IAAI,WAAW,CAAC,EAAE,CAAC,GAAG,OAAO,GAAG;IAC5E,MAAM,IAAI,KAAK,GAAG,CAAC,WAAW,CAAC,EAAE,CAAC,GAAG,OAAO,IAAI,WAAW,CAAC,EAAE,CAAC,GAAG,OAAO,GAAG;IAC5E,OAAO,AAAC,IAAI,KAAK,GAAG,CAAC,UAAU,SAAS,KAAO,IAAI,KAAK,GAAG,CAAC,UAAU,SAAS,MAAO;AACxF;AAEA,MAAM,wBAAwB,+JAAA,CAAA,UAAO;IAEnC,QAAQ,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,gBAAgB,EAAE;QAC9C,MAAM,EAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,KAAK,EAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;YAAC;YAAK;YAAK;YAAM;YAAM;SAAQ,EAAE;QAC7E,MAAM,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,IAAI;QACzE,IAAI,SAAS,OAAO,SAAS,KAAK;YAChC,OAAO,aAAa;gBAAC,GAAG;gBAAQ,GAAG;YAAM,GAAG,IAAI,CAAC,cAAc,CAAC,mBAAmB,QAAQ,GAAG;QAChG;QACA,MAAM,QAAQ,SAAS,MAAM;YAAC,OAAO;YAAG,KAAK;YAAI,OAAO;QAAM,IAAI;YAAC,OAAO;YAAG,KAAK;YAAI,OAAO;QAAM;QACnG,OAAO,QAAQ,OAAO;IACxB;IAEA,eAAe,gBAAgB,EAAE;QAC/B,OAAO,sBAAsB,IAAI,EAAE;IACrC;IAEA,KAAK,GAAG,EAAE;QACR,MAAM,UAAU,IAAI,CAAC,OAAO;QAC5B,MAAM,cAAc,QAAQ,WAAW;QACvC,IAAI,QAAQ,MAAM,GAAG,KAAK;YACxB;QACF;QACA,IAAI,IAAI;QACR,IAAI,SAAS,GAAG,QAAQ,eAAe;QACvC,eAAe,KAAK;QACpB,MAAM,SAAS,eAAe,KAAK;QACnC,UAAU,KAAK,IAAI,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO;QAC/C,IAAI,UAAU,CAAC,gBAAgB,QAAQ,UAAU,GAAG;YAClD,IAAI,WAAW,GAAG,QAAQ,iBAAiB;YAC3C,IAAI,MAAM;QACZ;QACA,IAAI,OAAO;QACX,QAAQ,WAAW,GAAG;IACxB;IAEA,yBAAyB,KAAK,EAAE,OAAO,EAAE;QACvC,MAAM,aAAa,uBAAuB,OAAO;QACjD,WAAW,cAAc,GAAG,wBAAwB,OAAO,YAAY;QACvE,OAAO;IACT;AACF;AAEA,gBAAgB,EAAE,GAAG;AAErB,gBAAgB,QAAQ,GAAG;IACzB,kBAAkB;IAClB,uBAAuB;IACvB,YAAY,EAAE;IACd,kBAAkB;IAClB,mBAAmB;IACnB,aAAa;IACb,SAAS;IACT,cAAc;IACd,MAAM;IACN,YAAY;IACZ,QAAQ;IACR,UAAU;IACV,YAAY;IACZ,eAAe;IACf,eAAe;IACf,SAAS;IACT,MAAM;IACN,MAAM;IACN,UAAU;IACV,QAAQ;IACR,SAAS;IACT,MAAM;IACN,MAAM;IACN,UAAU;IACV,QAAQ;IACR,GAAG;AACL;AAEA,gBAAgB,aAAa,GAAG;IAC9B,aAAa;IACb,iBAAiB;AACnB;AAEA,MAAM,0BAA0B,+JAAA,CAAA,UAAO;IAErC,QAAQ,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,gBAAgB,EAAE;QAC9C,IAAI,SAAS,OAAO,SAAS,KAAK;YAChC,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,KAAK,iBAAiB,IAAI,CAAC,QAAQ,EAAE,QAAQ,QAAQ;QACnH;QACA,MAAM,eAAe,QAAQ;YAAC,GAAG;YAAQ,GAAG;QAAM,GAAG,IAAI,CAAC,cAAc,CAAC,mBAAmB,CAAA,GAAA,wMAAA,CAAA,YAAS,AAAD,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ;QAC5H,MAAM,aAAa,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,QAAU,SAAS,MAAM,MAAM,EAAE,GAAG,MAAM,EAAE;QAClF,MAAM,QAAQ,KAAK,GAAG,IAAI;QAC1B,MAAM,MAAM,KAAK,GAAG,IAAI;QACxB,OAAO,YAAY,CAAC,KAAK,IAAI,SAAS,YAAY,CAAC,KAAK,IAAI;IAC9D;IAEA,eAAe,gBAAgB,EAAE;QAC/B,OAAO,sBAAsB,IAAI,EAAE;IACrC;IAEA,KAAK,GAAG,EAAE;QACR,MAAM,EAAC,QAAQ,EAAE,OAAO,EAAC,GAAG,IAAI;QAChC,IAAI,IAAI;QACR,IAAI,SAAS;QACb,IAAI,SAAS,GAAG,QAAQ,eAAe;QACvC,eAAe,KAAK;QACpB,MAAM,SAAS,eAAe,KAAK;QACnC,IAAI,QAAQ;QACZ,KAAK,MAAM,MAAM,SAAU;YACzB,IAAI,OAAO;gBACT,IAAI,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC;gBACrB,QAAQ;YACV,OAAO;gBACL,IAAI,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC;YACvB;QACF;QACA,IAAI,SAAS;QACb,IAAI,IAAI;QACR,8BAA8B;QAC9B,IAAI,QAAQ;YACV,IAAI,WAAW,GAAG,QAAQ,iBAAiB;YAC3C,IAAI,MAAM;QACZ;QACA,IAAI,OAAO;IACb;IAEA,yBAAyB,KAAK,EAAE,OAAO,EAAE;QACvC,MAAM,aAAa,uBAAuB,OAAO;QACjD,MAAM,EAAC,KAAK,EAAE,QAAQ,EAAC,GAAG;QAC1B,MAAM,WAAW,EAAE;QACnB,MAAM,QAAQ,AAAC,IAAI,iMAAA,CAAA,KAAE,GAAI;QACzB,IAAI,MAAM,WAAW,2MAAA,CAAA,cAAW;QAChC,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,KAAK,OAAO,MAAO;YAC5C,MAAM,UAAU,kBAAkB,YAAY,SAAS;YACvD,QAAQ,cAAc,GAAG,wBAAwB,OAAO,YAAY;YACpE,SAAS,IAAI,CAAC;QAChB;QACA,WAAW,QAAQ,GAAG;QACtB,OAAO;IACT;AACF;AAEA,kBAAkB,EAAE,GAAG;AAEvB,kBAAkB,QAAQ,GAAG;IAC3B,kBAAkB;IAClB,uBAAuB;IACvB,gBAAgB;IAChB,YAAY,EAAE;IACd,kBAAkB;IAClB,iBAAiB;IACjB,mBAAmB;IACnB,aAAa;IACb,SAAS;IACT,cAAc;IACd,MAAM;IACN,OAAO;QACL,QAAQ;IACV;IACA,QAAQ;IACR,UAAU;IACV,YAAY;IACZ,eAAe;IACf,eAAe;IACf,OAAO;IACP,SAAS;IACT,MAAM;IACN,MAAM;IACN,UAAU;IACV,QAAQ;IACR,SAAS;IACT,MAAM;IACN,MAAM;IACN,UAAU;IACV,QAAQ;IACR,GAAG;AACL;AAEA,kBAAkB,aAAa,GAAG;IAChC,aAAa;IACb,iBAAiB;AACnB;AAEA,SAAS,kBAAkB,EAAC,OAAO,EAAE,OAAO,EAAC,EAAE,EAAC,MAAM,EAAE,WAAW,EAAE,YAAY,EAAC,EAAE,GAAG;IACrF,MAAM,UAAU,CAAC,cAAc,YAAY,IAAI;IAC/C,MAAM,MAAM,KAAK,GAAG,CAAC;IACrB,MAAM,MAAM,KAAK,GAAG,CAAC;IACrB,MAAM,QAAQ;QAAC,GAAG,UAAU,MAAM;QAAQ,GAAG,UAAU,MAAM;IAAM;IACnE,OAAO;QACL,MAAM;QACN,aAAa;QACb,YAAY;YACV,GAAG,MAAM,CAAC;YACV,GAAG,MAAM,CAAC;YACV,SAAS,MAAM,CAAC;YAChB,SAAS,MAAM,CAAC;YAChB,IAAI,UAAU,MAAM,CAAC,SAAS,OAAO;YACrC,IAAI,UAAU,MAAM,CAAC,SAAS,OAAO;QACvC;IACF;AACF;AAEA,SAAS,iBAAiB,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,gBAAgB;IACtD,IAAI,WAAW;IACf,IAAI,IAAI,MAAM,CAAC,OAAO,MAAM,GAAG,EAAE,CAAC,QAAQ,CAAC;QAAC;QAAM;KAAK,EAAE;IACzD,KAAK,MAAM,SAAS,OAAQ;QAC1B,MAAM,IAAI,MAAM,QAAQ,CAAC;YAAC;YAAM;SAAK,EAAE;QACvC,IAAI,AAAC,EAAE,EAAE,GAAG,MAAQ,EAAE,EAAE,GAAG,KAAM,IAAI,CAAC,EAAE,EAAE,GAAG,EAAE,EAAE,IAAI,CAAC,IAAI,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,GAAG,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE;YACtF,WAAW,CAAC;QACd;QACA,IAAI;IACN;IACA,OAAO;AACT;AAEA,MAAM,kBAAkB;IACtB,KAAK;IACL,eAAe;IACf,SAAS;IACT,OAAO;IACP,MAAM;IACN,OAAO;IACP,SAAS;AACX;AAEA;;;;;;;CAOC,GACD,OAAO,IAAI,CAAC,iBAAiB,OAAO,CAAC,CAAA;IACnC,uMAAA,CAAA,WAAQ,CAAC,QAAQ,CAAC,CAAC,SAAS,EAAE,eAAe,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE;QACvD,WAAW;IACb;AACF;AAEA,MAAM,gBAAgB;IACpB,QAAQ,OAAO,MAAM;AACvB;AAEA,MAAM,UAAU,WAAW,MAAM,CAAC;AAClC,MAAM,UAAU,CAAC,OAAO,UAAY,CAAA,GAAA,uMAAA,CAAA,WAAQ,AAAD,EAAE,WAAW,WAAW,OAAO,WAAW;AAGrF;;;;CAIC,GAED;;;CAGC,GACD,MAAM,cAAc,CAAC,OAAS,SAAS,WAAW,SAAS;AAE3D;;;;CAIC,GACD,SAAS,YAAY,OAAO,MAAM;IAChC,IAAI,eAAe,CAAC,KAAK,EAAE;QACzB,OAAO;IACT;IACA,QAAQ,IAAI,CAAC,CAAC,0BAA0B,EAAE,KAAK,uBAAuB,CAAC;IACvE,OAAO;AACT;AAEA;;;;;CAKC,GACD,SAAS,eAAe,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,IAAI;IACjD,MAAM,aAAa,kBAAkB,OAAO,QAAQ,UAAU,EAAE;IAEhE,MAAM,cAAc,MAAM,WAAW;IACrC,MAAM,WAAW,eAAe,MAAM,QAAQ,EAAE;IAEhD,IAAK,IAAI,IAAI,GAAG,IAAI,YAAY,MAAM,EAAE,IAAK;QAC3C,MAAM,oBAAoB,WAAW,CAAC,EAAE;QACxC,MAAM,UAAU,mBAAmB,UAAU,GAAG,kBAAkB,IAAI;QACtE,MAAM,WAAW,kBAAkB,UAAU,CAAC,WAAW,OAAO,SAAS,UAAU;QACnF,MAAM,aAAa,QAAQ,wBAAwB,CAAC,OAAO;QAE3D,WAAW,IAAI,GAAG,OAAO;QAEzB,IAAI,cAAc,YAAY;YAC5B,kBAAkB,SAAS,WAAW,QAAQ,EAAE,UAAU;YAC1D,6EAA6E;YAC7E,2CAA2C;YAC3C,OAAO,WAAW,QAAQ;QAC5B;QAEA,IAAI,CAAC,CAAA,GAAA,sMAAA,CAAA,UAAO,AAAD,EAAE,QAAQ,CAAC,GAAG;YACvB,uEAAuE;YACvE,+EAA+E;YAC/E,2EAA2E;YAC3E,4BAA4B;YAC5B,OAAO,MAAM,CAAC,SAAS;QACzB;QAEA,OAAO,MAAM,CAAC,SAAS,WAAW,cAAc;QAChD,WAAW,OAAO,GAAG,yBAAyB;QAE9C,WAAW,MAAM,CAAC,SAAS;IAC7B;AACF;AAEA,SAAS,OAAO,UAAU;IACxB,OAAO,MAAM,WAAW,CAAC,KAAK,MAAM,WAAW,CAAC;AAClD;AAEA,SAAS,kBAAkB,KAAK,EAAE,QAAQ,EAAE,IAAI;IAC9C,IAAI,SAAS,WAAW,SAAS,UAAU,SAAS,UAAU;QAC5D,OAAO;IACT;IACA,OAAO,IAAI,+JAAA,CAAA,aAAU,CAAC,OAAO;AAC/B;AAEA,SAAS,kBAAkB,WAAW,EAAE,QAAQ,EAAE,QAAQ,EAAE,UAAU;IACpE,MAAM,cAAc,YAAY,QAAQ,IAAI,CAAC,YAAY,QAAQ,GAAG,EAAE;IACtE,YAAY,MAAM,GAAG,SAAS,MAAM;IACpC,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,MAAM,EAAE,IAAK;QACxC,MAAM,aAAa,QAAQ,CAAC,EAAE;QAC9B,MAAM,aAAa,WAAW,UAAU;QACxC,MAAM,aAAa,mBAAmB,aAAa,GAAG,WAAW,IAAI,EAAE,WAAW,cAAc;QAChG,MAAM,cAAc,QAAQ,CAAC,WAAW,WAAW,CAAC,CAAC,QAAQ,CAAC;QAC9D,WAAW,OAAO,GAAG,yBAAyB;QAC9C,WAAW,MAAM,CAAC,YAAY;IAChC;AACF;AAEA,SAAS,mBAAmB,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,cAAc;IAC/D,MAAM,eAAe,eAAe,CAAC,YAAY,MAAM;IACvD,IAAI,UAAU,QAAQ,CAAC,MAAM;IAC7B,IAAI,CAAC,WAAW,CAAC,CAAC,mBAAmB,YAAY,GAAG;QAClD,UAAU,QAAQ,CAAC,MAAM,GAAG,IAAI;QAChC,OAAO,MAAM,CAAC,SAAS;IACzB;IACA,OAAO;AACT;AAEA,SAAS,yBAAyB,QAAQ;IACxC,MAAM,eAAe,eAAe,CAAC,YAAY,SAAS,IAAI,EAAE;IAChE,MAAM,SAAS,CAAC;IAChB,OAAO,EAAE,GAAG,SAAS,EAAE;IACvB,OAAO,IAAI,GAAG,SAAS,IAAI;IAC3B,OAAO,QAAQ,GAAG,SAAS,QAAQ;IACnC,OAAO,MAAM,CAAC,QACZ,WAAW,UAAU,aAAa,QAAQ,GAC1C,WAAW,UAAU,aAAa,aAAa;IACjD,KAAK,MAAM,QAAQ,QAAS;QAC1B,MAAM,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK;IAC/B;IACA,OAAO;AACT;AAEA,SAAS,WAAW,QAAQ,EAAE,IAAI;IAChC,MAAM,SAAS,CAAC;IAChB,KAAK,MAAM,QAAQ,OAAO,IAAI,CAAC,MAAO;QACpC,MAAM,UAAU,IAAI,CAAC,KAAK;QAC1B,MAAM,QAAQ,QAAQ,CAAC,KAAK;QAC5B,IAAI,YAAY,SAAS,CAAA,GAAA,sMAAA,CAAA,UAAO,AAAD,EAAE,QAAQ;YACvC,MAAM,CAAC,KAAK,GAAG,MAAM,GAAG,CAAC,CAAC,OAAS,QAAQ,MAAM;QACnD,OAAO;YACL,MAAM,CAAC,KAAK,GAAG,QAAQ,OAAO;QAChC;IACF;IACA,OAAO;AACT;AAEA,SAAS,WAAW,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,UAAU;IACtD,OAAO,QAAQ,QAAQ,IAAI,CAAC,QAAQ,QAAQ,GAAG,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,MAAM,UAAU,KAAK;QAC9F;QACA,IAAI,YAAW;YACb,OAAO,SAAS,MAAM,CAAC,CAAC,KAAO,MAAM,GAAG,OAAO;QACjD;QACA,IAAI,WAAW,EAAE;QACjB,MAAM;IACR,EAAE;AACJ;AAEA,SAAS,eAAe,QAAQ,EAAE,WAAW;IAC3C,MAAM,QAAQ,YAAY,MAAM;IAChC,MAAM,QAAQ,SAAS,MAAM;IAE7B,IAAI,QAAQ,OAAO;QACjB,MAAM,MAAM,QAAQ;QACpB,SAAS,MAAM,CAAC,OAAO,MAAM,IAAI,MAAM;IACzC,OAAO,IAAI,QAAQ,OAAO;QACxB,SAAS,MAAM,CAAC,OAAO,QAAQ;IACjC;IACA,OAAO;AACT;AAEA,IAAI,UAAU;AAEd,MAAM,cAAc,IAAI;AACxB,MAAM,qBAAqB,CAAA,aAAc,WAAW,IAAI,KAAK;AAC7D,MAAM,QAAQ,WAAW,MAAM,CAAC;AAEhC,IAAI,aAAa;IACf,IAAI;IAEJ;IAEA;QACE,eAAe,YAAY,OAAO,+JAAA,CAAA,QAAK,CAAC,OAAO;IACjD;IAEA;QACE,+JAAA,CAAA,QAAK,CAAC,QAAQ,CAAC;IACjB;IAEA;QACE,+JAAA,CAAA,QAAK,CAAC,UAAU,CAAC;IACnB;IAEA,YAAW,KAAK;QACd,YAAY,GAAG,CAAC,OAAO;YACrB,aAAa,EAAE;YACf,UAAU,EAAE;YACZ,iBAAiB,EAAE;YACnB,WAAW,CAAC;YACZ,UAAU;YACV,cAAc;YACd,OAAO,CAAC;YACR,QAAQ;YACR,SAAS,EAAE;QACb;IACF;IAEA,cAAa,KAAK,EAAE,IAAI,EAAE,OAAO;QAC/B,MAAM,QAAQ,YAAY,GAAG,CAAC;QAC9B,MAAM,cAAc,MAAM,WAAW,GAAG,EAAE;QAE1C,IAAI,oBAAoB,QAAQ,WAAW;QAC3C,IAAI,CAAA,GAAA,uMAAA,CAAA,WAAQ,AAAD,EAAE,oBAAoB;YAC/B,OAAO,IAAI,CAAC,mBAAmB,OAAO,CAAC,CAAA;gBACrC,MAAM,QAAQ,iBAAiB,CAAC,IAAI;gBACpC,IAAI,CAAA,GAAA,uMAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ;oBACnB,MAAM,EAAE,GAAG;oBACX,YAAY,IAAI,CAAC;gBACnB;YACF;QACF,OAAO,IAAI,CAAA,GAAA,sMAAA,CAAA,UAAO,AAAD,EAAE,oBAAoB;YACrC,YAAY,IAAI,IAAI;QACtB;QACA,mBAAmB,YAAY,MAAM,CAAC,qBAAqB,MAAM,MAAM;IACzE;IAEA,iBAAgB,KAAK,EAAE,IAAI;QACzB,MAAM,QAAQ,YAAY,GAAG,CAAC;QAC9B,iBAAiB,OAAO,KAAK,KAAK,EAAE,MAAM,WAAW,CAAC,MAAM,CAAC,oBAAoB,MAAM,CAAC,CAAA,IAAK,EAAE,OAAO,IAAI,EAAE,gBAAgB;IAC9H;IAEA,aAAY,KAAK,EAAE,IAAI,EAAE,OAAO;QAC9B,MAAM,QAAQ,YAAY,GAAG,CAAC;QAC9B,gBAAgB,OAAO,OAAO;QAC9B,eAAe,OAAO,OAAO,SAAS,KAAK,IAAI;QAC/C,MAAM,eAAe,GAAG,MAAM,QAAQ,CAAC,MAAM,CAAC,CAAA,KAAM,CAAC,GAAG,IAAI,IAAI,GAAG,OAAO,CAAC,OAAO;QAClF,YAAY,OAAO,OAAO;IAC5B;IAEA,oBAAmB,KAAK,EAAE,KAAK,EAAE,OAAO;QACtC,KAAK,OAAO,sBAAsB,QAAQ,IAAI;IAChD;IAEA,mBAAkB,KAAK,EAAE,KAAK,EAAE,OAAO;QACrC,KAAK,OAAO,qBAAqB,QAAQ,IAAI;IAC/C;IAEA,mBAAkB,KAAK,EAAE,KAAK,EAAE,OAAO;QACrC,KAAK,OAAO,MAAM,KAAK,EAAE,QAAQ,IAAI;IACvC;IAEA,YAAW,KAAK,EAAE,KAAK,EAAE,OAAO;QAC9B,KAAK,OAAO,cAAc,QAAQ,IAAI;IACxC;IAEA,WAAU,KAAK,EAAE,KAAK,EAAE,OAAO;QAC7B,KAAK,OAAO,aAAa,QAAQ,IAAI;IACvC;IAEA,aAAY,KAAK,EAAE,IAAI,EAAE,OAAO;QAC9B,MAAM,QAAQ,YAAY,GAAG,CAAC;QAC9B,IAAI,YAAY,OAAO,KAAK,KAAK,EAAE,UAAU;YAC3C,KAAK,OAAO,GAAG;QACjB;IACF;IAEA,cAAa,KAAK;QAChB,YAAY,MAAM,CAAC;IACrB;IAEA,gBAAe,KAAK;QAClB,MAAM,QAAQ,YAAY,GAAG,CAAC;QAC9B,OAAO,QAAQ,MAAM,QAAQ,GAAG,EAAE;IACpC;IAEA,mBAAmB;IACnB,sCAAqC,eAAe,EAAE,KAAK,EAAE,OAAO;QAClE,OAAO,YAAY,iBAAiB,OAAO;IAC7C;IAEA,UAAU;QACR,YAAY;YACV,SAAS;gBACP,YAAY;oBAAC;oBAAK;oBAAK;oBAAM;oBAAM;oBAAS;oBAAU;oBAAW;oBAAW;oBAAU;oBAAU;iBAAS;gBACzG,MAAM;YACR;YACA,QAAQ;gBACN,YAAY;oBAAC;oBAAmB;iBAAc;gBAC9C,MAAM;YACR;QACF;QACA,MAAM;QACN,aAAa;YACX,MAAM;YACN,MAAM;YACN,WAAW;QACb;QACA,QAAQ;YACN,UAAU;YACV,MAAM;YACN,OAAO,CACP;QACF;IACF;IAEA,aAAa;QACX,YAAY;QACZ,aAAa,CAAC,OAAS,CAAC,MAAM,QAAQ,CAAC,SAAS,SAAS;QACzD,aAAa;YACX,UAAU;YACV,WAAW,CAAC,MAAM,OAAS,CAAC,SAAS,EAAE,eAAe,CAAC,YAAY,KAAK,IAAI,EAAE,CAAC,EAAE,EAAE;QACrF;QACA,aAAa;YACX,WAAW;QACb;QACA,QAAQ;YACN,OAAO;gBACL,YAAY;gBACZ,WAAW;YACb;YACA,YAAY;QACd;IACF;IAEA,wBAAwB;QAAC;KAAG;AAC9B;AAEA,SAAS,KAAK,KAAK,EAAE,MAAM,EAAE,IAAI;IAC/B,MAAM,EAAC,GAAG,EAAE,SAAS,EAAC,GAAG;IACzB,MAAM,QAAQ,YAAY,GAAG,CAAC;IAE9B,IAAI,MAAM;QACR,CAAA,GAAA,uMAAA,CAAA,WAAQ,AAAD,EAAE,KAAK;IAChB;IAEA,MAAM,mBAAmB,oBAAoB,MAAM,eAAe,EAAE,QAAQ,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC;IACpI,KAAK,MAAM,QAAQ,iBAAkB;QACnC,YAAY,KAAK,WAAW,OAAO;IACrC;IAEA,IAAI,MAAM;QACR,CAAA,GAAA,0MAAA,CAAA,aAAU,AAAD,EAAE;IACb;AACF;AAEA,SAAS,oBAAoB,QAAQ,EAAE,MAAM;IAC3C,MAAM,mBAAmB,EAAE;IAC3B,KAAK,MAAM,MAAM,SAAU;QACzB,IAAI,GAAG,OAAO,CAAC,QAAQ,KAAK,QAAQ;YAClC,iBAAiB,IAAI,CAAC;gBAAC,SAAS;gBAAI,MAAM;YAAI;QAChD;QACA,IAAI,GAAG,QAAQ,IAAI,GAAG,QAAQ,CAAC,MAAM,EAAE;YACrC,KAAK,MAAM,OAAO,GAAG,QAAQ,CAAE;gBAC7B,IAAI,IAAI,OAAO,CAAC,OAAO,IAAI,IAAI,OAAO,CAAC,QAAQ,KAAK,QAAQ;oBAC1D,iBAAiB,IAAI,CAAC;wBAAC,SAAS;oBAAG;gBACrC;YACF;QACF;IACF;IACA,OAAO;AACT;AAEA,SAAS,YAAY,GAAG,EAAE,SAAS,EAAE,KAAK,EAAE,IAAI;IAC9C,MAAM,KAAK,KAAK,OAAO;IACvB,IAAI,KAAK,IAAI,EAAE;QACb,WAAW,OAAO,IAAI;QACtB,GAAG,IAAI,CAAC,KAAK;QACb,WAAW,OAAO,IAAI;IACxB,OAAO;QACL,GAAG,IAAI,CAAC,KAAK;IACf;AACF", "ignoreList": [0], "debugId": null}}]}