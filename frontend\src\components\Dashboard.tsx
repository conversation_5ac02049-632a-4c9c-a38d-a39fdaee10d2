import React, { useEffect, useState } from 'react';
import apiClient from './ApiClient';
import { Doughn<PERSON> } from 'react-chartjs-2';
import { Chart as ChartJS, ArcElement, Tooltip, Legend, ChartOptions } from 'chart.js';
import { ProgressBar } from 'react-bootstrap';

ChartJS.register(ArcE<PERSON>, Tooltip, Legend);

interface DashboardData {
  net_patrimoine: number;
  total_assets: number;
  total_liabilities: number;
  allocation: { [key: string]: number };
}

interface AllocationTarget {
  category: string; // Display name
  categoryKey: string; // Unique key for matching
  currentValue: number;
  currentPercent: number;
  targetPercent: number; // This will become editable
  targetValue: number;
  amountToInvest: number;
  progressPercent: number;
}

interface FireAllocationTargetData {
  id: number;
  category_key: string;
  target_percentage: number;
}

const Dashboard: React.FC = () => {
  const [data, setData] = useState<DashboardData | null>(null);
  // State for editable fire allocation targets
  const [editableAllocations, setEditableAllocations] = useState<AllocationTarget[]>([]);
  const [initialTargetPercentages, setInitialTargetPercentages] = useState<Record<string, number>>({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [retryCount, setRetryCount] = useState(0);
  const [isSaving, setIsSaving] = useState(false);

  const FIRE_TARGET_AMOUNT = 910150; // Objectif FIRE global

  // Default allocations, to be used if API data is missing for a category
  const defaultTargetAllocationsConfig: Array<{ category: string; categoryKey: string; defaultTargetPercent: number }> = [
    { category: "Liquidité", categoryKey: "Liquidité", defaultTargetPercent: 2.5 },
    { category: "Bourse", categoryKey: "Bourse", defaultTargetPercent: 35.5 },
    { category: "Immobilier", categoryKey: "Immobilier", defaultTargetPercent: 35.0 },
    { category: "Fonds sécurisés", categoryKey: "Fonds sécurisés", defaultTargetPercent: 20.0 },
    { category: "Prêts participatifs", categoryKey: "Prêts participatifs", defaultTargetPercent: 2.0 },
    { category: "Crypto-Actifs", categoryKey: "Crypto-Actifs", defaultTargetPercent: 5.0 }
  ];

  const fetchAllData = () => {
    setLoading(true);
    setError(null);

    Promise.all([
      apiClient.get('/dashboard'),
      apiClient.get('/fire-allocation-targets/') // Removed /api prefix
    ])
    .then(([dashboardResponse, fireTargetsResponse]) => {
      const dashboardData = dashboardResponse.data;
      setData(dashboardData);

      const fireTargetsFromAPI: FireAllocationTargetData[] = fireTargetsResponse.data;
      const targetsMap = fireTargetsFromAPI.reduce((acc, current) => {
        acc[current.category_key] = current.target_percentage;
        return acc;
      }, {} as Record<string, number>);

      setInitialTargetPercentages(targetsMap); // Store initial values for reset

      // Initialize editableAllocations
      const processedAllocations = processAllocations(dashboardData, targetsMap);
      setEditableAllocations(processedAllocations);

      setLoading(false);
      setRetryCount(0);
    })
    .catch(err => {
      console.error('Error fetching data:', err);
      if (retryCount < 2) {
        setRetryCount(prev => prev + 1);
        setTimeout(() => fetchAllData(), 1000);
      } else {
        setError('Erreur lors de la récupération des données.');
        setLoading(false);
      }
    });
  };

  useEffect(() => {
    fetchAllData();
  }, []); // Runs once on mount

  const processAllocations = (
    dashboardData: DashboardData | null,
    apiTargetPercentages: Record<string, number>
  ): AllocationTarget[] => {
    if (!dashboardData) return [];

    return defaultTargetAllocationsConfig.map(defaultTarget => {
      const currentValue = dashboardData.allocation[defaultTarget.categoryKey] || 0;
      const currentPercent = dashboardData.total_assets > 0 ? (currentValue / dashboardData.total_assets) * 100 : 0;

      // Use API value if available, otherwise use default from config
      const targetPercent = apiTargetPercentages[defaultTarget.categoryKey] !== undefined
        ? apiTargetPercentages[defaultTarget.categoryKey]
        : defaultTarget.defaultTargetPercent;

      const targetValue = (FIRE_TARGET_AMOUNT * targetPercent) / 100;
      const amountToInvest = Math.max(0, targetValue - currentValue);
      const progressPercent = targetValue > 0 ? Math.min(100, (currentValue / targetValue) * 100) : 0;

      return {
        category: defaultTarget.category,
        categoryKey: defaultTarget.categoryKey,
        currentValue,
        currentPercent,
        targetPercent: targetPercent, // Initial value
        targetValue,
        amountToInvest,
        progressPercent
      };
    });
  };

  // Recalculate derived values when editableAllocations or dashboardData changes
  useEffect(() => {
    if (data) { // Ensure data is loaded
        const newProcessedAllocations = processAllocations(data, initialTargetPercentages); // Use initial for recalculation base
        // Now, merge any user edits from editableAllocations into newProcessedAllocations
        const updatedAllocationsWithUserEdits = newProcessedAllocations.map(processedAlloc => {
            const userEditedAlloc = editableAllocations.find(ea => ea.categoryKey === processedAlloc.categoryKey);
            if (userEditedAlloc) {
                // If user has edited this, use their targetPercent, then recalculate targetValue, amountToInvest, progressPercent
                const editedTargetPercent = userEditedAlloc.targetPercent;
                const targetValue = (FIRE_TARGET_AMOUNT * editedTargetPercent) / 100;
                const amountToInvest = Math.max(0, targetValue - processedAlloc.currentValue);
                const progressPercent = targetValue > 0 ? Math.min(100, (processedAlloc.currentValue / targetValue) * 100) : 0;
                return {
                    ...processedAlloc,
                    targetPercent: editedTargetPercent,
                    targetValue,
                    amountToInvest,
                    progressPercent,
                };
            }
            return processedAlloc;
        });
        // Only update if there are actual user edits, otherwise processAllocations already set it up
        if (editableAllocations.length > 0) {
             setEditableAllocations(updatedAllocationsWithUserEdits);
        }
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data]); // Dependency: data. Do not add editableAllocations here to avoid loops.


  const handleTargetPercentChange = (categoryKey: string, newTargetPercent: string) => {
    const numericValue = parseFloat(newTargetPercent);
    if (isNaN(numericValue) && newTargetPercent !== "" && newTargetPercent !== ".") return; // Allow empty or partial input

    setEditableAllocations(prevAllocations =>
      prevAllocations.map(alloc => {
        if (alloc.categoryKey === categoryKey) {
          const updatedTargetPercent = isNaN(numericValue) ? (newTargetPercent === "" ? 0 : alloc.targetPercent) : numericValue;
          const targetValue = (FIRE_TARGET_AMOUNT * updatedTargetPercent) / 100;
          const amountToInvest = Math.max(0, targetValue - alloc.currentValue);
          const progressPercent = targetValue > 0 ? Math.min(100, (alloc.currentValue / targetValue) * 100) : 0;
          return { ...alloc, targetPercent: updatedTargetPercent, targetValue, amountToInvest, progressPercent };
        }
        return alloc;
      })
    );
  };

  const handleSaveChanges = async () => {
    setIsSaving(true);
    setError(null);

    const totalTarget = editableAllocations.reduce((sum, alloc) => sum + (alloc.targetPercent || 0), 0);
    if (Math.abs(totalTarget - 100) > 0.1) { // Allow small tolerance for float precision
        // Consider using a modal or more prominent message for this warning
        if (!window.confirm(`Le total des pourcentages cibles est ${totalTarget.toFixed(1)}%, ce qui n'est pas égal à 100%. Voulez-vous continuer quand même ?`)) {
            setIsSaving(false);
            return;
        }
    }

    const payload = editableAllocations.map(alloc => ({
      category_key: alloc.categoryKey,
      target_percentage: alloc.targetPercent || 0 // Ensure it's a number, default to 0 if undefined/NaN
    }));

    try {
      await apiClient.post('/fire-allocation-targets/batch_update/', payload); // Removed /api prefix
      // Update initialTargetPercentages to reflect saved state
      const newInitialTargets = payload.reduce((acc, current) => {
        acc[current.category_key] = current.target_percentage;
        return acc;
      }, {} as Record<string, number>);
      setInitialTargetPercentages(newInitialTargets);
      // Optionally, show a success message
    } catch (err) {
      console.error("Erreur lors de la sauvegarde des allocations cibles:", err);
      setError("Erreur lors de la sauvegarde. Veuillez réessayer.");
    } finally {
      setIsSaving(false);
    }
  };

  const handleResetChanges = () => {
      if (!data) return;
      // Re-process allocations using the last saved (initial) percentages
      const resetAllocations = processAllocations(data, initialTargetPercentages);
      setEditableAllocations(resetAllocations);
  };

  const totalTargetPercentage = editableAllocations.reduce((sum, alloc) => sum + (alloc.targetPercent || 0), 0);

  // Check if there are unsaved changes
  const hasUnsavedChanges = editableAllocations.some(alloc => {
    const initialPercent = initialTargetPercentages[alloc.categoryKey];
    // Handle cases where initialPercent might be undefined (new category not yet in backend)
    // or if targetPercent is manipulated to be undefined/NaN by input
    const currentTarget = alloc.targetPercent || 0;
    const initialTarget = initialPercent === undefined ? (defaultTargetAllocationsConfig.find(d => d.categoryKey === alloc.categoryKey)?.defaultTargetPercent || 0) : initialPercent;
    return Math.abs(currentTarget - initialTarget) > 0.001; // Compare with tolerance
  });


  if (loading && !data) return <p>Chargement initial des données...</p>;
  if (error) return (
    <div>
      <p className="text-danger">{error}</p>
      <button className="btn btn-primary" onClick={fetchAllData}>
        Réessayer
      </button>
    </div>
  );
  if (!data) return <p>Aucune donnée disponible.</p>;

  // Configuration avancée du graphique avec couleurs améliorées et interactions
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR', maximumFractionDigits: 0 }).format(value);
  };

  // Couleurs thématiques pour chaque catégorie d'actif
  const categoryColors = {
    'Liquidité': { bg: '#17a2b8', border: '#138496', hover: '#20c997' },
    'Bourse': { bg: '#007bff', border: '#0056b3', hover: '#0069d9' },
    'Crypto-Actifs': { bg: '#fd7e14', border: '#e55a00', hover: '#ff8c42' },
    'Fonds sécurisés': { bg: '#28a745', border: '#1e7e34', hover: '#34ce57' },
    'Immobilier': { bg: '#6f42c1', border: '#59359a', hover: '#7952b3' },
    'Prêts participatifs': { bg: '#dc3545', border: '#bd2130', hover: '#e4606d' }
  };

  const labels = Object.keys(data.allocation);
  const values = Object.values(data.allocation);
  const total = values.reduce((sum, value) => sum + value, 0);

  const chartData = {
    labels: labels,
    datasets: [
      {
        label: 'Répartition du portefeuille',
        data: values,
        backgroundColor: labels.map(label => categoryColors[label as keyof typeof categoryColors]?.bg || '#6c757d'),
        borderColor: labels.map(label => categoryColors[label as keyof typeof categoryColors]?.border || '#495057'),
        hoverBackgroundColor: labels.map(label => categoryColors[label as keyof typeof categoryColors]?.hover || '#868e96'),
        borderWidth: 3,
        hoverBorderWidth: 4,
        borderRadius: 8,
        spacing: 4,
      },
    ],
  };

  const chartOptions: ChartOptions<'doughnut'> = {
    responsive: true,
    maintainAspectRatio: true,
    plugins: {
      legend: {
        position: 'bottom' as const,
        labels: {
          padding: 20,
          usePointStyle: true,
          pointStyle: 'circle',
          font: {
            size: 14,
            weight: 'bold' as const,
          },
          generateLabels: (chart) => {
            const data = chart.data;
            if (data.labels && data.datasets.length) {
              return data.labels.map((label, i) => {
                const value = data.datasets[0].data[i] as number;
                const percentage = total > 0 ? ((value / total) * 100).toFixed(1) : '0';
                const backgroundColor = Array.isArray(data.datasets[0].backgroundColor)
                  ? data.datasets[0].backgroundColor[i] as string
                  : data.datasets[0].backgroundColor as string;
                const borderColor = Array.isArray(data.datasets[0].borderColor)
                  ? data.datasets[0].borderColor[i] as string
                  : data.datasets[0].borderColor as string;
                return {
                  text: `${label} (${percentage}%)`,
                  fillStyle: backgroundColor,
                  strokeStyle: borderColor,
                  lineWidth: 2,
                  hidden: false,
                  index: i,
                };
              });
            }
            return [];
          },
        },
      },
      tooltip: {
        enabled: true,
        backgroundColor: 'rgba(0, 0, 0, 0.9)',
        titleColor: '#fff',
        bodyColor: '#fff',
        borderColor: '#fff',
        borderWidth: 1,
        cornerRadius: 8,
        displayColors: true,
        callbacks: {
          title: (context) => {
            return context[0].label || '';
          },
          label: (context) => {
            const value = context.parsed;
            const percentage = total > 0 ? ((value / total) * 100).toFixed(1) : '0';
            return [
              `Valeur: ${formatCurrency(value)}`,
              `Pourcentage: ${percentage}%`
            ];
          },
        },
      },
    },
    animation: {
      animateRotate: true,
      animateScale: true,
      duration: 1500,
      easing: 'easeInOutQuart',
    },
    interaction: {
      intersect: false,
      mode: 'index' as const,
    },
    onHover: (event, elements) => {
      if (event.native?.target) {
        (event.native.target as HTMLElement).style.cursor = elements.length > 0 ? 'pointer' : 'default';
      }
    },
    onClick: (event, elements) => {
      if (elements.length > 0) {
        const elementIndex = elements[0].index;
        const categoryName = labels[elementIndex];

        // Navigation vers la section appropriée selon la catégorie
        const navigationMap: { [key: string]: string } = {
          'Liquidité': 'assets',
          'Bourse': 'assets',
          'Crypto-Actifs': 'assets',
          'Fonds sécurisés': 'assets',
          'Immobilier': 'scpi',
          'Prêts participatifs': 'assets'
        };

        const targetSection = navigationMap[categoryName];
        if (targetSection) {
          // Afficher une alerte avec les détails (peut être remplacé par une navigation)
          const value = values[elementIndex];
          const percentage = total > 0 ? ((value / total) * 100).toFixed(1) : '0';
          alert(`${categoryName}\nValeur: ${formatCurrency(value)}\nPourcentage: ${percentage}%\n\nCliquez sur l'onglet "${targetSection === 'scpi' ? 'SCPI' : 'Patrimoine'}" pour gérer cette catégorie.`);
        }
      }
    },
  };

  return (
    <div>
      <h1 className="mb-4">Dashboard</h1>
      <div className="row">
        <div className="col-md-4">
          <div className="card text-white bg-primary mb-3">
            <div className="card-header">Patrimoine Net Total</div>
            <div className="card-body">
              <h5 className="card-title">{new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR' }).format(data.net_patrimoine)}</h5>
            </div>
          </div>
        </div>
        <div className="col-md-4">
          <div className="card text-white bg-success mb-3">
            <div className="card-header">Total Actifs</div>
            <div className="card-body">
              <h5 className="card-title">{new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR' }).format(data.total_assets)}</h5>
            </div>
          </div>
        </div>
        <div className="col-md-4">
          <div className="card text-white bg-danger mb-3">
            <div className="card-header">Total Passifs</div>
            <div className="card-body">
              <h5 className="card-title">{new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR' }).format(data.total_liabilities)}</h5>
            </div>
          </div>
        </div>
      </div>
      <div className="row mt-4">
        <div className="col-md-8 offset-md-2">
          <div className="text-center">
            <h2 className="mb-4">Répartition des Actifs</h2>
            <div style={{ maxWidth: '500px', margin: '0 auto' }}>
              <Doughnut data={chartData} options={chartOptions} />
            </div>
          </div>
        </div>
      </div>

      {/* Section Allocation Cible FIRE */}
      <div className="row mt-5">
        <div className="col-12">
          <div className="card">
            <div className="card-header">
              <h3 className="mb-0">🎯 Allocation Cible FIRE</h3>
              <small className="text-muted">
                Progression vers l'objectif de 910 150€ selon la stratégie documentée
              </small>
            </div>
            <div className="card-body">
              {error && <div className="alert alert-danger">{error}</div>}
              <div className="table-responsive">
                <table className="table table-striped table-hover">
                  <thead className="table-dark">
                    <tr>
                      <th>Catégorie d'Actif</th>
                      <th className="text-end">Valeur Actuelle</th>
                      <th className="text-end">% Actuel</th>
                      <th className="text-end" style={{minWidth: "100px"}}>% Cible</th>
                      <th className="text-end">Valeur Cible</th>
                      <th className="text-end">Montant à Investir</th>
                      <th className="text-center">Progression</th>
                    </tr>
                  </thead>
                  <tbody>
                    {editableAllocations.map((target, index) => (
                      <tr key={target.categoryKey}>
                        <td><strong>{target.category}</strong></td>
                        <td className="text-end">
                          {formatCurrency(target.currentValue)}
                        </td>
                        <td className="text-end">
                          {target.currentPercent.toFixed(1)}%
                        </td>
                        <td className="text-end">
                          <input
                            type="number"
                            value={target.targetPercent === undefined ? '' : target.targetPercent.toString()}
                            onChange={(e) => handleTargetPercentChange(target.categoryKey, e.target.value)}
                            className="form-control form-control-sm text-end"
                            style={{ width: '70px', display: 'inline-block' }}
                            step="0.1"
                            min="0"
                            max="100"
                          /> %
                        </td>
                        <td className="text-end">
                          {formatCurrency(target.targetValue)}
                        </td>
                        <td className="text-end">
                          {target.amountToInvest > 0 ? (
                            <span className="text-warning">
                              {formatCurrency(target.amountToInvest)}
                            </span>
                          ) : (
                            <span className="text-success">Objectif atteint</span>
                          )}
                        </td>
                        <td className="text-center" style={{ width: '150px' }}>
                          <ProgressBar
                            now={target.progressPercent}
                            variant={target.progressPercent >= 100 ? 'success' : target.progressPercent >= 75 ? 'info' : target.progressPercent >= 50 ? 'warning' : 'danger'}
                            style={{ height: '20px' }}
                            label={`${target.progressPercent.toFixed(0)}%`}
                          />
                        </td>
                      </tr>
                    ))}
                  </tbody>
                  <tfoot>
                    <tr className="table-info">
                      <th>TOTAL</th>
                      <th className="text-end">
                        {data ? formatCurrency(data.total_assets) : 'N/A'}
                      </th>
                      <th className="text-end">100%</th>
                      <th className="text-end">
                        <strong>{totalTargetPercentage.toFixed(1)}%</strong>
                        {Math.abs(totalTargetPercentage - 100) > 0.1 && <span className="text-danger ms-1">(≠100%)</span>}
                      </th>
                      <th className="text-end">
                        {data ? formatCurrency(FIRE_TARGET_AMOUNT) : 'N/A'}
                      </th>
                      <th className="text-end">
                        <strong className="text-primary">
                          {data ? formatCurrency(Math.max(0, FIRE_TARGET_AMOUNT - data.total_assets)) : 'N/A'}
                        </strong>
                      </th>
                      <th className="text-center">
                        <strong>
                          {data ? ((data.total_assets / FIRE_TARGET_AMOUNT) * 100).toFixed(1) : 'N/A'}%
                        </strong>
                      </th>
                    </tr>
                  </tfoot>
                </table>
              </div>

              {hasUnsavedChanges && (
                <div className="alert alert-warning mt-3">
                  Vous avez des modifications non enregistrées.
                </div>
              )}

              <div className="mt-3 d-flex justify-content-end">
                <button
                    className="btn btn-secondary me-2"
                    onClick={handleResetChanges}
                    disabled={!hasUnsavedChanges || isSaving}
                >
                    Réinitialiser
                </button>
                <button
                    className="btn btn-primary"
                    onClick={handleSaveChanges}
                    disabled={isSaving || !hasUnsavedChanges}
                >
                  {isSaving ? 'Sauvegarde...' : 'Sauvegarder les % Cibles'}
                </button>
              </div>

              {/* Résumé et recommandations */}
              <div className="row mt-4">
                <div className="col-md-4">
                  <div className="card text-white bg-info">
                    <div className="card-body text-center">
                      <h5>Progression Globale</h5>
                      <h4>{((data.total_assets / 910150) * 100).toFixed(1)}%</h4>
                      <small>vers l'objectif FIRE</small>
                    </div>
                  </div>
                </div>
                <div className="col-md-4">
                  <div className="card text-white bg-warning">
                    <div className="card-body text-center">
                      <h5>Capital Restant</h5>
                      <h4>{new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR', maximumFractionDigits: 0 }).format(Math.max(0, 910150 - data.total_assets))}</h4>
                      <small>à investir</small>
                    </div>
                  </div>
                </div>
                <div className="col-md-4">
                  <div className="card text-white bg-success">
                    <div className="card-body text-center">
                      <h5>Estimation</h5>
                      <h4>{Math.max(0, Math.ceil((910150 - data.total_assets) / (3415 * 12)))} ans</h4>
                      <small>à 3 415€/mois</small>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;