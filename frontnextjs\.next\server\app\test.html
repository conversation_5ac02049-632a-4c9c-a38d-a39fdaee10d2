<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="/_next/static/css/ef46db3751d8e999.css" data-precedence="next"/><link rel="stylesheet" href="/_next/static/css/991efdcd1ebedf92.css" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-42b02186dec27933.js"/><script src="/_next/static/chunks/4bd1b696-982cea289f4a0654.js" async=""></script><script src="/_next/static/chunks/684-de6d5eb66e77825d.js" async=""></script><script src="/_next/static/chunks/main-app-3e72c9d7c28ba4c3.js" async=""></script><script src="/_next/static/chunks/app/layout-70969b29acae8f7f.js" async=""></script><script src="/_next/static/chunks/464-be04f8198da07724.js" async=""></script><script src="/_next/static/chunks/app/test/page-25683739198c6309.js" async=""></script><meta name="theme-color" content="#000000"/><title>FIRE Dashboard</title><meta name="description" content="Application pour le suivi de l&#x27;objectif FIRE (Financial Independence, Retire Early)"/><link rel="manifest" href="/manifest.json"/><link rel="icon" href="/favicon.ico" type="image/x-icon" sizes="16x16"/><link rel="icon" href="/icon.png"/><link rel="apple-touch-icon" href="/apple-icon.png"/><script>document.querySelectorAll('body link[rel="icon"], body link[rel="apple-touch-icon"]').forEach(el => document.head.appendChild(el))</script><script src="/_next/static/chunks/polyfills-42372ed130431b0a.js" noModule=""></script></head><body><div hidden=""><!--$--><!--/$--></div><div class="App"><nav class="navbar navbar-expand-lg navbar-dark bg-dark"><div class="container-fluid"><a class="navbar-brand" href="/">FIRE Dashboard</a><button class="navbar-toggler" type="button" aria-controls="navbarNavContent" aria-expanded="false" aria-label="Toggle navigation"><span class="navbar-toggler-icon"></span></button><div class="collapse navbar-collapse " id="navbarNavContent"><ul class="navbar-nav me-auto mb-2 mb-lg-0"><li class="nav-item"><a class="nav-link" href="/">Dashboard</a></li><li class="nav-item"><a class="nav-link" href="/assets">Patrimoine</a></li><li class="nav-item"><a class="nav-link" href="/liabilities">Emprunts</a></li><li class="nav-item"><a class="nav-link" href="/scpi">SCPI</a></li><li class="nav-item"><a class="nav-link" href="/evolution">Évolution</a></li><li class="nav-item"><a class="nav-link" href="/budget">Budget FIRE</a></li><li class="nav-item"><a class="nav-link" href="/scenarios">Scénarios</a></li><li class="nav-item"><a class="nav-link" href="/fire">Objectif FIRE</a></li><li class="nav-item"><a class="nav-link" href="/test">Test API</a></li></ul></div></div></nav><main class="container mt-4"><div class="container mt-4"><h2>Test des Appels API vers le Backend</h2><p>Cet outil permet de vérifier la connectivité et les réponses de base du backend FastAPI. Assurez-vous que le serveur backend (<code>cd backend &amp;&amp; uvicorn main:app --reload</code>) est en cours d&#x27;exécution sur <code>http://localhost:8000</code>.</p><div class="mb-3"><button class="btn btn-primary me-2 mb-2">Test /dashboard (via apiClient Axios)</button><button class="btn btn-secondary me-2 mb-2">Test /dashboard (via fetch direct sur localhost:8000)</button><button class="btn btn-info mb-2">Test /dashboard (via fetch direct sur 127.0.0.1:8000)</button></div><pre class="bg-light p-3 border rounded" style="white-space:pre-wrap;word-break:break-all">Cliquez sur un bouton pour tester l&#x27;API.</pre></div><!--$--><!--/$--></main></div><script src="/_next/static/chunks/webpack-42b02186dec27933.js" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0])</script><script>self.__next_f.push([1,"1:\"$Sreact.fragment\"\n2:I[5494,[\"177\",\"static/chunks/app/layout-70969b29acae8f7f.js\"],\"default\"]\n3:I[7555,[],\"\"]\n4:I[1295,[],\"\"]\n5:I[894,[],\"ClientPageRoot\"]\n6:I[3529,[\"464\",\"static/chunks/464-be04f8198da07724.js\",\"11\",\"static/chunks/app/test/page-25683739198c6309.js\"],\"default\"]\n9:I[9665,[],\"OutletBoundary\"]\nc:I[4911,[],\"AsyncMetadataOutlet\"]\ne:I[9665,[],\"ViewportBoundary\"]\n10:I[9665,[],\"MetadataBoundary\"]\n12:I[6614,[],\"\"]\n:HL[\"/_next/static/css/ef46db3751d8e999.css\",\"style\"]\n:HL[\"/_next/static/css/991efdcd1ebedf92.css\",\"style\"]\n"])</script><script>self.__next_f.push([1,"0:{\"P\":null,\"b\":\"6M0JUDHn-5pElaKOYXpVJ\",\"p\":\"\",\"c\":[\"\",\"test\"],\"i\":false,\"f\":[[[\"\",{\"children\":[\"test\",{\"children\":[\"__PAGE__\",{}]}]},\"$undefined\",\"$undefined\",true],[\"\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/ef46db3751d8e999.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"1\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/991efdcd1ebedf92.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[\"$\",\"html\",null,{\"lang\":\"en\",\"children\":[\"$\",\"body\",null,{\"children\":[\"$\",\"div\",null,{\"className\":\"App\",\"children\":[[\"$\",\"$L2\",null,{}],[\"$\",\"main\",null,{\"className\":\"container mt-4\",\"children\":[\"$\",\"$L3\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L4\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":404}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]}]]}]}]}]]}],{\"children\":[\"test\",[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$L3\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L4\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}],{\"children\":[\"__PAGE__\",[\"$\",\"$1\",\"c\",{\"children\":[[\"$\",\"$L5\",null,{\"Component\":\"$6\",\"searchParams\":{},\"params\":{},\"promises\":[\"$@7\",\"$@8\"]}],null,[\"$\",\"$L9\",null,{\"children\":[\"$La\",\"$Lb\",[\"$\",\"$Lc\",null,{\"promise\":\"$@d\"}]]}]]}],{},null,false]},null,false]},null,false],[\"$\",\"$1\",\"h\",{\"children\":[null,[\"$\",\"$1\",\"zUvUOFCeXzrT7heL8n193v\",{\"children\":[[\"$\",\"$Le\",null,{\"children\":\"$Lf\"}],null]}],[\"$\",\"$L10\",null,{\"children\":\"$L11\"}]]}],false]],\"m\":\"$undefined\",\"G\":[\"$12\",\"$undefined\"],\"s\":false,\"S\":true}\n"])</script><script>self.__next_f.push([1,"13:\"$Sreact.suspense\"\n14:I[4911,[],\"AsyncMetadata\"]\n7:{}\n8:{}\n11:[\"$\",\"div\",null,{\"hidden\":true,\"children\":[\"$\",\"$13\",null,{\"fallback\":null,\"children\":[\"$\",\"$L14\",null,{\"promise\":\"$@15\"}]}]}]\n"])</script><script>self.__next_f.push([1,"b:null\n"])</script><script>self.__next_f.push([1,"f:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"}],[\"$\",\"meta\",\"1\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}],[\"$\",\"meta\",\"2\",{\"name\":\"theme-color\",\"content\":\"#000000\"}]]\na:null\n"])</script><script>self.__next_f.push([1,"d:{\"metadata\":[[\"$\",\"title\",\"0\",{\"children\":\"FIRE Dashboard\"}],[\"$\",\"meta\",\"1\",{\"name\":\"description\",\"content\":\"Application pour le suivi de l'objectif FIRE (Financial Independence, Retire Early)\"}],[\"$\",\"link\",\"2\",{\"rel\":\"manifest\",\"href\":\"/manifest.json\",\"crossOrigin\":\"$undefined\"}],[\"$\",\"link\",\"3\",{\"rel\":\"icon\",\"href\":\"/favicon.ico\",\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}],[\"$\",\"link\",\"4\",{\"rel\":\"icon\",\"href\":\"/icon.png\"}],[\"$\",\"link\",\"5\",{\"rel\":\"apple-touch-icon\",\"href\":\"/apple-icon.png\"}]],\"error\":null,\"digest\":\"$undefined\"}\n15:{\"metadata\":\"$d:metadata\",\"error\":null,\"digest\":\"$undefined\"}\n"])</script></body></html>