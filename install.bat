@echo OFF
ECHO ========================================
ECHO    Initialisation du projet fire_UI
ECHO ========================================
ECHO.

REM Vérification des prérequis
ECHO Vérification des prérequis...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    ECHO ERREUR: Python n'est pas installé ou pas dans le PATH
    ECHO Veuillez installer Python 3.8+ depuis https://python.org
    pause
    exit /b 1
)

node --version >nul 2>&1
if %errorlevel% neq 0 (
    ECHO ERREUR: Node.js n'est pas installé ou pas dans le PATH
    ECHO Veuillez installer Node.js depuis https://nodejs.org
    pause
    exit /b 1
)

ECHO ✓ Python et Node.js détectés
ECHO.

REM Installation Backend
ECHO ========================================
ECHO    Installation du Backend (Python)
ECHO ========================================
cd backend

ECHO Création de l'environnement virtuel Python...
python -m venv .venv
if %errorlevel% neq 0 (
    ECHO ERREUR: Impossible de créer l'environnement virtuel
    pause
    exit /b 1
)

ECHO Activation de l'environnement virtuel...
call .venv\Scripts\activate
if %errorlevel% neq 0 (
    ECHO ERREUR: Impossible d'activer l'environnement virtuel
    pause
    exit /b 1
)

ECHO Installation des dépendances Python...
pip install -r requirements.txt
if %errorlevel% neq 0 (
    ECHO ERREUR: Impossible d'installer les dépendances Python
    pause
    exit /b 1
)

ECHO Initialisation de la base de données...
python seed.py
if %errorlevel% neq 0 (
    ECHO ERREUR: Impossible d'initialiser la base de données
    pause
    exit /b 1
)

ECHO ✓ Backend installé avec succès
ECHO.

REM Installation Frontend
ECHO ========================================
ECHO    Installation du Frontend (React)
ECHO ========================================
cd ..\frontend

ECHO Installation des dépendances Node.js...
npm install
if %errorlevel% neq 0 (
    ECHO ERREUR: Impossible d'installer les dépendances Node.js
    pause
    exit /b 1
)

ECHO ✓ Frontend (React original) installé avec succès
ECHO.

REM Installation Frontend Next.js
ECHO ========================================
ECHO    Installation du Frontend (Next.js)
ECHO ========================================
cd ..\frontnextjs

ECHO Installation des dépendances Node.js pour Next.js...
npm install
if %errorlevel% neq 0 (
    ECHO ERREUR: Impossible d'installer les dépendances Node.js pour Next.js
    pause
    exit /b 1
)

ECHO ✓ Frontend (Next.js) installé avec succès
ECHO.

REM Retour à la racine
cd ..

ECHO ========================================
ECHO    Installation terminée avec succès!
ECHO ========================================
ECHO.
ECHO Prochaines étapes:
ECHO 1. Lancez les serveurs avec: run.bat
ECHO    (run.bat doit être mis à jour pour lancer frontnextjs)
ECHO 2. Accédez à l'application:
ECHO    - Frontend (Next.js): http://localhost:3000 (par défaut pour Next.js)
ECHO    - Frontend (React original): vérifier le port si lancé, potentiellement 3001 ou autre
ECHO    - Backend API: http://localhost:8000
ECHO    - Documentation API: http://localhost:8000/docs
ECHO.
ECHO Appuyez sur une touche pour continuer...
pause >nul
