(()=>{var e={};e.id=11,e.ids=[11],e.modules={848:(e,t,s)=>{Promise.resolve().then(s.bind(s,64645))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7472:(e,t,s)=>{Promise.resolve().then(s.bind(s,96663))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},24255:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,86346,23)),Promise.resolve().then(s.t.bind(s,27924,23)),Promise.resolve().then(s.t.bind(s,35656,23)),Promise.resolve().then(s.t.bind(s,40099,23)),Promise.resolve().then(s.t.bind(s,38243,23)),Promise.resolve().then(s.t.bind(s,28827,23)),Promise.resolve().then(s.t.bind(s,62763,23)),Promise.resolve().then(s.t.bind(s,97173,23))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29190:(e,t,s)=>{"use strict";s.d(t,{default:()=>o});var r=s(60687),a=s(43210),n=s(85814),i=s.n(n);let o=()=>{let[e,t]=(0,a.useState)(!1),s=()=>{t(!1)};return(0,r.jsx)("nav",{className:"navbar navbar-expand-lg navbar-dark bg-dark",children:(0,r.jsxs)("div",{className:"container-fluid",children:[(0,r.jsx)(i(),{className:"navbar-brand",href:"/",onClick:s,children:"FIRE Dashboard"}),(0,r.jsx)("button",{className:"navbar-toggler",type:"button",onClick:()=>{t(!e)},"aria-controls":"navbarNavContent","aria-expanded":e,"aria-label":"Toggle navigation",children:(0,r.jsx)("span",{className:"navbar-toggler-icon"})}),(0,r.jsx)("div",{className:`collapse navbar-collapse ${e?"show":""}`,id:"navbarNavContent",children:(0,r.jsxs)("ul",{className:"navbar-nav me-auto mb-2 mb-lg-0",children:[(0,r.jsx)("li",{className:"nav-item",children:(0,r.jsx)(i(),{className:"nav-link",href:"/",onClick:s,children:"Dashboard"})}),(0,r.jsx)("li",{className:"nav-item",children:(0,r.jsx)(i(),{className:"nav-link",href:"/assets",onClick:s,children:"Patrimoine"})}),(0,r.jsx)("li",{className:"nav-item",children:(0,r.jsx)(i(),{className:"nav-link",href:"/liabilities",onClick:s,children:"Emprunts"})}),(0,r.jsx)("li",{className:"nav-item",children:(0,r.jsx)(i(),{className:"nav-link",href:"/scpi",onClick:s,children:"SCPI"})}),(0,r.jsx)("li",{className:"nav-item",children:(0,r.jsx)(i(),{className:"nav-link",href:"/evolution",onClick:s,children:"\xc9volution"})}),(0,r.jsx)("li",{className:"nav-item",children:(0,r.jsx)(i(),{className:"nav-link",href:"/budget",onClick:s,children:"Budget FIRE"})}),(0,r.jsx)("li",{className:"nav-item",children:(0,r.jsx)(i(),{className:"nav-link",href:"/scenarios",onClick:s,children:"Sc\xe9narios"})}),(0,r.jsx)("li",{className:"nav-item",children:(0,r.jsx)(i(),{className:"nav-link",href:"/fire",onClick:s,children:"Objectif FIRE"})}),(0,r.jsx)("li",{className:"nav-item",children:(0,r.jsx)(i(),{className:"nav-link",href:"/test",onClick:s,children:"Test API"})})]})})]})})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30004:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\AI-Code\\\\fire_UI\\\\frontnextjs\\\\src\\\\components\\\\Navbar.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\AI-Code\\fire_UI\\frontnextjs\\src\\components\\Navbar.tsx","default")},33873:e=>{"use strict";e.exports=require("path")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},61135:()=>{},61431:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>p,pages:()=>d,routeModule:()=>u,tree:()=>c});var r=s(65239),a=s(48088),n=s(88170),i=s.n(n),o=s(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);s.d(t,l);let c={children:["",{children:["test",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,96663)),"D:\\AI-Code\\fire_UI\\frontnextjs\\src\\app\\test\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e),async e=>(await Promise.resolve().then(s.bind(s,78162))).default(e)],apple:[async e=>(await Promise.resolve().then(s.bind(s,94650))).default(e)],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"D:\\AI-Code\\fire_UI\\frontnextjs\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e),async e=>(await Promise.resolve().then(s.bind(s,78162))).default(e)],apple:[async e=>(await Promise.resolve().then(s.bind(s,94650))).default(e)],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["D:\\AI-Code\\fire_UI\\frontnextjs\\src\\app\\test\\page.tsx"],p={require:s,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/test/page",pathname:"/test",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},62907:(e,t,s)=>{Promise.resolve().then(s.bind(s,30004))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64645:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i});var r=s(60687),a=s(43210),n=s(90317);let i=()=>{let[e,t]=(0,a.useState)(""),[s,i]=(0,a.useState)(!1),o=async()=>{i(!0),t("Testing with Axios apiClient...");try{let e=await n.A.get("/dashboard");t(`Axios Success: ${JSON.stringify(e.data,null,2)}`)}catch(e){t(`Axios Error: ${e.message}
Details: ${JSON.stringify({status:e.response?.status,statusText:e.response?.statusText,data:e.response?.data,config:{url:e.config?.url,baseURL:e.config?.baseURL,method:e.config?.method}},null,2)}`)}finally{i(!1)}},l=async()=>{i(!0),t("Testing with fetch (http://localhost:8000/api/dashboard)...");try{let e=await fetch("http://localhost:8000/api/dashboard");if(!e.ok)throw Error(`HTTP error! status: ${e.status}`);let s=await e.json();t(`Fetch (localhost) Success: ${JSON.stringify(s,null,2)}`)}catch(e){t(`Fetch (localhost) Error: ${e.message}`)}finally{i(!1)}},c=async()=>{i(!0),t("Testing with fetch (http://127.0.0.1:8000/api/dashboard)...");try{let e=await fetch("http://127.0.0.1:8000/api/dashboard");if(!e.ok)throw Error(`HTTP error! status: ${e.status}`);let s=await e.json();t(`Fetch (127.0.0.1) Success: ${JSON.stringify(s,null,2)}`)}catch(e){t(`Fetch (127.0.0.1) Error: ${e.message}`)}finally{i(!1)}};return(0,r.jsxs)("div",{className:"container mt-4",children:[(0,r.jsx)("h2",{children:"Test des Appels API vers le Backend"}),(0,r.jsxs)("p",{children:["Cet outil permet de v\xe9rifier la connectivit\xe9 et les r\xe9ponses de base du backend FastAPI. Assurez-vous que le serveur backend (",(0,r.jsx)("code",{children:"cd backend && uvicorn main:app --reload"}),") est en cours d'ex\xe9cution sur ",(0,r.jsx)("code",{children:"http://localhost:8000"}),"."]}),(0,r.jsxs)("div",{className:"mb-3",children:[(0,r.jsx)("button",{className:"btn btn-primary me-2 mb-2",onClick:o,disabled:s,children:"Test /dashboard (via apiClient Axios)"}),(0,r.jsx)("button",{className:"btn btn-secondary me-2 mb-2",onClick:l,disabled:s,children:"Test /dashboard (via fetch direct sur localhost:8000)"}),(0,r.jsx)("button",{className:"btn btn-info mb-2",onClick:c,disabled:s,children:"Test /dashboard (via fetch direct sur 127.0.0.1:8000)"})]}),s&&(0,r.jsx)("p",{children:"Chargement du test..."}),(0,r.jsx)("pre",{className:"bg-light p-3 border rounded",style:{whiteSpace:"pre-wrap",wordBreak:"break-all"},children:e||"Cliquez sur un bouton pour tester l'API."})]})}},70440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},73523:(e,t,s)=>{Promise.resolve().then(s.bind(s,29190))},74075:e=>{"use strict";e.exports=require("zlib")},78162:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(31658);let a=async e=>[{type:"image/png",sizes:"192x192",url:(0,r.fillMetadataSegment)(".",await e.params,"icon.png")+"?d84b06edc1baa420"}]},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},90317:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let r=s(51060).A.create({baseURL:"http://localhost:8000/api",headers:{"Content-Type":"application/json"},timeout:1e4});r.interceptors.response.use(e=>e,e=>(console.error("API Error:",e.response?.status,e.config?.url,e.message),Promise.reject(e)));let a=r},93991:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,16444,23)),Promise.resolve().then(s.t.bind(s,16042,23)),Promise.resolve().then(s.t.bind(s,88170,23)),Promise.resolve().then(s.t.bind(s,49477,23)),Promise.resolve().then(s.t.bind(s,29345,23)),Promise.resolve().then(s.t.bind(s,12089,23)),Promise.resolve().then(s.t.bind(s,46577,23)),Promise.resolve().then(s.t.bind(s,31307,23))},94431:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>o,metadata:()=>n,viewport:()=>i});var r=s(37413);s(61135),s(27209);var a=s(30004);let n={title:"FIRE Dashboard",description:"Application pour le suivi de l'objectif FIRE (Financial Independence, Retire Early)",manifest:"/manifest.json",icons:{icon:"/icon.png",apple:"/apple-icon.png"}},i={themeColor:"#000000"};function o({children:e}){return(0,r.jsx)("html",{lang:"en",children:(0,r.jsx)("body",{children:(0,r.jsxs)("div",{className:"App",children:[(0,r.jsx)(a.default,{}),(0,r.jsx)("main",{className:"container mt-4",children:e})]})})})}},94650:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(31658);let a=async e=>[{type:"image/png",sizes:"192x192",url:(0,r.fillMetadataSegment)(".",await e.params,"apple-icon.png")+"?d84b06edc1baa420"}]},94735:e=>{"use strict";e.exports=require("events")},96663:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\AI-Code\\\\fire_UI\\\\frontnextjs\\\\src\\\\app\\\\test\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\AI-Code\\fire_UI\\frontnextjs\\src\\app\\test\\page.tsx","default")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[447,989,517],()=>s(61431));module.exports=r})();