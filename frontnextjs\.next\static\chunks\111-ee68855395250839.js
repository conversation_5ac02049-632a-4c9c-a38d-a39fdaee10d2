"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[111],{612:(e,r,n)=>{function t(){return(t=Object.assign?Object.assign.bind():function(e){for(var r=1;r<arguments.length;r++){var n=arguments[r];for(var t in n)({}).hasOwnProperty.call(n,t)&&(e[t]=n[t])}return e}).apply(null,arguments)}n.d(r,{Zw:()=>s});var a=n(3495),i=n(2115);n(1337);function o(e){return"default"+e.charAt(0).toUpperCase()+e.substr(1)}function l(e){var r=function(e,r){if("object"!=typeof e||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var t=n.call(e,r||"default");if("object"!=typeof t)return t;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(e)}(e,"string");return"symbol"==typeof r?r:String(r)}function s(e,r){return Object.keys(r).reduce(function(n,s){var u,f,c,d,v,p,m,y,b=n[o(s)],h=n[s],g=(0,a.A)(n,[o(s),s].map(l)),w=r[s],j=(u=e[w],f=(0,i.useRef)(void 0!==h),d=(c=(0,i.useState)(b))[0],v=c[1],p=void 0!==h,m=f.current,f.current=p,!p&&m&&d!==b&&v(b),[p?h:d,(0,i.useCallback)(function(e){for(var r=arguments.length,n=Array(r>1?r-1:0),t=1;t<r;t++)n[t-1]=arguments[t];u&&u.apply(void 0,[e].concat(n)),v(e)},[u])]),A=j[0],N=j[1];return t({},g,((y={})[s]=A,y[w]=N,y))},e)}},1337:e=>{e.exports=function(e,r,n,t,a,i,o,l){if(!e){var s;if(void 0===r)s=Error("Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.");else{var u=[n,t,a,i,o,l],f=0;(s=Error(r.replace(/%s/g,function(){return u[f++]}))).name="Invariant Violation"}throw s.framesToPop=1,s}}},7111:(e,r,n)=>{n.d(r,{A:()=>j});var t=n(9300),a=n.n(t),i=n(2115),o=n(612),l=n(7150),s=n(7390),u=n(8724),f=n(5155);let c=(0,u.A)("h4");c.displayName="DivStyledAsH4";let d=i.forwardRef((e,r)=>{let{className:n,bsPrefix:t,as:i=c,...o}=e;return t=(0,s.oU)(t,"alert-heading"),(0,f.jsx)(i,{ref:r,className:a()(n,t),...o})});d.displayName="AlertHeading",n(4956);var v=n(1730);n(8141),n(18),n(4583),new WeakMap;var p=n(2960);let m=["onKeyDown"],y=i.forwardRef((e,r)=>{let{onKeyDown:n}=e,t=function(e,r){if(null==e)return{};var n={};for(var t in e)if(({}).hasOwnProperty.call(e,t)){if(r.indexOf(t)>=0)continue;n[t]=e[t]}return n}(e,m),[a]=(0,p.Am)(Object.assign({tagName:"a"},t)),i=(0,v.A)(e=>{a.onKeyDown(e),null==n||n(e)});return function(e){return!e||"#"===e.trim()}(t.href)||"button"===t.role?(0,f.jsx)("a",Object.assign({ref:r},t,a,{onKeyDown:i})):(0,f.jsx)("a",Object.assign({ref:r},t,{onKeyDown:n}))});y.displayName="Anchor";let b=i.forwardRef((e,r)=>{let{className:n,bsPrefix:t,as:i=y,...o}=e;return t=(0,s.oU)(t,"alert-link"),(0,f.jsx)(i,{ref:r,className:a()(n,t),...o})});b.displayName="AlertLink";var h=n(4748),g=n(7706);let w=i.forwardRef((e,r)=>{let{bsPrefix:n,show:t=!0,closeLabel:i="Close alert",closeVariant:u,className:c,children:d,variant:v="primary",onClose:p,dismissible:m,transition:y=h.A,...b}=(0,o.Zw)(e,{show:"onClose"}),w=(0,s.oU)(n,"alert"),j=(0,l.A)(e=>{p&&p(!1,e)}),A=!0===y?h.A:y,N=(0,f.jsxs)("div",{role:"alert",...!A?b:void 0,ref:r,className:a()(c,w,v&&"".concat(w,"-").concat(v),m&&"".concat(w,"-dismissible")),children:[m&&(0,f.jsx)(g.A,{onClick:j,"aria-label":i,variant:u}),d]});return A?(0,f.jsx)(A,{unmountOnExit:!0,...b,ref:void 0,in:t,children:N}):t?N:null});w.displayName="Alert";let j=Object.assign(w,{Link:b,Heading:d})}}]);