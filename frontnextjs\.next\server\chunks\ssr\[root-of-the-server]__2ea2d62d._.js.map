{"version": 3, "sources": [], "sections": [{"offset": {"line": 111, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/AI-Code/fire_UI/frontnextjs/src/components/ApiClient.ts"], "sourcesContent": ["import axios from 'axios';\r\n\r\nconst apiClient = axios.create({\r\n  baseURL: 'http://localhost:8000/api',\r\n  headers: {\r\n    'Content-Type': 'application/json',\r\n  },\r\n  timeout: 10000, // 10 second timeout\r\n});\r\n\r\n// Add response interceptor for error handling\r\napiClient.interceptors.response.use(\r\n  (response) => response,\r\n  (error) => {\r\n    console.error('API Error:', error.response?.status, error.config?.url, error.message);\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\nexport default apiClient;\r\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,YAAY,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAC7B,SAAS;IACT,SAAS;QACP,gBAAgB;IAClB;IACA,SAAS;AACX;AAEA,8CAA8C;AAC9C,UAAU,YAAY,CAAC,QAAQ,CAAC,GAAG,CACjC,CAAC,WAAa,UACd,CAAC;IACC,QAAQ,KAAK,CAAC,cAAc,MAAM,QAAQ,EAAE,QAAQ,MAAM,MAAM,EAAE,KAAK,MAAM,OAAO;IACpF,OAAO,QAAQ,MAAM,CAAC;AACxB;uCAGa", "debugId": null}}, {"offset": {"line": 135, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/AI-Code/fire_UI/frontnextjs/src/app/fire/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useEffect, useState, FormEvent } from 'react';\r\nimport apiClient from '../../components/ApiClient'; // Ajusté\r\nimport { Modal, Button, Form, ProgressBar, Accordion, Badge } from 'react-bootstrap';\r\n\r\ninterface FireTargetData {\r\n  fire_target_amount: number;\r\n  secure_withdrawal_rate: number;\r\n  current_net_patrimoine: number;\r\n  total_assets: number;\r\n  total_liabilities: number;\r\n  remaining_to_invest: number;\r\n  potential_passive_income: number;\r\n  progress_percentage: number;\r\n}\r\n\r\n// FireSettings interface est utilisée implicitement par FireSettingsForm, pas besoin de la redéfinir ici\r\n// si FireSettingsForm ne l'exporte pas ou ne l'utilise pas en dehors de ses props.\r\n\r\ninterface FirePhase {\r\n  id: number;\r\n  name: string;\r\n  description: string;\r\n  startDate: string;\r\n  endDate: string;\r\n  startAge: number;\r\n  endAge: number;\r\n  targetAmount?: number;\r\n  keyMilestones: string[];\r\n  recommendedActions: string[];\r\n  status: 'completed' | 'current' | 'upcoming';\r\n}\r\n\r\ninterface PhaseProgress {\r\n  currentPhase: number;\r\n  progressInCurrentPhase: number;\r\n  yearsToNextPhase: number;\r\n  currentAge: number;\r\n}\r\n\r\nconst FireSettingsForm: React.FC<{\r\n  settings: { fire_target_amount: number; secure_withdrawal_rate: number } | null,\r\n  onSave: () => void,\r\n  onCancel: () => void\r\n}> = ({ settings, onSave, onCancel }) => {\r\n  const [formData, setFormData] = useState({\r\n    fire_target_amount: settings?.fire_target_amount || 910150,\r\n    secure_withdrawal_rate: (settings?.secure_withdrawal_rate || 0.04) * 100,\r\n  });\r\n\r\n  useEffect(() => {\r\n    setFormData({\r\n        fire_target_amount: settings?.fire_target_amount || 910150,\r\n        secure_withdrawal_rate: (settings?.secure_withdrawal_rate || 0.04) * 100,\r\n    });\r\n  }, [settings]);\r\n\r\n  const handleSubmit = async (e: FormEvent) => {\r\n    e.preventDefault();\r\n    const dataToSend = {\r\n      fire_target_amount: formData.fire_target_amount,\r\n      secure_withdrawal_rate: formData.secure_withdrawal_rate / 100,\r\n    };\r\n    try {\r\n      await apiClient.put('/fire-settings', dataToSend);\r\n      onSave();\r\n    } catch (error: unknown) {\r\n      const errorMessage = error instanceof Error ? error.message : 'Unknown error';\r\n      console.error(\"Erreur lors de la sauvegarde des paramètres FIRE\", errorMessage);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Form onSubmit={handleSubmit}>\r\n      <Form.Group className=\"mb-3\"><Form.Label>Objectif FIRE (€)</Form.Label><Form.Control type=\"number\" step=\"1000\" value={formData.fire_target_amount} onChange={(e) => setFormData({ ...formData, fire_target_amount: parseFloat(e.target.value) || 0 })} required /><Form.Text className=\"text-muted\">Montant total que vous souhaitez atteindre pour votre indépendance financière</Form.Text></Form.Group>\r\n      <Form.Group className=\"mb-3\"><Form.Label>Taux de retrait sécurisé (%)</Form.Label><Form.Control type=\"number\" step=\"0.1\" min=\"1\" max=\"10\" value={formData.secure_withdrawal_rate} onChange={(e) => setFormData({ ...formData, secure_withdrawal_rate: parseFloat(e.target.value) || 4 })} required /><Form.Text className=\"text-muted\">Pourcentage de votre patrimoine que vous pouvez retirer annuellement (règle des 4% = 4.0)</Form.Text></Form.Group>\r\n      <div className=\"d-flex justify-content-end gap-2\"><Button variant=\"secondary\" onClick={onCancel}>Annuler</Button><Button variant=\"primary\" type=\"submit\">Sauvegarder</Button></div>\r\n    </Form>\r\n  );\r\n};\r\n\r\nconst FirePhasesTracker: React.FC<{ currentNetPatrimoine: number }> = ({ currentNetPatrimoine }) => {\r\n  const firePhases: FirePhase[] = [\r\n    { id: 0, name: \"Phase 0: Bilan Patrimonial & Actions Immédiates\", description: \"Réorganisation et préparation\", startDate: \"Juin 2025\", endDate: \"Juillet 2025\", startAge: 47, endAge: 47, keyMilestones: [\"Apport SCPI Comète (27 000€)\", \"Optimisation PEA (72 750€ DCA Nasdaq-100)\", \"Consolidation crypto (75% BTC / 25% ETH)\", \"Sortie progressive crowdlending\"], recommendedActions: [\"Négocier taux prêt SCPI Comète (<3,95%)\", \"Déployer DCA adaptatif sur 6 mois\", \"Simplifier portefeuille crypto\", \"Liquider positions Trading 212\"], status: 'completed'},\r\n    { id: 1, name: \"Phase 1: Accumulation Intensive\", description: \"Investissement mensuel de 3 415€\", startDate: \"Août 2025\", endDate: \"Fin 2037\", startAge: 47, endAge: 59, keyMilestones: [\"Saturation PEA (mi-2033, âge 55 ans)\", \"Fin crédit SCPI existant (Nov 2035)\", \"Atteinte objectif 910k€ (courant 2035, âge 57 ans)\"], recommendedActions: [\"SCPI: 812€/mois + remboursements 1 303€/mois\", \"ETF Actions: 800€/mois (PUST puis SXR8)\", \"Fonds Euros: 200€/mois\", \"Crypto: 200€/mois (BTC/ETH)\"], status: 'current'},\r\n    { id: 2, name: \"Phase 2: Transition Pré-FIRE\", description: \"Réduction des risques et préparation\", startDate: \"Fin 2037\", endDate: \"Début 2038\", startAge: 59, endAge: 60, targetAmount: 910150, keyMilestones: [\"Capital FIRE atteint (910 150€)\", \"Réduction risque crypto (<7% patrimoine)\", \"Constitution poche ETF dividendes (30-40%)\"], recommendedActions: [\"Arbitrer PUST/SXR8 vers ETF dividendes\", \"Sécuriser gains crypto si >7-10%\", \"Vérifier fonds d'urgence (50 000€)\", \"Optimiser fiscalité retraits\"], status: 'upcoming'},\r\n    { id: 3, name: \"Phase 3: Vie en Mode FIRE\", description: \"Indépendance financière confortable\", startDate: \"2038\", endDate: \"2040\", startAge: 60, endAge: 62, targetAmount: 1200000, keyMilestones: [\"FIRE Confortable (revenus > dépenses + dette)\", \"Génération 37 400€/an revenus passifs\", \"Allocation: 40% SCPI, 40% ETF, 15% Fonds Euros, 5% Crypto\"], recommendedActions: [\"Optimiser retraits fiscaux (PFU/barème)\", \"Gérer allocation mix croissance/dividendes\", \"Suivi dépenses cibles (25 484€/an)\", \"Gestion budgétaire et CSM\"], status: 'upcoming'},\r\n    { id: 4, name: \"Phase 4: Évolution Post-FIRE & Retraite Légale\", description: \"FIRE pleine puissance et retraite\", startDate: \"2040\", endDate: \"2042+\", startAge: 62, endAge: 64, keyMilestones: [\"Fin prêt SCPI Comète (fin 2040, +10k€/an)\", \"Retraite légale (2042, âge 64 ans)\", \"Pension État (~1 800€/mois) + PER (45-55k€)\"], recommendedActions: [\"Augmentation revenu net (+10k€/an)\", \"Intégration pension État\", \"Déblocage PER Linxea Spirit\", \"Réduction besoin puisage capital FIRE\"], status: 'upcoming'}\r\n  ];\r\n\r\n  const calculatePhaseProgress = (): PhaseProgress => {\r\n    const currentYear = new Date().getFullYear();\r\n    const birthYear = 1978;\r\n    const currentAge = currentYear - birthYear;\r\n    let currentPhaseIdx = 1;\r\n\r\n    if (currentNetPatrimoine >= 910150) {\r\n      if (currentAge >= 62) currentPhaseIdx = 4;\r\n      else if (currentAge >= 60) currentPhaseIdx = 3;\r\n      else currentPhaseIdx = 2;\r\n    } else if (currentAge >= 59 && currentNetPatrimoine >= 800000) { // Approx.\r\n      currentPhaseIdx = 2;\r\n    }\r\n\r\n    let progressInCurrentPhase = 0;\r\n    let yearsToNextPhase = 0;\r\n    const currentPhaseData = firePhases[currentPhaseIdx];\r\n\r\n    if (currentPhaseIdx === 1) { // Accumulation\r\n        progressInCurrentPhase = currentPhaseData.targetAmount ? (currentNetPatrimoine / currentPhaseData.targetAmount) * 100 : (currentNetPatrimoine / 910150) * 100 ; // Utilise 910150 si targetAmount non défini pour phase 1\r\n        const monthlyInvestment = 3415;\r\n        const remainingAmount = (currentPhaseData.targetAmount || 910150) - currentNetPatrimoine;\r\n        yearsToNextPhase = Math.max(0, remainingAmount > 0 ? remainingAmount / (monthlyInvestment * 12) : 0);\r\n    } else if (currentPhaseData) { // Pour les autres phases basées sur l'âge ou objectifs spécifiques\r\n        const phaseDuration = currentPhaseData.endAge - currentPhaseData.startAge;\r\n        const yearsInPhase = Math.max(0, currentAge - currentPhaseData.startAge);\r\n        progressInCurrentPhase = phaseDuration > 0 ? (yearsInPhase / phaseDuration) * 100 : (currentAge >= currentPhaseData.startAge ? 100: 0);\r\n        yearsToNextPhase = Math.max(0, currentPhaseData.endAge - currentAge);\r\n        if (currentPhaseData.targetAmount && currentNetPatrimoine < currentPhaseData.targetAmount && currentPhaseIdx !== 1) {\r\n            // Si un objectif monétaire existe pour la phase et n'est pas atteint, la progression peut aussi en tenir compte.\r\n            // Pour simplifier, on se base sur l'âge pour les phases > 1, mais on pourrait affiner.\r\n        }\r\n    }\r\n\r\n    return { currentPhase: currentPhaseIdx, progressInCurrentPhase: Math.min(100, Math.max(0,progressInCurrentPhase)), yearsToNextPhase, currentAge };\r\n  };\r\n\r\n  const phaseProgress = calculatePhaseProgress();\r\n  const phasesWithStatus = firePhases.map((phase, index) => ({ ...phase, status: index < phaseProgress.currentPhase ? 'completed' : index === phaseProgress.currentPhase ? 'current' : 'upcoming' as 'completed' | 'current' | 'upcoming'}));\r\n  const getPhaseColor = (status: string) => ({ completed: 'success', current: 'primary', upcoming: 'secondary' }[status] || 'secondary');\r\n  const getPhaseIcon = (status: string) => ({ completed: '✅', current: '🔥', upcoming: '⏳' }[status] || '⏳');\r\n\r\n  return (\r\n    <div className=\"mt-4\">\r\n      <h3 className=\"mb-4\">🎯 Tracker des Phases FIRE</h3>\r\n      <div className=\"row mb-4\">\r\n        <div className=\"col-md-3\"><div className=\"card text-white bg-info\"><div className=\"card-body text-center\"><h5>Phase Actuelle</h5><h4>Phase {phaseProgress.currentPhase}</h4><small>{phasesWithStatus[phaseProgress.currentPhase]?.name.split(':')[1] || 'N/A'}</small></div></div></div>\r\n        <div className=\"col-md-3\"><div className=\"card text-white bg-warning\"><div className=\"card-body text-center\"><h5>Âge Actuel</h5><h4>{phaseProgress.currentAge} ans</h4><small>Né en septembre 1978</small></div></div></div>\r\n        <div className=\"col-md-3\"><div className=\"card text-white bg-success\"><div className=\"card-body text-center\"><h5>Progression Phase</h5><h4>{phaseProgress.progressInCurrentPhase.toFixed(1)}%</h4><small>Dans la phase actuelle</small></div></div></div>\r\n        <div className=\"col-md-3\"><div className=\"card text-white bg-danger\"><div className=\"card-body text-center\"><h5>Prochaine Phase</h5><h4>{phaseProgress.yearsToNextPhase.toFixed(1)} ans</h4><small>Estimation restante</small></div></div></div>\r\n      </div>\r\n      <div className=\"card\"><div className=\"card-header\"><h5 className=\"mb-0\">Timeline des Phases FIRE (2025-2042+)</h5></div>\r\n        <div className=\"card-body\">\r\n          <Accordion defaultActiveKey={phaseProgress.currentPhase.toString()}>\r\n            {phasesWithStatus.map((phase, index) => (\r\n              <Accordion.Item eventKey={index.toString()} key={phase.id}>\r\n                <Accordion.Header><div className=\"d-flex align-items-center w-100\"><span className=\"me-2\">{getPhaseIcon(phase.status)}</span><div className=\"flex-grow-1\"><strong>{phase.name}</strong><Badge bg={getPhaseColor(phase.status)} className=\"ms-2\">{phase.status === 'completed' ? 'Terminée' : phase.status === 'current' ? 'En cours' : 'À venir'}</Badge></div><small className=\"text-muted\">{phase.startDate} - {phase.endDate} (âge {phase.startAge}-{phase.endAge})</small></div></Accordion.Header>\r\n                <Accordion.Body><div className=\"row\">\r\n                    <div className=\"col-md-6\"><h6>🎯 Jalons Clés :</h6><ul>{phase.keyMilestones.map((milestone, idx) => (<li key={idx}>{milestone}</li>))}</ul>{phase.targetAmount && (<p><strong>Objectif Patrimoine :</strong> {new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR' }).format(phase.targetAmount)}</p>)}</div>\r\n                    <div className=\"col-md-6\"><h6>📋 Actions Recommandées :</h6><ul>{phase.recommendedActions.map((action, idx) => (<li key={idx}>{action}</li>))}</ul></div>\r\n                </div>{phase.status === 'current' && (<div className=\"mt-3\"><h6>Progression dans cette phase :</h6><ProgressBar now={phaseProgress.progressInCurrentPhase} label={`${phaseProgress.progressInCurrentPhase.toFixed(1)}%`} variant={getPhaseColor(phase.status)} style={{ height: '25px' }} /><small className=\"text-muted\">Estimation : {phaseProgress.yearsToNextPhase.toFixed(1)} années restantes</small></div>)}</Accordion.Body>\r\n              </Accordion.Item>))}\r\n          </Accordion>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nconst FireTargetPage: React.FC = () => {\r\n  const [data, setData] = useState<FireTargetData | null>(null);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const [retryCount, setRetryCount] = useState(0);\r\n  const [showModal, setShowModal] = useState(false);\r\n\r\n  const fetchFireTargetData = () => {\r\n    setLoading(true);\r\n    setError(null);\r\n    apiClient.get('/fire-target')\r\n      .then(response => { setData(response.data); setLoading(false); setRetryCount(0); })\r\n      .catch(error => {\r\n        console.error('FireTarget API error:', error);\r\n        if (retryCount < 2) { setRetryCount(prev => prev + 1); setTimeout(() => fetchFireTargetData(), 1000); }\r\n        else { setError('Erreur lors de la récupération des données FIRE.'); setLoading(false); }\r\n      });\r\n  };\r\n\r\n  useEffect(() => { fetchFireTargetData(); // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, []);\r\n  const handleSettingsSave = () => { setShowModal(false); fetchFireTargetData(); };\r\n\r\n  if (loading && retryCount === 0) return <p>Chargement de l&apos;objectif FIRE...</p>;\r\n  if (error && retryCount >=2) return (<div><p className=\"text-danger\">{error}</p><button className=\"btn btn-primary\" onClick={() => {setRetryCount(0); fetchFireTargetData();}}>Réessayer</button></div>);\r\n  if (!data && !loading) return <p>Aucune donnée d&apos;objectif FIRE disponible.</p>;\r\n  if (loading) return <p>Chargement de l&apos;objectif FIRE, tentative {retryCount + 1}...</p>;\r\n  if (!data) return <p>Données FIRE non disponibles.</p>; // Fallback final\r\n\r\n  const formatCurrency = (value: number) => new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR' }).format(value);\r\n  const formatPercentage = (value: number) => new Intl.NumberFormat('fr-FR', { style: 'percent', minimumFractionDigits: 1 }).format(value / 100);\r\n\r\n  return (\r\n    <div>\r\n      <div className=\"d-flex justify-content-between align-items-center mb-4\"><h1>Objectif FIRE</h1><Button variant=\"primary\" onClick={() => setShowModal(true)}>Modifier l&apos;objectif</Button></div>\r\n      <div className=\"row mb-4\"><div className=\"col-12\"><div className=\"card\"><div className=\"card-body\">\r\n        <h5 className=\"card-title\">Progression vers votre Objectif FIRE</h5>\r\n        <div className=\"row mb-3\">\r\n            <div className=\"col-md-4\"><div className=\"text-center\"><h3 className=\"text-primary\">{formatCurrency(data.current_net_patrimoine)}</h3><small className=\"text-muted\">Patrimoine Net Actuel</small></div></div>\r\n            <div className=\"col-md-4\"><div className=\"text-center\"><h3 className=\"text-success\">{formatCurrency(data.fire_target_amount)}</h3><small className=\"text-muted\">Objectif FIRE</small></div></div>\r\n            <div className=\"col-md-4\"><div className=\"text-center\"><h3 className=\"text-info\">{formatPercentage(data.progress_percentage)}</h3><small className=\"text-muted\">Progression</small></div></div>\r\n        </div>\r\n        <ProgressBar now={data.progress_percentage} label={`${data.progress_percentage.toFixed(1)}%`} style={{ height: '30px' }} className=\"mb-3\" />\r\n        <div className=\"row\">\r\n            <div className=\"col-md-6\"><p className=\"mb-1\"><strong>Montant restant à investir :</strong></p><p className=\"text-danger fs-5\">{formatCurrency(Math.max(0, data.remaining_to_invest))}</p></div>\r\n            <div className=\"col-md-6\"><p className=\"mb-1\"><strong>Revenu passif annuel potentiel :</strong></p><p className=\"text-success fs-5\">{formatCurrency(data.potential_passive_income)}</p><small className=\"text-muted\">Montant que vous pourriez retirer annuellement de votre patrimoine actuel selon la règle des {formatPercentage(data.secure_withdrawal_rate * 100)}</small></div>\r\n        </div>\r\n      </div></div></div></div>\r\n      <div className=\"row\">\r\n        <div className=\"col-md-4\"><div className=\"card text-white bg-success mb-3\"><div className=\"card-header\">Total Actifs</div><div className=\"card-body\"><h5 className=\"card-title\">{formatCurrency(data.total_assets)}</h5><p className=\"card-text\">Incluant les SCPI et tous vos investissements</p></div></div></div>\r\n        <div className=\"col-md-4\"><div className=\"card text-white bg-danger mb-3\"><div className=\"card-header\">Total Passifs</div><div className=\"card-body\"><h5 className=\"card-title\">{formatCurrency(data.total_liabilities)}</h5><p className=\"card-text\">Emprunts et dettes en cours</p></div></div></div>\r\n        <div className=\"col-md-4\"><div className=\"card text-white bg-info mb-3\"><div className=\"card-header\">Taux de Retrait Sécurisé</div><div className=\"card-body\"><h5 className=\"card-title\">{formatPercentage(data.secure_withdrawal_rate * 100)}</h5><p className=\"card-text\">Pourcentage de retrait annuel sécurisé</p></div></div></div>\r\n      </div>\r\n      <FirePhasesTracker currentNetPatrimoine={data.current_net_patrimoine} />\r\n      <Modal show={showModal} onHide={() => setShowModal(false)} size=\"lg\"><Modal.Header closeButton><Modal.Title>Modifier les Paramètres FIRE</Modal.Title></Modal.Header><Modal.Body><FireSettingsForm settings={data ? { fire_target_amount: data.fire_target_amount, secure_withdrawal_rate: data.secure_withdrawal_rate } : null} onSave={handleSettingsSave} onCancel={() => setShowModal(false)} /></Modal.Body></Modal>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default FireTargetPage;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA,8NAAoD,SAAS;AAC7D;AAAA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;AAyCA,MAAM,mBAID,CAAC,EAAE,QAAQ,EAAE,MAAM,EAAE,QAAQ,EAAE;IAClC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,oBAAoB,UAAU,sBAAsB;QACpD,wBAAwB,CAAC,UAAU,0BAA0B,IAAI,IAAI;IACvE;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,YAAY;YACR,oBAAoB,UAAU,sBAAsB;YACpD,wBAAwB,CAAC,UAAU,0BAA0B,IAAI,IAAI;QACzE;IACF,GAAG;QAAC;KAAS;IAEb,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,MAAM,aAAa;YACjB,oBAAoB,SAAS,kBAAkB;YAC/C,wBAAwB,SAAS,sBAAsB,GAAG;QAC5D;QACA,IAAI;YACF,MAAM,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAC,kBAAkB;YACtC;QACF,EAAE,OAAO,OAAgB;YACvB,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC9D,QAAQ,KAAK,CAAC,oDAAoD;QACpE;IACF;IAEA,qBACE,8OAAC,oLAAA,CAAA,OAAI;QAAC,UAAU;;0BACd,8OAAC,oLAAA,CAAA,OAAI,CAAC,KAAK;gBAAC,WAAU;;kCAAO,8OAAC,oLAAA,CAAA,OAAI,CAAC,KAAK;kCAAC;;;;;;kCAA8B,8OAAC,oLAAA,CAAA,OAAI,CAAC,OAAO;wBAAC,MAAK;wBAAS,MAAK;wBAAO,OAAO,SAAS,kBAAkB;wBAAE,UAAU,CAAC,IAAM,YAAY;gCAAE,GAAG,QAAQ;gCAAE,oBAAoB,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;4BAAE;wBAAI,QAAQ;;;;;;kCAAG,8OAAC,oLAAA,CAAA,OAAI,CAAC,IAAI;wBAAC,WAAU;kCAAa;;;;;;;;;;;;0BACpS,8OAAC,oLAAA,CAAA,OAAI,CAAC,KAAK;gBAAC,WAAU;;kCAAO,8OAAC,oLAAA,CAAA,OAAI,CAAC,KAAK;kCAAC;;;;;;kCAAyC,8OAAC,oLAAA,CAAA,OAAI,CAAC,OAAO;wBAAC,MAAK;wBAAS,MAAK;wBAAM,KAAI;wBAAI,KAAI;wBAAK,OAAO,SAAS,sBAAsB;wBAAE,UAAU,CAAC,IAAM,YAAY;gCAAE,GAAG,QAAQ;gCAAE,wBAAwB,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;4BAAE;wBAAI,QAAQ;;;;;;kCAAG,8OAAC,oLAAA,CAAA,OAAI,CAAC,IAAI;wBAAC,WAAU;kCAAa;;;;;;;;;;;;0BACvU,8OAAC;gBAAI,WAAU;;kCAAmC,8OAAC,wLAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAY,SAAS;kCAAU;;;;;;kCAAgB,8OAAC,wLAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAU,MAAK;kCAAS;;;;;;;;;;;;;;;;;;AAG/J;AAEA,MAAM,oBAAgE,CAAC,EAAE,oBAAoB,EAAE;IAC7F,MAAM,aAA0B;QAC9B;YAAE,IAAI;YAAG,MAAM;YAAmD,aAAa;YAAiC,WAAW;YAAa,SAAS;YAAgB,UAAU;YAAI,QAAQ;YAAI,eAAe;gBAAC;gBAAgC;gBAA6C;gBAA4C;aAAkC;YAAE,oBAAoB;gBAAC;gBAA2C;gBAAqC;gBAAkC;aAAiC;YAAE,QAAQ;QAAW;QACriB;YAAE,IAAI;YAAG,MAAM;YAAmC,aAAa;YAAoC,WAAW;YAAa,SAAS;YAAY,UAAU;YAAI,QAAQ;YAAI,eAAe;gBAAC;gBAAwC;gBAAuC;aAAqD;YAAE,oBAAoB;gBAAC;gBAAgD;gBAA2C;gBAA0B;aAA8B;YAAE,QAAQ;QAAS;QAC3f;YAAE,IAAI;YAAG,MAAM;YAAgC,aAAa;YAAwC,WAAW;YAAY,SAAS;YAAc,UAAU;YAAI,QAAQ;YAAI,cAAc;YAAQ,eAAe;gBAAC;gBAAmC;gBAA4C;aAA6C;YAAE,oBAAoB;gBAAC;gBAA0C;gBAAoC;gBAAsC;aAA+B;YAAE,QAAQ;QAAU;QAC5gB;YAAE,IAAI;YAAG,MAAM;YAA6B,aAAa;YAAuC,WAAW;YAAQ,SAAS;YAAQ,UAAU;YAAI,QAAQ;YAAI,cAAc;YAAS,eAAe;gBAAC;gBAAiD;gBAAyC;aAA4D;YAAE,oBAAoB;gBAAC;gBAA2C;gBAA8C;gBAAsC;aAA4B;YAAE,QAAQ;QAAU;QACjiB;YAAE,IAAI;YAAG,MAAM;YAAkD,aAAa;YAAqC,WAAW;YAAQ,SAAS;YAAS,UAAU;YAAI,QAAQ;YAAI,eAAe;gBAAC;gBAA6C;gBAAsC;aAA8C;YAAE,oBAAoB;gBAAC;gBAAsC;gBAA4B;gBAA+B;aAAwC;YAAE,QAAQ;QAAU;KACxf;IAED,MAAM,yBAAyB;QAC7B,MAAM,cAAc,IAAI,OAAO,WAAW;QAC1C,MAAM,YAAY;QAClB,MAAM,aAAa,cAAc;QACjC,IAAI,kBAAkB;QAEtB,IAAI,wBAAwB,QAAQ;YAClC,IAAI,cAAc,IAAI,kBAAkB;iBACnC,IAAI,cAAc,IAAI,kBAAkB;iBACxC,kBAAkB;QACzB,OAAO,IAAI,cAAc,MAAM,wBAAwB,QAAQ;YAC7D,kBAAkB;QACpB;QAEA,IAAI,yBAAyB;QAC7B,IAAI,mBAAmB;QACvB,MAAM,mBAAmB,UAAU,CAAC,gBAAgB;QAEpD,IAAI,oBAAoB,GAAG;YACvB,yBAAyB,iBAAiB,YAAY,GAAG,AAAC,uBAAuB,iBAAiB,YAAY,GAAI,MAAM,AAAC,uBAAuB,SAAU,KAAM,yDAAyD;YACzN,MAAM,oBAAoB;YAC1B,MAAM,kBAAkB,CAAC,iBAAiB,YAAY,IAAI,MAAM,IAAI;YACpE,mBAAmB,KAAK,GAAG,CAAC,GAAG,kBAAkB,IAAI,kBAAkB,CAAC,oBAAoB,EAAE,IAAI;QACtG,OAAO,IAAI,kBAAkB;YACzB,MAAM,gBAAgB,iBAAiB,MAAM,GAAG,iBAAiB,QAAQ;YACzE,MAAM,eAAe,KAAK,GAAG,CAAC,GAAG,aAAa,iBAAiB,QAAQ;YACvE,yBAAyB,gBAAgB,IAAI,AAAC,eAAe,gBAAiB,MAAO,cAAc,iBAAiB,QAAQ,GAAG,MAAK;YACpI,mBAAmB,KAAK,GAAG,CAAC,GAAG,iBAAiB,MAAM,GAAG;YACzD,IAAI,iBAAiB,YAAY,IAAI,uBAAuB,iBAAiB,YAAY,IAAI,oBAAoB,GAAG;YAChH,iHAAiH;YACjH,uFAAuF;YAC3F;QACJ;QAEA,OAAO;YAAE,cAAc;YAAiB,wBAAwB,KAAK,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC,GAAE;YAA0B;YAAkB;QAAW;IAClJ;IAEA,MAAM,gBAAgB;IACtB,MAAM,mBAAmB,WAAW,GAAG,CAAC,CAAC,OAAO,QAAU,CAAC;YAAE,GAAG,KAAK;YAAE,QAAQ,QAAQ,cAAc,YAAY,GAAG,cAAc,UAAU,cAAc,YAAY,GAAG,YAAY;QAAkD,CAAC;IACxO,MAAM,gBAAgB,CAAC,SAAoB,CAAA;YAAE,WAAW;YAAW,SAAS;YAAW,UAAU;QAAY,CAAA,CAAC,CAAC,OAAO,IAAI;IAC1H,MAAM,eAAe,CAAC,SAAoB,CAAA;YAAE,WAAW;YAAK,SAAS;YAAM,UAAU;QAAI,CAAA,CAAC,CAAC,OAAO,IAAI;IAEtG,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAG,WAAU;0BAAO;;;;;;0BACrB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCAAW,cAAA,8OAAC;4BAAI,WAAU;sCAA0B,cAAA,8OAAC;gCAAI,WAAU;;kDAAwB,8OAAC;kDAAG;;;;;;kDAAmB,8OAAC;;4CAAG;4CAAO,cAAc,YAAY;;;;;;;kDAAM,8OAAC;kDAAO,gBAAgB,CAAC,cAAc,YAAY,CAAC,EAAE,KAAK,MAAM,IAAI,CAAC,EAAE,IAAI;;;;;;;;;;;;;;;;;;;;;;kCACxP,8OAAC;wBAAI,WAAU;kCAAW,cAAA,8OAAC;4BAAI,WAAU;sCAA6B,cAAA,8OAAC;gCAAI,WAAU;;kDAAwB,8OAAC;kDAAG;;;;;;kDAAe,8OAAC;;4CAAI,cAAc,UAAU;4CAAC;;;;;;;kDAAS,8OAAC;kDAAM;;;;;;;;;;;;;;;;;;;;;;kCAC9K,8OAAC;wBAAI,WAAU;kCAAW,cAAA,8OAAC;4BAAI,WAAU;sCAA6B,cAAA,8OAAC;gCAAI,WAAU;;kDAAwB,8OAAC;kDAAG;;;;;;kDAAsB,8OAAC;;4CAAI,cAAc,sBAAsB,CAAC,OAAO,CAAC;4CAAG;;;;;;;kDAAM,8OAAC;kDAAM;;;;;;;;;;;;;;;;;;;;;;kCACzM,8OAAC;wBAAI,WAAU;kCAAW,cAAA,8OAAC;4BAAI,WAAU;sCAA4B,cAAA,8OAAC;gCAAI,WAAU;;kDAAwB,8OAAC;kDAAG;;;;;;kDAAoB,8OAAC;;4CAAI,cAAc,gBAAgB,CAAC,OAAO,CAAC;4CAAG;;;;;;;kDAAS,8OAAC;kDAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAErM,8OAAC;gBAAI,WAAU;;kCAAO,8OAAC;wBAAI,WAAU;kCAAc,cAAA,8OAAC;4BAAG,WAAU;sCAAO;;;;;;;;;;;kCACtE,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,8LAAA,CAAA,YAAS;4BAAC,kBAAkB,cAAc,YAAY,CAAC,QAAQ;sCAC7D,iBAAiB,GAAG,CAAC,CAAC,OAAO,sBAC5B,8OAAC,8LAAA,CAAA,YAAS,CAAC,IAAI;oCAAC,UAAU,MAAM,QAAQ;;sDACtC,8OAAC,8LAAA,CAAA,YAAS,CAAC,MAAM;sDAAC,cAAA,8OAAC;gDAAI,WAAU;;kEAAkC,8OAAC;wDAAK,WAAU;kEAAQ,aAAa,MAAM,MAAM;;;;;;kEAAS,8OAAC;wDAAI,WAAU;;0EAAc,8OAAC;0EAAQ,MAAM,IAAI;;;;;;0EAAU,8OAAC,sLAAA,CAAA,QAAK;gEAAC,IAAI,cAAc,MAAM,MAAM;gEAAG,WAAU;0EAAQ,MAAM,MAAM,KAAK,cAAc,aAAa,MAAM,MAAM,KAAK,YAAY,aAAa;;;;;;;;;;;;kEAAwB,8OAAC;wDAAM,WAAU;;4DAAc,MAAM,SAAS;4DAAC;4DAAI,MAAM,OAAO;4DAAC;4DAAO,MAAM,QAAQ;4DAAC;4DAAE,MAAM,MAAM;4DAAC;;;;;;;;;;;;;;;;;;sDACrc,8OAAC,8LAAA,CAAA,YAAS,CAAC,IAAI;;8DAAC,8OAAC;oDAAI,WAAU;;sEAC3B,8OAAC;4DAAI,WAAU;;8EAAW,8OAAC;8EAAG;;;;;;8EAAqB,8OAAC;8EAAI,MAAM,aAAa,CAAC,GAAG,CAAC,CAAC,WAAW,oBAAS,8OAAC;sFAAc;2EAAN;;;;;;;;;;gEAA8B,MAAM,YAAY,kBAAK,8OAAC;;sFAAE,8OAAC;sFAAO;;;;;;wEAA8B;wEAAE,IAAI,KAAK,YAAY,CAAC,SAAS;4EAAE,OAAO;4EAAY,UAAU;wEAAM,GAAG,MAAM,CAAC,MAAM,YAAY;;;;;;;;;;;;;sEAC9S,8OAAC;4DAAI,WAAU;;8EAAW,8OAAC;8EAAG;;;;;;8EAA8B,8OAAC;8EAAI,MAAM,kBAAkB,CAAC,GAAG,CAAC,CAAC,QAAQ,oBAAS,8OAAC;sFAAc;2EAAN;;;;;;;;;;;;;;;;;;;;;;gDACtH,MAAM,MAAM,KAAK,2BAAc,8OAAC;oDAAI,WAAU;;sEAAO,8OAAC;sEAAG;;;;;;sEAAmC,8OAAC,kMAAA,CAAA,cAAW;4DAAC,KAAK,cAAc,sBAAsB;4DAAE,OAAO,GAAG,cAAc,sBAAsB,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;4DAAE,SAAS,cAAc,MAAM,MAAM;4DAAG,OAAO;gEAAE,QAAQ;4DAAO;;;;;;sEAAK,8OAAC;4DAAM,WAAU;;gEAAa;gEAAc,cAAc,gBAAgB,CAAC,OAAO,CAAC;gEAAG;;;;;;;;;;;;;;;;;;;;mCALnU,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYvE;AAEA,MAAM,iBAA2B;IAC/B,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAyB;IACxD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,sBAAsB;QAC1B,WAAW;QACX,SAAS;QACT,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAC,gBACX,IAAI,CAAC,CAAA;YAAc,QAAQ,SAAS,IAAI;YAAG,WAAW;YAAQ,cAAc;QAAI,GAChF,KAAK,CAAC,CAAA;YACL,QAAQ,KAAK,CAAC,yBAAyB;YACvC,IAAI,aAAa,GAAG;gBAAE,cAAc,CAAA,OAAQ,OAAO;gBAAI,WAAW,IAAM,uBAAuB;YAAO,OACjG;gBAAE,SAAS;gBAAqD,WAAW;YAAQ;QAC1F;IACJ;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QAAQ,uBAAuB,uDAAuD;IAChG,GAAG,EAAE;IACL,MAAM,qBAAqB;QAAQ,aAAa;QAAQ;IAAuB;IAE/E,IAAI,WAAW,eAAe,GAAG,qBAAO,8OAAC;kBAAE;;;;;;IAC3C,IAAI,SAAS,cAAa,GAAG,qBAAQ,8OAAC;;0BAAI,8OAAC;gBAAE,WAAU;0BAAe;;;;;;0BAAU,8OAAC;gBAAO,WAAU;gBAAkB,SAAS;oBAAO,cAAc;oBAAI;gBAAsB;0BAAG;;;;;;;;;;;;IAC/K,IAAI,CAAC,QAAQ,CAAC,SAAS,qBAAO,8OAAC;kBAAE;;;;;;IACjC,IAAI,SAAS,qBAAO,8OAAC;;YAAE;YAA+C,aAAa;YAAE;;;;;;;IACrF,IAAI,CAAC,MAAM,qBAAO,8OAAC;kBAAE;;;;;cAAmC,iBAAiB;IAEzE,MAAM,iBAAiB,CAAC,QAAkB,IAAI,KAAK,YAAY,CAAC,SAAS;YAAE,OAAO;YAAY,UAAU;QAAM,GAAG,MAAM,CAAC;IACxH,MAAM,mBAAmB,CAAC,QAAkB,IAAI,KAAK,YAAY,CAAC,SAAS;YAAE,OAAO;YAAW,uBAAuB;QAAE,GAAG,MAAM,CAAC,QAAQ;IAE1I,qBACE,8OAAC;;0BACC,8OAAC;gBAAI,WAAU;;kCAAyD,8OAAC;kCAAG;;;;;;kCAAkB,8OAAC,wLAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAU,SAAS,IAAM,aAAa;kCAAO;;;;;;;;;;;;0BAC3J,8OAAC;gBAAI,WAAU;0BAAW,cAAA,8OAAC;oBAAI,WAAU;8BAAS,cAAA,8OAAC;wBAAI,WAAU;kCAAO,cAAA,8OAAC;4BAAI,WAAU;;8CACrF,8OAAC;oCAAG,WAAU;8CAAa;;;;;;8CAC3B,8OAAC;oCAAI,WAAU;;sDACX,8OAAC;4CAAI,WAAU;sDAAW,cAAA,8OAAC;gDAAI,WAAU;;kEAAc,8OAAC;wDAAG,WAAU;kEAAgB,eAAe,KAAK,sBAAsB;;;;;;kEAAO,8OAAC;wDAAM,WAAU;kEAAa;;;;;;;;;;;;;;;;;sDACpK,8OAAC;4CAAI,WAAU;sDAAW,cAAA,8OAAC;gDAAI,WAAU;;kEAAc,8OAAC;wDAAG,WAAU;kEAAgB,eAAe,KAAK,kBAAkB;;;;;;kEAAO,8OAAC;wDAAM,WAAU;kEAAa;;;;;;;;;;;;;;;;;sDAChK,8OAAC;4CAAI,WAAU;sDAAW,cAAA,8OAAC;gDAAI,WAAU;;kEAAc,8OAAC;wDAAG,WAAU;kEAAa,iBAAiB,KAAK,mBAAmB;;;;;;kEAAO,8OAAC;wDAAM,WAAU;kEAAa;;;;;;;;;;;;;;;;;;;;;;;8CAEpK,8OAAC,kMAAA,CAAA,cAAW;oCAAC,KAAK,KAAK,mBAAmB;oCAAE,OAAO,GAAG,KAAK,mBAAmB,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;oCAAE,OAAO;wCAAE,QAAQ;oCAAO;oCAAG,WAAU;;;;;;8CACnI,8OAAC;oCAAI,WAAU;;sDACX,8OAAC;4CAAI,WAAU;;8DAAW,8OAAC;oDAAE,WAAU;8DAAO,cAAA,8OAAC;kEAAO;;;;;;;;;;;8DAAyC,8OAAC;oDAAE,WAAU;8DAAoB,eAAe,KAAK,GAAG,CAAC,GAAG,KAAK,mBAAmB;;;;;;;;;;;;sDACnL,8OAAC;4CAAI,WAAU;;8DAAW,8OAAC;oDAAE,WAAU;8DAAO,cAAA,8OAAC;kEAAO;;;;;;;;;;;8DAA6C,8OAAC;oDAAE,WAAU;8DAAqB,eAAe,KAAK,wBAAwB;;;;;;8DAAM,8OAAC;oDAAM,WAAU;;wDAAa;wDAA8F,iBAAiB,KAAK,sBAAsB,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAGxW,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCAAW,cAAA,8OAAC;4BAAI,WAAU;;8CAAkC,8OAAC;oCAAI,WAAU;8CAAc;;;;;;8CAAkB,8OAAC;oCAAI,WAAU;;sDAAY,8OAAC;4CAAG,WAAU;sDAAc,eAAe,KAAK,YAAY;;;;;;sDAAO,8OAAC;4CAAE,WAAU;sDAAY;;;;;;;;;;;;;;;;;;;;;;;kCACjP,8OAAC;wBAAI,WAAU;kCAAW,cAAA,8OAAC;4BAAI,WAAU;;8CAAiC,8OAAC;oCAAI,WAAU;8CAAc;;;;;;8CAAmB,8OAAC;oCAAI,WAAU;;sDAAY,8OAAC;4CAAG,WAAU;sDAAc,eAAe,KAAK,iBAAiB;;;;;;sDAAO,8OAAC;4CAAE,WAAU;sDAAY;;;;;;;;;;;;;;;;;;;;;;;kCACtP,8OAAC;wBAAI,WAAU;kCAAW,cAAA,8OAAC;4BAAI,WAAU;;8CAA+B,8OAAC;oCAAI,WAAU;8CAAc;;;;;;8CAA8B,8OAAC;oCAAI,WAAU;;sDAAY,8OAAC;4CAAG,WAAU;sDAAc,iBAAiB,KAAK,sBAAsB,GAAG;;;;;;sDAAU,8OAAC;4CAAE,WAAU;sDAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAE9Q,8OAAC;gBAAkB,sBAAsB,KAAK,sBAAsB;;;;;;0BACpE,8OAAC,sLAAA,CAAA,QAAK;gBAAC,MAAM;gBAAW,QAAQ,IAAM,aAAa;gBAAQ,MAAK;;kCAAK,8OAAC,sLAAA,CAAA,QAAK,CAAC,MAAM;wBAAC,WAAW;kCAAC,cAAA,8OAAC,sLAAA,CAAA,QAAK,CAAC,KAAK;sCAAC;;;;;;;;;;;kCAAyD,8OAAC,sLAAA,CAAA,QAAK,CAAC,IAAI;kCAAC,cAAA,8OAAC;4BAAiB,UAAU,OAAO;gCAAE,oBAAoB,KAAK,kBAAkB;gCAAE,wBAAwB,KAAK,sBAAsB;4BAAC,IAAI;4BAAM,QAAQ;4BAAoB,UAAU,IAAM,aAAa;;;;;;;;;;;;;;;;;;;;;;;AAGhY;uCAEe", "debugId": null}}]}